package com.weinuo.quickcommands.utils

import android.content.Context
import android.net.Uri
import android.provider.DocumentsContract
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.activity.compose.rememberLauncherForActivityResult

/**
 * 文件选择器工具类
 *
 * 提供系统文件选择器和目录选择器的调用功能，
 * 支持不同文件类型的选择和目录选择。
 */
object FilePickerUtil {

    /**
     * 创建目录选择器启动器
     *
     * @param onDirectorySelected 目录选择回调，参数为选择的目录URI
     * @return ActivityResultLauncher用于启动目录选择器
     */
    @Composable
    fun rememberDirectoryPickerLauncher(
        onDirectorySelected: (Uri?) -> Unit
    ): ActivityResultLauncher<Uri?> {
        return rememberLauncherForActivityResult(
            contract = ActivityResultContracts.OpenDocumentTree()
        ) { uri ->
            onDirectorySelected(uri)
        }
    }

    /**
     * 创建文件选择器启动器
     *
     * @param onFileSelected 文件选择回调，参数为选择的文件URI
     * @return ActivityResultLauncher用于启动文件选择器
     */
    @Composable
    fun rememberFilePickerLauncher(
        onFileSelected: (Uri?) -> Unit
    ): ActivityResultLauncher<Array<String>> {
        return rememberLauncherForActivityResult(
            contract = ActivityResultContracts.OpenDocument()
        ) { uri ->
            onFileSelected(uri)
        }
    }

    /**
     * 创建多文件选择器启动器
     *
     * @param onFilesSelected 文件选择回调，参数为选择的文件URI列表
     * @return ActivityResultLauncher用于启动多文件选择器
     */
    @Composable
    fun rememberMultipleFilePickerLauncher(
        onFilesSelected: (List<Uri>) -> Unit
    ): ActivityResultLauncher<Array<String>> {
        return rememberLauncherForActivityResult(
            contract = ActivityResultContracts.OpenMultipleDocuments()
        ) { uris ->
            onFilesSelected(uris)
        }
    }

    /**
     * 获取不同文件选择模式对应的MIME类型数组
     *
     * @param selectionMode 文件选择模式
     * @return MIME类型数组
     */
    fun getMimeTypesForSelectionMode(selectionMode: com.weinuo.quickcommands.model.FileSelectionMode): Array<String> {
        return when (selectionMode) {
            com.weinuo.quickcommands.model.FileSelectionMode.ALL_FILES -> arrayOf("*/*")
            com.weinuo.quickcommands.model.FileSelectionMode.ALL_MEDIA -> arrayOf(
                "image/*", "video/*", "audio/*"
            )
            com.weinuo.quickcommands.model.FileSelectionMode.IMAGES -> arrayOf(
                "image/*"
            )
            com.weinuo.quickcommands.model.FileSelectionMode.VIDEOS -> arrayOf(
                "video/*"
            )
            com.weinuo.quickcommands.model.FileSelectionMode.AUDIO -> arrayOf(
                "audio/*"
            )
            com.weinuo.quickcommands.model.FileSelectionMode.SPECIFIC_PATTERN -> arrayOf("*/*")
            com.weinuo.quickcommands.model.FileSelectionMode.FOLDERS -> arrayOf("*/*") // 文件夹模式使用目录选择器
        }
    }

    /**
     * 将URI转换为可读的路径字符串
     *
     * @param context 上下文
     * @param uri 文件或目录URI
     * @return 可读的路径字符串
     */
    fun getDisplayPath(context: Context, uri: Uri): String {
        return try {
            // 尝试获取显示名称
            val cursor = context.contentResolver.query(
                uri,
                arrayOf(DocumentsContract.Document.COLUMN_DISPLAY_NAME),
                null,
                null,
                null
            )
            cursor?.use {
                if (it.moveToFirst()) {
                    val displayName = it.getString(0)
                    if (!displayName.isNullOrEmpty()) {
                        return displayName
                    }
                }
            }

            // 如果无法获取显示名称，使用URI的最后一段
            uri.lastPathSegment ?: uri.toString()
        } catch (e: Exception) {
            // 如果出现异常，返回URI字符串
            uri.toString()
        }
    }

    /**
     * 检查URI是否为目录
     *
     * @param context 上下文
     * @param uri 要检查的URI
     * @return 是否为目录
     */
    fun isDirectory(context: Context, uri: Uri): Boolean {
        return try {
            val cursor = context.contentResolver.query(
                uri,
                arrayOf(DocumentsContract.Document.COLUMN_MIME_TYPE),
                null,
                null,
                null
            )
            cursor?.use {
                if (it.moveToFirst()) {
                    val mimeType = it.getString(0)
                    return DocumentsContract.Document.MIME_TYPE_DIR == mimeType
                }
            }
            false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取URI的真实路径（如果可能）
     *
     * @param context 上下文
     * @param uri 文件URI
     * @return 真实路径字符串，如果无法获取则返回null
     */
    fun getRealPath(context: Context, uri: Uri): String? {
        return try {
            // 对于file://类型的URI，直接返回路径
            if (uri.scheme == "file") {
                return uri.path
            }

            // 对于content://类型的URI，尝试解析真实路径
            if (uri.scheme == "content") {
                // 尝试从DocumentsContract解析路径
                if (DocumentsContract.isDocumentUri(context, uri)) {
                    val docId = DocumentsContract.getDocumentId(uri)

                    // 处理外部存储文档
                    if (isExternalStorageDocument(uri)) {
                        val split = docId.split(":")
                        if (split.size >= 2) {
                            val type = split[0]
                            if ("primary".equals(type, ignoreCase = true)) {
                                return "${android.os.Environment.getExternalStorageDirectory()}/${split[1]}"
                            }
                        }
                    }
                    // 处理下载文档
                    else if (isDownloadsDocument(uri)) {
                        if (docId.startsWith("raw:")) {
                            return docId.replaceFirst("raw:", "")
                        }
                    }
                    // 处理媒体文档
                    else if (isMediaDocument(uri)) {
                        val split = docId.split(":")
                        if (split.size >= 2) {
                            val type = split[0]
                            val contentUri = when (type) {
                                "image" -> android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                                "video" -> android.provider.MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                                "audio" -> android.provider.MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                                else -> null
                            }

                            contentUri?.let {
                                val selection = "_id=?"
                                val selectionArgs = arrayOf(split[1])
                                return getDataColumn(context, it, selection, selectionArgs)
                            }
                        }
                    }
                }

                // 如果无法解析为真实路径，返回URI字符串
                return uri.toString()
            }

            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 检查URI是否为外部存储文档
     */
    private fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }

    /**
     * 检查URI是否为下载文档
     */
    private fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }

    /**
     * 检查URI是否为媒体文档
     */
    private fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }

    /**
     * 从内容提供者获取数据列
     */
    private fun getDataColumn(
        context: Context,
        uri: Uri,
        selection: String?,
        selectionArgs: Array<String>?
    ): String? {
        return try {
            val cursor = context.contentResolver.query(
                uri,
                arrayOf(android.provider.MediaStore.Images.Media.DATA),
                selection,
                selectionArgs,
                null
            )
            cursor?.use {
                if (it.moveToFirst()) {
                    val columnIndex = it.getColumnIndexOrThrow(android.provider.MediaStore.Images.Media.DATA)
                    return it.getString(columnIndex)
                }
            }
            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取目录URI的真实路径
     *
     * @param context 上下文
     * @param uri 目录URI
     * @return 真实目录路径，如果无法获取则返回null
     */
    fun getDirectoryRealPath(context: Context, uri: Uri): String? {
        return try {
            // 对于file://类型的URI，直接返回路径
            if (uri.scheme == "file") {
                return uri.path
            }

            // 对于content://类型的URI，尝试解析真实路径
            if (uri.scheme == "content" && DocumentsContract.isDocumentUri(context, uri)) {
                val docId = DocumentsContract.getDocumentId(uri)

                // 处理外部存储文档
                if (isExternalStorageDocument(uri)) {
                    val split = docId.split(":")
                    if (split.size >= 2) {
                        val type = split[0]
                        if ("primary".equals(type, ignoreCase = true)) {
                            val path = if (split[1].isEmpty()) {
                                android.os.Environment.getExternalStorageDirectory().absolutePath
                            } else {
                                "${android.os.Environment.getExternalStorageDirectory()}/${split[1]}"
                            }
                            return path
                        }
                    }
                }
            }

            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 启动目录选择器
     *
     * @param launcher 目录选择器启动器
     * @param initialUri 初始目录URI（可选）
     */
    fun launchDirectoryPicker(
        launcher: ActivityResultLauncher<Uri?>,
        initialUri: Uri? = null
    ) {
        launcher.launch(initialUri)
    }

    /**
     * 启动文件选择器
     *
     * @param launcher 文件选择器启动器
     * @param mimeTypes 允许的MIME类型数组
     */
    fun launchFilePicker(
        launcher: ActivityResultLauncher<Array<String>>,
        mimeTypes: Array<String>
    ) {
        launcher.launch(mimeTypes)
    }

    /**
     * 启动多文件选择器
     *
     * @param launcher 多文件选择器启动器
     * @param mimeTypes 允许的MIME类型数组
     */
    fun launchMultipleFilePicker(
        launcher: ActivityResultLauncher<Array<String>>,
        mimeTypes: Array<String>
    ) {
        launcher.launch(mimeTypes)
    }
}

package com.weinuo.quickcommands.floating

import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.WindowManager
import com.weinuo.quickcommands.utils.OverlayPermissionUtil

/**
 * 悬浮加速球服务
 *
 * 负责悬浮加速球的具体实现，包括：
 * - 悬浮窗创建和管理
 * - 内存监控
 * - 拖动处理
 * - 动画效果
 */
class FloatingAcceleratorService : Service() {

    companion object {
        private const val TAG = "FloatingAcceleratorService"
        private const val ACTION_START = "action_start"
        private const val ACTION_STOP = "action_stop"

        /**
         * 启动悬浮加速球窗口
         */
        fun startFloatingWindow(context: Context) {
            val intent = Intent(context, FloatingAcceleratorService::class.java).apply {
                action = ACTION_START
            }
            context.startService(intent)
        }

        /**
         * 停止悬浮加速球窗口
         */
        fun stopFloatingWindow(context: Context) {
            val intent = Intent(context, FloatingAcceleratorService::class.java).apply {
                action = ACTION_STOP
            }
            context.startService(intent)
        }
    }

    private var windowManager: WindowManager? = null
    private var floatingAcceleratorBall: FloatingAcceleratorBall? = null
    private var memoryMonitor: MemoryMonitor? = null

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "FloatingAcceleratorService created")
        
        // 初始化内存监控器
        memoryMonitor = MemoryMonitor(this) { memoryPercentage ->
            // 更新悬浮球显示的内存占用百分比
            floatingAcceleratorBall?.updateMemoryPercentage(memoryPercentage)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START -> {
                Log.d(TAG, "Starting floating accelerator")
                startFloatingAccelerator()
            }
            ACTION_STOP -> {
                Log.d(TAG, "Stopping floating accelerator")
                stopFloatingAccelerator()
            }
        }
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "FloatingAcceleratorService destroyed")
        stopFloatingAccelerator()
    }

    /**
     * 启动悬浮加速球
     */
    private fun startFloatingAccelerator() {
        // 检查悬浮窗权限
        if (!OverlayPermissionUtil.hasOverlayPermission(this)) {
            Log.w(TAG, "没有悬浮窗权限，无法显示悬浮加速球")
            stopSelf()
            return
        }

        // 如果加速球已存在，先移除
        if (floatingAcceleratorBall != null) {
            stopFloatingAccelerator()
        }

        try {
            // 创建并显示悬浮加速球
            floatingAcceleratorBall = FloatingAcceleratorBall(
                context = this,
                onMemoryCleanup = { performMemoryCleanup() },
                onDragStart = { startDragAnimation() },
                onDragEnd = { endDragAnimation() }
            )

            if (floatingAcceleratorBall?.show() == true) {
                Log.d(TAG, "悬浮加速球已启动")
                // 开始内存监控
                memoryMonitor?.startMonitoring()
            } else {
                Log.e(TAG, "显示悬浮加速球失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动悬浮加速球失败", e)
            stopSelf()
        }
    }

    /**
     * 停止悬浮加速球
     */
    private fun stopFloatingAccelerator() {
        try {
            // 停止内存监控
            memoryMonitor?.stopMonitoring()
            
            // 隐藏悬浮加速球
            floatingAcceleratorBall?.hide()
            floatingAcceleratorBall = null
            
            Log.d(TAG, "悬浮加速球已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止悬浮加速球失败", e)
        }
        
        stopSelf()
    }

    /**
     * 执行内存清理
     */
    private fun performMemoryCleanup() {
        Log.d(TAG, "执行内存清理")
        // TODO: 实现内存清理逻辑
        // 可以调用系统的垃圾回收或其他清理机制
        System.gc()
    }

    /**
     * 开始拖动动画
     */
    private fun startDragAnimation() {
        Log.d(TAG, "开始拖动动画")
        // TODO: 实现拖动动画逻辑
        // 显示夜空背景、云朵、水体、鲸鱼等动画元素
    }

    /**
     * 结束拖动动画
     */
    private fun endDragAnimation() {
        Log.d(TAG, "结束拖动动画")
        // TODO: 实现拖动结束动画逻辑
        // 隐藏动画元素，恢复静止状态
    }
}

/**
 * 内存监控器
 */
class MemoryMonitor(
    private val context: Context,
    private val onMemoryUpdate: (Int) -> Unit
) {
    private var isMonitoring = false
    private var monitoringThread: Thread? = null

    /**
     * 开始监控内存
     */
    fun startMonitoring() {
        if (isMonitoring) return

        isMonitoring = true
        monitoringThread = Thread {
            while (isMonitoring) {
                try {
                    val memoryPercentage = getCurrentMemoryUsagePercentage()
                    onMemoryUpdate(memoryPercentage)
                    Thread.sleep(1000) // 每秒更新一次
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    Log.e("MemoryMonitor", "内存监控出错", e)
                }
            }
        }
        monitoringThread?.start()
    }

    /**
     * 停止监控内存
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitoringThread?.interrupt()
        monitoringThread = null
    }

    /**
     * 获取当前内存使用百分比
     */
    private fun getCurrentMemoryUsagePercentage(): Int {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val memoryInfo = android.app.ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val totalMemory = memoryInfo.totalMem
            val availableMemory = memoryInfo.availMem
            val usedMemory = totalMemory - availableMemory
            val memoryPercentage = (usedMemory.toFloat() / totalMemory * 100).toInt()

            Log.d("MemoryMonitor", "内存使用率: ${memoryPercentage}%")
            memoryPercentage.coerceIn(0, 100)
        } catch (e: Exception) {
            Log.e("MemoryMonitor", "获取内存使用率失败", e)
            0
        }
    }
}

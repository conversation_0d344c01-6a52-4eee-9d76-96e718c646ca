# 天空蓝主题对话框组件使用文档

## 概述

天空蓝主题对话框组件是一个完全自定义的对话框实现，专为天空蓝主题设计。它**完全不依赖Material Design 3**，支持动态圆角大小控制、iOS风格的背景模糊效果，并完全集成了全局设置系统。

## 主要特点

### 1. 完全自定义实现
- **不依赖Material Design 3**，避免MD3不支持模糊效果的限制
- 完全基于Compose基础组件构建
- 自定义按钮、卡片等UI组件
- 完全符合天空蓝主题的设计语言

### 2. 动态圆角控制
- 圆角大小可通过全局设置实时调整（0-50dp）
- 自动从`GlobalSettings.dialogCornerRadius`读取配置
- 支持实时预览，无需重启应用

### 3. 智能模糊效果
- 使用Haze库实现iOS风格的背景模糊
- 支持所有Android版本（包括Android 11及以下）
- 可通过全局设置开关控制（`GlobalSettings.dialogBlurEnabled`）
- 支持模糊强度调节（`GlobalSettings.dialogBlurIntensity`，范围0.0-1.0）

### 4. 智能键盘避让
- 自动检测键盘显示/隐藏状态
- 键盘出现时对话框自动向上移动，避免被遮挡
- 平滑的动画过渡效果
- 智能边界检查，确保对话框不会移出屏幕

### 5. 完整的功能支持
- 支持所有标准对话框功能
- 完整的可访问性支持
- 键盘导航友好
- 自定义样式和颜色

## 组件列表

### 1. SkyBlueDialog（完整版本）
最完整的对话框组件，支持所有自定义选项。**完全不依赖MD3**。

```kotlin
@Composable
fun SkyBlueDialog(
    onDismissRequest: () -> Unit,
    settingsRepository: SettingsRepository,
    confirmButton: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    dismissButton: @Composable (() -> Unit)? = null,
    icon: @Composable (() -> Unit)? = null,
    title: @Composable (() -> Unit)? = null,
    text: @Composable (() -> Unit)? = null,
    containerColor: Color = Color.White,
    iconContentColor: Color = Color(0xFF0A59F7),
    titleContentColor: Color = Color(0xE5000000),
    textContentColor: Color = Color(0x99000000),
    properties: DialogProperties = DialogProperties(),
    maxHeight: Dp = 400.dp
)
```

### 1.1. SkyBlueDialogButton（自定义按钮）
专为对话框设计的自定义按钮组件，不依赖MD3。

```kotlin
@Composable
fun SkyBlueDialogButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isPrimary: Boolean = false,
    enabled: Boolean = true
)
```

### 2. SkyBlueDialog（简化版本）
适用于简单文本对话框的便捷版本。

```kotlin
@Composable
fun SkyBlueDialog(
    onDismissRequest: () -> Unit,
    settingsRepository: SettingsRepository,
    title: String,
    message: String,
    confirmText: String,
    onConfirm: () -> Unit,
    dismissText: String? = null,
    onDismiss: (() -> Unit)? = null,
    maxHeight: Dp = 400.dp
)
```

### 3. SkyBlueSelectionDialog
专门用于显示选择列表的对话框。

```kotlin
@Composable
fun <T> SkyBlueSelectionDialog(
    onDismissRequest: () -> Unit,
    settingsRepository: SettingsRepository,
    title: String,
    description: String? = null,
    items: List<T>,
    onItemSelected: (T) -> Unit,
    itemContent: @Composable (T) -> Unit,
    maxHeight: Dp = 400.dp
)
```

## 使用示例

### 基本文本对话框

```kotlin
@Composable
fun BasicDialogExample(settingsRepository: SettingsRepository) {
    var showDialog by remember { mutableStateOf(false) }
    
    Button(onClick = { showDialog = true }) {
        Text("显示对话框")
    }
    
    if (showDialog) {
        SkyBlueDialog(
            onDismissRequest = { showDialog = false },
            settingsRepository = settingsRepository,
            title = "确认操作",
            message = "您确定要执行此操作吗？",
            confirmText = "确定",
            onConfirm = { 
                // 处理确认操作
                showDialog = false 
            },
            dismissText = "取消",
            onDismiss = { showDialog = false }
        )
    }
}
```

### 带图标的对话框

```kotlin
@Composable
fun IconDialogExample(settingsRepository: SettingsRepository) {
    var showDialog by remember { mutableStateOf(false) }

    if (showDialog) {
        SkyBlueDialog(
            onDismissRequest = { showDialog = false },
            settingsRepository = settingsRepository,
            icon = {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = "警告",
                    tint = Color(0xFFE84026) // 自定义错误颜色
                )
            },
            title = { Text("警告") },
            text = { Text("此操作不可撤销，请谨慎操作。") },
            confirmButton = {
                SkyBlueDialogButton(
                    text = "我知道了",
                    onClick = { showDialog = false },
                    isPrimary = true
                )
            }
        )
    }
}
```

### 选择列表对话框

```kotlin
@Composable
fun SelectionDialogExample(settingsRepository: SettingsRepository) {
    var showDialog by remember { mutableStateOf(false) }
    val options = listOf("选项一", "选项二", "选项三")
    
    if (showDialog) {
        SkyBlueSelectionDialog(
            onDismissRequest = { showDialog = false },
            settingsRepository = settingsRepository,
            title = "请选择",
            description = "选择一个选项：",
            items = options,
            onItemSelected = { option ->
                // 处理选择
                println("选择了：$option")
                showDialog = false
            },
            itemContent = { option ->
                // 自定义选项项，不依赖MD3
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .background(Color(0xFFF1F3F5))
                        .clickable {
                            // 处理点击
                            showDialog = false
                        }
                        .padding(16.dp)
                ) {
                    Text(
                        text = option,
                        color = Color(0xE5000000),
                        fontSize = 14.sp
                    )
                }
            }
        )
    }
}
```

## 配置管理

### SkyBlueDialogConfigManager

配置管理器提供了对话框样式的动态管理功能：

```kotlin
class SkyBlueDialogConfigManager(
    private val settingsRepository: SettingsRepository
) {
    // 获取动态配置
    @Composable
    fun getDynamicDialogStyleConfig(): DialogStyleConfig
    
    // 更新圆角大小
    fun updateCornerRadius(cornerRadius: Int)
    
    // 更新模糊效果开关
    fun updateBlurEnabled(enabled: Boolean)
    
    // 更新模糊强度
    fun updateBlurIntensity(intensity: Float)
    
    // 重置为默认配置
    fun resetToDefault()
}
```

### 配置示例

```kotlin
@Composable
fun ConfigurationExample(settingsRepository: SettingsRepository) {
    val configManager = remember { SkyBlueDialogConfigManager(settingsRepository) }
    
    Column {
        // 显示当前配置
        Text("当前配置：${configManager.getConfigSummary()}")
        
        // 调整圆角大小
        Button(onClick = { configManager.updateCornerRadius(20) }) {
            Text("设置圆角为20dp")
        }
        
        // 切换模糊效果
        Button(onClick = { configManager.updateBlurEnabled(false) }) {
            Text("关闭模糊效果")
        }
        
        // 调整模糊强度
        Button(onClick = { configManager.updateBlurIntensity(0.8f) }) {
            Text("设置模糊强度为80%")
        }
    }
}
```

## 全局设置集成

对话框组件完全集成了全局设置系统，相关设置字段：

```kotlin
data class GlobalSettings(
    // ... 其他字段
    
    // 对话框样式设置
    var dialogBlurEnabled: Boolean = true,        // 模糊效果开关
    var dialogCornerRadius: Int = 28,             // 圆角大小（dp）
    var dialogBlurIntensity: Float = 0.6f         // 模糊强度（0.0-1.0）
)
```

## 性能考虑

1. **模糊效果性能**：
   - 使用Haze库的优化实现
   - 支持所有Android版本
   - 自动选择最佳渲染策略

2. **内存使用**：
   - 配置管理器使用单例模式
   - 自动释放不需要的资源

3. **渲染优化**：
   - 智能重组避免
   - 高效的状态管理

## 注意事项

1. **依赖要求**：
   - 需要Haze库（已在项目中集成）
   - **不依赖Material 3组件库**，完全自定义实现
   - 仅依赖Compose基础组件

2. **Android版本支持**：
   - 最低支持Android 8.0 (API 26)
   - 在所有版本上都能正常工作
   - 低版本设备使用备用渲染策略

3. **主题兼容性**：
   - 专为天空蓝主题设计
   - 完全自定义实现，可以轻松适配其他主题
   - 所有颜色和样式都可以自定义

4. **与MD3的区别**：
   - 不受MD3模糊效果限制
   - 更好的模糊效果支持
   - 完全可控的样式定制

## 故障排除

### 模糊效果不显示
1. 检查`dialogBlurEnabled`设置是否为true
2. 确认设备支持模糊效果
3. 检查Haze库是否正确集成

### 圆角不生效
1. 确认`dialogCornerRadius`设置在有效范围内（0-50）
2. 检查是否有其他样式覆盖

### 性能问题
1. 降低模糊强度（`dialogBlurIntensity`）
2. 在低端设备上关闭模糊效果
3. 检查是否有内存泄漏

## 集成到现有项目

### 1. 在Activity中使用

```kotlin
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val settingsRepository = SettingsRepository(this)

        setContent {
            YourAppTheme {
                // 在需要的地方使用对话框
                SkyBlueDialogExample(settingsRepository)
            }
        }
    }
}
```

### 2. 在现有屏幕中集成

```kotlin
@Composable
fun ExistingScreen(settingsRepository: SettingsRepository) {
    var showConfirmDialog by remember { mutableStateOf(false) }

    // 现有的UI内容
    Column {
        // ... 其他组件

        Button(onClick = { showConfirmDialog = true }) {
            Text("删除项目")
        }
    }

    // 添加对话框
    if (showConfirmDialog) {
        SkyBlueDialog(
            onDismissRequest = { showConfirmDialog = false },
            settingsRepository = settingsRepository,
            title = "确认删除",
            message = "此操作不可撤销，确定要删除吗？",
            confirmText = "删除",
            onConfirm = {
                // 执行删除操作
                showConfirmDialog = false
            },
            dismissText = "取消",
            onDismiss = { showConfirmDialog = false }
        )
    }
}
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持动态圆角和模糊效果
- 完整的Material 3集成
- 全局设置系统集成
- 提供完整的使用示例和文档

package com.weinuo.quickcommands.data

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.model.QuickCommand
import com.weinuo.quickcommands.model.ManualTriggerCondition
import com.weinuo.quickcommands.model.ManualTriggerType
import com.weinuo.quickcommands.storage.QuickCommandStorageCoordinator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * 快捷指令仓库类（单例模式）
 * 使用原生数据类型存储架构的新版本仓库
 *
 * 重构说明：
 * - 从JSON序列化存储迁移到原生数据类型存储
 * - 使用QuickCommandStorageCoordinator进行存储协调
 * - 提供响应式数据流支持
 * - 更好的性能和可扩展性
 * - 完整的错误处理和日志记录
 * - 单例模式确保全应用数据一致性
 *
 * 架构特性：
 * - 原生数据类型存储：避免JSON序列化问题，提高可靠性
 * - 响应式数据流：使用StateFlow提供实时数据更新
 * - 协程支持：所有存储操作都是异步非阻塞的
 * - 错误处理：完善的异常处理和日志记录
 * - 批量操作：支持高效的批量数据操作
 * - 数据一致性：确保内存数据与存储数据的一致性
 * - 单例模式：全应用共享同一个数据流，确保实时同步
 */
class QuickCommandRepository private constructor(private val context: Context) {

    companion object {
        private const val TAG = "QuickCommandRepository"

        @Volatile
        private var INSTANCE: QuickCommandRepository? = null

        /**
         * 获取单例实例
         * 使用双重检查锁定模式确保线程安全
         *
         * @param context Android上下文
         * @return QuickCommandRepository单例实例
         */
        fun getInstance(context: Context): QuickCommandRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: QuickCommandRepository(context.applicationContext).also {
                    INSTANCE = it
                    Log.d(TAG, "QuickCommandRepository singleton instance created")
                }
            }
        }

        /**
         * 清除单例实例（仅用于测试）
         * 生产环境不应调用此方法
         */
        @Suppress("unused")
        fun clearInstance() {
            synchronized(this) {
                INSTANCE = null
                Log.d(TAG, "QuickCommandRepository singleton instance cleared")
            }
        }
    }

    // 使用新的存储协调器
    private val storageCoordinator = QuickCommandStorageCoordinator(context)

    // 响应式数据流
    private val _quickCommands = MutableStateFlow<List<QuickCommand>>(emptyList())
    val quickCommands: StateFlow<List<QuickCommand>> = _quickCommands.asStateFlow()

    // 初始化时加载数据
    init {
        loadCommandsToFlow()
        Log.d(TAG, "QuickCommandRepository initialized with singleton pattern")
    }

    /**
     * 加载快捷指令到数据流
     */
    private fun loadCommandsToFlow() {
        try {
            val commands = storageCoordinator.loadAllQuickCommands()
            _quickCommands.value = commands
            Log.d(TAG, "Loaded ${commands.size} commands to flow")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading commands to flow", e)
            _quickCommands.value = emptyList()
        }
    }

    /**
     * 保存快捷指令列表
     *
     * @param commands 要保存的快捷指令列表
     */
    suspend fun saveCommands(commands: List<QuickCommand>) = withContext(Dispatchers.IO) {
        try {
            val successCount = storageCoordinator.saveQuickCommands(commands)
            if (successCount == commands.size) {
                Log.d(TAG, "Successfully saved all ${commands.size} commands")
                _quickCommands.value = commands
            } else {
                Log.w(TAG, "Partially saved commands: $successCount/${commands.size}")
                // 重新加载以确保数据一致性
                loadCommandsToFlow()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error saving commands", e)
        }
    }

    /**
     * 加载快捷指令列表
     *
     * @return 快捷指令列表
     */
    suspend fun loadCommands(): List<QuickCommand> = withContext(Dispatchers.IO) {
        try {
            val commands = storageCoordinator.loadAllQuickCommands()
            Log.d(TAG, "Successfully loaded ${commands.size} commands")
            _quickCommands.value = commands
            commands
        } catch (e: Exception) {
            Log.e(TAG, "Error loading commands", e)
            emptyList()
        }
    }

    /**
     * 保存单个快捷指令
     *
     * @param command 要保存的快捷指令
     */
    suspend fun saveCommand(command: QuickCommand) = withContext(Dispatchers.IO) {
        try {
            val success = storageCoordinator.saveQuickCommand(command)
            if (success) {
                Log.d(TAG, "Successfully saved command: ${command.name}")
                // 更新数据流
                val currentCommands = _quickCommands.value.toMutableList()
                val existingIndex = currentCommands.indexOfFirst { it.id == command.id }
                if (existingIndex != -1) {
                    currentCommands[existingIndex] = command
                } else {
                    currentCommands.add(command)
                }
                _quickCommands.value = currentCommands

                // 检查是否有动态快捷方式触发条件，如果有则自动创建桌面快捷方式
                checkAndCreateDynamicShortcuts(command)

                // 检查是否有静态快捷方式触发条件，如果有则自动关联静态快捷方式
                checkAndAssociateStaticShortcuts(command)

                // 检查是否有桌面小组件触发条件，如果有则自动关联桌面小组件
                checkAndAssociateDesktopWidgets(command)

                // 检查是否有悬浮按钮触发条件，如果有则自动创建悬浮按钮
                checkAndCreateFloatingButtons(command)
            } else {
                Log.e(TAG, "Failed to save command: ${command.name}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error saving command: ${command.name}", e)
        }
    }

    /**
     * 删除快捷指令
     *
     * @param commandId 要删除的快捷指令ID
     */
    suspend fun deleteCommand(commandId: String) = withContext(Dispatchers.IO) {
        try {
            // 在删除前获取快捷指令信息，用于清理相关资源
            val command = _quickCommands.value.find { it.id == commandId }

            val success = storageCoordinator.deleteQuickCommand(commandId)
            if (success) {
                Log.d(TAG, "Successfully deleted command with ID: $commandId")

                // 清理相关资源
                if (command != null) {
                    cleanupCommandResources(command)
                }

                // 更新数据流
                val currentCommands = _quickCommands.value.toMutableList()
                currentCommands.removeAll { it.id == commandId }
                _quickCommands.value = currentCommands
            } else {
                Log.w(TAG, "Failed to delete command or command not found: $commandId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting command: $commandId", e)
        }
    }

    /**
     * 根据ID获取快捷指令
     *
     * @param commandId 快捷指令ID
     * @return 快捷指令对象，如果不存在则返回null
     */
    suspend fun getCommandById(commandId: String): QuickCommand? = withContext(Dispatchers.IO) {
        try {
            val command = storageCoordinator.loadQuickCommand(commandId)
            if (command != null) {
                Log.d(TAG, "Successfully loaded command: ${command.name}")
            } else {
                Log.d(TAG, "Command not found: $commandId")
            }
            command
        } catch (e: Exception) {
            Log.e(TAG, "Error loading command: $commandId", e)
            null
        }
    }

    /**
     * 清空所有快捷指令
     */
    suspend fun clearAllCommands() = withContext(Dispatchers.IO) {
        try {
            val success = storageCoordinator.clearAllData()
            if (success) {
                Log.d(TAG, "Successfully cleared all commands")
                _quickCommands.value = emptyList()
            } else {
                Log.e(TAG, "Failed to clear all commands")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all commands", e)
        }
    }

    /**
     * 检查快捷指令是否存在
     *
     * @param commandId 快捷指令ID
     * @return 快捷指令是否存在
     */
    suspend fun commandExists(commandId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val exists = storageCoordinator.quickCommandExists(commandId)
            Log.d(TAG, "Command exists check: $commandId -> $exists")
            exists
        } catch (e: Exception) {
            Log.e(TAG, "Error checking command existence: $commandId", e)
            false
        }
    }

    /**
     * 获取快捷指令数量
     *
     * @return 快捷指令数量
     */
    suspend fun getCommandCount(): Int = withContext(Dispatchers.IO) {
        try {
            val count = storageCoordinator.getQuickCommandCount()
            Log.d(TAG, "Command count: $count")
            count
        } catch (e: Exception) {
            Log.e(TAG, "Error getting command count", e)
            0
        }
    }

    /**
     * 批量删除快捷指令
     *
     * @param commandIds 要删除的快捷指令ID列表
     * @return 成功删除的数量
     */
    suspend fun deleteCommands(commandIds: List<String>): Int = withContext(Dispatchers.IO) {
        try {
            // 在删除前获取快捷指令信息，用于清理相关资源
            val commandsToDelete = _quickCommands.value.filter { it.id in commandIds }

            val successCount = storageCoordinator.deleteQuickCommands(commandIds)
            Log.d(TAG, "Batch delete completed: $successCount/${commandIds.size}")

            if (successCount > 0) {
                // 清理相关资源
                commandsToDelete.forEach { command ->
                    cleanupCommandResources(command)
                }

                // 更新数据流
                val currentCommands = _quickCommands.value.toMutableList()
                currentCommands.removeAll { it.id in commandIds }
                _quickCommands.value = currentCommands
            }

            successCount
        } catch (e: Exception) {
            Log.e(TAG, "Error in batch delete", e)
            0
        }
    }

    /**
     * 刷新数据流
     * 从存储重新加载所有数据
     */
    suspend fun refresh() = withContext(Dispatchers.IO) {
        loadCommandsToFlow()
    }

    /**
     * 检查快捷指令是否包含动态快捷方式触发条件，如果有则自动创建桌面快捷方式
     *
     * @param command 要检查的快捷指令
     */
    private fun checkAndCreateDynamicShortcuts(command: QuickCommand) {
        try {
            // 检查触发条件中是否有动态快捷方式类型
            val dynamicShortcutConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.DYNAMIC_SHORTCUT }

            if (dynamicShortcutConditions.isNotEmpty()) {
                Log.d(TAG, "Found ${dynamicShortcutConditions.size} dynamic shortcut trigger conditions in command: ${command.name}")

                // 创建ShortcutManager实例
                val shortcutManager = com.weinuo.quickcommands.shortcut.ShortcutManager(context)

                // 为每个动态快捷方式条件创建桌面快捷方式
                dynamicShortcutConditions.forEach { condition ->
                    val shortcutName = if (condition.shortcutName.isNotEmpty()) {
                        condition.shortcutName
                    } else {
                        command.name // 如果没有指定快捷方式名称，使用快捷指令名称
                    }

                    // 创建一个临时的QuickCommand对象，使用自定义的快捷方式名称
                    val shortcutCommand = command.copy(name = shortcutName)

                    // 创建桌面快捷方式
                    val success = shortcutManager.createQuickCommandShortcut(shortcutCommand)

                    if (success) {
                        Log.d(TAG, "Successfully created dynamic shortcut: $shortcutName for command: ${command.name}")
                    } else {
                        Log.w(TAG, "Failed to create dynamic shortcut: $shortcutName for command: ${command.name}")
                    }
                }
            } else {
                Log.d(TAG, "No dynamic shortcut trigger conditions found in command: ${command.name}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking and creating dynamic shortcuts for command: ${command.name}", e)
        }
    }

    /**
     * 检查快捷指令是否包含静态快捷方式触发条件，如果有则自动关联静态快捷方式
     *
     * @param command 要检查的快捷指令
     */
    private fun checkAndAssociateStaticShortcuts(command: QuickCommand) {
        try {
            // 检查触发条件中是否有静态快捷方式类型
            val staticShortcutConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.STATIC_SHORTCUT }

            if (staticShortcutConditions.isNotEmpty()) {
                Log.d(TAG, "Found ${staticShortcutConditions.size} static shortcut trigger conditions in command: ${command.name}")

                // 创建ShortcutManager实例
                val shortcutManager = com.weinuo.quickcommands.shortcut.ShortcutManager(context)

                // 为每个静态快捷方式条件关联槽位
                staticShortcutConditions.forEach { condition ->
                    val success = shortcutManager.associateWithStaticShortcut(command, condition.slotIndex)

                    if (success) {
                        Log.d(TAG, "Successfully associated static shortcut slot ${condition.slotIndex} for command: ${command.name}")
                    } else {
                        Log.w(TAG, "Failed to associate static shortcut slot ${condition.slotIndex} for command: ${command.name}")
                    }
                }
            } else {
                Log.d(TAG, "No static shortcut trigger conditions found in command: ${command.name}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking and associating static shortcuts for command: ${command.name}", e)
        }
    }

    /**
     * 检查快捷指令是否包含桌面小组件触发条件，如果有则自动关联桌面小组件
     *
     * @param command 要检查的快捷指令
     */
    private fun checkAndAssociateDesktopWidgets(command: QuickCommand) {
        try {
            // 检查触发条件中是否有桌面小组件类型
            val desktopWidgetConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.DESKTOP_WIDGET }

            if (desktopWidgetConditions.isNotEmpty()) {
                Log.d(TAG, "Found ${desktopWidgetConditions.size} desktop widget trigger conditions in command: ${command.name}")

                // 创建ShortcutManager实例
                val shortcutManager = com.weinuo.quickcommands.shortcut.ShortcutManager(context)

                // 为每个桌面小组件条件关联槽位
                desktopWidgetConditions.forEach { condition ->
                    val success = shortcutManager.associateWithWidget(command, condition.slotIndex)

                    if (success) {
                        Log.d(TAG, "Successfully associated desktop widget slot ${condition.slotIndex} for command: ${command.name}")
                    } else {
                        Log.w(TAG, "Failed to associate desktop widget slot ${condition.slotIndex} for command: ${command.name}")
                    }
                }
            } else {
                Log.d(TAG, "No desktop widget trigger conditions found in command: ${command.name}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking and associating desktop widgets for command: ${command.name}", e)
        }
    }

    /**
     * 检查快捷指令是否包含悬浮按钮触发条件，如果有则自动创建悬浮按钮
     * 注意：此方法只检查权限，不显示UI对话框，UI层需要单独处理权限引导
     *
     * @param command 要检查的快捷指令
     */
    private fun checkAndCreateFloatingButtons(command: QuickCommand) {
        try {
            // 检查触发条件中是否有悬浮按钮类型
            val floatingButtonConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.FLOATING_BUTTON }

            if (floatingButtonConditions.isNotEmpty()) {
                Log.d(TAG, "Found ${floatingButtonConditions.size} floating button trigger conditions in command: ${command.name}")

                // 检查悬浮窗权限
                if (!com.weinuo.quickcommands.utils.OverlayPermissionUtil.hasOverlayPermission(context)) {
                    Log.w(TAG, "No overlay permission, cannot create floating buttons for command: ${command.name}")
                    // 这里不显示对话框，权限引导应该在UI层处理
                    return
                }

                // 为每个悬浮按钮条件创建悬浮按钮
                floatingButtonConditions.forEach { condition ->
                    val success = com.weinuo.quickcommands.floating.FloatingButtonManager.createFloatingButton(
                        context = context,
                        commandId = command.id,
                        buttonSize = condition.buttonSize,
                        buttonAlpha = condition.buttonAlpha,
                        showText = condition.buttonText.isNotEmpty(),
                        buttonText = condition.buttonText
                    )

                    if (success) {
                        Log.d(TAG, "Successfully created floating button for command: ${command.name}")
                    } else {
                        Log.w(TAG, "Failed to create floating button for command: ${command.name}")
                    }
                }
            } else {
                Log.d(TAG, "No floating button trigger conditions found in command: ${command.name}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking and creating floating buttons for command: ${command.name}", e)
        }
    }

    /**
     * 清理快捷指令相关的外部资源
     * 在删除快捷指令时调用，清理动态快捷方式、悬浮按钮、静态快捷方式关联等外部资源
     * 注意：数据存储层面的删除已由QuickCommandStorageCoordinator处理
     *
     * @param command 要清理资源的快捷指令
     */
    private fun cleanupCommandResources(command: QuickCommand) {
        try {
            Log.d(TAG, "Cleaning up external resources for command: ${command.name}")

            // 创建ShortcutManager实例
            val shortcutManager = com.weinuo.quickcommands.shortcut.ShortcutManager(context)

            // 1. 删除动态快捷方式（如果有的话）
            val dynamicShortcutConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.DYNAMIC_SHORTCUT }

            if (dynamicShortcutConditions.isNotEmpty()) {
                Log.d(TAG, "Removing dynamic shortcuts for command: ${command.name}")
                val success = shortcutManager.removeQuickCommandShortcut(command.id)
                if (success) {
                    Log.d(TAG, "Successfully removed dynamic shortcuts for command: ${command.name}")
                } else {
                    Log.w(TAG, "Failed to remove dynamic shortcuts for command: ${command.name}")
                }
            }

            // 2. 清理静态快捷方式关联（如果有的话）
            val staticShortcutConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.STATIC_SHORTCUT }

            if (staticShortcutConditions.isNotEmpty()) {
                Log.d(TAG, "Clearing static shortcut associations for command: ${command.name}")
                val prefs = context.getSharedPreferences(com.weinuo.quickcommands.shortcut.ShortcutManager.PREFS_NAME, Context.MODE_PRIVATE)
                val editor = prefs.edit()

                staticShortcutConditions.forEach { condition ->
                    val key = com.weinuo.quickcommands.shortcut.ShortcutManager.PREF_STATIC_SHORTCUT_PREFIX + condition.slotIndex
                    val associatedCommandId = prefs.getString(key, null)
                    if (associatedCommandId == command.id) {
                        editor.remove(key)
                        Log.d(TAG, "Cleared static shortcut slot ${condition.slotIndex} for command: ${command.name}")
                    }
                }
                editor.apply()
            }

            // 3. 清理桌面小组件关联（如果有的话）
            val desktopWidgetConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.DESKTOP_WIDGET }

            if (desktopWidgetConditions.isNotEmpty()) {
                Log.d(TAG, "Clearing desktop widget associations for command: ${command.name}")
                val prefs = context.getSharedPreferences(com.weinuo.quickcommands.shortcut.ShortcutManager.PREFS_NAME, Context.MODE_PRIVATE)
                val editor = prefs.edit()

                desktopWidgetConditions.forEach { condition ->
                    val key = com.weinuo.quickcommands.shortcut.ShortcutManager.PREF_WIDGET_PREFIX + condition.slotIndex
                    val associatedCommandId = prefs.getString(key, null)
                    if (associatedCommandId == command.id) {
                        editor.remove(key)
                        Log.d(TAG, "Cleared desktop widget slot ${condition.slotIndex} for command: ${command.name}")
                    }
                }
                editor.apply()
            }

            // 4. 删除悬浮按钮（如果有的话）
            val floatingButtonConditions = command.triggerConditions
                .filterIsInstance<ManualTriggerCondition>()
                .filter { it.triggerType == ManualTriggerType.FLOATING_BUTTON }

            if (floatingButtonConditions.isNotEmpty()) {
                Log.d(TAG, "Removing floating buttons for command: ${command.name}")
                val success = com.weinuo.quickcommands.floating.FloatingButtonManager.removeFloatingButton(context, command.id)
                if (success) {
                    Log.d(TAG, "Successfully removed floating buttons for command: ${command.name}")
                } else {
                    Log.w(TAG, "Failed to remove floating buttons for command: ${command.name}")
                }
            }

            Log.d(TAG, "External resource cleanup completed for command: ${command.name}")

        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up external resources for command: ${command.name}", e)
        }
    }



    /**
     * 获取存储统计信息
     *
     * @return 存储统计信息的映射
     */
    suspend fun getStorageStats(): Map<String, Any> = withContext(Dispatchers.IO) {
        try {
            val stats = mutableMapOf<String, Any>()
            stats["commandCount"] = storageCoordinator.getQuickCommandCount()
            stats["storageType"] = "NativeTypeStorage"
            stats["version"] = "2.0"

            Log.d(TAG, "Storage stats: $stats")
            stats.toMap()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting storage stats", e)
            mapOf<String, Any>("error" to (e.message ?: "Unknown error"))
        }
    }

}

package com.weinuo.quickcommands.utils

import android.app.AppOpsManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Process
import android.provider.Settings
import androidx.compose.runtime.Composable
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 应用使用统计权限工具类
 * 管理应用使用统计权限的检查和引导
 * 适用于应用状态监控、应用启动关闭检测等功能
 *
 * 注意：PACKAGE_USAGE_STATS是特殊权限，无法通过常规方式申请
 * 必须引导用户到系统设置中手动开启
 */
object UsageStatsPermissionUtil {

    /**
     * 检查是否拥有使用统计权限
     * @param context 上下文
     * @return 是否拥有使用统计权限
     */
    fun hasUsageStatsPermission(context: Context): Boolean {
        return try {
            val appOps = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                appOps.unsafeCheckOpNoThrow(
                    AppOpsManager.OPSTR_GET_USAGE_STATS,
                    Process.myUid(),
                    context.packageName
                )
            } else {
                @Suppress("DEPRECATION")
                appOps.checkOpNoThrow(
                    AppOpsManager.OPSTR_GET_USAGE_STATS,
                    Process.myUid(),
                    context.packageName
                )
            }
            mode == AppOpsManager.MODE_ALLOWED
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 打开使用统计权限设置页面
     * @param context 上下文
     */
    fun openUsageStatsSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS).apply {
                // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在非Activity context中也能正常启动
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开使用统计设置，则打开应用详情页面
            openAppDetailsSettings(context)
        }
    }

    /**
     * 打开应用详情设置页面
     * @param context 上下文
     */
    fun openAppDetailsSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在非Activity context中也能正常启动
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 忽略异常，用户可以手动到设置中授权
        }
    }

    /**
     * 使用统计权限说明对话框
     * 向用户解释为什么需要使用统计权限
     */
    @Composable
    fun UsageStatsPermissionRationaleDialog(
        onConfirm: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "使用统计权限说明",
            message = """
                为了使用应用状态监控功能，应用需要使用统计权限：

                • 应用启动检测：检测指定应用何时启动
                • 应用关闭检测：检测指定应用何时关闭
                • 前台后台切换：检测应用在前台和后台之间的切换
                • 应用使用时长统计：监控应用的使用情况

                隐私保护承诺：
                • 仅用于条件检测，不会收集您的应用使用数据
                • 不会上传或分享您的应用使用信息
                • 您可以随时在系统设置中撤销此权限

                注意：此权限需要在系统设置中手动开启，无法通过应用直接申请。
            """.trimIndent(),
            confirmText = "前往设置",
            dismissText = "取消",
            onConfirm = onConfirm,
            onDismiss = onDismiss
        )
    }

    /**
     * 使用统计权限被拒绝后的说明对话框
     */
    @Composable
    fun UsageStatsPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "使用统计权限未开启",
            message = """
                应用状态监控功能需要使用统计权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动开启使用统计权限：
                • 应用启动和关闭检测
                • 应用前台后台状态检测
                • 应用使用情况监控

                开启步骤：
                1. 点击"前往设置"按钮
                2. 在使用统计权限页面中找到"后台管理"应用
                3. 开启权限开关

                您可以随时在系统设置中关闭此权限。
            """.trimIndent(),
            confirmText = "前往设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }

    /**
     * 检查使用统计权限的详细状态
     * @param context 上下文
     * @return 权限状态描述
     */
    fun getUsageStatsPermissionStatus(context: Context): String {
        return if (hasUsageStatsPermission(context)) {
            "已授权"
        } else {
            "未授权"
        }
    }

    /**
     * 获取使用统计权限的系统设置Intent
     * @return 设置Intent，如果无法创建则返回null
     */
    fun getUsageStatsSettingsIntent(): Intent? {
        return try {
            Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
        } catch (e: Exception) {
            null
        }
    }
}

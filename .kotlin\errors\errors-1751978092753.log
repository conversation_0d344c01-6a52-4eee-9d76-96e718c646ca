kotlin version: 2.0.21
error message: kotlin.NotImplementedError: An operation is not implemented: Unknown file
	at org.jetbrains.kotlin.ir.util.IrUtilsKt.getFile(IrUtils.kt:634)
	at androidx.compose.compiler.plugins.kotlin.lower.InferenceFunctionDeclaration.toScheme(ComposableTargetAnnotationsTransformer.kt:680)
	at androidx.compose.compiler.plugins.kotlin.lower.InferenceFunctionDeclaration.toDeclaredScheme(ComposableTargetAnnotationsTransformer.kt:671)
	at androidx.compose.compiler.plugins.kotlin.lower.InferenceFunction.toDeclaredScheme$default(ComposableTargetAnnotationsTransformer.kt:614)
	at androidx.compose.compiler.plugins.kotlin.lower.ComposableTargetAnnotationsTransformer$infer$1.declaredSchemaOf(ComposableTargetAnnotationsTransformer.kt:155)
	at androidx.compose.compiler.plugins.kotlin.lower.ComposableTargetAnnotationsTransformer$infer$1.declaredSchemaOf(ComposableTargetAnnotationsTransformer.kt:152)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.toLazyScheme$lambda$3$declaredSchemeOf(ApplierInferencer.kt:187)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.toLazyScheme(ApplierInferencer.kt:202)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.toLazyScheme$default(ApplierInferencer.kt:183)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.restartable$schemeOf(ApplierInferencer.kt:317)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.restartable$callBindingsOf(ApplierInferencer.kt:348)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.access$restartable$callBindingsOf(ApplierInferencer.kt:167)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer$restartable$1.invoke(ApplierInferencer.kt:353)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer$restartable$1.invoke(ApplierInferencer.kt:353)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.visitCall$lambda$14(ApplierInferencer.kt:393)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.restartable(ApplierInferencer.kt:353)
	at androidx.compose.compiler.plugins.kotlin.inference.ApplierInferencer.visitCall(ApplierInferencer.kt:391)
	at androidx.compose.compiler.plugins.kotlin.lower.ComposableTargetAnnotationsTransformer.visitCall(ComposableTargetAnnotationsTransformer.kt:360)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitCall(IrElementTransformerVoid.kt:299)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitCall(IrElementTransformerVoid.kt:19)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.ir.expressions.IrExpression.transform(IrExpression.kt:24)
	at org.jetbrains.kotlin.ir.expressions.IrExpression.transform(IrExpression.kt:20)
	at org.jetbrains.kotlin.ir.util.TransformKt.transformInPlace(transform.kt:35)
	at org.jetbrains.kotlin.ir.expressions.IrBlockBody.transformChildren(IrBlockBody.kt:27)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitBody(IrElementTransformerVoid.kt:174)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitBlockBody(IrElementTransformerVoid.kt:188)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitBlockBody(IrElementTransformerVoid.kt:191)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitBlockBody(IrElementTransformerVoid.kt:19)
	at org.jetbrains.kotlin.ir.expressions.IrBlockBody.accept(IrBlockBody.kt:20)
	at org.jetbrains.kotlin.ir.expressions.IrBody.transform(IrBody.kt:20)
	at org.jetbrains.kotlin.ir.declarations.IrFunction.transformChildren(IrFunction.kt:58)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitDeclaration(IrElementTransformerVoid.kt:40)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitFunction(IrElementTransformerVoid.kt:72)
	at androidx.compose.compiler.plugins.kotlin.lower.ComposableTargetAnnotationsTransformer.visitFunction(ComposableTargetAnnotationsTransformer.kt:254)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitSimpleFunction(IrElementTransformerVoid.kt:128)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitSimpleFunction(IrElementTransformerVoid.kt:131)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitSimpleFunction(IrElementTransformerVoid.kt:19)
	at org.jetbrains.kotlin.ir.declarations.IrSimpleFunction.accept(IrSimpleFunction.kt:39)
	at org.jetbrains.kotlin.ir.IrElementBase.transform(IrElementBase.kt:33)
	at org.jetbrains.kotlin.ir.util.TransformKt.transformInPlace(transform.kt:35)
	at org.jetbrains.kotlin.ir.declarations.IrFile.transformChildren(IrFile.kt:38)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitPackageFragment(IrElementTransformerVoid.kt:146)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitFile(IrElementTransformerVoid.kt:160)
	at androidx.compose.compiler.plugins.kotlin.lower.ComposableTargetAnnotationsTransformer.visitFile(ComposableTargetAnnotationsTransformer.kt:230)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitFile(IrElementTransformerVoid.kt:163)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoid.visitFile(IrElementTransformerVoid.kt:19)
	at org.jetbrains.kotlin.ir.declarations.IrFile.accept(IrFile.kt:28)
	at org.jetbrains.kotlin.ir.declarations.IrFile.transform(IrFile.kt:31)
	at org.jetbrains.kotlin.ir.declarations.IrFile.transform(IrFile.kt:20)
	at org.jetbrains.kotlin.ir.util.TransformKt.transformInPlace(transform.kt:35)
	at org.jetbrains.kotlin.ir.declarations.IrModuleFragment.transformChildren(IrModuleFragment.kt:43)
	at org.jetbrains.kotlin.ir.visitors.IrElementTransformerVoidKt.transformChildrenVoid(IrElementTransformerVoid.kt:565)
	at androidx.compose.compiler.plugins.kotlin.lower.ComposableTargetAnnotationsTransformer.lower(ComposableTargetAnnotationsTransformer.kt:223)
	at androidx.compose.compiler.plugins.kotlin.ComposeIrGenerationExtension.generate(ComposeIrGenerationExtension.kt:235)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.applyIrGenerationExtensions(convertToIr.kt:442)
	at org.jetbrains.kotlin.fir.pipeline.Fir2IrPipeline.runActualizationPipeline(convertToIr.kt:246)
	at org.jetbrains.kotlin.fir.pipeline.Fir2IrPipeline.convertToIrAndActualize(convertToIr.kt:130)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize(convertToIr.kt:99)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize$default(convertToIr.kt:72)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertToIrAndActualizeForJvm(jvmCompilerPipeline.kt:196)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertAnalyzedFirToIr(jvmCompilerPipeline.kt:169)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.compileModulesUsingFrontendIrAndLightTree(jvmCompilerPipeline.kt:140)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:148)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:43)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:103)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:49)
	at org.jetbrains.kotlin.cli.common.CLITool.exec(CLITool.kt:101)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:464)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:73)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:506)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:423)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:249)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:675)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1660)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:351)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:166)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:543)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:744)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:623)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)
	at java.base/java.lang.Thread.run(Thread.java:1447)



package com.weinuo.quickcommands.smartreminder

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.BroadcastReceiver
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.Log
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.service.SmartReminderOverlayService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 新应用提醒处理器
 *
 * 检测新应用安装，当检测到新应用安装时，
 * 发送智能提醒建议用户打开新安装的应用。
 *
 * 检测逻辑：
 * - 监听应用安装广播
 * - 检测新安装的应用（非更新）
 * - 过滤系统应用，只提醒用户应用
 * - 在合适的时机发送提醒通知
 *
 * 设计特点：
 * - 智能防重复：避免频繁提醒
 * - 延迟检测：给系统安装时间
 * - 状态感知：只在需要时提醒
 * - 用户友好：提供直接的操作建议
 * - 图标适配：支持不同形状的应用图标
 */
class NewAppReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "NewAppReminderHandler"

        // 默认配置值
        private const val DEFAULT_REMINDER_DELAY = 3000L // 3秒延迟提醒，给安装过程时间
        private const val DEFAULT_REMINDER_COOLDOWN = 60000L // 60秒冷却时间
    }

    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val configAdapter = SmartReminderConfigAdapter(context)

    private var packageReceiver: BroadcastReceiver? = null
    private var isMonitoring = false
    private var lastReminderTime = 0L

    /**
     * 开始监控新应用安装
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Already monitoring new app installations")
            return
        }

        try {
            // 创建广播接收器
            packageReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    handlePackageIntent(intent)
                }
            }

            // 注册广播接收器
            val filter = IntentFilter().apply {
                addAction(Intent.ACTION_PACKAGE_ADDED)
                addDataScheme("package")
            }

            // Android 14+ 需要指定导出标志
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                context.registerReceiver(packageReceiver, filter, Context.RECEIVER_EXPORTED)
            } else {
                context.registerReceiver(packageReceiver, filter)
            }

            isMonitoring = true
            Log.d(TAG, "Started monitoring new app installations")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting new app monitoring", e)
        }
    }

    /**
     * 停止监控新应用安装
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Not currently monitoring new app installations")
            return
        }

        try {
            packageReceiver?.let { receiver ->
                context.unregisterReceiver(receiver)
            }
            packageReceiver = null
            isMonitoring = false
            Log.d(TAG, "Stopped monitoring new app installations")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping new app monitoring", e)
        }
    }

    /**
     * 处理应用安装广播
     */
    private fun handlePackageIntent(intent: Intent?) {
        if (intent?.action != Intent.ACTION_PACKAGE_ADDED) return

        val packageName = intent.data?.schemeSpecificPart ?: return

        // 检查是否为新安装（非更新）
        val isReplacing = intent.getBooleanExtra(Intent.EXTRA_REPLACING, false)
        if (isReplacing) {
            Log.d(TAG, "Package $packageName is being updated, skipping reminder")
            return
        }

        Log.d(TAG, "New package installed: $packageName")

        // 异步处理新应用提醒
        handlerScope.launch {
            handleNewAppInstalled(packageName)
        }
    }

    /**
     * 处理新应用安装事件
     */
    private suspend fun handleNewAppInstalled(packageName: String) {
        try {
            // 加载配置
            val config = loadNewAppReminderConfig()

            // 检查冷却时间
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastReminderTime < config.cooldownTime * 1000L) {
                Log.d(TAG, "Reminder in cooldown period, skipping")
                return
            }

            // 获取应用信息
            val appInfo = getAppInfo(packageName) ?: return

            // 根据配置过滤应用
            if (appInfo.isSystemApp && !config.includeSystemApps) {
                Log.d(TAG, "Skipping system app: $packageName (includeSystemApps=false)")
                return
            }

            // 延迟提醒，给安装过程时间
            delay(config.delayTime * 1000L)

            // 再次检查应用是否仍然存在
            if (!isAppInstalled(packageName)) {
                Log.d(TAG, "App $packageName no longer installed, skipping reminder")
                return
            }

            Log.d(TAG, "Showing new app reminder for: ${appInfo.appName}")
            showNewAppReminder(appInfo)
            lastReminderTime = currentTime

        } catch (e: Exception) {
            Log.e(TAG, "Error handling new app installation: $packageName", e)
        }
    }

    /**
     * 获取应用信息
     */
    private fun getAppInfo(packageName: String): AppInfo? {
        return try {
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            val appName = packageManager.getApplicationLabel(applicationInfo).toString()
            val isSystemApp = applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM != 0
            val icon = applicationInfo.loadIcon(packageManager)

            AppInfo(
                packageName = packageName,
                appName = appName,
                isSystemApp = isSystemApp,
                icon = icon
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get app info for $packageName", e)
            null
        }
    }

    /**
     * 检查应用是否已安装
     */
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            context.packageManager.getApplicationInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * 显示新应用提醒悬浮窗
     */
    private fun showNewAppReminder(appInfo: AppInfo) {
        try {
            Log.d(TAG, "Attempting to show new app reminder for: ${appInfo.appName}")

            val title = context.getString(R.string.new_app_reminder_title)
            val message = "检测到新安装的应用「${appInfo.appName}」，建议打开体验"

            Log.d(TAG, "Calling SmartReminderOverlayService.showNewAppReminder")

            SmartReminderOverlayService.showNewAppReminder(
                context = context,
                title = title,
                message = message,
                appInfo = appInfo
            )

            Log.d(TAG, "SmartReminderOverlayService.showNewAppReminder called successfully")

            // 触发回调通知
            onReminderTriggered(mapOf(
                "type" to "new_app_reminder",
                "timestamp" to System.currentTimeMillis(),
                "packageName" to appInfo.packageName,
                "appName" to appInfo.appName,
                "message" to message
            ))
        } catch (e: Exception) {
            Log.e(TAG, "Error showing new app reminder", e)
        }
    }

    /**
     * 加载新应用提醒配置
     */
    private fun loadNewAppReminderConfig(): SmartReminderConfigAdapter.NewAppReminderConfig {
        return try {
            val reminderTypeId = SmartReminderType.NEW_APP_REMINDER.id
            if (configAdapter.hasConfig(reminderTypeId)) {
                configAdapter.loadNewAppReminderConfig(reminderTypeId)
            } else {
                // 返回默认配置
                SmartReminderConfigAdapter.NewAppReminderConfig()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading new app reminder config", e)
            SmartReminderConfigAdapter.NewAppReminderConfig()
        }
    }

    /**
     * 应用信息数据类
     */
    data class AppInfo(
        val packageName: String,
        val appName: String,
        val isSystemApp: Boolean,
        val icon: Drawable
    )
}

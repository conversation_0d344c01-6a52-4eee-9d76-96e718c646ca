package com.weinuo.quickcommands.ui.theme.config

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.Dp

/**
 * 底部导航栏配置
 */
data class BottomNavigationConfig(
    val tabs: List<NavigationTab>,
    val selectedIndex: Int,
    val onTabSelected: (Int) -> Unit,
    val modifier: Modifier = Modifier,
    val backgroundColor: Color? = null,
    val contentColor: Color? = null,
    val elevation: Dp? = null,
    val blurEnabled: Boolean = false,
    val blurRadius: Dp? = null
)

/**
 * 导航标签页数据
 */
data class NavigationTab(
    val label: String,
    val icon: ImageVector,
    val selectedIcon: ImageVector? = null,
    val badge: String? = null,
    val enabled: Boolean = true
)

/**
 * 顶部应用栏配置
 */
data class TopAppBarConfig(
    val title: String,
    val navigationIcon: ImageVector? = null,
    val navigationContentDescription: String? = null,
    val onNavigationClick: (() -> Unit)? = null,
    val actions: List<TopAppBarAction> = emptyList(),
    val modifier: Modifier = Modifier,
    val backgroundColor: Color? = null,
    val contentColor: Color? = null,
    val elevation: Dp? = null,
    val blurEnabled: Boolean = false,
    val blurRadius: Dp? = null,
    val scrollBehavior: Any? = null,
    val style: TopAppBarStyle = TopAppBarStyle.STANDARD,
    val collapsible: Boolean = false,
    val windowInsets: Any? = null
)

/**
 * 顶部应用栏样式枚举
 */
enum class TopAppBarStyle {
    STANDARD    // 标准高度 (64dp)，可折叠功能通过collapsible参数控制
}

/**
 * 顶部应用栏类型枚举
 * 定义整合设计系统中支持的两种标题栏类型
 */
enum class TopAppBarType(val displayName: String, val description: String) {
    /** 标准顶部应用栏 - 固定高度，简洁设计 */
    STANDARD("标准顶部应用栏", "固定高度的标准设计"),

    /** 可折叠顶部应用栏 - 支持滚动折叠，动态高度 */
    COLLAPSIBLE("可折叠顶部应用栏", "支持滚动折叠的动态设计")
}

/**
 * 顶部应用栏操作
 */
data class TopAppBarAction(
    val icon: ImageVector,
    val contentDescription: String,
    val onClick: () -> Unit,
    val enabled: Boolean = true
)

/**
 * 卡片配置
 */
data class CardConfig(
    val onClick: (() -> Unit)? = null,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val colors: Any? = null, // CardColors
    val elevation: Any? = null, // CardElevation
    val border: Any? = null, // BorderStroke
    val shape: Any? = null, // Shape
    val backgroundColor: Color? = null,
    val contentColor: Color? = null,
    val content: @Composable () -> Unit
)

/**
 * 图标位置枚举
 */
enum class IconPosition {
    START,  // 图标在文本前
    END,    // 图标在文本后
    TOP     // 图标在文本上（用于垂直布局）
}

/**
 * 按钮配置
 */
data class ButtonConfig(
    val onClick: () -> Unit,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val text: String,
    val icon: ImageVector? = null,
    val iconPosition: IconPosition = IconPosition.START,
    val colors: Any? = null, // ButtonColors
    val elevation: Any? = null, // ButtonElevation
    val border: Any? = null, // BorderStroke
    val contentPadding: PaddingValues = ButtonDefaults.ContentPadding,
    val shape: Any? = null, // Shape
    val backgroundColor: Color? = null,
    val contentColor: Color? = null
)

/**
 * 文本输入框配置
 */
data class TextFieldConfig(
    val value: String,
    val onValueChange: (String) -> Unit,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val readOnly: Boolean = false,
    val label: String? = null,
    val placeholder: String? = null,
    val leadingIcon: ImageVector? = null,
    val trailingIcon: ImageVector? = null,
    val onTrailingIconClick: (() -> Unit)? = null,
    val prefix: String? = null,
    val suffix: String? = null,
    val supportingText: String? = null,
    val isError: Boolean = false,
    val visualTransformation: Any? = null, // VisualTransformation
    val keyboardOptions: Any? = null, // KeyboardOptions
    val keyboardActions: Any? = null, // KeyboardActions
    val singleLine: Boolean = false,
    val maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    val minLines: Int = 1,
    val colors: Any? = null, // TextFieldColors
    val shape: Any? = null // Shape
)

/**
 * 搜索框配置
 *
 * 专门用于搜索功能的文本输入框配置
 * 包含搜索特有的功能如清除按钮、搜索图标等
 */
data class SearchTextFieldConfig(
    val searchQuery: String,
    val onSearchQueryChange: (String) -> Unit,
    val onClearSearch: () -> Unit,
    val placeholder: String,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val shape: Any? = null, // Shape - 允许主题自定义圆角
    val colors: Any? = null, // TextFieldColors - 允许主题自定义颜色
    val keyboardOptions: Any? = null, // KeyboardOptions
    val keyboardActions: Any? = null // KeyboardActions
)

/**
 * 浮动操作按钮配置
 */
data class FloatingActionButtonConfig(
    val onClick: () -> Unit,
    val modifier: Modifier = Modifier,
    val shape: Any? = null, // Shape
    val containerColor: Color? = null,
    val contentColor: Color? = null,
    val elevation: Any? = null, // FloatingActionButtonElevation
    val content: @Composable () -> Unit
)

/**
 * 对话框配置
 */
data class DialogConfig(
    val onDismissRequest: () -> Unit,
    val modifier: Modifier = Modifier,
    val properties: Any? = null, // DialogProperties
    val blurEnabled: Boolean = false,
    val blurRadius: Dp? = null,
    val content: @Composable () -> Unit
)

/**
 * 底部弹窗配置
 */
data class BottomSheetConfig(
    val onDismissRequest: () -> Unit,
    val modifier: Modifier = Modifier,
    val sheetState: Any? = null, // BottomSheetState
    val shape: Any? = null, // Shape
    val containerColor: Color? = null,
    val contentColor: Color? = null,
    val tonalElevation: Dp? = null,
    val scrimColor: Color? = null,
    val dragHandle: (@Composable () -> Unit)? = null,
    val blurEnabled: Boolean = false,
    val blurRadius: Dp? = null,
    val content: @Composable () -> Unit
)

/**
 * 开关配置
 */
data class SwitchConfig(
    val checked: Boolean,
    val onCheckedChange: ((Boolean) -> Unit)?,
    val modifier: Modifier = Modifier,
    val thumbContent: (@Composable () -> Unit)? = null,
    val enabled: Boolean = true,
    val colors: Any? = null // SwitchColors
)

/**
 * 复选框配置
 */
data class CheckboxConfig(
    val checked: Boolean,
    val onCheckedChange: ((Boolean) -> Unit)?,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val colors: Any? = null // CheckboxColors
)

/**
 * 单选按钮配置
 */
data class RadioButtonConfig(
    val selected: Boolean,
    val onClick: (() -> Unit)?,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val colors: Any? = null // RadioButtonColors
)

/**
 * 滑块配置
 */
data class SliderConfig(
    val value: Float,
    val onValueChange: (Float) -> Unit,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val valueRange: ClosedFloatingPointRange<Float> = 0f..1f,
    val steps: Int = 0,
    val onValueChangeFinished: (() -> Unit)? = null,
    val colors: Any? = null, // SliderColors
    val thumb: (@Composable () -> Unit)? = null,
    val track: (@Composable () -> Unit)? = null
)

/**
 * 进度指示器配置
 */
data class ProgressIndicatorConfig(
    val progress: Float? = null, // null表示不确定进度
    val modifier: Modifier = Modifier,
    val color: Color? = null,
    val trackColor: Color? = null,
    val strokeWidth: Dp? = null,
    val strokeCap: Any? = null // StrokeCap
)

/**
 * 分割线配置
 */
data class DividerConfig(
    val modifier: Modifier = Modifier,
    val thickness: Dp? = null,
    val color: Color? = null
)

/**
 * 芯片配置
 */
data class ChipConfig(
    val onClick: () -> Unit,
    val label: @Composable () -> Unit,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val leadingIcon: (@Composable () -> Unit)? = null,
    val trailingIcon: (@Composable () -> Unit)? = null,
    val colors: Any? = null, // ChipColors
    val elevation: Any? = null, // ChipElevation
    val border: Any? = null, // ChipBorder
    val shape: Any? = null // Shape
)

/**
 * 标签页配置
 */
data class TabsConfig(
    val selectedTabIndex: Int,
    val modifier: Modifier = Modifier,
    val containerColor: Color? = null,
    val contentColor: Color? = null,
    val indicator: (@Composable () -> Unit)? = null,
    val divider: (@Composable () -> Unit)? = null,
    val tabs: List<TabConfig>
)

/**
 * 单个标签页配置
 */
data class TabConfig(
    val selected: Boolean,
    val onClick: () -> Unit,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val text: (@Composable () -> Unit)? = null,
    val icon: (@Composable () -> Unit)? = null,
    val selectedContentColor: Color? = null,
    val unselectedContentColor: Color? = null
)

/**
 * 列表项配置
 */
data class ListItemConfig(
    val headlineContent: @Composable () -> Unit,
    val modifier: Modifier = Modifier,
    val overlineContent: (@Composable () -> Unit)? = null,
    val supportingContent: (@Composable () -> Unit)? = null,
    val leadingContent: (@Composable () -> Unit)? = null,
    val trailingContent: (@Composable () -> Unit)? = null,
    val colors: Any? = null, // ListItemColors
    val tonalElevation: Dp? = null,
    val shadowElevation: Dp? = null
)

/**
 * 图标按钮配置
 */
data class IconButtonConfig(
    val onClick: () -> Unit,
    val modifier: Modifier = Modifier,
    val enabled: Boolean = true,
    val colors: Any? = null, // IconButtonColors
    val content: @Composable () -> Unit
)

/**
 * 徽章配置
 */
data class BadgeConfig(
    val modifier: Modifier = Modifier,
    val containerColor: Color? = null,
    val contentColor: Color? = null,
    val content: (@Composable () -> Unit)? = null
)

/**
 * 工具提示配置
 */
data class TooltipConfig(
    val tooltip: @Composable () -> Unit,
    val modifier: Modifier = Modifier,
    val focusable: Boolean? = true,
    val enableUserInput: Boolean? = true,
    val positionProvider: Any? = null, // TooltipPositionProvider
    val state: Any? = null, // TooltipState
    val content: @Composable () -> Unit
)

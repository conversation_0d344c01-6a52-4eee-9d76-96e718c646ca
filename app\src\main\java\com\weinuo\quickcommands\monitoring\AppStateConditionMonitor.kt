package com.weinuo.quickcommands.monitoring

import android.app.usage.UsageStatsManager
import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.shizuku.ShizukuManager
import kotlinx.coroutines.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 应用状态条件监听器
 * 监听应用状态变化，触发相应的条件
 *
 * 支持的监听功能：
 * - 应用进入前台
 * - 应用进入后台
 * - 应用状态变化
 * - 应用启动和关闭（通过前台状态推断）
 */
class AppStateConditionMonitor(
    private val context: Context,
    private val onConditionTriggered: (AppStateCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "AppStateConditionMonitor"
        private const val DEFAULT_APP_STATE_CHECK_INTERVAL = 3000L // 默认3秒检查一次
        private const val DEFAULT_BACKGROUND_TIME_CHECK_INTERVAL = 30000L // 默认30秒检查一次后台时间
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<AppStateCondition>()

    // 应用状态检查任务
    private var appStateCheckJob: Job? = null

    // 后台时间检查任务
    private var backgroundTimeCheckJob: Job? = null

    // 上次前台应用状态
    private var lastForegroundApp: String? = null

    // 后台时间跟踪（独立的后台时间跟踪系统）
    private val backgroundTimeTracker = mutableMapOf<String, Long>() // packageName -> 进入后台的时间戳
    private val appLastSeenTime = mutableMapOf<String, Long>() // packageName -> 最后一次在前台的时间

    /**
     * 注册应用状态条件
     */
    fun registerCondition(condition: AppStateCondition) {
        synchronized(registeredConditions) {
            if (!registeredConditions.any { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered app state condition: ${condition.getDescription()}")

                // 验证条件的完整性
                validateCondition(condition)

                // 如果是界面交互条件，注册到界面交互无障碍服务
                if (condition.categoryType == AppStateCategoryType.INTERFACE_INTERACTION) {
                    registerInterfaceInteractionCondition(condition)
                }
            } else {
                Log.w(TAG, "App state condition already registered: ${condition.id}")
            }
        }
    }

    /**
     * 验证条件的完整性
     */
    private fun validateCondition(condition: AppStateCondition) {
        try {
            // 验证基本属性
            if (condition.id.isEmpty()) {
                Log.e(TAG, "Invalid condition: empty ID")
                return
            }

            // 验证应用包名（如果是特定应用模式）
            if (condition.detectionMode == AppDetectionMode.SELECTED_APPS && condition.selectedApps.isEmpty() && condition.targetPackageName.isEmpty()) {
                Log.w(TAG, "Specific app condition without package name: ${condition.id}")
            }

            // 验证状态类型和类别的匹配
            val isValidCombination = when (condition.categoryType) {
                AppStateCategoryType.STATE_CHANGE -> {
                    condition.stateType in listOf(
                        AppStateType.FOREGROUND,
                        AppStateType.BACKGROUND,
                        AppStateType.STATE_CHANGED,
                        AppStateType.BACKGROUND_TIME_EXCEEDED
                    )
                }
                AppStateCategoryType.LIFECYCLE -> {
                    condition.stateType in listOf(AppStateType.LAUNCHED, AppStateType.CLOSED)
                }
                AppStateCategoryType.PACKAGE_MANAGEMENT -> {
                    condition.stateType in listOf(AppStateType.INSTALLED, AppStateType.UNINSTALLED, AppStateType.UPDATED)
                }
                AppStateCategoryType.INTERFACE_INTERACTION -> {
                    condition.stateType in listOf(AppStateType.INTERFACE_CLICK, AppStateType.SCREEN_CONTENT)
                }
                AppStateCategoryType.TASKER_LOCALE_PLUGIN -> {
                    condition.stateType == AppStateType.TASKER_LOCALE_PLUGIN_CONDITION
                }
            }

            if (!isValidCombination) {
                Log.w(TAG, "Invalid state type and category combination: ${condition.stateType} with ${condition.categoryType}")
            }

            Log.d(TAG, "Condition validation passed: ${condition.getDescription()}")
        } catch (e: Exception) {
            Log.e(TAG, "Error validating condition: ${condition.id}", e)
        }
    }

    /**
     * 取消注册应用状态条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            // 查找要删除的条件
            val conditionToRemove = registeredConditions.find { it.id == conditionId }

            // 如果是界面交互条件，从界面交互无障碍服务中取消注册
            if (conditionToRemove?.categoryType == AppStateCategoryType.INTERFACE_INTERACTION) {
                unregisterInterfaceInteractionCondition(conditionId)
            }

            registeredConditions.removeAll { it.id == conditionId }
            Log.d(TAG, "Unregistered app state condition: $conditionId")
        }
    }

    /**
     * 清除所有注册的条件
     */
    fun clearAllConditions() {
        synchronized(registeredConditions) {
            registeredConditions.clear()
            Log.d(TAG, "Cleared all app state conditions")
        }
    }

    /**
     * 开始监听应用状态
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "App state monitoring already started")
            return
        }

        try {
            // 重置上次前台应用状态，避免启动时的状态混乱
            lastForegroundApp = null

            // 清空后台时间跟踪数据
            backgroundTimeTracker.clear()
            appLastSeenTime.clear()

            // 初始化界面交互无障碍服务
            initializeInterfaceInteractionService()

            startAppStateCheckJob()
            startBackgroundTimeCheckJob()
            isMonitoring = true
            Log.d(TAG, "App state monitoring started successfully with ${registeredConditions.size} conditions")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting app state monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监听应用状态
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "App state monitoring already stopped")
            return
        }

        try {
            appStateCheckJob?.cancel()
            appStateCheckJob = null

            // 停止后台时间检查任务
            backgroundTimeCheckJob?.cancel()
            backgroundTimeCheckJob = null

            // 重置状态
            lastForegroundApp = null
            backgroundTimeTracker.clear()
            appLastSeenTime.clear()

            isMonitoring = false
            Log.d(TAG, "App state monitoring stopped successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping app state monitoring", e)
        }
    }

    /**
     * 启动应用状态检查任务
     */
    private fun startAppStateCheckJob() {
        appStateCheckJob = monitorScope.launch {
            Log.d(TAG, "Starting app state check job...")

            while (isActive) {
                try {
                    // 检查应用状态变化
                    checkAppStateConditions()

                    // 计算动态检测间隔
                    val checkInterval = calculateAppStateCheckInterval()
                    delay(checkInterval)
                } catch (e: CancellationException) {
                    Log.d(TAG, "App state check job cancelled")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "Error in app state check job", e)
                    if (isActive) {
                        delay(10000) // 出错时等待10秒再重试
                    }
                }
            }
            Log.d(TAG, "App state check job ended")
        }
    }

    /**
     * 检查应用状态条件
     */
    private suspend fun checkAppStateConditions() {
        try {
            // 获取当前前台应用（不使用AppStateMonitor的缓存，避免状态冲突）
            val currentForegroundApp = getCurrentForegroundAppDirect()

            // 如果应用状态发生变化，检查相关条件
            if (currentForegroundApp != lastForegroundApp) {
                Log.d(TAG, "App state changed: $lastForegroundApp -> $currentForegroundApp")

                // 复制条件列表以避免在suspend函数中使用synchronized
                val conditionsToCheck = synchronized(registeredConditions) {
                    registeredConditions.toList()
                }

                for (condition in conditionsToCheck) {
                    if (isAppStateConditionTriggered(condition, lastForegroundApp, currentForegroundApp)) {
                        triggerCondition(condition, createEventData(lastForegroundApp, currentForegroundApp))
                    }
                }

                // 更新上次前台应用
                lastForegroundApp = currentForegroundApp
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking app state conditions", e)
        }
    }

    /**
     * 直接获取当前前台应用，不使用AppStateMonitor的缓存
     */
    private suspend fun getCurrentForegroundAppDirect(): String? = withContext(Dispatchers.IO) {
        try {
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as? UsageStatsManager
            if (usageStatsManager == null) {
                Log.e(TAG, "UsageStatsManager not available")
                return@withContext null
            }

            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 30 * 60 * 1000 // 30分钟前

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            if (usageStats.isNullOrEmpty()) {
                return@withContext null
            }

            // 查找最近活跃的应用
            var mostRecentApp: String? = null
            var mostRecentTime = 0L

            for (stat in usageStats) {
                val lastTimeUsed = stat.lastTimeUsed
                val totalTimeInForeground = stat.totalTimeInForeground

                // 检查是否在活跃阈值内（15分钟）
                if (currentTime - lastTimeUsed <= 15 * 60 * 1000L &&
                    totalTimeInForeground > 0 &&
                    lastTimeUsed > mostRecentTime) {
                    mostRecentTime = lastTimeUsed
                    mostRecentApp = stat.packageName
                }
            }

            return@withContext mostRecentApp

        } catch (e: Exception) {
            Log.e(TAG, "Error getting current foreground app directly", e)
            return@withContext null
        }
    }

    /**
     * 检查应用状态条件是否被触发
     */
    private suspend fun isAppStateConditionTriggered(
        condition: AppStateCondition,
        lastForegroundApp: String?,
        currentForegroundApp: String?
    ): Boolean {
        return when (condition.categoryType) {
            AppStateCategoryType.STATE_CHANGE -> {
                when (condition.stateType) {
                    AppStateType.FOREGROUND -> {
                        // 应用进入前台
                        when (condition.detectionMode) {
                            AppDetectionMode.ANY_APP -> {
                                // 任何应用进入前台
                                currentForegroundApp != null && currentForegroundApp != lastForegroundApp
                            }
                            AppDetectionMode.SELECTED_APPS -> {
                                // 指定应用进入前台
                                if (condition.selectedApps.isNotEmpty()) {
                                    // 新版本多选模式
                                    val targetApps = condition.selectedApps.map { it.packageName }
                                    when (condition.appStateTriggerMode) {
                                        AppStateTriggerMode.ANY_APP -> {
                                            // 任意一个应用进入前台
                                            targetApps.any { packageName ->
                                                currentForegroundApp == packageName &&
                                                lastForegroundApp != packageName
                                            }
                                        }
                                        AppStateTriggerMode.ALL_APPS -> {
                                            // 所有应用都进入前台：检查是否所有应用在一定时间窗口内都进入过前台
                                            checkAllAppsTriggered(targetApps, "foreground")
                                        }
                                    }
                                } else {
                                    // 兼容旧版本单选模式
                                    currentForegroundApp == condition.targetPackageName &&
                                    lastForegroundApp != condition.targetPackageName
                                }
                            }
                        }
                    }
                    AppStateType.BACKGROUND -> {
                        // 应用进入后台
                        when (condition.detectionMode) {
                            AppDetectionMode.ANY_APP -> {
                                // 任何应用进入后台
                                lastForegroundApp != null && currentForegroundApp != lastForegroundApp
                            }
                            AppDetectionMode.SELECTED_APPS -> {
                                // 指定应用进入后台
                                if (condition.selectedApps.isNotEmpty()) {
                                    // 新版本多选模式
                                    val targetApps = condition.selectedApps.map { it.packageName }
                                    when (condition.appStateTriggerMode) {
                                        AppStateTriggerMode.ANY_APP -> {
                                            // 任意一个应用进入后台
                                            targetApps.any { packageName ->
                                                lastForegroundApp == packageName &&
                                                currentForegroundApp != packageName
                                            }
                                        }
                                        AppStateTriggerMode.ALL_APPS -> {
                                            // 所有应用都进入后台
                                            targetApps.all { packageName ->
                                                isAppInBackground(packageName)
                                            }
                                        }
                                    }
                                } else {
                                    // 兼容旧版本单选模式
                                    lastForegroundApp == condition.targetPackageName &&
                                    currentForegroundApp != condition.targetPackageName
                                }
                            }
                        }
                    }
                    AppStateType.STATE_CHANGED -> {
                        // 应用状态变化
                        when (condition.detectionMode) {
                            AppDetectionMode.ANY_APP -> {
                                // 任何应用状态变化
                                lastForegroundApp != currentForegroundApp
                            }
                            AppDetectionMode.SELECTED_APPS -> {
                                // 指定应用状态变化
                                if (condition.selectedApps.isNotEmpty()) {
                                    // 新版本多选模式
                                    val targetApps = condition.selectedApps.map { it.packageName }
                                    when (condition.appStateTriggerMode) {
                                        AppStateTriggerMode.ANY_APP -> {
                                            // 任意一个应用状态变化
                                            targetApps.any { packageName ->
                                                (lastForegroundApp == packageName && currentForegroundApp != packageName) ||
                                                (lastForegroundApp != packageName && currentForegroundApp == packageName)
                                            }
                                        }
                                        AppStateTriggerMode.ALL_APPS -> {
                                            // 所有应用都状态变化（在一次检查中不太可能，简化为任意一个）
                                            targetApps.any { packageName ->
                                                (lastForegroundApp == packageName && currentForegroundApp != packageName) ||
                                                (lastForegroundApp != packageName && currentForegroundApp == packageName)
                                            }
                                        }
                                    }
                                } else {
                                    // 兼容旧版本单选模式
                                    (lastForegroundApp == condition.targetPackageName && currentForegroundApp != condition.targetPackageName) ||
                                    (lastForegroundApp != condition.targetPackageName && currentForegroundApp == condition.targetPackageName)
                                }
                            }
                        }
                    }
                    else -> false
                }
            }
            AppStateCategoryType.LIFECYCLE -> {
                when (condition.stateType) {
                    AppStateType.LAUNCHED -> {
                        // 应用启动（通过进入前台推断）
                        when (condition.detectionMode) {
                            AppDetectionMode.ANY_APP -> {
                                // 任何应用启动（从无前台应用到有前台应用）
                                currentForegroundApp != null && lastForegroundApp == null
                            }
                            AppDetectionMode.SELECTED_APPS -> {
                                if (condition.selectedApps.isNotEmpty()) {
                                    // 新版本多选模式
                                    val targetApps = condition.selectedApps.map { it.packageName }
                                    when (condition.appStateTriggerMode) {
                                        AppStateTriggerMode.ANY_APP -> {
                                            // 任意一个应用启动
                                            targetApps.any { packageName ->
                                                currentForegroundApp == packageName &&
                                                lastForegroundApp != packageName
                                            }
                                        }
                                        AppStateTriggerMode.ALL_APPS -> {
                                            // 所有应用都启动（简化为任意一个）
                                            targetApps.any { packageName ->
                                                currentForegroundApp == packageName &&
                                                lastForegroundApp != packageName
                                            }
                                        }
                                    }
                                } else {
                                    // 兼容旧版本单选模式
                                    currentForegroundApp == condition.targetPackageName &&
                                    lastForegroundApp != condition.targetPackageName
                                }
                            }
                        }
                    }
                    AppStateType.CLOSED -> {
                        // 应用关闭（通过离开前台推断）
                        when (condition.detectionMode) {
                            AppDetectionMode.ANY_APP -> {
                                // 任何应用关闭（从有前台应用到无前台应用）
                                lastForegroundApp != null && currentForegroundApp == null
                            }
                            AppDetectionMode.SELECTED_APPS -> {
                                if (condition.selectedApps.isNotEmpty()) {
                                    // 新版本多选模式
                                    val targetApps = condition.selectedApps.map { it.packageName }
                                    when (condition.appStateTriggerMode) {
                                        AppStateTriggerMode.ANY_APP -> {
                                            // 任意一个应用关闭
                                            targetApps.any { packageName ->
                                                lastForegroundApp == packageName &&
                                                currentForegroundApp != packageName
                                            }
                                        }
                                        AppStateTriggerMode.ALL_APPS -> {
                                            // 所有应用都关闭
                                            targetApps.all { packageName ->
                                                isAppInBackground(packageName)
                                            }
                                        }
                                    }
                                } else {
                                    // 兼容旧版本单选模式
                                    lastForegroundApp == condition.targetPackageName &&
                                    currentForegroundApp != condition.targetPackageName
                                }
                            }
                        }
                    }
                    else -> false
                }
            }
            AppStateCategoryType.PACKAGE_MANAGEMENT -> {
                // 包管理事件（安装、删除、更新）需要通过广播接收器检测
                // 这些事件不通过前台应用状态变化检测，而是通过PackageReceiver处理
                // 这里返回false，实际触发由PackageReceiver负责
                false
            }

            AppStateCategoryType.INTERFACE_INTERACTION -> {
                // 界面交互条件（界面点击、屏幕内容）需要通过界面交互无障碍服务处理
                // 这里返回false，实际触发由InterfaceInteractionAccessibilityService处理
                false
            }
            AppStateCategoryType.TASKER_LOCALE_PLUGIN -> {
                // Tasker/Locale插件条件需要通过专门的插件状态检查
                // 这里返回false，实际触发由SharedConditionEvaluator处理
                false
            }
        }
    }

    /**
     * 创建事件数据
     */
    private fun createEventData(lastForegroundApp: String?, currentForegroundApp: String?): Map<String, Any> {
        return mapOf(
            "lastForegroundApp" to (lastForegroundApp ?: ""),
            "currentForegroundApp" to (currentForegroundApp ?: ""),
            "timestamp" to System.currentTimeMillis()
        )
    }

    /**
     * 触发条件
     */
    private fun triggerCondition(condition: AppStateCondition, eventData: Map<String, Any>) {
        try {
            Log.d(TAG, "App state condition triggered: ${condition.getDescription()}")
            onConditionTriggered(condition, eventData)
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering condition: ${condition.getDescription()}", e)
        }
    }

    /**
     * 获取监听状态
     */
    fun isMonitoring(): Boolean = isMonitoring

    /**
     * 获取注册的条件数量
     */
    fun getRegisteredConditionCount(): Int = registeredConditions.size

    /**
     * 手动触发包管理类型的应用状态条件
     * 用于处理来自PackageReceiver的事件
     */
    fun triggerPackageManagementCondition(stateType: AppStateType, packageName: String, appName: String) {
        try {
            Log.d(TAG, "Triggering package management condition: $stateType for $packageName")

            synchronized(registeredConditions) {
                var matchedCount = 0
                for (condition in registeredConditions) {
                    // 只处理包管理类型的条件
                    if (condition.categoryType != AppStateCategoryType.PACKAGE_MANAGEMENT) {
                        continue
                    }

                    // 检查状态类型是否匹配
                    if (condition.stateType != stateType) {
                        Log.d(TAG, "State type mismatch: expected ${condition.stateType}, got $stateType")
                        continue
                    }

                    // 检查应用是否匹配
                    val isMatched = when (condition.detectionMode) {
                        AppDetectionMode.ANY_APP -> {
                            Log.d(TAG, "Condition matches any app: ${condition.getDescription()}")
                            true
                        }
                        AppDetectionMode.SELECTED_APPS -> {
                            if (condition.selectedApps.isNotEmpty()) {
                                // 新版本多选模式
                                val matches = condition.selectedApps.any { it.packageName == packageName }
                                Log.d(TAG, "Selected apps condition: matches=$matches for $packageName")
                                matches
                            } else {
                                // 兼容旧版本单选模式
                                val matches = condition.targetPackageName == packageName
                                Log.d(TAG, "Legacy specific app condition: target=${condition.targetPackageName}, actual=$packageName, matches=$matches")
                                matches
                            }
                        }
                    }

                    if (isMatched) {
                        val eventData = mapOf(
                            "packageName" to packageName,
                            "appName" to appName,
                            "stateType" to stateType.name,
                            "timestamp" to System.currentTimeMillis()
                        )

                        Log.d(TAG, "Package management condition matched: ${condition.getDescription()}")
                        triggerCondition(condition, eventData)
                        matchedCount++
                    }
                }

                if (matchedCount == 0) {
                    Log.d(TAG, "No package management conditions matched for $stateType on $packageName")
                } else {
                    Log.d(TAG, "Triggered $matchedCount package management conditions for $stateType on $packageName")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering package management condition", e)
        }
    }

    /**
     * 启动后台时间检查任务
     */
    private fun startBackgroundTimeCheckJob() {
        backgroundTimeCheckJob = monitorScope.launch {
            Log.d(TAG, "Starting background time check job...")

            while (isActive) {
                try {
                    // 检查后台时间条件
                    checkBackgroundTimeConditions()

                    // 计算动态检测间隔
                    val checkInterval = calculateBackgroundTimeCheckInterval()
                    delay(checkInterval)
                } catch (e: CancellationException) {
                    Log.d(TAG, "Background time check job cancelled")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "Error in background time check job", e)
                    if (isActive) {
                        delay(30000) // 出错时等待30秒再重试
                    }
                }
            }
            Log.d(TAG, "Background time check job ended")
        }
    }

    /**
     * 计算应用状态检测间隔
     * 根据注册的条件中最频繁的检测频率来确定
     */
    private fun calculateAppStateCheckInterval(): Long {
        synchronized(registeredConditions) {
            if (registeredConditions.isEmpty()) {
                return DEFAULT_APP_STATE_CHECK_INTERVAL
            }

            // 找到最短的检测间隔（最频繁的检测）
            var minInterval = DEFAULT_APP_STATE_CHECK_INTERVAL

            for (condition in registeredConditions) {
                // 只考虑非后台时间超过阈值的条件
                if (condition.stateType != AppStateType.BACKGROUND_TIME_EXCEEDED) {
                    val interval = if (condition.enableCustomCheckFrequency) {
                        condition.customCheckFrequencySeconds * 1000L
                    } else {
                        condition.checkFrequency.intervalMs
                    }

                    if (interval < minInterval) {
                        minInterval = interval
                    }
                }
            }

            return minInterval
        }
    }

    /**
     * 计算后台时间检测间隔
     * 根据注册的后台时间条件中最频繁的检测频率来确定
     */
    private fun calculateBackgroundTimeCheckInterval(): Long {
        synchronized(registeredConditions) {
            val backgroundTimeConditions = registeredConditions.filter {
                it.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED
            }

            if (backgroundTimeConditions.isEmpty()) {
                return DEFAULT_BACKGROUND_TIME_CHECK_INTERVAL
            }

            // 找到最短的检测间隔（最频繁的检测）
            var minInterval = DEFAULT_BACKGROUND_TIME_CHECK_INTERVAL

            for (condition in backgroundTimeConditions) {
                val interval = if (condition.enableCustomBackgroundTimeCheckFrequency) {
                    condition.customBackgroundTimeCheckFrequencySeconds * 1000L
                } else {
                    condition.backgroundTimeCheckFrequency.intervalMs
                }

                if (interval < minInterval) {
                    minInterval = interval
                }
            }

            return minInterval
        }
    }

    /**
     * 检查后台时间条件
     */
    private suspend fun checkBackgroundTimeConditions() {
        try {
            val currentTime = System.currentTimeMillis()
            val currentForegroundApp = getCurrentForegroundAppDirect()

            // 更新后台时间跟踪
            updateBackgroundTimeTracking(currentForegroundApp, currentTime)

            // 检查后台时间条件（现在在STATE_CHANGE类别下）
            val backgroundTimeConditions = synchronized(registeredConditions) {
                registeredConditions.filter {
                    it.categoryType == AppStateCategoryType.STATE_CHANGE &&
                    it.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED
                }
            }

            for (condition in backgroundTimeConditions) {
                checkSingleBackgroundTimeCondition(condition, currentTime, currentForegroundApp)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking background time conditions", e)
        }
    }

    /**
     * 更新后台时间跟踪
     */
    private fun updateBackgroundTimeTracking(currentForegroundApp: String?, currentTime: Long) {
        // 获取所有需要跟踪的应用包名（现在在STATE_CHANGE类别下）
        val appsToTrack = mutableSetOf<String>()
        synchronized(registeredConditions) {
            registeredConditions.filter {
                it.categoryType == AppStateCategoryType.STATE_CHANGE &&
                it.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED
            }.forEach { condition ->
                appsToTrack.addAll(condition.selectedApps.map { it.packageName })
            }
        }

        // 更新每个应用的状态
        for (packageName in appsToTrack) {
            if (packageName == currentForegroundApp) {
                // 应用在前台，移除后台时间记录，更新最后见到时间
                backgroundTimeTracker.remove(packageName)
                appLastSeenTime[packageName] = currentTime
            } else {
                // 应用不在前台
                if (!backgroundTimeTracker.containsKey(packageName)) {
                    // 第一次进入后台，记录时间
                    val lastSeenTime = appLastSeenTime[packageName] ?: currentTime
                    backgroundTimeTracker[packageName] = lastSeenTime
                    Log.d(TAG, "App $packageName entered background at ${lastSeenTime}")
                }
            }
        }
    }

    /**
     * 检查单个后台时间条件
     */
    private suspend fun checkSingleBackgroundTimeCondition(
        condition: AppStateCondition,
        currentTime: Long,
        currentForegroundApp: String?
    ) {
        try {
            val thresholdMillis = condition.backgroundTimeThresholdMinutes * 60 * 1000L
            val triggeredApps = mutableListOf<SimpleAppInfo>()

            // 检查每个选择的应用
            for (app in condition.selectedApps) {
                val packageName = app.packageName
                val backgroundStartTime = backgroundTimeTracker[packageName]

                if (backgroundStartTime != null) {
                    val backgroundDuration = currentTime - backgroundStartTime

                    // 检查是否超过阈值
                    if (backgroundDuration >= thresholdMillis) {
                        // 检查例外处理
                        if (shouldSkipApp(app, condition, currentForegroundApp)) {
                            Log.d(TAG, "Skipping app ${app.appName} due to exception rules")
                            continue
                        }

                        triggeredApps.add(app)
                        Log.d(TAG, "App ${app.appName} background time exceeded: ${backgroundDuration / 1000 / 60} minutes")
                    }
                }
            }

            // 根据触发模式决定是否触发条件
            val shouldTrigger = when (condition.appStateTriggerMode) {
                AppStateTriggerMode.ANY_APP -> triggeredApps.isNotEmpty()
                AppStateTriggerMode.ALL_APPS -> triggeredApps.size == condition.selectedApps.size
            }

            if (shouldTrigger) {
                // 触发条件，传递触发的应用信息
                val eventData = createBackgroundTimeEventData(condition, triggeredApps, currentTime)
                triggerCondition(condition, eventData)

                // 重置已触发应用的后台时间，避免重复触发
                for (app in triggeredApps) {
                    backgroundTimeTracker.remove(app.packageName)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking background time condition: ${condition.getDescription()}", e)
        }
    }

    /**
     * 获取正在播放音乐的应用包名集合（快捷指令专用版本）
     *
     * 通过媒体会话检测正在播放音乐的应用
     * 使用dumpsys media_session命令检查媒体会话状态
     * 此方法与主功能的音乐播放检测逻辑完全一致，但保持独立实现
     *
     * @return 正在播放音乐的应用包名集合，如果没有则返回空集合
     */
    private suspend fun getMusicPlayingPackagesForQuickCommands(): Set<String> = withContext(Dispatchers.IO) {
        try {
            // 检查Shizuku权限，只有在有权限时才执行命令
            if (!ShizukuManager.checkShizukuPermission()) {
                Log.w(TAG, "No Shizuku permission, cannot detect music playing packages for quick commands")
                return@withContext emptySet()
            }

            // 使用dumpsys media_session命令检查媒体会话状态
            val result = ShizukuManager.executeCommand("dumpsys media_session")

            if (result.isNotEmpty()) {
                // 解析媒体会话输出，提取正在播放音乐的应用包名
                val packages = parseMediaSessionPackagesForQuickCommands(result)

                Log.d(TAG, "Music playing packages for quick commands: ${packages.joinToString(", ")}")
                return@withContext packages
            } else {
                Log.w(TAG, "Empty result from dumpsys media_session command for quick commands")
                return@withContext emptySet()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting music playing packages for quick commands", e)
            return@withContext emptySet()
        }
    }

    /**
     * 解析媒体会话输出，提取正在播放音乐的应用包名（快捷指令专用版本）
     *
     * @param output dumpsys media_session命令的输出
     * @return 正在播放音乐的应用包名集合
     */
    private fun parseMediaSessionPackagesForQuickCommands(output: String): Set<String> {
        val packages = mutableSetOf<String>()

        try {
            val lines = output.split("\n")
            var currentPackage: String? = null
            var isPlaying = false

            for (line in lines) {
                val trimmedLine = line.trim()

                // 查找包名
                if (trimmedLine.startsWith("package=")) {
                    currentPackage = trimmedLine.substringAfter("package=").trim()
                    isPlaying = false
                }

                // 查找播放状态
                if (trimmedLine.contains("state=3") || // STATE_PLAYING
                    trimmedLine.contains("playing") ||
                    trimmedLine.contains("PLAYING")) {
                    isPlaying = true
                }

                // 如果找到了包名和播放状态，添加到结果中
                if (currentPackage != null && isPlaying) {
                    packages.add(currentPackage)
                    Log.d(TAG, "Added music playing package for quick commands: $currentPackage")
                    currentPackage = null
                    isPlaying = false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing media session output for quick commands", e)
        }

        return packages
    }

    /**
     * 检查是否应该跳过应用
     */
    private suspend fun shouldSkipApp(
        app: SimpleAppInfo,
        condition: AppStateCondition,
        currentForegroundApp: String?
    ): Boolean {
        // 跳过前台应用
        if (condition.skipForegroundApp && app.packageName == currentForegroundApp) {
            return true
        }

        // 跳过音乐播放应用（使用与主功能完全一致的检测逻辑）
        if (condition.skipMusicPlayingApp) {
            val musicPlayingPackages = getMusicPlayingPackagesForQuickCommands()
            if (musicPlayingPackages.contains(app.packageName)) {
                return true
            }
        }

        // 跳过VPN应用
        if (condition.skipVpnApp) {
            // 检查是否在选择的VPN应用列表中
            if (condition.selectedVpnApps.any { it.packageName == app.packageName }) {
                return true
            }
        }

        return false
    }

    /**
     * 创建后台时间事件数据
     */
    private fun createBackgroundTimeEventData(
        condition: AppStateCondition,
        triggeredApps: List<SimpleAppInfo>,
        currentTime: Long
    ): Map<String, Any> {
        return mapOf(
            "triggerApp" to (triggeredApps.firstOrNull() ?: SimpleAppInfo("", "", false)),
            "triggeredApps" to triggeredApps,
            "allSelectedApps" to condition.selectedApps,
            "backgroundTimeThresholdMinutes" to condition.backgroundTimeThresholdMinutes,
            "triggerMode" to condition.appStateTriggerMode.name,
            "timestamp" to currentTime
        )
    }

    /**
     * 初始化界面交互无障碍服务
     */
    private fun initializeInterfaceInteractionService() {
        try {
            val interfaceInteractionService = com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService.getInstance()
            if (interfaceInteractionService != null) {
                // 设置条件触发回调
                interfaceInteractionService.setConditionTriggeredCallback { condition, eventData ->
                    onConditionTriggered(condition, eventData)
                }

                // 注册所有已存在的界面交互条件
                synchronized(registeredConditions) {
                    registeredConditions.filter {
                        it.categoryType == AppStateCategoryType.INTERFACE_INTERACTION
                    }.forEach { condition ->
                        interfaceInteractionService.registerCondition(condition)
                    }
                }

                Log.d(TAG, "Interface interaction service initialized successfully")
            } else {
                Log.w(TAG, "Interface interaction accessibility service not available")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing interface interaction service", e)
        }
    }

    /**
     * 注册界面交互条件到无障碍服务
     */
    private fun registerInterfaceInteractionCondition(condition: AppStateCondition) {
        try {
            val interfaceInteractionService = com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService.getInstance()
            if (interfaceInteractionService != null) {
                interfaceInteractionService.registerCondition(condition)
                Log.d(TAG, "Registered interface interaction condition to accessibility service: ${condition.getDescription()}")
            } else {
                Log.w(TAG, "Interface interaction accessibility service not available for condition registration")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error registering interface interaction condition", e)
        }
    }

    /**
     * 从无障碍服务取消注册界面交互条件
     */
    private fun unregisterInterfaceInteractionCondition(conditionId: String) {
        try {
            val interfaceInteractionService = com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService.getInstance()
            if (interfaceInteractionService != null) {
                interfaceInteractionService.unregisterCondition(conditionId)
                Log.d(TAG, "Unregistered interface interaction condition from accessibility service: $conditionId")
            } else {
                Log.w(TAG, "Interface interaction accessibility service not available for condition unregistration")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering interface interaction condition", e)
        }
    }

    /**
     * 检查应用是否在后台
     */
    private suspend fun isAppInBackground(packageName: String): Boolean {
        return try {
            val currentForegroundApp = getCurrentForegroundAppDirect()
            currentForegroundApp != packageName
        } catch (e: Exception) {
            Log.e(TAG, "检查应用后台状态失败: ${e.message}")
            false
        }
    }

    // 应用触发历史记录（用于ALL_APPS模式）
    private val appTriggerHistory = mutableMapOf<String, MutableMap<String, Long>>() // conditionId -> (packageName -> timestamp)
    private val triggerTimeWindow = 5 * 60 * 1000L // 5分钟时间窗口

    /**
     * 检查所有应用是否都在时间窗口内触发过
     */
    private fun checkAllAppsTriggered(targetApps: List<String>, triggerType: String): Boolean {
        val conditionKey = "${triggerType}_all_apps"
        val currentTime = System.currentTimeMillis()

        // 获取或创建此条件的触发历史
        val history = appTriggerHistory.getOrPut(conditionKey) { mutableMapOf() }

        // 清理过期的触发记录
        history.entries.removeAll { (_, timestamp) ->
            currentTime - timestamp > triggerTimeWindow
        }

        // 记录当前触发的应用
        val currentForegroundApp = lastForegroundApp
        if (currentForegroundApp != null && targetApps.contains(currentForegroundApp)) {
            history[currentForegroundApp] = currentTime
        }

        // 检查是否所有目标应用都在时间窗口内触发过
        val allTriggered = targetApps.all { packageName ->
            history.containsKey(packageName) &&
            (currentTime - history[packageName]!!) <= triggerTimeWindow
        }

        if (allTriggered) {
            // 清理此条件的历史记录，避免重复触发
            history.clear()
            Log.d(TAG, "All apps triggered within time window for $triggerType: ${targetApps.joinToString(", ")}")
        }

        return allTriggered
    }
}

package com.weinuo.quickcommands.ui.components.themed

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands.ui.theme.config.BottomNavigationConfig
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.effects.HazeManager
import dev.chrisbanes.haze.HazeState

/**
 * 主题感知的底部导航组件
 *
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用分层设计风格（LayeredBottomNavigation）
 * - 天空蓝主题：使用整合设计风格（IntegratedBottomNavigation）
 * - 未来主题：可以使用各自独有的实现
 *
 * 这是主题系统的核心组件之一，提供了统一的接口，
 * 同时允许不同主题有完全不同的视觉实现。
 */
@Composable
fun ThemedBottomNavigation(
    config: BottomNavigationConfig,
    hazeState: HazeState,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current

    // 检查是否支持Haze模糊效果
    if (themeContext.componentFactory is com.weinuo.quickcommands.ui.theme.system.HazeAwareComponentFactory) {
        // 使用支持模糊效果的方法
        (themeContext.componentFactory as com.weinuo.quickcommands.ui.theme.system.HazeAwareComponentFactory)
            .createBottomNavigationWithHaze()(
                config.copy(modifier = modifier),
                hazeState
            )
    } else {
        // 降级到普通方法
        themeContext.componentFactory.createBottomNavigation()(
            config.copy(modifier = modifier)
        )
    }
}

/**
 * 主题感知的底部导航组件 - 便捷版本
 *
 * 提供更简洁的API，自动处理常见的配置
 */
@Composable
fun ThemedBottomNavigation(
    tabs: List<com.weinuo.quickcommands.ui.theme.config.NavigationTab>,
    selectedIndex: Int,
    onTabSelected: (Int) -> Unit,
    hazeState: HazeState? = null,
    modifier: Modifier = Modifier,
    blurEnabled: Boolean? = null
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current
    val actualHazeState = hazeState ?: HazeManager.getInstance(context).globalHazeState

    // 创建配置对象
    val config = BottomNavigationConfig(
        tabs = tabs,
        selectedIndex = selectedIndex,
        onTabSelected = onTabSelected,
        modifier = modifier,
        blurEnabled = blurEnabled ?: themeContext.blurConfiguration.bottomBarBlurEnabled
    )

    // 使用主题感知组件
    ThemedBottomNavigation(config = config, hazeState = actualHazeState)
}

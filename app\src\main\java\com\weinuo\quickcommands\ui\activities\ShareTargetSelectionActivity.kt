package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import com.weinuo.quickcommands.model.ShareTarget
import com.weinuo.quickcommands.model.ShareTargetSelectionMode
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme

/**
 * 分享目标选择独立Activity
 *
 * 提供全屏的分享目标选择界面，支持单选模式
 * 使用ActivityResult机制返回选中的分享目标信息
 * 
 * 启动方式：
 * - startForSelection: 启动分享目标选择界面
 * 
 * 返回结果：
 * - RESULT_OK: 选择成功，通过Intent extras返回分享目标信息
 * - RESULT_CANCELED: 用户取消选择
 */
class ShareTargetSelectionActivity : ComponentActivity() {
    
    companion object {
        // 结果数据键
        const val RESULT_PACKAGE_NAME = "result_package_name"
        const val RESULT_APP_NAME = "result_app_name"
        const val RESULT_ACTIVITY_NAME = "result_activity_name"
        const val RESULT_TARGET_LABEL = "result_target_label"
        const val RESULT_IS_SYSTEM_APP = "result_is_system_app"
        
        /**
         * 启动分享目标选择界面
         * 
         * @param context 上下文
         */
        fun startForSelection(context: Context) {
            val intent = Intent(context, ShareTargetSelectionActivity::class.java)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            QuickCommandsTheme {
                ShareTargetSelectionActivityContent(
                    onShareTargetSelected = { selectedTarget ->
                        finishWithResult(selectedTarget)
                    },
                    onFinish = { finish() }
                )
            }
        }
    }
    
    /**
     * 完成选择并返回结果
     */
    private fun finishWithResult(selectedTarget: ShareTarget) {
        val intent = Intent().apply {
            putExtra(RESULT_PACKAGE_NAME, selectedTarget.packageName)
            putExtra(RESULT_APP_NAME, selectedTarget.appName)
            putExtra(RESULT_ACTIVITY_NAME, selectedTarget.activityName)
            putExtra(RESULT_TARGET_LABEL, selectedTarget.targetLabel)
            putExtra(RESULT_IS_SYSTEM_APP, selectedTarget.isSystemApp)
        }
        setResult(Activity.RESULT_OK, intent)
        finish()
    }
}

/**
 * 分享目标选择Activity内容组件
 * 
 * 不依赖NavController的可复用组件，使用回调函数处理导航
 * 
 * @param onShareTargetSelected 分享目标选择完成回调
 * @param onFinish 完成回调（返回/取消）
 */
@Composable
fun ShareTargetSelectionActivityContent(
    onShareTargetSelected: (ShareTarget) -> Unit,
    onFinish: () -> Unit
) {
    // 使用原有的ShareTargetSelectionScreen组件，但使用回调函数处理导航
    com.weinuo.quickcommands.ui.screens.ShareTargetSelectionScreen(
        selectionMode = ShareTargetSelectionMode.SINGLE,
        onShareTargetSelected = onShareTargetSelected,
        onDismiss = onFinish
    )
}

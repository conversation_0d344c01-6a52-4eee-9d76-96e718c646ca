package com.weinuo.quickcommands.ui.theme.manager

import androidx.compose.material3.ColorScheme
import com.weinuo.quickcommands.ui.theme.system.AppTheme
import com.weinuo.quickcommands.ui.theme.system.ComponentFactory
import com.weinuo.quickcommands.ui.theme.system.StyleConfiguration
import com.weinuo.quickcommands.ui.theme.system.InteractionConfiguration
import com.weinuo.quickcommands.ui.theme.system.AnimationConfiguration
import com.weinuo.quickcommands.ui.theme.config.BlurConfiguration
import java.util.concurrent.ConcurrentHashMap

/**
 * 缓存统计信息数据类
 *
 * @param totalSize 缓存总大小
 * @param cacheHits 缓存命中次数
 * @param cacheMisses 缓存未命中次数
 * @param hitRate 缓存命中率（百分比）
 * @param colorSchemeCount 颜色方案缓存数量
 * @param componentFactoryCount 组件工厂缓存数量
 * @param styleConfigurationCount 样式配置缓存数量
 * @param interactionConfigurationCount 交互配置缓存数量
 * @param animationConfigurationCount 动画配置缓存数量
 * @param blurConfigurationCount 模糊配置缓存数量
 */
data class CacheStats(
    val totalSize: Int,
    val cacheHits: Int,
    val cacheMisses: Int,
    val hitRate: Float,
    val colorSchemeCount: Int,
    val componentFactoryCount: Int,
    val styleConfigurationCount: Int,
    val interactionConfigurationCount: Int,
    val animationConfigurationCount: Int,
    val blurConfigurationCount: Int
)

/**
 * 主题缓存管理器
 *
 * 负责缓存主题相关的配置对象，提高主题切换性能
 * 使用线程安全的ConcurrentHashMap确保多线程环境下的安全性
 */
class ThemeCache {
    
    // 颜色方案缓存
    private val colorSchemeCache = ConcurrentHashMap<String, ColorScheme>()
    
    // 组件工厂缓存
    private val componentFactoryCache = ConcurrentHashMap<String, ComponentFactory>()
    
    // 样式配置缓存
    private val styleConfigurationCache = ConcurrentHashMap<String, StyleConfiguration>()
    
    // 交互配置缓存
    private val interactionConfigurationCache = ConcurrentHashMap<String, InteractionConfiguration>()
    
    // 动画配置缓存
    private val animationConfigurationCache = ConcurrentHashMap<String, AnimationConfiguration>()
    
    // 模糊配置缓存
    private val blurConfigurationCache = ConcurrentHashMap<String, BlurConfiguration>()
    
    // 缓存统计
    private var cacheHits = 0
    private var cacheMisses = 0

    // 缓存大小限制
    private val maxCacheSize = 10

    // 最近使用时间记录
    private val lastAccessTime = ConcurrentHashMap<String, Long>()
    
    /**
     * 缓存主题的所有配置
     */
    fun cacheTheme(theme: AppTheme) {
        val themeId = theme.id
        val provider = theme.themeProvider
        
        try {
            // 缓存各种配置
            colorSchemeCache[themeId] = provider.getColorScheme()
            componentFactoryCache[themeId] = provider.getComponentFactory()
            styleConfigurationCache[themeId] = provider.getStyleConfiguration()
            interactionConfigurationCache[themeId] = provider.getInteractionConfiguration()
            animationConfigurationCache[themeId] = provider.getAnimationConfiguration()
            blurConfigurationCache[themeId] = provider.getBlurConfiguration()
            
        } catch (e: Exception) {
            android.util.Log.e("ThemeCache", "Failed to cache theme: $themeId", e)
            // 清理可能部分缓存的数据
            clearThemeCache(themeId)
        }
    }
    
    /**
     * 获取颜色方案（带缓存）
     */
    fun getColorScheme(theme: AppTheme): ColorScheme {
        return colorSchemeCache[theme.id] ?: run {
            cacheMisses++
            ThemePerformanceMonitor.recordCacheMiss()
            val colorScheme = theme.themeProvider.getColorScheme()
            colorSchemeCache[theme.id] = colorScheme
            colorScheme
        }.also {
            cacheHits++
            ThemePerformanceMonitor.recordCacheHit()
        }
    }
    
    /**
     * 获取组件工厂（带缓存）
     */
    fun getComponentFactory(theme: AppTheme): ComponentFactory {
        return componentFactoryCache[theme.id] ?: run {
            cacheMisses++
            val factory = theme.themeProvider.getComponentFactory()
            componentFactoryCache[theme.id] = factory
            factory
        }.also { cacheHits++ }
    }
    
    /**
     * 获取样式配置（带缓存）
     */
    fun getStyleConfiguration(theme: AppTheme): StyleConfiguration {
        return styleConfigurationCache[theme.id] ?: run {
            cacheMisses++
            val config = theme.themeProvider.getStyleConfiguration()
            styleConfigurationCache[theme.id] = config
            config
        }.also { cacheHits++ }
    }
    
    /**
     * 获取交互配置（带缓存）
     */
    fun getInteractionConfiguration(theme: AppTheme): InteractionConfiguration {
        return interactionConfigurationCache[theme.id] ?: run {
            cacheMisses++
            val config = theme.themeProvider.getInteractionConfiguration()
            interactionConfigurationCache[theme.id] = config
            config
        }.also { cacheHits++ }
    }
    
    /**
     * 获取动画配置（带缓存）
     */
    fun getAnimationConfiguration(theme: AppTheme): AnimationConfiguration {
        return animationConfigurationCache[theme.id] ?: run {
            cacheMisses++
            val config = theme.themeProvider.getAnimationConfiguration()
            animationConfigurationCache[theme.id] = config
            config
        }.also { cacheHits++ }
    }
    
    /**
     * 获取模糊配置（带缓存）
     */
    fun getBlurConfiguration(theme: AppTheme): BlurConfiguration {
        return blurConfigurationCache[theme.id] ?: run {
            cacheMisses++
            val config = theme.themeProvider.getBlurConfiguration()
            blurConfigurationCache[theme.id] = config
            config
        }.also { cacheHits++ }
    }
    
    /**
     * 检查主题是否已缓存
     */
    fun isThemeCached(theme: AppTheme): Boolean {
        val themeId = theme.id
        return colorSchemeCache.containsKey(themeId) &&
               componentFactoryCache.containsKey(themeId) &&
               styleConfigurationCache.containsKey(themeId) &&
               interactionConfigurationCache.containsKey(themeId) &&
               animationConfigurationCache.containsKey(themeId) &&
               blurConfigurationCache.containsKey(themeId)
    }
    
    /**
     * 清理特定主题的缓存
     */
    fun clearThemeCache(themeId: String) {
        colorSchemeCache.remove(themeId)
        componentFactoryCache.remove(themeId)
        styleConfigurationCache.remove(themeId)
        interactionConfigurationCache.remove(themeId)
        animationConfigurationCache.remove(themeId)
        blurConfigurationCache.remove(themeId)
    }
    
    /**
     * 清理除指定主题外的所有缓存
     */
    fun clearCacheExcept(currentTheme: AppTheme) {
        val currentThemeId = currentTheme.id
        
        colorSchemeCache.keys.filter { it != currentThemeId }.forEach { 
            colorSchemeCache.remove(it) 
        }
        componentFactoryCache.keys.filter { it != currentThemeId }.forEach { 
            componentFactoryCache.remove(it) 
        }
        styleConfigurationCache.keys.filter { it != currentThemeId }.forEach { 
            styleConfigurationCache.remove(it) 
        }
        interactionConfigurationCache.keys.filter { it != currentThemeId }.forEach { 
            interactionConfigurationCache.remove(it) 
        }
        animationConfigurationCache.keys.filter { it != currentThemeId }.forEach { 
            animationConfigurationCache.remove(it) 
        }
        blurConfigurationCache.keys.filter { it != currentThemeId }.forEach { 
            blurConfigurationCache.remove(it) 
        }
    }
    
    /**
     * 清理所有缓存
     */
    fun clearCache() {
        colorSchemeCache.clear()
        componentFactoryCache.clear()
        styleConfigurationCache.clear()
        interactionConfigurationCache.clear()
        animationConfigurationCache.clear()
        blurConfigurationCache.clear()
        
        // 重置统计
        cacheHits = 0
        cacheMisses = 0
    }
    
    /**
     * 获取缓存大小
     */
    fun getCacheSize(): Int {
        return colorSchemeCache.size + 
               componentFactoryCache.size + 
               styleConfigurationCache.size + 
               interactionConfigurationCache.size + 
               animationConfigurationCache.size + 
               blurConfigurationCache.size
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): CacheStats {
        val totalRequests = cacheHits + cacheMisses
        val hitRate = if (totalRequests > 0) (cacheHits.toFloat() / totalRequests) * 100 else 0f
        
        return CacheStats(
            totalSize = getCacheSize(),
            cacheHits = cacheHits,
            cacheMisses = cacheMisses,
            hitRate = hitRate,
            colorSchemeCount = colorSchemeCache.size,
            componentFactoryCount = componentFactoryCache.size,
            styleConfigurationCount = styleConfigurationCache.size,
            interactionConfigurationCount = interactionConfigurationCache.size,
            animationConfigurationCount = animationConfigurationCache.size,
            blurConfigurationCount = blurConfigurationCache.size
        )
    }
    
    /**
     * 预热缓存
     * 预加载指定主题的所有配置
     */
    fun warmupCache(themes: List<AppTheme>) {
        themes.forEach { theme ->
            if (!isThemeCached(theme)) {
                cacheTheme(theme)
            }
        }
    }
    
    /**
     * 验证缓存一致性
     */
    fun validateCache(): CacheValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        try {
            // 检查缓存大小一致性
            val sizes = listOf(
                colorSchemeCache.size,
                componentFactoryCache.size,
                styleConfigurationCache.size,
                interactionConfigurationCache.size,
                animationConfigurationCache.size,
                blurConfigurationCache.size
            )
            
            if (sizes.distinct().size > 1) {
                warnings.add("Cache sizes are inconsistent: $sizes")
            }
            
            // 检查缓存键一致性
            val allKeys = setOf(
                colorSchemeCache.keys,
                componentFactoryCache.keys,
                styleConfigurationCache.keys,
                interactionConfigurationCache.keys,
                animationConfigurationCache.keys,
                blurConfigurationCache.keys
            ).flatten().toSet()
            
            allKeys.forEach { key ->
                val missingCaches = mutableListOf<String>()
                if (!colorSchemeCache.containsKey(key)) missingCaches.add("ColorScheme")
                if (!componentFactoryCache.containsKey(key)) missingCaches.add("ComponentFactory")
                if (!styleConfigurationCache.containsKey(key)) missingCaches.add("StyleConfiguration")
                if (!interactionConfigurationCache.containsKey(key)) missingCaches.add("InteractionConfiguration")
                if (!animationConfigurationCache.containsKey(key)) missingCaches.add("AnimationConfiguration")
                if (!blurConfigurationCache.containsKey(key)) missingCaches.add("BlurConfiguration")
                
                if (missingCaches.isNotEmpty()) {
                    warnings.add("Theme $key is missing caches: $missingCaches")
                }
            }
            
        } catch (e: Exception) {
            errors.add("Cache validation error: ${e.message}")
        }
        
        return CacheValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * 设置最大缓存大小
     */
    fun setMaxCacheSize(size: Int) {
        // 实现缓存大小限制逻辑
        if (colorSchemeCache.size > size) {
            cleanupOldestEntries(colorSchemeCache, size)
        }
        if (componentFactoryCache.size > size) {
            cleanupOldestEntries(componentFactoryCache, size)
        }
        // 其他缓存类似处理...
    }

    /**
     * 启用/禁用激进清理
     */
    fun enableAggressiveCleanup(enabled: Boolean) {
        // 实现激进清理逻辑
        if (enabled) {
            // 立即清理未使用的缓存
            cleanupUnusedCache()
        }
    }

    /**
     * 清理未使用的缓存
     * 性能优化：支持保护特定主题不被清理
     */
    fun cleanupUnusedCache(protectedThemeIds: Set<String> = emptySet()) {
        val currentTime = System.currentTimeMillis()
        val maxAge = 300000L // 5分钟

        lastAccessTime.entries.removeAll { (key, time) ->
            // 保护指定的主题不被清理
            if (protectedThemeIds.contains(key)) {
                false
            } else if (currentTime - time > maxAge) {
                // 移除过期的缓存项
                colorSchemeCache.remove(key)
                componentFactoryCache.remove(key)
                styleConfigurationCache.remove(key)
                interactionConfigurationCache.remove(key)
                animationConfigurationCache.remove(key)
                blurConfigurationCache.remove(key)
                android.util.Log.d("ThemeCache", "清理过期缓存: $key")
                true
            } else {
                false
            }
        }
    }

    /**
     * 智能清理：只保留当前主题和最近使用的主题
     */
    fun performSmartCleanup(currentThemeId: String, maxCacheSize: Int = 2) {
        if (getCacheSize() <= maxCacheSize) {
            return // 缓存大小在限制内，无需清理
        }

        val protectedThemes = setOf(currentThemeId)
        val sortedByTime = lastAccessTime.entries
            .filter { !protectedThemes.contains(it.key) }
            .sortedByDescending { it.value }

        // 保留最近使用的主题，清理其他
        val toKeep = sortedByTime.take(maxCacheSize - protectedThemes.size).map { it.key }
        val toRemove = lastAccessTime.keys - protectedThemes - toKeep.toSet()

        toRemove.forEach { themeId ->
            clearThemeCache(themeId)
            android.util.Log.d("ThemeCache", "智能清理缓存: $themeId")
        }
    }

    /**
     * 清理最旧的缓存项
     */
    private fun <T> cleanupOldestEntries(cache: ConcurrentHashMap<String, T>, maxSize: Int) {
        if (cache.size <= maxSize) return

        val sortedByTime = lastAccessTime.entries.sortedBy { it.value }
        val toRemove = cache.size - maxSize

        sortedByTime.take(toRemove).forEach { (key, _) ->
            cache.remove(key)
            lastAccessTime.remove(key)
        }
    }

    /**
     * 清空所有缓存
     */
    fun clearAll() {
        colorSchemeCache.clear()
        componentFactoryCache.clear()
        styleConfigurationCache.clear()
        interactionConfigurationCache.clear()
        animationConfigurationCache.clear()
        blurConfigurationCache.clear()
        lastAccessTime.clear()
        cacheHits = 0
        cacheMisses = 0
    }

    /**
     * 获取缓存统计信息
     */
    fun getCacheStatistics(): CacheStatistics {
        val totalOperations = cacheHits + cacheMisses
        val hitRate = if (totalOperations > 0) cacheHits.toFloat() / totalOperations else 0f

        return CacheStatistics(
            totalSize = colorSchemeCache.size + componentFactoryCache.size +
                       styleConfigurationCache.size + interactionConfigurationCache.size +
                       animationConfigurationCache.size + blurConfigurationCache.size,
            cacheHits = cacheHits,
            cacheMisses = cacheMisses,
            hitRate = hitRate,
            colorSchemeCount = colorSchemeCache.size,
            componentFactoryCount = componentFactoryCache.size,
            styleConfigurationCount = styleConfigurationCache.size,
            interactionConfigurationCount = interactionConfigurationCache.size,
            animationConfigurationCount = animationConfigurationCache.size,
            blurConfigurationCount = blurConfigurationCache.size
        )
    }
}

/**
 * 缓存统计信息
 */
data class CacheStatistics(
    val totalSize: Int,
    val cacheHits: Int,
    val cacheMisses: Int,
    val hitRate: Float,
    val colorSchemeCount: Int,
    val componentFactoryCount: Int,
    val styleConfigurationCount: Int,
    val interactionConfigurationCount: Int,
    val animationConfigurationCount: Int,
    val blurConfigurationCount: Int
)

/**
 * 缓存验证结果
 */
data class CacheValidationResult(
    val isValid: Boolean,
    val errors: List<String>,
    val warnings: List<String>
)

/**
 * 缓存性能监控器
 */
object CachePerformanceMonitor {
    private var totalCacheOperations = 0L
    private var totalCacheTime = 0L

    /**
     * 记录缓存操作性能
     */
    fun recordCacheOperation(operationTimeMs: Long) {
        totalCacheOperations++
        totalCacheTime += operationTimeMs
    }

    /**
     * 获取平均缓存操作时间
     */
    fun getAverageCacheTime(): Double {
        return if (totalCacheOperations > 0) {
            totalCacheTime.toDouble() / totalCacheOperations
        } else 0.0
    }

    /**
     * 重置性能统计
     */
    fun reset() {
        totalCacheOperations = 0L
        totalCacheTime = 0L
    }

    /**
     * 获取性能报告
     */
    fun getPerformanceReport(): String {
        return """
            缓存性能报告:
            - 总操作次数: $totalCacheOperations
            - 总耗时: ${totalCacheTime}ms
            - 平均耗时: ${String.format("%.2f", getAverageCacheTime())}ms
        """.trimIndent()
    }
}

package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.components.RingtoneConfigurationContent
import com.weinuo.quickcommands.utils.RingtoneHelper

/**
 * 信息任务配置提供器
 *
 * 提供信息任务的配置项列表，包括发送短信和发送邮件功能。
 * 使用新的模块化配置系统架构，支持高度复用的通用组件。
 *
 * 设计特点：
 * - 模块化设计：每个信息操作类型独立配置
 * - 权限集成：内置权限检查逻辑
 * - 可扩展性：新增信息操作类型只需添加配置项
 * - 统一体验：使用通用的ExpandableConfigurationCard和DetailConfigurationScreen
 *
 * <AUTHOR>
 * @since 1.0.0
 */
object InformationTaskConfigProvider {

    /**
     * 获取信息任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 信息任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<InformationOperation>> {
        return listOf(
            // 发送短信配置项
            ConfigurationCardItem(
                id = "send_sms",
                title = context.getString(R.string.info_send_sms),
                description = context.getString(R.string.info_send_sms_description),
                operationType = InformationOperation.SEND_SMS,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SendSmsConfigContent(operation, onComplete)
                }
            ),

            // 发送邮件配置项
            ConfigurationCardItem(
                id = "send_email",
                title = context.getString(R.string.info_send_email),
                description = context.getString(R.string.info_send_email_description),
                operationType = InformationOperation.SEND_EMAIL,
                permissionRequired = true, // 需要检查通知权限（当用户启用通知选项时）
                content = { operation, onComplete ->
                    SendEmailConfigContent(operation, onComplete)
                }
            ),

            // 信息铃声设置配置项
            ConfigurationCardItem(
                id = "message_ringtone",
                title = context.getString(R.string.info_message_ringtone),
                description = context.getString(R.string.info_message_ringtone_description),
                operationType = InformationOperation.MESSAGE_RINGTONE,
                permissionRequired = false,
                content = { operation, onComplete ->
                    MessageRingtoneConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 发送短信配置内容组件
 *
 * 提供发送短信的详细配置界面，包括：
 * - 电话号码输入
 * - 消息内容输入
 * - SIM卡选择
 * - 预填写模式选择
 *
 * @param operation 信息操作类型
 * @param onComplete 配置完成回调
 */
@Composable
fun SendSmsConfigContent(
    operation: InformationOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理

    // 配置状态
    var phoneNumber by rememberSaveable { mutableStateOf("") }
    var messageText by rememberSaveable { mutableStateOf("") }
    var selectedSimCard by rememberSaveable { mutableStateOf(SimCardSelection.ASK_EACH_TIME) }
    var draftOnly by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置短信发送任务",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 电话号码输入
        OutlinedTextField(
            value = phoneNumber,
            onValueChange = { phoneNumber = it },
            label = { Text("电话号码") },
            placeholder = { Text("请输入电话号码") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 消息内容输入
        OutlinedTextField(
            value = messageText,
            onValueChange = { messageText = it },
            label = { Text("消息内容") },
            placeholder = { Text("请输入要发送的消息内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 5
        )

        // SIM卡选择
        Text(
            text = "SIM卡选择",
            style = MaterialTheme.typography.labelLarge,
            color = MaterialTheme.colorScheme.onSurface
        )

        Column(
            modifier = Modifier.selectableGroup(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            SimCardSelection.values().forEach { simCard ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = selectedSimCard == simCard,
                            onClick = { selectedSimCard = simCard },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedSimCard == simCard,
                        onClick = { selectedSimCard = simCard }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (simCard) {
                            SimCardSelection.SIM1 -> "SIM卡1"
                            SimCardSelection.SIM2 -> "SIM卡2"
                            SimCardSelection.ASK_EACH_TIME -> "每次询问"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 预填写模式选择
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = draftOnly,
                    onValueChange = { draftOnly = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = draftOnly,
                onCheckedChange = { draftOnly = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "仅预填写不发送",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = InformationTask(
                    operation = operation,
                    phoneNumber = phoneNumber,
                    messageText = messageText,
                    simCardSelection = selectedSimCard,
                    draftOnly = draftOnly
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = phoneNumber.isNotBlank() && messageText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 发送邮件配置内容组件
 *
 * 提供发送邮件的详细配置界面，包括：
 * - 收件人邮箱地址
 * - 邮件主题
 * - 邮件内容
 * - SMTP服务器配置
 * - 身份验证选项
 * - 通知选项
 *
 * @param operation 信息操作类型
 * @param onComplete 配置完成回调
 */
@Composable
fun SendEmailConfigContent(
    operation: InformationOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理

    // 配置状态
    var emailRecipient by rememberSaveable { mutableStateOf("") }
    var emailSubject by rememberSaveable { mutableStateOf("") }
    var messageText by rememberSaveable { mutableStateOf("") }
    var smtpServer by rememberSaveable { mutableStateOf("") }
    var smtpPort by rememberSaveable { mutableStateOf("587") }
    var senderEmail by rememberSaveable { mutableStateOf("") }
    var senderPassword by rememberSaveable { mutableStateOf("") }
    var useSSL by rememberSaveable { mutableStateOf(true) }

    // 新增的配置状态
    var useAuthentication by rememberSaveable { mutableStateOf(true) }
    var supportStartTLS by rememberSaveable { mutableStateOf(true) }
    var notifyOnCompletion by rememberSaveable { mutableStateOf(false) }
    var notifyOnFailure by rememberSaveable { mutableStateOf(false) }
    var isHtmlEmail by rememberSaveable { mutableStateOf(false) }
    var username by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置邮件发送任务",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 收件人邮箱
        OutlinedTextField(
            value = emailRecipient,
            onValueChange = { emailRecipient = it },
            label = { Text("收件人邮箱") },
            placeholder = { Text("请输入收件人邮箱地址") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 邮件主题
        OutlinedTextField(
            value = emailSubject,
            onValueChange = { emailSubject = it },
            label = { Text("邮件主题") },
            placeholder = { Text("请输入邮件主题") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 邮件内容
        OutlinedTextField(
            value = messageText,
            onValueChange = { messageText = it },
            label = { Text("邮件内容") },
            placeholder = { Text("请输入邮件内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 5
        )

        // SMTP配置分组
        Text(
            text = "SMTP服务器配置",
            style = MaterialTheme.typography.labelLarge,
            color = MaterialTheme.colorScheme.onSurface
        )

        // SMTP服务器地址
        OutlinedTextField(
            value = smtpServer,
            onValueChange = { smtpServer = it },
            label = { Text("SMTP服务器地址") },
            placeholder = { Text("例如：smtp.gmail.com") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // SMTP端口
        OutlinedTextField(
            value = smtpPort,
            onValueChange = { smtpPort = it },
            label = { Text("SMTP端口") },
            placeholder = { Text("例如：587") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 发件人邮箱
        OutlinedTextField(
            value = senderEmail,
            onValueChange = { senderEmail = it },
            label = { Text("发件人邮箱") },
            placeholder = { Text("请输入发件人邮箱地址") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 用户名
        OutlinedTextField(
            value = username,
            onValueChange = { username = it },
            label = { Text("用户名") },
            placeholder = { Text("请输入用户名（通常与邮箱相同）") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 密码
        OutlinedTextField(
            value = senderPassword,
            onValueChange = { senderPassword = it },
            label = { Text("密码") },
            placeholder = { Text("请输入邮箱密码") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            visualTransformation = PasswordVisualTransformation()
        )

        // 身份验证选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = useAuthentication,
                    onValueChange = { useAuthentication = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useAuthentication,
                onCheckedChange = { useAuthentication = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "使用身份验证",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // StartTLS支持
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = supportStartTLS,
                    onValueChange = { supportStartTLS = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = supportStartTLS,
                onCheckedChange = { supportStartTLS = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "支持 StartTLS",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // SSL选择
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = useSSL,
                    onValueChange = { useSSL = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useSSL,
                onCheckedChange = { useSSL = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "使用SSL加密",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // HTML邮件选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = isHtmlEmail,
                    onValueChange = { isHtmlEmail = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = isHtmlEmail,
                onCheckedChange = { isHtmlEmail = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "HTML",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 通知选项分组
        Text(
            text = "通知选项",
            style = MaterialTheme.typography.labelLarge,
            color = MaterialTheme.colorScheme.onSurface
        )

        // 完成后通知
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = notifyOnCompletion,
                    onValueChange = { notifyOnCompletion = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = notifyOnCompletion,
                onCheckedChange = { notifyOnCompletion = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "完成后通知",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 通知失败
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = notifyOnFailure,
                    onValueChange = { notifyOnFailure = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = notifyOnFailure,
                onCheckedChange = { notifyOnFailure = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "通知失败",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = InformationTask(
                    operation = operation,
                    emailRecipient = emailRecipient,
                    emailSubject = emailSubject,
                    messageText = messageText,
                    smtpServer = smtpServer,
                    smtpPort = smtpPort.toIntOrNull() ?: 587,
                    senderEmail = senderEmail,
                    senderPassword = senderPassword,
                    useSSL = useSSL,
                    useAuthentication = useAuthentication,
                    supportStartTLS = supportStartTLS,
                    notifyOnCompletion = notifyOnCompletion,
                    notifyOnFailure = notifyOnFailure,
                    isHtmlEmail = isHtmlEmail,
                    username = username
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = emailRecipient.isNotBlank() &&
                     emailSubject.isNotBlank() &&
                     messageText.isNotBlank() &&
                     smtpServer.isNotBlank() &&
                     senderEmail.isNotBlank() &&
                     (!useAuthentication || (username.isNotBlank() && senderPassword.isNotBlank()))
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 信息铃声配置内容组件
 *
 * 专门用于配置短信和消息通知铃声的组件
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun MessageRingtoneConfigContent(
    operation: InformationOperation,
    onComplete: (Any) -> Unit
) {
    // 使用通用铃声配置组件，固定为通知铃声类型（短信通常使用通知铃声）
    RingtoneConfigurationContent(
        title = "配置信息铃声",
        description = "选择短信和消息通知时使用的铃声",
        ringtoneType = RingtoneHelper.RingtoneType.NOTIFICATION,
        showTypeSelection = false, // 不显示类型选择，固定为通知铃声
        onComplete = { selectedRingtoneUri, selectedRingtoneName ->
            val task = InformationTask(
                operation = operation,
                selectedRingtoneUri = selectedRingtoneUri,
                selectedRingtoneName = selectedRingtoneName
            )
            onComplete(task)
        }
    )
}
package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 通知任务存储适配器
 *
 * 负责NotificationTask的原生数据类型存储和重建。
 * 将复杂的通知任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - Toast相关：toastMessage、toastDuration
 * - 对话框相关：dialogTitle、dialogText、dialogHtmlFormat、dialogPreventBackClose、dialogRequireCompletion、dialogNotificationSound
 * - 气泡相关：bubbleTitle、bubbleText、bubbleHtmlFormat
 * - 通知相关：notificationTitle、notificationText、notificationIcon、notificationIconPath、notificationChannel、notificationChannelId、notificationPriority、notificationId、autoCancel、ongoing
 * - 清除通知相关：clearNotificationMode、clearNotificationPackage、clearNotificationId
 * - 恢复通知相关：restoreNotificationMode、restoreNotificationPackage
 * - 浮动通知相关：floatingNotificationOperation
 * - 回复通知相关：replyNotificationPackage、replyNotificationText
 * - 交互通知相关：interactionNotificationPackage、interactionNotificationAction
 * - 振动和铃声相关：enableVibration、vibrationMode、vibrationIntensity、selectedRingtoneUri、selectedRingtoneName
 *
 * 存储格式示例：
 * task_{id}_type = "notification"
 * task_{id}_operation = "SHOW_TOAST"
 * task_{id}_toast_message = "Hello World"
 * task_{id}_toast_duration = 2000
 * task_{id}_dialog_title = "Alert"
 * task_{id}_dialog_text = "This is a dialog"
 * task_{id}_dialog_html_format = false
 *
 * @param storageManager 原生类型存储管理器
 */
class NotificationTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<NotificationTask>(storageManager) {

    companion object {
        private const val TAG = "NotificationTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_TOAST_MESSAGE = "toast_message"
        private const val FIELD_TOAST_DURATION = "toast_duration"
        private const val FIELD_DIALOG_TITLE = "dialog_title"
        private const val FIELD_DIALOG_TEXT = "dialog_text"
        private const val FIELD_DIALOG_HTML_FORMAT = "dialog_html_format"
        private const val FIELD_DIALOG_PREVENT_BACK_CLOSE = "dialog_prevent_back_close"
        private const val FIELD_DIALOG_REQUIRE_COMPLETION = "dialog_require_completion"
        private const val FIELD_DIALOG_NOTIFICATION_SOUND = "dialog_notification_sound"
        private const val FIELD_BUBBLE_TITLE = "bubble_title"
        private const val FIELD_BUBBLE_TEXT = "bubble_text"
        private const val FIELD_BUBBLE_HTML_FORMAT = "bubble_html_format"
        private const val FIELD_NOTIFICATION_TITLE = "notification_title"
        private const val FIELD_NOTIFICATION_TEXT = "notification_text"
        private const val FIELD_NOTIFICATION_ICON = "notification_icon"
        private const val FIELD_NOTIFICATION_ICON_PATH = "notification_icon_path"
        private const val FIELD_NOTIFICATION_CHANNEL = "notification_channel"
        private const val FIELD_NOTIFICATION_CHANNEL_ID = "notification_channel_id"
        private const val FIELD_NOTIFICATION_PRIORITY = "notification_priority"
        private const val FIELD_NOTIFICATION_ID = "notification_id"
        private const val FIELD_AUTO_CANCEL = "auto_cancel"
        private const val FIELD_ONGOING = "ongoing"
        private const val FIELD_CLEAR_NOTIFICATION_MODE = "clear_notification_mode"
        private const val FIELD_CLEAR_NOTIFICATION_PACKAGE = "clear_notification_package"
        private const val FIELD_CLEAR_NOTIFICATION_ID = "clear_notification_id"
        private const val FIELD_RESTORE_NOTIFICATION_MODE = "restore_notification_mode"
        private const val FIELD_RESTORE_NOTIFICATION_PACKAGE = "restore_notification_package"
        private const val FIELD_FLOATING_NOTIFICATION_OPERATION = "floating_notification_operation"
        private const val FIELD_REPLY_NOTIFICATION_PACKAGE = "reply_notification_package"
        private const val FIELD_REPLY_NOTIFICATION_TEXT = "reply_notification_text"
        private const val FIELD_INTERACTION_NOTIFICATION_PACKAGE = "interaction_notification_package"
        private const val FIELD_INTERACTION_NOTIFICATION_ACTION = "interaction_notification_action"
        private const val FIELD_ENABLE_VIBRATION = "enable_vibration"
        private const val FIELD_VIBRATION_MODE = "vibration_mode"
        private const val FIELD_VIBRATION_INTENSITY = "vibration_intensity"
        private const val FIELD_SELECTED_RINGTONE_URI = "selected_ringtone_uri"
        private const val FIELD_SELECTED_RINGTONE_NAME = "selected_ringtone_name"
    }

    override fun getTaskType() = "notification"

    /**
     * 保存通知任务
     * 将NotificationTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的通知任务
     * @return 操作是否成功
     */
    override fun save(task: NotificationTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存通知任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存通知任务特有字段（第一部分）
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),

                // Toast相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TOAST_MESSAGE),
                    task.toastMessage
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TOAST_DURATION),
                    task.toastDuration
                ),

                // 对话框相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DIALOG_TITLE),
                    task.dialogTitle
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DIALOG_TEXT),
                    task.dialogText
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DIALOG_HTML_FORMAT),
                    task.dialogHtmlFormat
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DIALOG_PREVENT_BACK_CLOSE),
                    task.dialogPreventBackClose
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DIALOG_REQUIRE_COMPLETION),
                    task.dialogRequireCompletion
                ),
                saveEnum(generateKey(task.id, FIELD_DIALOG_NOTIFICATION_SOUND), task.dialogNotificationSound),

                // 气泡相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_BUBBLE_TITLE),
                    task.bubbleTitle
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_BUBBLE_TEXT),
                    task.bubbleText
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_BUBBLE_HTML_FORMAT),
                    task.bubbleHtmlFormat
                )
            ))

            // 保存通知任务特有字段（第二部分）
            operations.addAll(saveNotificationFields(task.id, task))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "通知任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            Log.e(TAG, "Exception while saving NotificationTask: ${task.id}", e)
            false
        }
    }

    /**
     * 保存通知相关字段
     */
    private fun saveNotificationFields(taskId: String, task: NotificationTask): List<StorageOperation> {
        return listOf(
            // 通知相关字段
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_TITLE),
                task.notificationTitle
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_TEXT),
                task.notificationText
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_ICON),
                task.notificationIcon
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_ICON_PATH),
                task.notificationIconPath
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_CHANNEL),
                task.notificationChannel
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_CHANNEL_ID),
                task.notificationChannelId
            ),
            StorageOperation.createIntOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_PRIORITY),
                task.notificationPriority
            ),
            StorageOperation.createIntOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_NOTIFICATION_ID),
                task.notificationId
            ),
            StorageOperation.createBooleanOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_AUTO_CANCEL),
                task.autoCancel
            ),
            StorageOperation.createBooleanOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_ONGOING),
                task.ongoing
            ),

            // 清除通知相关字段
            saveEnum(generateKey(taskId, FIELD_CLEAR_NOTIFICATION_MODE), task.clearNotificationMode),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_CLEAR_NOTIFICATION_PACKAGE),
                task.clearNotificationPackage
            ),
            StorageOperation.createIntOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_CLEAR_NOTIFICATION_ID),
                task.clearNotificationId
            ),

            // 恢复通知相关字段
            saveEnum(generateKey(taskId, FIELD_RESTORE_NOTIFICATION_MODE), task.restoreNotificationMode),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_RESTORE_NOTIFICATION_PACKAGE),
                task.restoreNotificationPackage
            ),

            // 浮动通知相关字段
            saveEnum(generateKey(taskId, FIELD_FLOATING_NOTIFICATION_OPERATION), task.floatingNotificationOperation),

            // 回复通知相关字段
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_REPLY_NOTIFICATION_PACKAGE),
                task.replyNotificationPackage
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_REPLY_NOTIFICATION_TEXT),
                task.replyNotificationText
            ),

            // 交互通知相关字段
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_INTERACTION_NOTIFICATION_PACKAGE),
                task.interactionNotificationPackage
            ),
            saveEnum(generateKey(taskId, FIELD_INTERACTION_NOTIFICATION_ACTION), task.interactionNotificationAction),

            // 振动和铃声相关字段
            StorageOperation.createBooleanOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_ENABLE_VIBRATION),
                task.enableVibration
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_VIBRATION_MODE),
                task.vibrationMode
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_VIBRATION_INTENSITY),
                task.vibrationIntensity
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_SELECTED_RINGTONE_URI),
                task.selectedRingtoneUri
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_SELECTED_RINGTONE_NAME),
                task.selectedRingtoneName
            )
        )
    }

    /**
     * 加载通知任务
     * 从原生数据类型重建NotificationTask对象
     *
     * @param taskId 任务ID
     * @return 加载的通知任务，失败时返回null
     */
    override fun load(taskId: String): NotificationTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载通知任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "通知任务不存在: $taskId")
                return null
            }

            NotificationTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { NotificationOperation.valueOf(it) }
                    ?: NotificationOperation.SHOW_TOAST,

                // Toast相关字段
                toastMessage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TOAST_MESSAGE),
                    ""
                ),
                toastDuration = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TOAST_DURATION),
                    2000
                ),

                // 对话框相关字段
                dialogTitle = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DIALOG_TITLE),
                    ""
                ),
                dialogText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DIALOG_TEXT),
                    ""
                ),
                dialogHtmlFormat = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DIALOG_HTML_FORMAT),
                    false
                ),
                dialogPreventBackClose = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DIALOG_PREVENT_BACK_CLOSE),
                    false
                ),
                dialogRequireCompletion = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DIALOG_REQUIRE_COMPLETION),
                    false
                ),
                dialogNotificationSound = loadEnum(generateKey(taskId, FIELD_DIALOG_NOTIFICATION_SOUND)) { NotificationSoundType.valueOf(it) }
                    ?: NotificationSoundType.DEFAULT,

                // 气泡相关字段
                bubbleTitle = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_BUBBLE_TITLE),
                    ""
                ),
                bubbleText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_BUBBLE_TEXT),
                    ""
                ),
                bubbleHtmlFormat = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_BUBBLE_HTML_FORMAT),
                    false
                ),

                // 通知相关字段
                notificationTitle = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_TITLE),
                    ""
                ),
                notificationText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_TEXT),
                    ""
                ),
                notificationIcon = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_ICON),
                    ""
                ),
                notificationIconPath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_ICON_PATH),
                    ""
                ),
                notificationChannel = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_CHANNEL),
                    "default"
                ),
                notificationChannelId = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_CHANNEL_ID),
                    "default"
                ),
                notificationPriority = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_PRIORITY),
                    0
                ),
                notificationId = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFICATION_ID),
                    1
                ),
                autoCancel = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_AUTO_CANCEL),
                    true
                ),
                ongoing = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ONGOING),
                    false
                ),

                // 清除通知相关字段
                clearNotificationMode = loadEnum(generateKey(taskId, FIELD_CLEAR_NOTIFICATION_MODE)) { NotificationClearMode.valueOf(it) }
                    ?: NotificationClearMode.ALL,
                clearNotificationPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLEAR_NOTIFICATION_PACKAGE),
                    ""
                ),
                clearNotificationId = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLEAR_NOTIFICATION_ID),
                    -1
                ),

                // 恢复通知相关字段
                restoreNotificationMode = loadEnum(generateKey(taskId, FIELD_RESTORE_NOTIFICATION_MODE)) { NotificationRestoreMode.valueOf(it) }
                    ?: NotificationRestoreMode.ALL,
                restoreNotificationPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RESTORE_NOTIFICATION_PACKAGE),
                    ""
                ),

                // 浮动通知相关字段
                floatingNotificationOperation = loadEnum(generateKey(taskId, FIELD_FLOATING_NOTIFICATION_OPERATION)) { SwitchOperation.valueOf(it) }
                    ?: SwitchOperation.TOGGLE,

                // 回复通知相关字段
                replyNotificationPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_REPLY_NOTIFICATION_PACKAGE),
                    ""
                ),
                replyNotificationText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_REPLY_NOTIFICATION_TEXT),
                    ""
                ),

                // 交互通知相关字段
                interactionNotificationPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_INTERACTION_NOTIFICATION_PACKAGE),
                    ""
                ),
                interactionNotificationAction = loadEnum(generateKey(taskId, FIELD_INTERACTION_NOTIFICATION_ACTION)) { NotificationActionType.valueOf(it) }
                    ?: NotificationActionType.CLICK,

                // 振动和铃声相关字段
                enableVibration = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_VIBRATION),
                    false
                ),
                vibrationMode = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VIBRATION_MODE),
                    "SINGLE"
                ),
                vibrationIntensity = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VIBRATION_INTENSITY),
                    "MEDIUM"
                ),
                selectedRingtoneUri = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_URI),
                    ""
                ),
                selectedRingtoneName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_NAME),
                    ""
                )
            ).also {
                Log.d(TAG, "通知任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            Log.e(TAG, "Exception while loading NotificationTask: $taskId", e)
            null
        }
    }
}

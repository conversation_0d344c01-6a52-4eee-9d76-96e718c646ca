<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|start"
    android:layout_margin="24dp">


    <!-- 圆形浮动按钮 -->
    <FrameLayout
        android:id="@+id/circular_reminder_button"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:background="@drawable/circular_reminder_background"
        android:clickable="true"
        android:focusable="true">

        <!-- 提醒图标 -->
        <ImageView
            android:id="@+id/iv_reminder_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_screen_rotation_24"
            android:tint="@android:color/white"
            android:contentDescription="屏幕旋转提醒" />

    </FrameLayout>

    <!-- 自动消失进度环 -->
    <ProgressBar
        android:id="@+id/progress_auto_dismiss"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="center"
        android:indeterminate="false"
        android:progressDrawable="@drawable/circular_progress_drawable"
        android:visibility="gone" />

</FrameLayout>

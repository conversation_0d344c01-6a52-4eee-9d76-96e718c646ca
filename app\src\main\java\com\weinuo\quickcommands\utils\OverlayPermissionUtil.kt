package com.weinuo.quickcommands.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.util.Log

/**
 * 悬浮窗权限工具类
 * 提供悬浮窗权限检查和引导功能
 */
object OverlayPermissionUtil {

    private const val TAG = "OverlayPermissionUtil"

    /**
     * 检查是否有悬浮窗权限
     */
    fun hasOverlayPermission(context: Context): <PERSON><PERSON><PERSON> {
        return Settings.canDrawOverlays(context)
    }

    /**
     * 申请悬浮窗权限
     */
    fun requestOverlayPermission(context: Context) {
        openOverlayPermissionSettings(context)
    }

    /**
     * 打开悬浮窗权限设置页面
     */
    fun openOverlayPermissionSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = Uri.parse("package:${context.packageName}")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening overlay permission settings", e)
        }
    }
}

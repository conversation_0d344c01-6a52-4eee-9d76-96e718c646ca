package com.weinuo.quickcommands.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 通信权限工具类
 * 提供通信相关权限的检查、申请和说明功能
 * 支持通话记录、短信、联系人等权限的统一管理
 */
object CommunicationPermissionUtil {
    
    /**
     * 通信相关权限列表
     */
    private val COMMUNICATION_PERMISSIONS = arrayOf(
        Manifest.permission.READ_CALL_LOG,
        Manifest.permission.WRITE_CALL_LOG,
        Manifest.permission.CALL_PHONE,
        Manifest.permission.MANAGE_OWN_CALLS,
        Manifest.permission.READ_SMS,
        Manifest.permission.SEND_SMS,
        Manifest.permission.READ_CONTACTS
    )
    
    /**
     * 通话记录权限列表
     */
    private val CALL_LOG_PERMISSIONS = arrayOf(
        Manifest.permission.READ_CALL_LOG,
        Manifest.permission.WRITE_CALL_LOG,
        Manifest.permission.CALL_PHONE,
        Manifest.permission.MANAGE_OWN_CALLS
    )
    
    /**
     * 短信权限列表
     */
    private val SMS_PERMISSIONS = arrayOf(
        Manifest.permission.READ_SMS,
        Manifest.permission.SEND_SMS
    )
    
    /**
     * 联系人权限列表
     */
    private val CONTACTS_PERMISSIONS = arrayOf(
        Manifest.permission.READ_CONTACTS
    )
    
    /**
     * 检查是否拥有通话记录权限
     */
    fun hasCallLogPermission(context: Context): Boolean {
        return CALL_LOG_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查是否拥有短信权限
     */
    fun hasSmsPermission(context: Context): Boolean {
        return SMS_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查是否拥有联系人权限
     */
    fun hasContactsPermission(context: Context): Boolean {
        return CONTACTS_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查是否拥有所有通信权限
     */
    fun hasAllCommunicationPermissions(context: Context): Boolean {
        return COMMUNICATION_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取缺失的权限列表
     */
    fun getMissingPermissions(context: Context): List<String> {
        return COMMUNICATION_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查拨打电话权限
     */
    fun hasCallPhonePermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.CALL_PHONE) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 检查通话记录写入权限
     */
    fun hasWriteCallLogPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_CALL_LOG) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 检查通话记录读取权限
     */
    fun hasReadCallLogPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_CALL_LOG) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 创建权限申请启动器
     * 在Activity中使用，用于申请通信权限
     */
    fun createPermissionLauncher(
        activity: ComponentActivity,
        onPermissionsResult: (Map<String, Boolean>) -> Unit
    ): ActivityResultLauncher<Array<String>> {
        return activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            onPermissionsResult(permissions)
        }
    }
    
    /**
     * 申请通信权限
     */
    fun requestCommunicationPermissions(
        launcher: ActivityResultLauncher<Array<String>>
    ) {
        launcher.launch(COMMUNICATION_PERMISSIONS)
    }
    
    /**
     * 申请特定类型的权限
     */
    fun requestCallLogPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(CALL_LOG_PERMISSIONS)
    }
    
    fun requestSmsPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(SMS_PERMISSIONS)
    }
    
    fun requestContactsPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(CONTACTS_PERMISSIONS)
    }
    
    /**
     * 权限说明对话框
     * 向用户解释为什么需要通信权限
     */
    @Composable
    fun PermissionRationaleDialog(
        onConfirm: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "通信权限说明",
            message = """
                为了使用通信状态条件功能，应用需要以下权限：

                • 通话记录权限：检测通话状态变化（拨出、接听、结束等）
                • 短信权限：检测短信发送和接收状态
                • 联系人权限：根据联系人筛选通信事件

                这些权限仅用于条件检测，不会收集或上传您的个人信息。
                您可以随时在系统设置中撤销这些权限。
            """.trimIndent(),
            confirmText = "申请权限",
            dismissText = "取消",
            onConfirm = onConfirm,
            onDismiss = onDismiss
        )
    }
    
    /**
     * 权限被拒绝后的说明对话框
     */
    @Composable
    fun PermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "权限被拒绝",
            message = """
                通信状态条件需要相关权限才能正常工作。

                如果您想使用此功能，请在系统设置中手动授予以下权限：
                • 电话权限
                • 短信权限
                • 联系人权限

                您可以点击"打开设置"直接跳转到应用权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }
    
    /**
     * 获取权限的友好名称
     */
    fun getPermissionDisplayName(permission: String): String {
        return when (permission) {
            Manifest.permission.READ_CALL_LOG -> "通话记录读取"
            Manifest.permission.WRITE_CALL_LOG -> "通话记录写入"
            Manifest.permission.CALL_PHONE -> "拨打电话"
            Manifest.permission.MANAGE_OWN_CALLS -> "管理通话"
            Manifest.permission.READ_SMS -> "短信读取"
            Manifest.permission.SEND_SMS -> "短信发送"
            Manifest.permission.READ_CONTACTS -> "联系人读取"
            else -> permission
        }
    }

    /**
     * 获取所需的通信权限列表
     * @return 通信权限数组
     */
    fun getRequiredCommunicationPermissions(): Array<String> {
        return COMMUNICATION_PERMISSIONS
    }
}

# 后台时间条件事件数据传递机制说明

## 概述

本次更新为后台时间条件添加了事件数据传递机制，让强制停止触发应用功能能够准确获取到触发条件的应用信息。

## 设计原则

**精准定向**：只有后台时间条件需要传递触发应用信息，其他条件不需要此功能，因此采用最小化改动的方案。

## 主要改进

### 1. SharedExecutionHandler 最小化改进

#### 方法签名更新
- `executeQuickCommand()` - 新增 `triggerEventData` 参数（可选）
- `executeTasks()` - 新增 `triggerEventData` 参数（可选）
- `executeTaskInternal()` - 新增 `eventData` 参数（可选）
- `executeApplicationTask()` - 新增 `eventData` 参数（可选）
- `executeTask()` - 新增 `eventData` 参数（可选，公共方法）

#### 事件数据传递链路
```
后台时间条件监控器 → BackgroundManagerService → SharedExecutionHandler → ApplicationTask → ForceStopTriggerApp
```

### 2. BackgroundManagerService 精准改进

#### 条件处理方法
- 只有 `handleAppStateConditionTriggered()` 中的后台时间条件会传递事件数据
- 其他条件处理方法保持原样，不传递事件数据

#### 核心方法改进
- `handleTriggerCondition()` - 新增可选的 `eventData` 参数
- 只有后台时间条件调用时才传递事件数据，其他条件传递 null

### 3. 强制停止触发应用功能完善

#### executeForceStopTriggerApp 方法改进
- **事件数据优先**：优先从事件数据中获取 `triggerApp` 信息
- **回退机制**：当事件数据为空时，回退到简化实现（查找最近后台应用）
- **详细日志**：记录数据来源（"事件数据" 或 "回退检测"）

#### executeForceStopSingleTriggerApp 新方法
- 统一的触发应用强制停止逻辑
- 完整的例外处理（前台、音乐、VPN应用）
- 详细的执行日志和错误处理

## 事件数据格式

### 后台时间条件事件数据
```kotlin
mapOf(
    "triggerApp" to SimpleAppInfo(...),        // 主要触发应用
    "triggeredApps" to List<SimpleAppInfo>,    // 所有触发的应用
    "allSelectedApps" to List<SimpleAppInfo>,  // 所有选择的应用
    "backgroundTimeThresholdMinutes" to Int,   // 时间阈值
    "triggerMode" to String,                   // 触发模式
    "timestamp" to Long                        // 时间戳
)
```

### 其他条件
其他条件不需要事件数据传递，保持原有的简单触发机制。

## 功能验证

### 测试场景
1. **正常事件数据传递**
   - 配置后台时间条件
   - 配置强制停止触发应用任务
   - 验证能正确获取触发应用并停止

2. **回退机制验证**
   - 手动触发快捷指令（无事件数据）
   - 验证回退到最近后台应用检测

3. **例外处理验证**
   - 验证跳过前台应用
   - 验证跳过音乐播放应用
   - 验证跳过VPN应用

## 实现特点

### 最小化影响
- 只有后台时间条件相关的代码路径会传递事件数据
- 其他条件保持原有的简单实现
- 避免了不必要的复杂性

## 总结

针对后台时间条件的事件数据传递机制使得：

1. **强制停止触发应用功能更加精确**
   - 能准确获取后台时间超时的应用
   - 避免误停其他应用

2. **架构保持简洁**
   - 只在需要的地方添加事件数据传递
   - 不影响其他条件的简单性

3. **用户体验更好**
   - 后台时间条件 + 强制停止触发应用的组合功能符合预期
   - 详细的日志便于问题排查

4. **完全替代主功能**
   - 快捷指令中的后台时间条件功能现在可以完全替代应用的主要功能
   - 用户可以自主选择使用方式，获得更高的自由度

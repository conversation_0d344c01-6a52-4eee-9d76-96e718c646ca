package com.weinuo.quickcommands.ui.recording

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.weinuo.quickcommands.model.GestureRecording
import com.weinuo.quickcommands.model.TouchEvent
import com.weinuo.quickcommands.storage.GestureRecordingNativeManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * 手势录制编辑ViewModel
 *
 * 管理手势录制编辑界面的状态和业务逻辑，支持：
 * - 加载和保存录制数据
 * - 编辑操作（调整延迟、删除事件、移动事件等）
 * - 撤销/重做功能
 * - 预览功能
 */
class GestureRecordingEditViewModel(
    private val context: Context,
    private val recordingId: String
) : ViewModel() {

    companion object {
        private const val TAG = "GestureRecordingEditVM"
    }

    private val gestureRecordingManager = GestureRecordingNativeManager(context)

    // SharedPreferences监听器，用于监听继续录制完成事件
    private val sharedPrefs: SharedPreferences = context.getSharedPreferences("recording_update_events", Context.MODE_PRIVATE)
    private val sharedPrefsListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
        if (key == "recording_updated_$recordingId") {
            Log.d(TAG, "检测到录制数据更新事件，重新加载数据")
            // 只有在没有未保存的修改时才重新加载数据
            if (!_isModified.value) {
                loadRecording()
            } else {
                Log.d(TAG, "存在未保存的修改，跳过重新加载")
            }
        }
    }

    // 原始录制数据
    private val _originalRecording = MutableStateFlow<GestureRecording?>(null)
    val originalRecording: StateFlow<GestureRecording?> = _originalRecording.asStateFlow()

    // 当前编辑中的录制数据
    private val _currentRecording = MutableStateFlow<GestureRecording?>(null)
    val currentRecording: StateFlow<GestureRecording?> = _currentRecording.asStateFlow()

    // 是否已修改
    private val _isModified = MutableStateFlow(false)
    val isModified: StateFlow<Boolean> = _isModified.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 保存状态
    private val _isSaving = MutableStateFlow(false)
    val isSaving: StateFlow<Boolean> = _isSaving.asStateFlow()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    init {
        loadRecording()
        // 注册SharedPreferences监听器
        sharedPrefs.registerOnSharedPreferenceChangeListener(sharedPrefsListener)
    }

    override fun onCleared() {
        super.onCleared()
        // 取消注册SharedPreferences监听器
        sharedPrefs.unregisterOnSharedPreferenceChangeListener(sharedPrefsListener)
    }

    /**
     * 加载录制数据
     */
    private fun loadRecording() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null

                val recording = gestureRecordingManager.loadRecording(recordingId)
                if (recording != null) {
                    _originalRecording.value = recording
                    _currentRecording.value = recording.copy() // 创建副本用于编辑
                    Log.d(TAG, "录制数据加载成功: ${recording.name}, 事件数量: ${recording.events.size}")
                } else {
                    _errorMessage.value = "无法加载录制数据"
                    Log.e(TAG, "录制数据加载失败: $recordingId")
                }
            } catch (e: Exception) {
                _errorMessage.value = "加载录制数据时发生错误: ${e.message}"
                Log.e(TAG, "加载录制数据异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 更新事件
     */
    fun updateEvent(eventIndex: Int, updatedEvent: TouchEvent) {
        val currentRecording = _currentRecording.value ?: return
        val events = currentRecording.events.toMutableList()

        if (eventIndex !in 0 until events.size) return

        val oldEvent = events[eventIndex]
        events[eventIndex] = updatedEvent
        _currentRecording.value = currentRecording.copy(events = events)
        _isModified.value = true

        Log.d(TAG, "更新事件 $eventIndex: ${updatedEvent.getDisplayName()}")
        Log.d(TAG, "延迟时间变化: ${oldEvent.delayAfter}ms -> ${updatedEvent.delayAfter}ms")
        Log.d(TAG, "延迟描述变化: ${oldEvent.getDelayDescription()} -> ${updatedEvent.getDelayDescription()}")
    }

    /**
     * 删除事件
     */
    fun deleteEvent(eventIndex: Int) {
        val currentRecording = _currentRecording.value ?: return
        val events = currentRecording.events.toMutableList()

        if (eventIndex !in 0 until events.size) return

        events.removeAt(eventIndex)
        _currentRecording.value = currentRecording.copy(events = events)
        _isModified.value = true

        Log.d(TAG, "删除事件: $eventIndex")
    }

    /**
     * 复制事件
     */
    fun duplicateEvent(eventIndex: Int) {
        val currentRecording = _currentRecording.value ?: return
        val events = currentRecording.events.toMutableList()

        if (eventIndex !in 0 until events.size) return

        // 复制事件并生成新的ID
        val originalEvent = events[eventIndex]
        val duplicatedEvent = originalEvent.copy(
            id = java.util.UUID.randomUUID().toString(),
            description = if (originalEvent.description.isNotEmpty()) {
                "${originalEvent.description} (副本)"
            } else {
                "${originalEvent.getDisplayName()} (副本)"
            }
        )

        // 在原事件后面插入复制的事件
        events.add(eventIndex + 1, duplicatedEvent)
        _currentRecording.value = currentRecording.copy(events = events)
        _isModified.value = true

        Log.d(TAG, "复制事件 $eventIndex: ${originalEvent.getDisplayName()}")
    }



    /**
     * 保存编辑后的录制数据
     */
    fun saveRecording(): Boolean {
        val currentRecording = _currentRecording.value ?: return false

        viewModelScope.launch {
            try {
                _isSaving.value = true
                _errorMessage.value = null

                val success = gestureRecordingManager.saveRecording(currentRecording, recordingId) != null
                if (success) {
                    _isModified.value = false
                    Log.d(TAG, "录制数据保存成功")
                } else {
                    _errorMessage.value = "保存录制数据失败"
                    Log.e(TAG, "录制数据保存失败")
                }
            } catch (e: Exception) {
                _errorMessage.value = "保存录制数据时发生错误: ${e.message}"
                Log.e(TAG, "保存录制数据异常", e)
            } finally {
                _isSaving.value = false
            }
        }

        return true
    }



    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
}

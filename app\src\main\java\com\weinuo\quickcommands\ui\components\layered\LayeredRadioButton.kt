package com.weinuo.quickcommands.ui.components.layered

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.weinuo.quickcommands.ui.theme.config.RadioButtonConfig

/**
 * 分层设计风格的单选按钮
 *
 * 特点：
 * - 使用标准Material 3 RadioButton设计
 * - 简洁清晰的视觉分离
 * - 遵循Material Design 3规范
 * - 保持与old项目一致的简洁风格
 * - 确保海洋蓝主题的原有RadioButton样式不变
 */
@Composable
fun LayeredRadioButton(
    config: RadioButtonConfig,
    modifier: Modifier = Modifier
) {
    RadioButton(
        selected = config.selected,
        onClick = config.onClick,
        modifier = modifier,
        enabled = config.enabled,
        colors = config.colors as? RadioButtonColors ?: RadioButtonDefaults.colors()
    )
}

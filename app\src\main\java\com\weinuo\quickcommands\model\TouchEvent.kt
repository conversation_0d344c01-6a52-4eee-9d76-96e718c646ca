package com.weinuo.quickcommands.model

import android.graphics.PointF
import java.util.UUID

/**
 * 触摸事件数据类
 *
 * 用于记录和回放用户的触摸操作，支持多种触摸类型。
 * 采用相对坐标系统，便于在不同屏幕尺寸间适配。
 *
 * @property id 事件唯一标识符
 * @property type 触摸事件类型
 * @property position 触摸位置信息
 * @property duration 动作本身持续时间（毫秒，用于长按等操作）
 * @property delayAfter 完成后延迟时间（毫秒，这个动作完成后等待多久执行下一个动作）
 * @property description 事件描述（可选，用于用户标记）
 */
data class TouchEvent(
    val id: String = UUID.randomUUID().toString(),
    val type: TouchEventType,
    val position: TouchPosition,
    val duration: Long = 0L,
    val delayAfter: Long = 0L,
    val description: String = ""
) {
    /**
     * 获取事件的显示名称
     */
    fun getDisplayName(): String {
        return when (type) {
            TouchEventType.TAP -> "点击"
            TouchEventType.LONG_PRESS -> "长按"
            TouchEventType.SWIPE -> "滑动"
            TouchEventType.MULTI_TAP -> "多点触控"
        }
    }



    /**
     * 获取延迟描述
     */
    fun getDelayDescription(): String {
        return if (delayAfter > 0) {
            "完成后延迟${delayAfter / 1000.0}秒"
        } else {
            "无延迟"
        }
    }

    /**
     * 转换为绝对坐标
     *
     * @param screenWidth 目标屏幕宽度
     * @param screenHeight 目标屏幕高度
     * @return 转换后的触摸事件
     */
    fun toAbsoluteCoordinates(screenWidth: Int, screenHeight: Int): TouchEvent {
        return copy(position = position.toAbsolute(screenWidth, screenHeight))
    }

    /**
     * 转换为相对坐标
     *
     * @param screenWidth 当前屏幕宽度
     * @param screenHeight 当前屏幕高度
     * @return 转换后的触摸事件
     */
    fun toRelativeCoordinates(screenWidth: Int, screenHeight: Int): TouchEvent {
        return copy(position = position.toRelative(screenWidth, screenHeight))
    }
}

/**
 * 触摸位置数据类
 *
 * 表示触摸操作的位置信息，支持点击和滑动等操作。
 * 坐标使用相对坐标系统（0.0-1.0），便于跨设备适配。
 *
 * @property startX 起始X坐标（相对坐标，0.0-1.0）
 * @property startY 起始Y坐标（相对坐标，0.0-1.0）
 * @property endX 结束X坐标（滑动操作用，默认与起始坐标相同）
 * @property endY 结束Y坐标（滑动操作用，默认与起始坐标相同）
 * @property touchPoints 触摸点数量（多点触控用）
 */
data class TouchPosition(
    val startX: Float,
    val startY: Float,
    val endX: Float = startX,
    val endY: Float = startY,
    val touchPoints: Int = 1
) {
    /**
     * 检查坐标是否有效（在0.0-1.0范围内）
     */
    fun isValid(): Boolean {
        return startX in 0.0f..1.0f && startY in 0.0f..1.0f &&
               endX in 0.0f..1.0f && endY in 0.0f..1.0f
    }

    /**
     * 获取起始点的PointF对象
     */
    fun getStartPointF(): PointF = PointF(startX, startY)

    /**
     * 获取结束点的PointF对象
     */
    fun getEndPointF(): PointF = PointF(endX, endY)

    /**
     * 转换为绝对坐标
     */
    fun toAbsolute(screenWidth: Int, screenHeight: Int): TouchPosition {
        return copy(
            startX = startX * screenWidth,
            startY = startY * screenHeight,
            endX = endX * screenWidth,
            endY = endY * screenHeight
        )
    }

    /**
     * 转换为相对坐标
     */
    fun toRelative(screenWidth: Int, screenHeight: Int): TouchPosition {
        return copy(
            startX = startX / screenWidth,
            startY = startY / screenHeight,
            endX = endX / screenWidth,
            endY = endY / screenHeight
        )
    }
}

/**
 * 触摸事件类型枚举
 */
enum class TouchEventType(val displayName: String) {
    TAP("点击"),              // 单击
    LONG_PRESS("长按"),       // 长按
    SWIPE("滑动"),           // 滑动
    MULTI_TAP("多点触控")     // 多点触控
}



/**
 * 手势录制数据类
 *
 * 包含完整的手势录制信息，包括元数据和事件序列。
 *
 * 注意：已移除序列化注解，改用原生存储方式
 *
 * @property name 录制名称
 * @property description 录制描述
 * @property createdTime 创建时间戳
 * @property duration 总持续时间（毫秒）
 * @property screenWidth 录制时的屏幕宽度
 * @property screenHeight 录制时的屏幕高度
 * @property events 触摸事件列表
 * @property version 录制格式版本
 */
data class GestureRecording(
    val name: String,
    val description: String = "",
    val createdTime: Long = System.currentTimeMillis(),
    val duration: Long = 0L,
    val screenWidth: Int = 0,
    val screenHeight: Int = 0,
    val events: List<TouchEvent> = emptyList(),
    val version: Int = 1
) {
    /**
     * 获取事件数量
     */
    fun getEventCount(): Int = events.size

    /**
     * 获取有效事件数量
     */
    fun getValidEventCount(): Int = events.size

    /**
     * 检查录制是否为空
     */
    fun isEmpty(): Boolean = events.isEmpty()
}

/**
 * TouchEvent 扩展方法
 */

/**
 * 获取动作显示名称
 */
fun TouchEvent.getDisplayName(): String = when (type) {
    TouchEventType.TAP -> "点击"
    TouchEventType.LONG_PRESS -> "长按"
    TouchEventType.SWIPE -> "滑动"
    TouchEventType.MULTI_TAP -> "多点触控"
}

/**
 * 获取详细描述（使用像素坐标）
 */
fun TouchEvent.getDetailedDescription(screenWidth: Int = 1080, screenHeight: Int = 1920): String {
    val baseDesc = getDisplayName()
    val positionDesc = when (type) {
        TouchEventType.SWIPE -> {
            "从 (${(position.startX * screenWidth).toInt()}px, ${(position.startY * screenHeight).toInt()}px) " +
            "到 (${(position.endX * screenWidth).toInt()}px, ${(position.endY * screenHeight).toInt()}px)"
        }
        else -> "位置 (${(position.startX * screenWidth).toInt()}px, ${(position.startY * screenHeight).toInt()}px)"
    }

    return "$baseDesc - $positionDesc"
}

/**
 * 获取延迟描述
 */
fun TouchEvent.getDelayDescription(): String {
    return if (delayAfter > 0) {
        "延迟 ${delayAfter / 1000.0}秒"
    } else {
        "无延迟"
    }


}

/**
 * 录制配置数据类
 *
 * 用于配置录制和回放的参数。
 *
 * @property recordingName 录制名称
 * @property recordingDescription 录制描述
 * @property enablePressureRecording 是否录制压力信息
 * @property enableSizeRecording 是否录制触摸区域大小
 * @property minimumEventInterval 最小事件间隔（毫秒）
 * @property maxRecordingDuration 最大录制时长（毫秒，0表示无限制）
 * @property autoSave 是否自动保存
 * @property savePath 保存路径
 */
data class RecordingConfig(
    val recordingName: String = "",
    val recordingDescription: String = "",
    val enablePressureRecording: Boolean = false,
    val enableSizeRecording: Boolean = false,
    val minimumEventInterval: Long = 10L,
    val maxRecordingDuration: Long = 0L,
    val autoSave: Boolean = true,
    val savePath: String = ""
) {
    /**
     * 验证配置是否有效
     */
    fun isValid(): Boolean {
        return recordingName.isNotBlank() &&
               minimumEventInterval > 0 &&
               (maxRecordingDuration == 0L || maxRecordingDuration > 0)
    }
}

/**
 * 回放配置数据类
 *
 * 用于配置回放的参数。
 *
 * @property loopCount 循环次数（0表示无限循环）
 * @property playbackSpeed 回放速度倍率（1.0为正常速度）
 * @property delayBetweenLoops 循环间延时（毫秒）
 * @property adaptToScreenSize 是否适配屏幕尺寸
 * @property stopOnError 遇到错误时是否停止
 */
data class PlaybackConfig(
    val loopCount: Int = 1,
    val playbackSpeed: Float = 1.0f,
    val delayBetweenLoops: Long = 1000L,
    val adaptToScreenSize: Boolean = true,
    val stopOnError: Boolean = true
)

package com.weinuo.quickcommands.utils

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.data.SettingsRepository
import kotlinx.coroutines.flow.StateFlow

/**
 * 应用语言管理器
 *
 * 整合LanguageManager和SettingsRepository，提供统一的语言管理接口
 *
 * 设计原则：
 * 1. 统一接口：提供一致的语言设置和获取方法
 * 2. 性能优化：利用LanguageManager的缓存机制
 * 3. 数据同步：确保LanguageManager和SettingsRepository的数据一致性
 * 4. 零性能损耗：系统默认语言时不进行额外操作
 *
 * 使用场景：
 * - 全局设置界面的语言设置
 * - 应用启动时的语言初始化
 * - 运行时的语言切换
 */
class AppLanguageManager(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {
    companion object {
        private const val TAG = "AppLanguageManager"

        // 支持的语言选项
        val SUPPORTED_LANGUAGES = listOf(
            LanguageManager.LANGUAGE_SYSTEM,
            LanguageManager.LANGUAGE_CHINESE,
            LanguageManager.LANGUAGE_ENGLISH
        )

        // 语言显示名称映射
        val LANGUAGE_DISPLAY_NAMES = mapOf(
            LanguageManager.LANGUAGE_SYSTEM to "系统默认",
            LanguageManager.LANGUAGE_CHINESE to "中文",
            LanguageManager.LANGUAGE_ENGLISH to "English"
        )
    }

    /**
     * 获取当前语言设置
     *
     * 优先从LanguageManager缓存获取，确保性能最优
     *
     * @return 当前语言代码
     */
    fun getCurrentLanguage(): String {
        return LanguageManager.getCurrentLanguage(context)
    }

    /**
     * 设置应用语言
     *
     * 同时更新LanguageManager缓存和SettingsRepository持久化存储
     *
     * @param language 语言代码
     * @return 是否设置成功
     */
    fun setLanguage(language: String): Boolean {
        try {
            // 验证语言代码
            if (language !in SUPPORTED_LANGUAGES) {
                Log.w(TAG, "Unsupported language: $language")
                return false
            }

            // 检查是否需要更新
            val currentLanguage = getCurrentLanguage()
            if (currentLanguage == language) {
                Log.d(TAG, "Language already set to: $language")
                return true
            }

            // 更新LanguageManager（立即生效）
            val languageManagerSuccess = LanguageManager.setLanguage(context, language)
            if (!languageManagerSuccess) {
                Log.e(TAG, "Failed to update LanguageManager")
                return false
            }

            // 更新SettingsRepository（持久化存储）
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(appLanguage = language)
            settingsRepository.saveGlobalSettings(newSettings)

            // 清除LocaleHelper缓存，确保新语言立即生效
            LocaleHelper.clearCache()

            Log.i(TAG, "Language successfully changed from '$currentLanguage' to '$language'")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "Error setting language to: $language", e)
            return false
        }
    }

    /**
     * 获取语言显示名称
     *
     * @param language 语言代码
     * @return 显示名称
     */
    fun getLanguageDisplayName(language: String): String {
        return LANGUAGE_DISPLAY_NAMES[language] ?: language
    }

    /**
     * 获取所有支持的语言选项
     *
     * @return 语言代码列表
     */
    fun getSupportedLanguages(): List<String> {
        return SUPPORTED_LANGUAGES
    }

    /**
     * 检查是否需要应用语言设置
     *
     * @return 是否需要自定义语言设置
     */
    fun needsLanguageOverride(): Boolean {
        return LanguageManager.needsLanguageOverride(context)
    }

    /**
     * 初始化语言设置
     *
     * 在应用启动时调用，确保LanguageManager和SettingsRepository数据同步
     */
    fun initializeLanguage() {
        try {
            val settingsLanguage = settingsRepository.globalSettings.value.appLanguage
            val managerLanguage = LanguageManager.getCurrentLanguage(context)

            // 如果数据不一致，以SettingsRepository为准
            if (settingsLanguage != managerLanguage) {
                Log.d(TAG, "Language mismatch detected, syncing: $managerLanguage -> $settingsLanguage")
                LanguageManager.setLanguage(context, settingsLanguage)
            }

            Log.d(TAG, "Language initialized: $settingsLanguage")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing language", e)
        }
    }

    /**
     * 获取全局设置的StateFlow
     *
     * 用于UI组件监听语言设置变化
     */
    fun getGlobalSettingsFlow(): StateFlow<com.weinuo.quickcommands.model.GlobalSettings> {
        return settingsRepository.globalSettings
    }

    /**
     * 清除所有语言相关缓存
     *
     * 在语言设置发生变化时调用，确保新设置立即生效
     */
    fun clearAllCaches() {
        LanguageManager.clearCache()
        LocaleHelper.clearCache()
        Log.d(TAG, "All language caches cleared")
    }
}

/**
 * Activity扩展函数：应用语言设置到Activity
 *
 * 性能优化：
 * 1. 系统默认语言时直接返回原Activity，零性能损耗
 * 2. 只在需要时才进行Context包装
 * 3. 使用缓存机制避免重复操作
 *
 * 使用方法：
 * ```kotlin
 * override fun attachBaseContext(newBase: Context) {
 *     super.attachBaseContext(newBase.applyAppLanguage())
 * }
 * ```
 */
fun Context.applyAppLanguage(): Context {
    return if (LocaleHelper.needsContextWrapping(this)) {
        LocaleHelper.wrapContext(this)
    } else {
        // 系统默认语言时直接返回，零性能损耗
        this
    }
}

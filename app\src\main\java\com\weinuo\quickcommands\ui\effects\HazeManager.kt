package com.weinuo.quickcommands.ui.effects

import android.content.Context
import android.os.Build
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import dev.chrisbanes.haze.HazeState
import com.weinuo.quickcommands.model.QuickCommand

/**
 * Haze模糊效果管理器
 *
 * 管理全局的HazeState，提供iOS风格的背景模糊效果
 * 支持所有Android版本，包括Android 11及以下的实验性RenderScript模糊
 *
 * 工作原理：
 * 1. hazeSource: 标记需要被模糊的内容源（如主要内容区域）
 * 2. hazeEffect: 在指定位置应用模糊效果，模糊其下方的hazeSource内容
 * 3. HazeState: 连接source和effect的状态管理器
 * 4. 实时更新: 当source内容变化时，effect会自动更新
 *
 * Android版本支持：
 * - Android 14+ (API 33+): 完全支持，最佳性能
 * - Android 12-13 (API 31-32): 支持，使用手动失效机制
 * - Android 12及以下 (API ≤31): 支持，使用手动失效机制
 * - Android 11及以下 (API ≤30): 支持实验性RenderScript模糊
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
class HazeManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: HazeManager? = null

        /**
         * 获取HazeManager单例实例
         *
         * @param context 应用上下文
         * @return HazeManager实例
         */
        fun getInstance(context: Context): HazeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: HazeManager(context.applicationContext).also {
                    INSTANCE = it
                    Log.d("HazeManager", "HazeManager实例已创建，Android API: ${Build.VERSION.SDK_INT}")
                }
            }
        }
    }

    /**
     * 全局的HazeState，强制启用所有Android版本的模糊效果
     *
     * 用于连接所有的hazeSource和hazeEffect
     * 确保模糊效果在整个应用中保持一致
     *
     * 注意：Android 11及以下版本使用实验性RenderScript模糊，强制启用
     */
    val globalHazeState = HazeState(initialBlurEnabled = true).also {
        Log.d("HazeManager", "创建全局HazeState，强制启用模糊效果，Android API: ${Build.VERSION.SDK_INT}")
        Log.d("HazeManager", "模糊实现类型: ${getBlurImplementationType()}")
        if (needsForceBlurEnabled()) {
            Log.w("HazeManager", "Android ${Build.VERSION.SDK_INT} 使用实验性RenderScript模糊，可能存在性能影响")
        }
    }

    /**
     * 检查是否支持模糊效果
     *
     * 现在所有Android版本都支持模糊效果：
     * - Android 12+: 使用RenderEffect
     * - Android 11及以下: 使用实验性RenderScript模糊
     *
     * @return 始终返回true，因为所有Android版本都支持模糊
     */
    fun isBlurSupported(): Boolean {
        return true
    }

    /**
     * 检查模糊效果是否默认启用
     * Android 12+默认启用，Android 11及以下需要手动启用
     */
    private fun isBlurSupportedByDefault(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S // Android 12+
    }

    /**
     * 检查是否需要强制启用模糊效果
     * Android 11及以下版本需要在hazeEffect中设置blurEnabled = true
     */
    fun needsForceBlurEnabled(): Boolean {
        return Build.VERSION.SDK_INT <= Build.VERSION_CODES.R // Android 11及以下
    }

    /**
     * 获取当前Android版本的模糊实现类型
     *
     * @return 模糊实现类型描述
     */
    fun getBlurImplementationType(): String {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> "RenderEffect (Android 13+)"
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> "RenderEffect with workarounds (Android 12)"
            else -> "Experimental RenderScript (Android 11 and below)"
        }
    }

    /**
     * 全局上下文菜单状态管理
     *
     * 用于在IntegratedMainLayout层级管理上下文菜单的显示状态
     * 确保模糊遮罩层能够正确覆盖所有组件，包括底部导航栏
     */
    var showContextMenu by mutableStateOf(false)
        private set

    var contextMenuCommand by mutableStateOf<QuickCommand?>(null)
        private set

    /**
     * 显示上下文菜单
     *
     * @param command 要显示菜单的快捷指令
     */
    fun showContextMenu(command: QuickCommand) {
        contextMenuCommand = command
        showContextMenu = true
        Log.d("HazeManager", "显示上下文菜单: ${command.name}")
    }

    /**
     * 隐藏上下文菜单
     */
    fun hideContextMenu() {
        showContextMenu = false
        contextMenuCommand = null
        Log.d("HazeManager", "隐藏上下文菜单")
    }

    /**
     * 释放资源
     *
     * 在应用销毁时调用，清理相关资源
     */
    fun release() {
        Log.d("HazeManager", "HazeManager资源已释放")
        // HazeState会自动管理其生命周期，无需手动释放
        // 清理上下文菜单状态
        hideContextMenu()
    }
}

/**
 * Composable函数，用于获取HazeManager实例
 * 
 * @param context 上下文
 * @return HazeManager实例
 */
@Composable
fun rememberHazeManager(context: Context): HazeManager {
    return remember { HazeManager.getInstance(context) }
}

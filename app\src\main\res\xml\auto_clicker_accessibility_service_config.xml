<?xml version="1.0" encoding="utf-8"?>
<!--
快捷指令-自动点击器服务配置
用于自动点击器功能，包括录制用户触摸操作和回放操作序列
采用按需激活设计以最小化资源消耗
支持全局手势执行，包括桌面和所有应用
-->
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/auto_clicker_accessibility_service_description"
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFlags="flagDefault|flagRetrieveInteractiveWindows"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:notificationTimeout="0"
    android:canRetrieveWindowContent="true"
    android:canRequestTouchExplorationMode="true"
    android:canRequestEnhancedWebAccessibility="false"
    android:canRequestFilterKeyEvents="false"
    android:canPerformGestures="true"
    android:settingsActivity="com.weinuo.quickcommands.MainActivity" />

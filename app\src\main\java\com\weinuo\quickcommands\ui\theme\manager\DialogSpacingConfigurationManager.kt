package com.weinuo.quickcommands.ui.theme.manager

import android.content.Context
import androidx.compose.runtime.*
import com.weinuo.quickcommands.data.SettingsRepository

/**
 * 对话框间距配置管理器
 * 
 * 负责管理对话框中各种UI元素的间距配置，包括：
 * - 对话框外边距
 * - 图标、标题、内容、按钮之间的间距
 * - 标题字体大小
 */
class DialogSpacingConfigurationManager private constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {
    
    /**
     * 对话框间距配置数据类
     */
    data class DialogSpacingConfiguration(
        val outerPadding: Int = 12,                 // 对话框外边距（dp）
        val iconBottomPadding: Int = 16,            // 图标下方间距（dp）
        val titleBottomPadding: Int = 16,           // 标题下方间距（dp）
        val contentBottomPadding: Int = 4,          // 内容下方间距（dp）
        val contentVerticalPadding: Int = 8,        // 内容区域垂直间距（dp）
        val inputBottomPadding: Int = 8,            // 输入框下方间距（dp）
        val buttonTopPadding: Int = 2,              // 按钮上方间距（dp）
        val buttonBottomPadding: Int = 14,          // 按钮下方间距（dp）
        val titleFontSize: Int = 17,                // 对话框标题字体大小（sp）
        val dividerHorizontalPadding: Int = 12      // 对话框分割线水平间距（dp）
    )
    
    // 当前配置状态
    private var _currentConfiguration by mutableStateOf(loadConfiguration())

    /**
     * 获取当前对话框间距配置
     */
    fun getDialogSpacingConfiguration(): DialogSpacingConfiguration {
        // 每次获取时重新加载配置，确保获取最新值
        _currentConfiguration = loadConfiguration()
        return _currentConfiguration
    }
    
    /**
     * 从设置中加载配置
     */
    private fun loadConfiguration(): DialogSpacingConfiguration {
        val settings = settingsRepository.globalSettings.value
        return DialogSpacingConfiguration(
            outerPadding = settings.dialogOuterPadding,
            iconBottomPadding = settings.dialogIconBottomPadding,
            titleBottomPadding = settings.dialogTitleBottomPadding,
            contentBottomPadding = settings.dialogContentBottomPadding,
            contentVerticalPadding = settings.dialogContentVerticalPadding,
            inputBottomPadding = settings.dialogInputBottomPadding,
            buttonTopPadding = settings.dialogButtonTopPadding,
            buttonBottomPadding = settings.dialogButtonBottomPadding,
            titleFontSize = settings.dialogTitleFontSize,
            dividerHorizontalPadding = settings.dialogDividerHorizontalPadding
        )
    }

    /**
     * 保存配置到设置
     */
    private fun saveConfiguration() {
        val currentSettings = settingsRepository.globalSettings.value
        val updatedSettings = currentSettings.copy(
            dialogOuterPadding = _currentConfiguration.outerPadding,
            dialogIconBottomPadding = _currentConfiguration.iconBottomPadding,
            dialogTitleBottomPadding = _currentConfiguration.titleBottomPadding,
            dialogContentBottomPadding = _currentConfiguration.contentBottomPadding,
            dialogContentVerticalPadding = _currentConfiguration.contentVerticalPadding,
            dialogInputBottomPadding = _currentConfiguration.inputBottomPadding,
            dialogButtonTopPadding = _currentConfiguration.buttonTopPadding,
            dialogButtonBottomPadding = _currentConfiguration.buttonBottomPadding,
            dialogTitleFontSize = _currentConfiguration.titleFontSize,
            dialogDividerHorizontalPadding = _currentConfiguration.dividerHorizontalPadding
        )
        settingsRepository.saveGlobalSettings(updatedSettings)
    }

    /**
     * 更新对话框外边距
     */
    fun updateOuterPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(outerPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新图标下方间距
     */
    fun updateIconBottomPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(iconBottomPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新标题下方间距
     */
    fun updateTitleBottomPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(titleBottomPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新内容下方间距
     */
    fun updateContentBottomPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(contentBottomPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新内容区域垂直间距
     */
    fun updateContentVerticalPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(contentVerticalPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新输入框下方间距
     */
    fun updateInputBottomPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(inputBottomPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新按钮上方间距
     */
    fun updateButtonTopPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(buttonTopPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新按钮下方间距
     */
    fun updateButtonBottomPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(buttonBottomPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新标题字体大小
     */
    fun updateTitleFontSize(fontSize: Int) {
        _currentConfiguration = _currentConfiguration.copy(titleFontSize = fontSize)
        saveConfiguration()
    }

    /**
     * 更新分割线水平间距
     */
    fun updateDividerHorizontalPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(dividerHorizontalPadding = padding)
        saveConfiguration()
    }

    /**
     * 重置为默认配置
     */
    fun resetToDefaults() {
        _currentConfiguration = DialogSpacingConfiguration()
        saveConfiguration()
    }

    /**
     * 刷新配置（从设置重新加载）
     */
    fun refreshConfiguration() {
        _currentConfiguration = loadConfiguration()
    }

    companion object {
        @Volatile
        private var INSTANCE: DialogSpacingConfigurationManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context, settingsRepository: SettingsRepository): DialogSpacingConfigurationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DialogSpacingConfigurationManager(context.applicationContext, settingsRepository).also { INSTANCE = it }
            }
        }
    }
}

package com.weinuo.quickcommands.utils

import android.content.Context
import android.media.RingtoneManager
import android.net.Uri
import android.os.Parcelable
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize

/**
 * 铃声辅助工具类
 * 封装铃声相关的操作逻辑
 */
object RingtoneHelper {
    private const val TAG = "RingtoneHelper"

    /**
     * 铃声信息数据类
     * 使用 @Parcelize 注解使其可序列化，以便在 SavedStateHandle 中传递
     */
    @Parcelize
    data class RingtoneInfo(
        val id: String,
        val title: String,
        val uriString: String, // 改为字符串存储URI，避免序列化问题
        val type: RingtoneType
    ) : Parcelable {
        /**
         * 获取 Uri 对象的便捷属性
         */
        val uri: Uri
            get() = Uri.parse(uriString)
    }

    /**
     * 铃声类型枚举
     * 使用 @Parcelize 注解使其可序列化
     */
    @Parcelize
    enum class RingtoneType(val displayName: String, val ringtoneManagerType: Int) : Parcelable {
        RINGTONE("电话铃声", RingtoneManager.TYPE_RINGTONE),
        NOTIFICATION("通知铃声", RingtoneManager.TYPE_NOTIFICATION),
        ALARM("闹钟铃声", RingtoneManager.TYPE_ALARM)
    }

    /**
     * 获取指定类型的所有铃声列表
     * 包含缓存机制，减少重复查询
     */
    suspend fun getRingtones(context: Context, type: RingtoneType): List<RingtoneInfo> = withContext(Dispatchers.IO) {
        try {
            val ringtones = mutableListOf<RingtoneInfo>()

            val ringtoneManager = RingtoneManager(context)
            ringtoneManager.setType(type.ringtoneManagerType)

            val cursor = ringtoneManager.cursor
            cursor?.use { c ->
                val titleIndex = RingtoneManager.TITLE_COLUMN_INDEX
                val uriIndex = RingtoneManager.URI_COLUMN_INDEX
                val idIndex = RingtoneManager.ID_COLUMN_INDEX

                while (c.moveToNext()) {
                    val title = c.getString(titleIndex) ?: "未知铃声"
                    val uriString = c.getString(uriIndex) ?: continue
                    val id = c.getString(idIndex) ?: continue

                    try {
                        val fullUriString = "$uriString/$id"
                        ringtones.add(
                            RingtoneInfo(
                                id = id,
                                title = title,
                                uriString = fullUriString,
                                type = type
                            )
                        )
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to parse ringtone URI: $uriString/$id", e)
                    }
                }
            }

            Log.d(TAG, "Found ${ringtones.size} ringtones for type ${type.displayName}")
            ringtones
        } catch (e: Exception) {
            Log.e(TAG, "Error getting ringtones for type ${type.displayName}", e)
            emptyList()
        }
    }

    /**
     * 获取默认铃声
     */
    fun getDefaultRingtone(context: Context, type: RingtoneType): RingtoneInfo? {
        return try {
            val defaultUri = RingtoneManager.getDefaultUri(type.ringtoneManagerType)
            if (defaultUri != null) {
                val ringtone = RingtoneManager.getRingtone(context, defaultUri)
                val title = ringtone?.getTitle(context) ?: "默认${type.displayName}"
                RingtoneInfo(
                    id = "default",
                    title = title,
                    uriString = defaultUri.toString(),
                    type = type
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting default ringtone for type ${type.displayName}", e)
            null
        }
    }

    /**
     * 播放铃声预览
     */
    fun playRingtone(context: Context, ringtoneInfo: RingtoneInfo): Boolean {
        return try {
            val ringtone = RingtoneManager.getRingtone(context, ringtoneInfo.uri)
            ringtone?.play()
            Log.d(TAG, "Playing ringtone: ${ringtoneInfo.title}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error playing ringtone: ${ringtoneInfo.title}", e)
            false
        }
    }

    /**
     * 停止所有铃声播放
     */
    fun stopAllRingtones() {
        try {
            // 这里可以添加停止所有铃声播放的逻辑
            // 由于RingtoneManager没有提供全局停止方法，
            // 实际应用中可能需要维护一个播放中的铃声列表
            Log.d(TAG, "Stopping all ringtones")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping ringtones", e)
        }
    }

    /**
     * 根据URI获取铃声信息
     */
    fun getRingtoneByUri(context: Context, uri: String, type: RingtoneType): RingtoneInfo? {
        return try {
            val ringtoneUri = Uri.parse(uri)
            val ringtone = RingtoneManager.getRingtone(context, ringtoneUri)
            val title = ringtone?.getTitle(context) ?: "未知铃声"

            RingtoneInfo(
                id = ringtoneUri.lastPathSegment ?: "unknown",
                title = title,
                uriString = uri,
                type = type
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting ringtone by URI: $uri", e)
            null
        }
    }

    /**
     * 验证铃声URI是否有效
     */
    fun isValidRingtoneUri(context: Context, uri: String): Boolean {
        return try {
            val ringtoneUri = Uri.parse(uri)
            val ringtone = RingtoneManager.getRingtone(context, ringtoneUri)
            ringtone != null
        } catch (e: Exception) {
            Log.w(TAG, "Invalid ringtone URI: $uri", e)
            false
        }
    }

    /**
     * 设置系统铃声
     *
     * @param context 上下文
     * @param ringtoneUri 铃声URI字符串
     * @param ringtoneType 铃声类型
     * @return 是否设置成功
     */
    fun setRingtone(context: Context, ringtoneUri: String, ringtoneType: RingtoneType): Boolean {
        return try {
            val uri = Uri.parse(ringtoneUri)
            RingtoneManager.setActualDefaultRingtoneUri(context, ringtoneType.ringtoneManagerType, uri)
            Log.d(TAG, "Successfully set ${ringtoneType.displayName} to: $ringtoneUri")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error setting ${ringtoneType.displayName} to: $ringtoneUri", e)
            false
        }
    }
}

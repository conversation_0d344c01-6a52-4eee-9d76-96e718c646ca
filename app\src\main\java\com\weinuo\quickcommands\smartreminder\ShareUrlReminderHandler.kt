package com.weinuo.quickcommands.smartreminder

import android.content.ClipboardManager
import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.service.SmartReminderOverlayService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.*

/**
 * 分享网址提醒处理器
 *
 * 监听剪贴板变化，检测网址链接并建议分享。
 * 当用户复制网址时，自动识别并显示提醒悬浮窗。
 *
 * 功能特点：
 * - 实时监听剪贴板变化
 * - 智能识别网址链接
 * - 过滤应用专用链接（避免冲突）
 * - 显示分享图标的提醒悬浮窗
 * - 防重复提醒机制
 *
 * 设计原则：
 * - 遵循现有架构模式
 * - 与应用链接提醒保持一致的显示方式
 * - 最小化资源消耗
 * - 用户体验优先
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class ShareUrlReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "ShareUrlReminder"
        private const val DEFAULT_REMINDER_COOLDOWN = 30000L // 30秒冷却时间
        private const val DEFAULT_DETECTION_DELAY = 500L // 检测延迟，避免频繁触发
    }

    /**
     * 网址信息数据类
     */
    data class UrlInfo(
        val url: String,
        val domain: String?,
        val displayText: String
    )

    private val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    private val configAdapter = SmartReminderConfigAdapter(context)
    
    private var clipboardListener: ClipboardManager.OnPrimaryClipChangedListener? = null
    private var isMonitoring = false
    private var lastReminderTime = 0L
    private var currentConfig: SmartReminderConfigAdapter.ShareUrlReminderConfig? = null
    
    // 协程作用域
    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    /**
     * 开始监听剪贴板变化
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Already monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Starting share URL reminder monitoring")

            // 加载配置
            loadConfiguration()

            // 创建剪贴板监听器
            clipboardListener = ClipboardManager.OnPrimaryClipChangedListener {
                handlerScope.launch {
                    // 使用配置的延迟时间
                    val delayTime = currentConfig?.detectionDelay?.toLong() ?: DEFAULT_DETECTION_DELAY
                    delay(delayTime)
                    handleClipboardChange()
                }
            }

            // 注册监听器
            clipboardManager.addPrimaryClipChangedListener(clipboardListener)
            isMonitoring = true

            Log.d(TAG, "Share URL reminder monitoring started")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting share URL reminder monitoring", e)
        }
    }

    /**
     * 停止监听剪贴板变化
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Not monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Stopping share URL reminder monitoring")

            // 移除剪贴板监听器
            clipboardListener?.let { listener ->
                clipboardManager.removePrimaryClipChangedListener(listener)
            }
            clipboardListener = null
            isMonitoring = false

            // 取消所有协程
            handlerScope.coroutineContext.cancelChildren()

            Log.d(TAG, "Share URL reminder monitoring stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping share URL reminder monitoring", e)
        }
    }

    /**
     * 加载配置
     */
    private fun loadConfiguration() {
        try {
            currentConfig = configAdapter.loadShareUrlReminderConfig("share_url_reminder")
            Log.d(TAG, "Configuration loaded: $currentConfig")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading configuration", e)
            // 使用默认配置
            currentConfig = SmartReminderConfigAdapter.ShareUrlReminderConfig()
        }
    }

    /**
     * 处理剪贴板变化事件
     */
    private suspend fun handleClipboardChange() {
        try {
            // 确保配置已加载
            if (currentConfig == null) {
                loadConfiguration()
            }

            val config = currentConfig ?: return

            // 检查冷却时间
            val currentTime = System.currentTimeMillis()
            val cooldownTime = config.cooldownTime * 1000L // 转换为毫秒
            if (currentTime - lastReminderTime < cooldownTime) {
                Log.d(TAG, "Reminder in cooldown period, skipping")
                return
            }

            // 获取剪贴板内容
            val clipText = getClipboardText() ?: return

            Log.d(TAG, "Clipboard changed, checking for shareable URLs: ${clipText.take(50)}...")

            // 检测网址链接
            val detectionResult = ShareUrlDetector.detectShareableUrl(
                text = clipText,
                customUrlPatterns = config.customUrlPatterns
            )
            
            if (!detectionResult.isUrl || detectionResult.url == null) {
                Log.d(TAG, "No shareable URL detected")
                return
            }

            val url = detectionResult.url
            val domain = detectionResult.domain
            Log.d(TAG, "Detected shareable URL: $url")

            // 创建URL信息
            val urlInfo = UrlInfo(
                url = url,
                domain = domain,
                displayText = domain ?: url
            )

            Log.d(TAG, "Showing share URL reminder for: ${urlInfo.displayText}")

            // 显示提醒
            showShareUrlReminder(urlInfo)

            // 更新最后提醒时间
            lastReminderTime = currentTime

            // 触发回调
            onReminderTriggered(mapOf(
                "type" to "share_url_reminder",
                "timestamp" to currentTime,
                "url" to url,
                "domain" to (domain ?: ""),
                "clipboardText" to clipText.take(100)
            ))

        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard change", e)
        }
    }

    /**
     * 获取剪贴板文本内容
     */
    private fun getClipboardText(): String? {
        return try {
            val clip = clipboardManager.primaryClip
            if (clip != null && clip.itemCount > 0) {
                val item = clip.getItemAt(0)
                item.text?.toString()
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting clipboard text", e)
            null
        }
    }

    /**
     * 显示分享网址提醒悬浮窗
     */
    private fun showShareUrlReminder(urlInfo: UrlInfo) {
        try {
            Log.d(TAG, "Attempting to show share URL reminder for: ${urlInfo.displayText}")

            val title = context.getString(R.string.share_url_reminder_title)
            val message = "检测到网址链接，建议分享「${urlInfo.displayText}」"

            Log.d(TAG, "Calling SmartReminderOverlayService.showShareUrlReminder")

            SmartReminderOverlayService.showShareUrlReminder(
                context = context,
                title = title,
                message = message,
                urlInfo = urlInfo
            )

            Log.d(TAG, "SmartReminderOverlayService.showShareUrlReminder called successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing share URL reminder overlay", e)
        }
    }

    /**
     * 检查是否正在监听
     */
    fun isMonitoring(): Boolean {
        return isMonitoring
    }

    /**
     * 重新加载配置
     */
    fun reloadConfiguration() {
        loadConfiguration()
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopMonitoring()
        handlerScope.cancel()
    }
}

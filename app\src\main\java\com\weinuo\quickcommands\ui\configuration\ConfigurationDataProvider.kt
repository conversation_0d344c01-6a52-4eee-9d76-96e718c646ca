package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.screens.DetailConfigurationContent

/**
 * 搜索项数据类
 *
 * 用于扩展搜索功能，包含主配置项和子配置项的信息。
 *
 * @param mainItem 主配置项
 * @param subItems 子配置项列表（来自各个配置提供器）
 * @param searchableText 可搜索的文本内容（包含主项和子项的标题、描述）
 */
data class SearchableConfigurationItem(
    val mainItem: ConfigurationItem,
    val subItems: List<SubConfigurationItem> = emptyList(),
    val searchableText: String
)

/**
 * 子配置项数据类
 *
 * 表示主配置项下的具体子配置选项。
 *
 * @param id 子配置项ID
 * @param title 子配置项标题
 * @param description 子配置项描述
 * @param parentId 父配置项ID
 */
data class SubConfigurationItem(
    val id: String,
    val title: String,
    val description: String,
    val parentId: String
)

/**
 * 配置数据提供器
 *
 * 为统一配置选择系统提供配置项数据源。
 * 根据配置模式返回对应的配置项列表，支持条件和任务的统一管理。
 * 支持扩展搜索功能，能够搜索到子配置项的内容。
 */
object ConfigurationDataProvider {

    /**
     * 根据配置模式获取配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @param mode 配置模式（触发条件、中止条件、任务）
     * @return 对应的配置项列表
     */
    fun getItemsForMode(context: Context, mode: ConfigurationMode): List<ConfigurationItem> {
        return when (mode) {
            ConfigurationMode.TRIGGER_CONDITION,
            ConfigurationMode.ABORT_CONDITION -> getConditionItems(context)
            ConfigurationMode.TASK -> getTaskItems(context)
        }
    }

    /**
     * 根据配置模式获取扩展搜索数据
     *
     * 包含主配置项和所有子配置项的信息，用于支持深度搜索功能。
     *
     * @param context 上下文，用于获取字符串资源
     * @param mode 配置模式（触发条件、中止条件、任务）
     * @return 扩展搜索数据列表
     */
    fun getSearchableItemsForMode(context: Context, mode: ConfigurationMode): List<SearchableConfigurationItem> {
        val mainItems = getItemsForMode(context, mode)
        return mainItems.map { mainItem ->
            val subItems = getSubItemsForMainItem(context, mainItem)
            val searchableText = buildSearchableText(mainItem, subItems)
            SearchableConfigurationItem(
                mainItem = mainItem,
                subItems = subItems,
                searchableText = searchableText
            )
        }
    }



    /**
     * 获取条件配置项列表
     *
     * 包含所有支持的条件类型：通信状态、连接状态、传感器状态、
     * 应用状态、设备事件、时间条件、手动触发
     *
     * @param context 上下文，用于获取字符串资源
     */
    private fun getConditionItems(context: Context): List<ConfigurationItem> = listOf(
        // 1. 通信状态条件 - 现在通过 CommunicationStateConfigProvider 和 DetailConfigurationScreen 处理
        ConfigurationItem(
            id = "communication_state",
            title = context.getString(R.string.communication_state),
            description = context.getString(R.string.communication_state_description),
            icon = Icons.Default.Phone,
            type = CommunicationStateType.SMS_RECEIVED,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = CommunicationStateConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 2. 连接状态条件
        ConfigurationItem(
            id = "connection_state",
            title = context.getString(R.string.connection_state),
            description = context.getString(R.string.connection_state_description),
            icon = Icons.Default.Wifi,
            type = ConnectionType.WIFI_NETWORK,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = ConnectionStateConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 3. 传感器状态条件
        ConfigurationItem(
            id = "sensor_state",
            title = context.getString(R.string.sensor_state),
            description = context.getString(R.string.sensor_state_description),
            icon = Icons.Default.Sensors,
            type = SensorStateType.ACTIVITY_RECOGNITION,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = SensorStateConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 4. 应用状态条件 - 现在通过 AppStateConfigProvider 和 DetailConfigurationScreen 处理
        ConfigurationItem(
            id = "app_state",
            title = context.getString(R.string.app_state),
            description = context.getString(R.string.app_state_description),
            icon = Icons.Default.Apps,
            type = AppStateCategoryType.STATE_CHANGE,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = AppStateConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 5. 设备事件条件 - 现在通过 DeviceEventConfigProvider 和 DetailConfigurationScreen 处理
        ConfigurationItem(
            id = "device_event",
            title = context.getString(R.string.device_event),
            description = context.getString(R.string.device_event_description),
            icon = Icons.Default.PhoneAndroid,
            type = DeviceEventType.GPS_STATE,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = DeviceEventConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 6. 时间条件 - 现在使用新的模块化配置系统
        ConfigurationItem(
            id = "time_based",
            title = context.getString(R.string.time_based),
            description = context.getString(R.string.time_based_description),
            icon = Icons.Default.Schedule,
            type = TimeConditionType.STOPWATCH,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = TimeConditionConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 7. 手动触发条件 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "manual_trigger",
            title = context.getString(R.string.manual_trigger),
            description = context.getString(R.string.manual_trigger_description),
            icon = Icons.Default.TouchApp,
            type = ManualTriggerType.DYNAMIC_SHORTCUT,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = ManualTriggerConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 8. 电池状态条件 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "battery_state",
            title = context.getString(R.string.battery_state),
            description = context.getString(R.string.battery_state_description),
            icon = Icons.Default.Battery3Bar,
            type = BatteryConditionType.BATTERY_LEVEL,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = BatteryStateConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        )
    )

    /**
     * 获取任务配置项列表
     *
     * 包含所有支持的任务类型：电话、媒体、位置、文件、相机、信息、
     * 连接、屏幕控制、设备设置、应用、音量、设备操作、通知、日期时间
     *
     * @param context 上下文，用于获取字符串资源
     */
    private fun getTaskItems(context: Context): List<ConfigurationItem> = listOf(
        // 1. 电话任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "phone",
            title = context.getString(R.string.phone_task),
            description = context.getString(R.string.phone_task_description),
            icon = Icons.Default.Phone,
            type = PhoneOperation.MAKE_CALL,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = PhoneTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 2. 媒体任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "media_task",
            title = context.getString(R.string.media_task),
            description = context.getString(R.string.media_task_description),
            icon = Icons.Default.PlayArrow,
            type = MediaOperation.MULTIMEDIA_CONTROL,
            configComposable = { initial, onConfigured, onDismiss ->
                // 获取实验性功能状态并过滤配置项
                val settingsRepository = SettingsRepository(context)
                val globalSettings = settingsRepository.globalSettings.value
                val allConfigItems = MediaTaskConfigProvider.getConfigurationItems(context)
                val filteredConfigItems = if (globalSettings.experimentalFeaturesEnabled) {
                    // 实验性功能启用时，显示所有配置项
                    allConfigItems
                } else {
                    // 实验性功能未启用时，过滤掉实验性功能项
                    allConfigItems.filter { item ->
                        !item.isExperimental
                    }
                }

                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = filteredConfigItems,
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 3. 位置任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "location_task",
            title = context.getString(R.string.location_task),
            description = context.getString(R.string.location_task_description),
            icon = Icons.Default.LocationOn,
            type = LocationOperation.SHARE_LOCATION,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = LocationTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 4. 文件操作任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "file_operation_task",
            title = context.getString(R.string.file_operation_task),
            description = context.getString(R.string.file_operation_task_description),
            icon = Icons.Default.Folder,
            type = FileOperation.WRITE_FILE,
            configComposable = { initial, onConfigured, onDismiss ->
                // 获取实验性功能状态并过滤配置项
                val settingsRepository = SettingsRepository(context)
                val globalSettings = settingsRepository.globalSettings.value
                val allConfigItems = FileOperationTaskConfigProvider.getConfigurationItems(context)
                val filteredConfigItems = if (globalSettings.experimentalFeaturesEnabled) {
                    // 实验性功能启用时，显示所有配置项
                    allConfigItems
                } else {
                    // 实验性功能未启用时，过滤掉实验性功能项
                    allConfigItems.filter { item ->
                        !item.isExperimental
                    }
                }

                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = filteredConfigItems,
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 5. 照片任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "camera_task",
            title = context.getString(R.string.camera_task),
            description = context.getString(R.string.camera_task_description),
            icon = Icons.Default.Image,
            type = CameraOperation.OPEN_LAST_PHOTO,
            configComposable = { initial, onConfigured, onDismiss ->
                // 获取实验性功能状态并过滤配置项
                val settingsRepository = SettingsRepository(context)
                val globalSettings = settingsRepository.globalSettings.value
                val allConfigItems = CameraTaskConfigProvider.getConfigurationItems(context)
                val filteredConfigItems = if (globalSettings.experimentalFeaturesEnabled) {
                    // 实验性功能启用时，显示所有配置项
                    allConfigItems
                } else {
                    // 实验性功能未启用时，过滤掉实验性功能项
                    allConfigItems.filter { item ->
                        !item.isExperimental
                    }
                }

                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = filteredConfigItems,
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 6. 信息任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "information_task",
            title = context.getString(R.string.information_task),
            description = context.getString(R.string.information_task_description),
            icon = Icons.Default.Message,
            type = InformationOperation.SEND_SMS,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = InformationTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 7. 连接任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "connectivity_task",
            title = context.getString(R.string.connectivity_task),
            description = context.getString(R.string.connectivity_task_description),
            icon = Icons.Default.Wifi,
            type = ConnectivityOperation.WIFI_CONTROL,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = ConnectivityTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 8. 屏幕控制任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "screen_control",
            title = context.getString(R.string.screen_control_task),
            description = context.getString(R.string.screen_control_task_description),
            icon = Icons.Default.Brightness6,
            type = ScreenControlOperation.BRIGHTNESS_CONTROL,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = ScreenControlTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 9. 应用任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "application",
            title = context.getString(R.string.application_task),
            description = context.getString(R.string.application_task_description),
            icon = Icons.Default.Apps,
            type = ApplicationOperation.LAUNCH_APP,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = ApplicationTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 10. 音量任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "volume",
            title = context.getString(R.string.volume_task),
            description = context.getString(R.string.volume_task_description),
            icon = Icons.Default.VolumeUp,
            type = VolumeOperation.SPEAKERPHONE_CONTROL,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = VolumeTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 11. 设备动作任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "device_action",
            title = context.getString(R.string.device_action_task),
            description = context.getString(R.string.device_action_task_description),
            icon = Icons.Default.PhoneAndroid,
            type = DeviceActionOperation.SHARE_TEXT,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = DeviceActionTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 12. 通知任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "notification",
            title = context.getString(R.string.notification_task),
            description = context.getString(R.string.notification_task_description),
            icon = Icons.Default.Notifications,
            type = NotificationOperation.SHOW_TOAST,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = NotificationTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        ),

        // 13. 日期时间任务 - 使用新的模块化配置系统
        ConfigurationItem(
            id = "date_time",
            title = context.getString(R.string.datetime_task),
            description = context.getString(R.string.datetime_task_description),
            icon = Icons.Default.Schedule,
            type = DateTimeTaskType.STOPWATCH,
            configComposable = { initial, onConfigured, onDismiss ->
                // 使用新的模块化配置系统
                DetailConfigurationContent(
                    configurationItems = DateTimeTaskConfigProvider.getConfigurationItems(context),
                    initialConfigObject = initial,
                    onConfigurationComplete = onConfigured
                )
            }
        )
    )

    /**
     * 获取主配置项对应的子配置项列表
     *
     * 根据主配置项的ID，从对应的配置提供器中获取子配置项信息。
     * 会根据实验性功能设置过滤掉实验性功能项。
     *
     * @param context 上下文，用于获取字符串资源
     * @param mainItem 主配置项
     * @return 子配置项列表（已过滤实验性功能）
     */
    private fun getSubItemsForMainItem(context: Context, mainItem: ConfigurationItem): List<SubConfigurationItem> {
        // 获取实验性功能状态
        val settingsRepository = SettingsRepository(context)
        val globalSettings = settingsRepository.globalSettings.value
        return when (mainItem.id) {
            // === 条件类型的子项 ===

            // 通信状态条件的子项
            "communication_state" -> CommunicationStateConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 连接状态条件的子项
            "connection_state" -> ConnectionStateConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 传感器状态条件的子项
            "sensor_state" -> SensorStateConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 应用状态条件的子项
            "app_state" -> AppStateConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 设备事件条件的子项
            "device_event" -> DeviceEventConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 时间条件的子项
            "time_based" -> TimeConditionConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 手动触发条件的子项
            "manual_trigger" -> ManualTriggerConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 电池状态条件的子项
            "battery_state" -> BatteryStateConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // === 任务类型的子项 ===

            // 电话任务的子项
            "phone" -> PhoneTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 媒体任务的子项
            "media_task" -> {
                val allItems = MediaTaskConfigProvider.getConfigurationItems(context)
                val filteredItems = if (globalSettings.experimentalFeaturesEnabled) {
                    allItems
                } else {
                    allItems.filter { !it.isExperimental }
                }
                filteredItems.map { item ->
                    SubConfigurationItem(
                        id = item.id,
                        title = item.title,
                        description = item.description,
                        parentId = mainItem.id
                    )
                }
            }

            // 位置任务的子项
            "location_task" -> LocationTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 文件操作任务的子项
            "file_operation_task" -> {
                val allItems = FileOperationTaskConfigProvider.getConfigurationItems(context)
                val filteredItems = if (globalSettings.experimentalFeaturesEnabled) {
                    allItems
                } else {
                    allItems.filter { !it.isExperimental }
                }
                filteredItems.map { item ->
                    SubConfigurationItem(
                        id = item.id,
                        title = item.title,
                        description = item.description,
                        parentId = mainItem.id
                    )
                }
            }

            // 相机任务的子项
            "camera_task" -> {
                val allItems = CameraTaskConfigProvider.getConfigurationItems(context)
                val filteredItems = if (globalSettings.experimentalFeaturesEnabled) {
                    allItems
                } else {
                    allItems.filter { !it.isExperimental }
                }
                filteredItems.map { item ->
                    SubConfigurationItem(
                        id = item.id,
                        title = item.title,
                        description = item.description,
                        parentId = mainItem.id
                    )
                }
            }

            // 信息任务的子项
            "information_task" -> InformationTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 连接任务的子项
            "connectivity_task" -> ConnectivityTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 屏幕控制任务的子项
            "screen_control" -> ScreenControlTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 设备设置任务的子项
            "device_settings" -> DeviceSettingsTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 应用任务的子项
            "application" -> ApplicationTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 音量任务的子项
            "volume_task" -> VolumeTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 设备动作任务的子项
            "device_action" -> DeviceActionTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 通知任务的子项
            "notification" -> NotificationTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 日期时间任务的子项
            "date_time" -> DateTimeTaskConfigProvider.getConfigurationItems(context).map { item ->
                SubConfigurationItem(
                    id = item.id,
                    title = item.title,
                    description = item.description,
                    parentId = mainItem.id
                )
            }

            // 其他配置项暂时没有子项
            else -> emptyList()
        }
    }

    /**
     * 构建可搜索的文本内容
     *
     * 将主配置项和子配置项的标题、描述组合成一个可搜索的文本字符串。
     *
     * @param mainItem 主配置项
     * @param subItems 子配置项列表
     * @return 可搜索的文本内容
     */
    private fun buildSearchableText(mainItem: ConfigurationItem, subItems: List<SubConfigurationItem>): String {
        val mainText = "${mainItem.title} ${mainItem.description}"
        val subText = subItems.joinToString(" ") { "${it.title} ${it.description}" }
        return "$mainText $subText"
    }
}

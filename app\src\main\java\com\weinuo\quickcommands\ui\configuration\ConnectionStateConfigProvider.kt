package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.clickable
import androidx.compose.material3.*
import androidx.compose.material.icons.filled.Delete
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem

/**
 * WiFi筛选模式枚举
 */
enum class WifiFilterMode {
    ANY_NETWORK,        // 任何网络
    SPECIFIC_NETWORKS   // 指定网络
}

/**
 * 连接状态配置数据提供器
 *
 * 提供连接状态特定的配置项列表，为每个连接类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object ConnectionStateConfigProvider {

    /**
     * 获取连接状态配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 连接状态配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<ConnectionType>> {
        return listOf(
            // WiFi状态配置项
            ConfigurationCardItem(
                id = "wifi_state",
                title = context.getString(R.string.conn_wifi_state),
                description = context.getString(R.string.conn_wifi_state_description),
                operationType = ConnectionType.WIFI_STATE,
                permissionRequired = true,
                content = { type, onComplete ->
                    WiFiStateConfigContent(type, onComplete)
                }
            ),

            // WiFi网络配置项
            ConfigurationCardItem(
                id = "wifi_network",
                title = context.getString(R.string.conn_wifi_network),
                description = context.getString(R.string.conn_wifi_network_description),
                operationType = ConnectionType.WIFI_NETWORK,
                permissionRequired = true,
                content = { type, onComplete ->
                    WiFiNetworkConfigContent(type, onComplete)
                }
            ),

            // 移动数据配置项
            ConfigurationCardItem(
                id = "mobile_data",
                title = "移动数据",
                description = "当移动数据连接状态改变时触发条件",
                operationType = ConnectionType.MOBILE_DATA,
                permissionRequired = true,
                content = { type, onComplete ->
                    MobileDataConfigContent(type, onComplete)
                }
            ),

            // 通用网络配置项
            ConfigurationCardItem(
                id = "network_general",
                title = "网络连接",
                description = "当任何网络连接状态改变时触发条件",
                operationType = ConnectionType.NETWORK_GENERAL,
                permissionRequired = false,
                content = { type, onComplete ->
                    NetworkGeneralConfigContent(type, onComplete)
                }
            ),

            // 蓝牙状态配置项
            ConfigurationCardItem(
                id = "bluetooth_state",
                title = "蓝牙状态",
                description = "当蓝牙开启/关闭或连接状态改变时触发条件",
                operationType = ConnectionType.BLUETOOTH_STATE,
                permissionRequired = true,
                content = { type, onComplete ->
                    BluetoothStateConfigContent(type, onComplete)
                }
            ),

            // 特定蓝牙设备配置项
            ConfigurationCardItem(
                id = "bluetooth_device",
                title = "蓝牙设备",
                description = "当特定蓝牙设备连接状态改变时触发条件",
                operationType = ConnectionType.BLUETOOTH_DEVICE,
                permissionRequired = true,
                content = { type, onComplete ->
                    BluetoothDeviceConfigContent(type, onComplete)
                }
            ),

            // VPN连接配置项
            ConfigurationCardItem(
                id = "vpn_connection",
                title = "VPN连接",
                description = "当VPN连接状态改变时触发条件",
                operationType = ConnectionType.VPN_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    VpnConnectionConfigContent(type, onComplete)
                }
            ),

            // 数据漫游配置项
            ConfigurationCardItem(
                id = "roaming_state",
                title = "数据漫游",
                description = "当数据漫游状态改变时触发条件",
                operationType = ConnectionType.ROAMING_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    RoamingStateConfigContent(type, onComplete)
                }
            ),

            // 个人热点配置项
            ConfigurationCardItem(
                id = "hotspot_state",
                title = "个人热点",
                description = "当个人热点状态改变时触发条件",
                operationType = ConnectionType.HOTSPOT_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    HotspotStateConfigContent(type, onComplete)
                }
            ),

            // 耳机连接配置项
            ConfigurationCardItem(
                id = "headphone",
                title = "耳机连接",
                description = "当耳机连接状态改变时触发条件",
                operationType = ConnectionType.HEADPHONE,
                permissionRequired = false,
                content = { type, onComplete ->
                    HeadphoneConfigContent(type, onComplete)
                }
            ),

            // USB设备配置项
            ConfigurationCardItem(
                id = "usb_device",
                title = "USB设备",
                description = "当USB设备连接状态改变时触发条件",
                operationType = ConnectionType.USB_DEVICE,
                permissionRequired = false,
                content = { type, onComplete ->
                    UsbDeviceConfigContent(type, onComplete)
                }
            ),

            // IP地址配置项
            ConfigurationCardItem(
                id = "ip_address",
                title = "IP地址",
                description = "当设备IP地址状态改变时触发条件",
                operationType = ConnectionType.IP_ADDRESS,
                permissionRequired = false,
                content = { type, onComplete ->
                    IpAddressConfigContent(type, onComplete)
                }
            ),

            // 基站连接配置项
            ConfigurationCardItem(
                id = "cell_tower",
                title = "基站连接",
                description = "当基站连接状态改变时触发条件",
                operationType = ConnectionType.CELL_TOWER,
                permissionRequired = true,
                content = { type, onComplete ->
                    CellTowerConfigContent(type, onComplete)
                }
            ),

            // 手机信号配置项
            ConfigurationCardItem(
                id = "mobile_signal",
                title = "手机信号",
                description = "当手机信号状态改变时触发条件",
                operationType = ConnectionType.MOBILE_SIGNAL,
                permissionRequired = true,
                content = { type, onComplete ->
                    MobileSignalConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * WiFi状态配置内容组件
 */
@Composable
private fun WiFiStateConfigContent(
    type: ConnectionType,
    onComplete: (Any) -> Unit
) {
    var selectedSubType by rememberSaveable { mutableStateOf(ConnectionSubType.WIFI_CONNECTED) }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置WiFi状态的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * WiFi网络配置内容组件
 */
@Composable
private fun WiFiNetworkConfigContent(
    type: ConnectionType,
    onComplete: (Any) -> Unit
) {
    var selectedSubType by rememberSaveable { mutableStateOf(ConnectionSubType.WIFI_NETWORK_CONNECTED) }
    var selectedWifiNetworks by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }
    var selectedFilterMode by rememberSaveable { mutableStateOf(WifiFilterMode.ANY_NETWORK) }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置WiFi网络的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // WiFi网络筛选模式
        WifiFilterModeSelector(
            selectedFilterMode = selectedFilterMode,
            onFilterModeChanged = { selectedFilterMode = it }
        )

        // 根据筛选模式显示相应的配置选项
        if (selectedFilterMode == WifiFilterMode.SPECIFIC_NETWORKS) {
            WifiNetworkSelector(
                selectedNetworks = selectedWifiNetworks,
                onNetworksChanged = { selectedWifiNetworks = it }
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType,
                    specificValue = if (selectedFilterMode == WifiFilterMode.SPECIFIC_NETWORKS)
                        selectedWifiNetworks.joinToString(",") else ""
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 移动数据配置内容组件
 */
@Composable
private fun MobileDataConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.MOBILE_DATA_CONNECTED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置移动数据连接的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 通用网络配置内容组件
 */
@Composable
private fun NetworkGeneralConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.NETWORK_CONNECTED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置通用网络连接的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 蓝牙状态配置内容组件
 */
@Composable
private fun BluetoothStateConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.BLUETOOTH_CONNECTED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置蓝牙状态的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 蓝牙设备配置内容组件
 */
@Composable
private fun BluetoothDeviceConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.BLUETOOTH_DEVICE_CONNECTED)
    }
    var deviceName by rememberSaveable { mutableStateOf("") }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置特定蓝牙设备的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 设备名称输入（可选）
        OutlinedTextField(
            value = deviceName,
            onValueChange = { deviceName = it },
            label = { Text("蓝牙设备名称 (可选)") },
            placeholder = { Text("例如: AirPods Pro") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType,
                    specificValue = deviceName
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 数据漫游配置内容组件
 */
@Composable
private fun RoamingStateConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.ROAMING_ENABLED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置数据漫游的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 个人热点配置内容组件
 */
@Composable
private fun HotspotStateConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.HOTSPOT_ENABLED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置个人热点的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 耳机连接配置内容组件
 */
@Composable
private fun HeadphoneConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.HEADPHONE_CONNECTED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置耳机连接的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * USB设备配置内容组件
 */
@Composable
private fun UsbDeviceConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.USB_DEVICE_CONNECTED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置USB设备的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * IP地址配置内容组件
 */
@Composable
private fun IpAddressConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.IP_ADDRESS_CHANGED)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置IP地址的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 手机信号配置内容组件
 */
@Composable
private fun MobileSignalConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.MOBILE_SIGNAL_AVAILABLE)
    }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置手机信号的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * VPN连接配置内容组件
 */
@Composable
private fun VpnConnectionConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.VPN_CONNECTED)
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置VPN连接的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 基站连接配置内容组件
 */
@Composable
private fun CellTowerConfigContent(type: ConnectionType, onComplete: (Any) -> Unit) {
    var selectedSubType by rememberSaveable {
        mutableStateOf(ConnectionSubType.CELL_TOWER_ENTERED)
    }
    var specificValue by rememberSaveable { mutableStateOf("") }

    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置基站连接的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 连接子类型选择
        ConnectionSubTypeSelector(
            connectionType = type,
            selectedSubType = selectedSubType,
            onSubTypeChanged = { selectedSubType = it }
        )

        // 基站标识输入（可选）
        OutlinedTextField(
            value = specificValue,
            onValueChange = { specificValue = it },
            label = { Text("基站标识 (可选)") },
            placeholder = { Text("例如: Cell ID 或 LAC") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = ConnectionStateCondition(
                    connectionType = type,
                    subType = selectedSubType,
                    specificValue = specificValue
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 连接子类型选择器
 * 根据连接类型显示可用的子类型选项
 */
@Composable
private fun ConnectionSubTypeSelector(
    connectionType: ConnectionType,
    selectedSubType: ConnectionSubType,
    onSubTypeChanged: (ConnectionSubType) -> Unit
) {
    // 根据连接类型获取可用的子类型
    val availableSubTypes = when (connectionType) {
        ConnectionType.WIFI_STATE -> listOf(
            ConnectionSubType.WIFI_ENABLED,
            ConnectionSubType.WIFI_DISABLED,
            ConnectionSubType.WIFI_CONNECTED,
            ConnectionSubType.WIFI_DISCONNECTED
        )
        ConnectionType.WIFI_NETWORK -> listOf(
            ConnectionSubType.WIFI_NETWORK_CHANGED,
            ConnectionSubType.WIFI_NETWORK_CONNECTED,
            ConnectionSubType.WIFI_NETWORK_DISCONNECTED
        )
        ConnectionType.MOBILE_DATA -> listOf(
            ConnectionSubType.MOBILE_DATA_ENABLED,
            ConnectionSubType.MOBILE_DATA_DISABLED,
            ConnectionSubType.MOBILE_DATA_CONNECTED,
            ConnectionSubType.MOBILE_DATA_DISCONNECTED
        )
        ConnectionType.NETWORK_GENERAL -> listOf(
            ConnectionSubType.NETWORK_CONNECTED,
            ConnectionSubType.NETWORK_DISCONNECTED
        )
        ConnectionType.VPN_STATE -> listOf(
            ConnectionSubType.VPN_CONNECTED,
            ConnectionSubType.VPN_DISCONNECTED
        )
        ConnectionType.ROAMING_STATE -> listOf(
            ConnectionSubType.ROAMING_ENABLED,
            ConnectionSubType.ROAMING_DISABLED
        )
        ConnectionType.HOTSPOT_STATE -> listOf(
            ConnectionSubType.HOTSPOT_ENABLED,
            ConnectionSubType.HOTSPOT_DISABLED
        )
        ConnectionType.BLUETOOTH_STATE -> listOf(
            ConnectionSubType.BLUETOOTH_ENABLED,
            ConnectionSubType.BLUETOOTH_DISABLED,
            ConnectionSubType.BLUETOOTH_CONNECTED,
            ConnectionSubType.BLUETOOTH_DISCONNECTED
        )
        ConnectionType.BLUETOOTH_DEVICE -> listOf(
            ConnectionSubType.BLUETOOTH_DEVICE_CONNECTED,
            ConnectionSubType.BLUETOOTH_DEVICE_DISCONNECTED
        )
        ConnectionType.HEADPHONE -> listOf(
            ConnectionSubType.HEADPHONE_CONNECTED,
            ConnectionSubType.HEADPHONE_DISCONNECTED
        )
        ConnectionType.USB_DEVICE -> listOf(
            ConnectionSubType.USB_DEVICE_CONNECTED,
            ConnectionSubType.USB_DEVICE_DISCONNECTED
        )

        ConnectionType.IP_ADDRESS -> listOf(
            ConnectionSubType.IP_ADDRESS_CHANGED,
            ConnectionSubType.IP_ADDRESS_OBTAINED,
            ConnectionSubType.IP_ADDRESS_LOST
        )
        ConnectionType.CELL_TOWER -> listOf(
            ConnectionSubType.CELL_TOWER_ENTERED,
            ConnectionSubType.CELL_TOWER_EXITED,
            ConnectionSubType.CELL_TOWER_CHANGED
        )
        ConnectionType.MOBILE_SIGNAL -> listOf(
            ConnectionSubType.MOBILE_SIGNAL_AVAILABLE,
            ConnectionSubType.MOBILE_SIGNAL_UNAVAILABLE
        )
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择连接状态",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        availableSubTypes.forEach { subType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onSubTypeChanged(subType) },
                verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedSubType == subType,
                    onClick = { onSubTypeChanged(subType) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = getSubTypeDisplayName(subType),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * WiFi筛选模式选择器
 */
@Composable
private fun WifiFilterModeSelector(
    selectedFilterMode: WifiFilterMode,
    onFilterModeChanged: (WifiFilterMode) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "WiFi网络筛选",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onFilterModeChanged(WifiFilterMode.ANY_NETWORK) },
            verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selectedFilterMode == WifiFilterMode.ANY_NETWORK,
                onClick = { onFilterModeChanged(WifiFilterMode.ANY_NETWORK) }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "任何WiFi网络",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onFilterModeChanged(WifiFilterMode.SPECIFIC_NETWORKS) },
            verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selectedFilterMode == WifiFilterMode.SPECIFIC_NETWORKS,
                onClick = { onFilterModeChanged(WifiFilterMode.SPECIFIC_NETWORKS) }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "指定WiFi网络",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * WiFi网络选择器
 */
@Composable
private fun WifiNetworkSelector(
    selectedNetworks: List<String>,
    onNetworksChanged: (List<String>) -> Unit
) {
    var networkInput by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "指定WiFi网络名称",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedTextField(
            value = networkInput,
            onValueChange = { networkInput = it },
            label = { Text("WiFi网络名称 (SSID)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Button(
            onClick = {
                if (networkInput.isNotBlank() && networkInput !in selectedNetworks) {
                    onNetworksChanged(selectedNetworks + networkInput)
                    networkInput = ""
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = networkInput.isNotBlank() && networkInput !in selectedNetworks
        ) {
            Text("添加网络")
        }

        if (selectedNetworks.isNotEmpty()) {
            Text(
                text = "已选择的网络：",
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            selectedNetworks.forEach { network ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
                ) {
                    Text(
                        text = network,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                    IconButton(
                        onClick = {
                            onNetworksChanged(selectedNetworks - network)
                        }
                    ) {
                        Icon(
                            imageVector = androidx.compose.material.icons.Icons.Default.Delete,
                            contentDescription = "删除"
                        )
                    }
                }
            }
        }
    }
}

/**
 * 获取子类型的显示名称
 */
private fun getSubTypeDisplayName(subType: ConnectionSubType): String {
    return when (subType) {
        // WiFi状态
        ConnectionSubType.WIFI_ENABLED -> "WiFi已开启"
        ConnectionSubType.WIFI_DISABLED -> "WiFi已关闭"
        ConnectionSubType.WIFI_CONNECTED -> "WiFi已连接"
        ConnectionSubType.WIFI_DISCONNECTED -> "WiFi已断开"

        // WiFi网络
        ConnectionSubType.WIFI_NETWORK_CHANGED -> "WiFi网络已切换"
        ConnectionSubType.WIFI_NETWORK_CONNECTED -> "连接到指定WiFi网络"
        ConnectionSubType.WIFI_NETWORK_DISCONNECTED -> "从指定WiFi网络断开"

        // 移动数据
        ConnectionSubType.MOBILE_DATA_ENABLED -> "移动数据已开启"
        ConnectionSubType.MOBILE_DATA_DISABLED -> "移动数据已关闭"
        ConnectionSubType.MOBILE_DATA_CONNECTED -> "移动数据已连接"
        ConnectionSubType.MOBILE_DATA_DISCONNECTED -> "移动数据已断开"

        // 通用网络
        ConnectionSubType.NETWORK_CONNECTED -> "网络已连接"
        ConnectionSubType.NETWORK_DISCONNECTED -> "网络已断开"

        // VPN
        ConnectionSubType.VPN_CONNECTED -> "VPN已连接"
        ConnectionSubType.VPN_DISCONNECTED -> "VPN已断开"

        // 数据漫游
        ConnectionSubType.ROAMING_ENABLED -> "数据漫游已开启"
        ConnectionSubType.ROAMING_DISABLED -> "数据漫游已关闭"

        // 个人热点
        ConnectionSubType.HOTSPOT_ENABLED -> "个人热点已开启"
        ConnectionSubType.HOTSPOT_DISABLED -> "个人热点已关闭"

        // 蓝牙状态
        ConnectionSubType.BLUETOOTH_ENABLED -> "蓝牙已开启"
        ConnectionSubType.BLUETOOTH_DISABLED -> "蓝牙已关闭"
        ConnectionSubType.BLUETOOTH_CONNECTED -> "蓝牙已连接"
        ConnectionSubType.BLUETOOTH_DISCONNECTED -> "蓝牙已断开"

        // 特定蓝牙设备
        ConnectionSubType.BLUETOOTH_DEVICE_CONNECTED -> "特定蓝牙设备已连接"
        ConnectionSubType.BLUETOOTH_DEVICE_DISCONNECTED -> "特定蓝牙设备已断开"

        // 耳机
        ConnectionSubType.HEADPHONE_CONNECTED -> "耳机已连接"
        ConnectionSubType.HEADPHONE_DISCONNECTED -> "耳机已断开"

        // USB设备
        ConnectionSubType.USB_DEVICE_CONNECTED -> "USB设备已连接"
        ConnectionSubType.USB_DEVICE_DISCONNECTED -> "USB设备已断开"



        // IP地址
        ConnectionSubType.IP_ADDRESS_CHANGED -> "IP地址已变化"
        ConnectionSubType.IP_ADDRESS_OBTAINED -> "获得IP地址"
        ConnectionSubType.IP_ADDRESS_LOST -> "失去IP地址"

        // 基站
        ConnectionSubType.CELL_TOWER_ENTERED -> "进入基站区域"
        ConnectionSubType.CELL_TOWER_EXITED -> "离开基站区域"
        ConnectionSubType.CELL_TOWER_CHANGED -> "基站改变"

        // 手机信号
        ConnectionSubType.MOBILE_SIGNAL_AVAILABLE -> "手机信号可用"
        ConnectionSubType.MOBILE_SIGNAL_UNAVAILABLE -> "手机信号不可用"
    }
}

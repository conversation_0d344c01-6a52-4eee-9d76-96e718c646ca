package com.weinuo.quickcommands.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 蓝牙权限工具类
 * 管理蓝牙相关权限的检查、申请和引导
 * 适用于蓝牙设备连接检测、蓝牙状态检测等功能
 * 支持Android 12+的新权限模型
 */
object BluetoothPermissionUtil {

    /**
     * 获取当前系统版本需要的蓝牙权限列表
     * Android 12+使用BLUETOOTH_CONNECT权限
     * Android 12以下使用BLUETOOTH权限
     */
    fun getRequiredBluetoothPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12+ (API 31+)
            arrayOf(
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.BLUETOOTH_SCAN
            )
        } else {
            // Android 12以下
            arrayOf(
                Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADMIN
            )
        }
    }

    /**
     * 检查是否拥有蓝牙权限
     * 兼容新旧版本的权限模型
     * @param context 上下文
     * @return 是否拥有蓝牙权限
     */
    fun hasBluetoothPermission(context: Context): Boolean {
        val requiredPermissions = getRequiredBluetoothPermissions()
        return requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查是否拥有蓝牙连接权限
     * @param context 上下文
     * @return 是否拥有蓝牙连接权限
     */
    fun hasBluetoothConnectPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.BLUETOOTH_CONNECT
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.BLUETOOTH
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查是否拥有蓝牙扫描权限
     * @param context 上下文
     * @return 是否拥有蓝牙扫描权限
     */
    fun hasBluetoothScanPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.BLUETOOTH_SCAN
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.BLUETOOTH_ADMIN
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 申请蓝牙权限
     * @param launcher 权限申请启动器
     */
    fun requestBluetoothPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(getRequiredBluetoothPermissions())
    }

    /**
     * 打开蓝牙设置页面
     * @param context 上下文
     */
    fun openBluetoothSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS).apply {
                // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在非Activity context中也能正常启动
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开蓝牙设置，则打开应用详情页面
            openAppDetailsSettings(context)
        }
    }

    /**
     * 打开应用详情设置页面
     * @param context 上下文
     */
    fun openAppDetailsSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在非Activity context中也能正常启动
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 忽略异常，用户可以手动到设置中授权
        }
    }

    /**
     * 蓝牙权限说明对话框
     * 向用户解释为什么需要蓝牙权限
     */
    @Composable
    fun BluetoothPermissionRationaleDialog(
        onConfirm: () -> Unit,
        onDismiss: () -> Unit
    ) {
        val permissionDescription = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            """
                为了使用蓝牙相关功能，应用需要蓝牙权限：

                • 蓝牙连接权限：检测蓝牙设备连接和断开状态
                • 蓝牙扫描权限：扫描和发现蓝牙设备
                • 蓝牙状态检测：监控蓝牙开启/关闭状态变化

                这些权限仅用于功能检测，不会收集或上传您的设备信息。
                您可以随时在系统设置中撤销这些权限。
            """.trimIndent()
        } else {
            """
                为了使用蓝牙相关功能，应用需要蓝牙权限：

                • 蓝牙权限：检测蓝牙设备连接和断开状态
                • 蓝牙管理权限：管理蓝牙连接和设备发现
                • 蓝牙状态检测：监控蓝牙开启/关闭状态变化

                这些权限仅用于功能检测，不会收集或上传您的设备信息。
                您可以随时在系统设置中撤销这些权限。
            """.trimIndent()
        }

        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "蓝牙权限说明",
            message = permissionDescription,
            confirmText = "申请权限",
            dismissText = "取消",
            onConfirm = onConfirm,
            onDismiss = onDismiss
        )
    }

    /**
     * 蓝牙权限被拒绝后的说明对话框
     */
    @Composable
    fun BluetoothPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "蓝牙权限被拒绝",
            message = """
                蓝牙相关功能需要蓝牙权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动授予蓝牙权限：
                • 蓝牙设备连接检测
                • 蓝牙状态变化检测
                • 特定蓝牙设备连接监控

                您可以点击"打开设置"直接跳转到蓝牙权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }
}

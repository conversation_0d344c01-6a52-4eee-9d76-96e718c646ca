package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueSaveButton

/**
 * 添加清理规则全屏界面
 * 替代原有的对话框实现，提供更好的用户体验
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddCleanupRuleScreen(
    onRuleAdded: (CleanupRule) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    var selectedRuleType by rememberSaveable { mutableStateOf(CleanupRuleType.NORMAL_APPS) }
    var hours by rememberSaveable { mutableStateOf(6) }
    var memoryThreshold by rememberSaveable { mutableStateOf(200) }
    var launchCountThreshold by rememberSaveable { mutableStateOf(5) }
    var timeRange by rememberSaveable { mutableStateOf(UsageTimeRange.ONE_WEEK) }
    var sortMode by rememberSaveable { mutableStateOf(UsageFrequencyMode.BY_SMART_SCORE) }
    var limit by rememberSaveable { mutableStateOf(10) }

    Column(modifier = Modifier.fillMaxSize()) {
        // 顶部应用栏
        TopAppBar(
            title = {
                Text(
                    text = "添加清理规则",
                    style = themeAwareTitleStyle
                )
            },
            navigationIcon = {
                if (themeManager.getCurrentThemeId() == "sky_blue") {
                    // 天空蓝主题：使用专用的返回按钮
                    SkyBlueBackButton(onClick = onNavigateBack)
                } else {
                    // 其他主题：使用原有的箭头图标
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                    }
                }
            },
            actions = {
                // 保存按钮 - 主题感知
                if (themeManager.getCurrentThemeId() == "sky_blue") {
                    // 天空蓝主题：使用专用的保存按钮（勾号图标）
                    SkyBlueSaveButton(
                        onClick = {
                        val parameters = when (selectedRuleType) {
                            CleanupRuleType.LONG_UNUSED_APPS -> mapOf(
                                "hours" to hours,
                                "description" to "清理超过${hours}小时未使用的应用"
                            )
                            CleanupRuleType.MEMORY_HEAVY_APPS -> mapOf(
                                "memoryThresholdMB" to memoryThreshold,
                                "description" to "清理占用内存超过${memoryThreshold}MB的应用"
                            )
                            CleanupRuleType.FREQUENT_APPS -> mapOf(
                                "timeRange" to timeRange.name,
                                "launchCountThreshold" to launchCountThreshold,
                                "description" to "清理${timeRange.displayName}内启动超过${launchCountThreshold}次的应用"
                            )
                            CleanupRuleType.RARE_APPS -> mapOf(
                                "timeRange" to timeRange.name,
                                "launchCountThreshold" to launchCountThreshold,
                                "description" to "清理${timeRange.displayName}内启动少于${launchCountThreshold}次的应用"
                            )
                            CleanupRuleType.MOST_USED_APPS, CleanupRuleType.LEAST_USED_APPS -> mapOf(
                                "timeRange" to timeRange.name,
                                "sortMode" to sortMode.name,
                                "limit" to limit,
                                "description" to "按${sortMode.displayName}排序，${if (selectedRuleType == CleanupRuleType.MOST_USED_APPS) "最常用" else "最少用"}的前${limit}个应用"
                            )
                            else -> mapOf(
                                "description" to "清理${selectedRuleType.displayName}"
                            )
                        }

                        val newRule = CleanupRule(
                            type = selectedRuleType,
                            order = 0, // 将在添加时重新设置
                            parameters = parameters
                        )
                        onRuleAdded(newRule)
                        }
                    )
                } else {
                    // 其他主题：使用原有的文本按钮
                    TextButton(
                        onClick = {
                            val parameters = when (selectedRuleType) {
                                CleanupRuleType.LONG_UNUSED_APPS -> mapOf(
                                    "hours" to hours,
                                    "description" to "清理超过${hours}小时未使用的应用"
                                )
                                CleanupRuleType.MEMORY_HEAVY_APPS -> mapOf(
                                    "memoryThresholdMB" to memoryThreshold,
                                    "description" to "清理占用内存超过${memoryThreshold}MB的应用"
                                )
                                CleanupRuleType.FREQUENT_APPS -> mapOf(
                                    "timeRange" to timeRange.name,
                                    "launchCountThreshold" to launchCountThreshold,
                                    "description" to "清理${timeRange.displayName}内启动超过${launchCountThreshold}次的应用"
                                )
                                CleanupRuleType.RARE_APPS -> mapOf(
                                    "timeRange" to timeRange.name,
                                    "launchCountThreshold" to launchCountThreshold,
                                    "description" to "清理${timeRange.displayName}内启动少于${launchCountThreshold}次的应用"
                                )
                                CleanupRuleType.MOST_USED_APPS, CleanupRuleType.LEAST_USED_APPS -> mapOf(
                                    "timeRange" to timeRange.name,
                                    "sortMode" to sortMode.name,
                                    "limit" to limit,
                                    "description" to "按${sortMode.displayName}排序，${if (selectedRuleType == CleanupRuleType.MOST_USED_APPS) "最常用" else "最少用"}的前${limit}个应用"
                                )
                                else -> mapOf(
                                    "description" to "清理${selectedRuleType.displayName}"
                                )
                            }

                            val newRule = CleanupRule(
                                type = selectedRuleType,
                                order = 0, // 将在添加时重新设置
                                parameters = parameters
                            )
                            onRuleAdded(newRule)
                        }
                    ) {
                        Text("添加")
                    }
                }
            }
        )

        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 规则类型选择
            Text(
                text = "选择清理规则类型：",
                style = MaterialTheme.typography.titleMedium
            )

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CleanupRuleType.values().forEach { ruleType ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selectedRuleType == ruleType,
                                onClick = { selectedRuleType = ruleType }
                            )
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedRuleType == ruleType,
                            onClick = { selectedRuleType = ruleType }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text(
                                text = ruleType.displayName,
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = ruleType.description,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }

            // 参数配置区域
            when (selectedRuleType) {
                CleanupRuleType.LONG_UNUSED_APPS -> {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Text(
                                text = "参数配置",
                                style = MaterialTheme.typography.titleSmall
                            )

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "超过",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                OutlinedTextField(
                                    value = hours.toString(),
                                    onValueChange = {
                                        it.toIntOrNull()?.let { value ->
                                            hours = value.coerceIn(1, 168) // 1小时到7天
                                        }
                                    },
                                    modifier = Modifier.width(80.dp),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    singleLine = true
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "小时未使用",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                CleanupRuleType.MEMORY_HEAVY_APPS -> {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Text(
                                text = "参数配置",
                                style = MaterialTheme.typography.titleSmall
                            )

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "内存占用超过",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                OutlinedTextField(
                                    value = memoryThreshold.toString(),
                                    onValueChange = {
                                        it.toIntOrNull()?.let { value ->
                                            memoryThreshold = value.coerceIn(50, 2048) // 50MB到2GB
                                        }
                                    },
                                    modifier = Modifier.width(100.dp),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    singleLine = true
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "MB",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                CleanupRuleType.FREQUENT_APPS, CleanupRuleType.RARE_APPS -> {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Text(
                                text = "参数配置",
                                style = MaterialTheme.typography.titleSmall
                            )

                            // 时间范围选择
                            Text(
                                text = "统计时间范围：",
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                UsageTimeRange.values().forEach { range ->
                                    FilterChip(
                                        selected = timeRange == range,
                                        onClick = { timeRange = range },
                                        label = { Text(range.displayName) }
                                    )
                                }
                            }

                            // 启动次数阈值
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = if (selectedRuleType == CleanupRuleType.FREQUENT_APPS) "启动次数超过" else "启动次数少于",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                OutlinedTextField(
                                    value = launchCountThreshold.toString(),
                                    onValueChange = {
                                        it.toIntOrNull()?.let { value ->
                                            launchCountThreshold = value.coerceIn(1, 100)
                                        }
                                    },
                                    modifier = Modifier.width(80.dp),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    singleLine = true
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "次",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                else -> {
                    // 其他规则类型的说明
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "规则说明",
                                style = MaterialTheme.typography.titleSmall
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = selectedRuleType.description,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

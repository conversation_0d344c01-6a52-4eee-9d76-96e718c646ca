# 条件配置保存问题修复报告

## 问题描述

用户反馈："添加条件时没办法保存配置，新建快捷指令界面还是空的"

## 问题分析

通过日志分析发现了两个关键问题：

### 1. 存储域未注册错误
```
Unknown storage domain: UI_STATE
Unknown storage domain: NAVIGATION_DATA
```

### 2. 数据传递链路问题
- 详细配置界面使用错误的键名 `"configured_item_json"` 保存导航键
- 快捷指令表单界面监听 `"navigation_key"` 键名
- 键名不匹配导致配置数据无法正确传递

## 修复内容

### 修复1：注册缺失的存储域

**文件**：`app/src/main/java/com/my/backgroundmanager/storage/NativeTypeStorageManager.kt`

**问题**：`DOMAIN_CONFIGS`映射中缺少`UI_STATE`和`NAVIGATION_DATA`存储域的配置

**修复前**：
```kotlin
private val DOMAIN_CONFIGS = mapOf(
    StorageDomain.CONDITIONS to "background_manager_conditions",
    StorageDomain.TASKS to "background_manager_tasks",
    StorageDomain.QUICK_COMMANDS to "background_manager_quick_commands",
    StorageDomain.APP_LISTS to "background_manager_app_lists"
)
```

**修复后**：
```kotlin
private val DOMAIN_CONFIGS = mapOf(
    StorageDomain.CONDITIONS to "background_manager_conditions",
    StorageDomain.TASKS to "background_manager_tasks",
    StorageDomain.QUICK_COMMANDS to "background_manager_quick_commands",
    StorageDomain.APP_LISTS to "background_manager_app_lists",
    StorageDomain.UI_STATE to "background_manager_ui_state",
    StorageDomain.NAVIGATION_DATA to "background_manager_navigation_data"
)
```

### 修复2：统一数据回传键名

**文件**：`app/src/main/java/com/weinuo/quickcommands/MainActivity.kt`

**问题**：详细配置界面中多处使用了错误的键名`"configured_item_json"`

**修复内容**：将所有`"configured_item_json"`替换为`"navigation_key"`

**涉及的配置类型**：
- 通信状态条件配置
- 连接状态条件配置
- 传感器状态条件配置
- 应用状态条件配置
- 设备事件条件配置
- 时间条件配置
- 手动触发条件配置
- 电池状态条件配置
- 文件操作任务配置
- 相机任务配置
- 信息任务配置
- 连接任务配置
- 屏幕控制任务配置
- 设备设置任务配置
- 应用任务配置
- 音量任务配置
- 设备动作任务配置
- 日期时间任务配置

### 修复3：完善错误处理

**文件**：`app/src/main/java/com/weinuo/quickcommands/ui/screens/QuickCommandFormScreen.kt`

**修复内容**：
1. 修改`handleEditedConfigNative`函数返回`Boolean`值
2. 在各个配置分支中返回处理结果
3. 增强日志记录，便于问题排查
4. 修复语法错误（多余的大括号）

## 修复原理

### 数据流修复前
```
详细配置界面 → 保存配置数据到NavigationDataStorageManager →
设置savedStateHandle["configured_item_json"] = navigationKey →
快捷指令表单界面监听"navigation_key" →
❌ 键名不匹配，无法接收数据
```

### 数据流修复后
```
详细配置界面 → 保存配置数据到NavigationDataStorageManager →
设置savedStateHandle["navigation_key"] = navigationKey →
快捷指令表单界面监听"navigation_key" →
✅ 键名匹配，成功接收数据 →
从NavigationDataStorageManager加载配置数据 →
更新界面显示
```

## 预期效果

修复后，用户应该能够：

1. **正常添加条件**：点击"添加条件"按钮后，能够正常配置条件并返回表单界面
2. **正确显示配置**：配置完成后，新添加的条件应该正确显示在条件列表中
3. **数据持久化**：配置数据能够正确保存，界面刷新后仍然显示
4. **UI状态保存**：界面状态能够正确保存和恢复

## 测试建议

当有设备连接时，可以按以下步骤测试：

1. **测试添加条件**：
   - 进入快捷指令表单界面
   - 点击"添加条件"按钮
   - 选择任意条件类型（如WiFi状态）进行配置
   - 完成配置后检查是否返回表单界面
   - 验证新条件是否显示在列表中

2. **测试添加任务**：
   - 在同一界面点击"添加任务"按钮
   - 配置任务并完成
   - 验证任务是否正确显示

3. **测试保存快捷指令**：
   - 添加完条件和任务后
   - 填写快捷指令名称
   - 保存快捷指令
   - 验证是否能正常保存

## 技术要点

1. **存储域管理**：确保所有使用的存储域都在`DOMAIN_CONFIGS`中正确注册
2. **数据传递一致性**：保持导航数据传递中键名的一致性
3. **错误处理完善**：提供详细的日志记录和错误处理机制
4. **原生数据类型存储**：使用原生数据类型替代JSON序列化，提高稳定性

## 修复状态

✅ **已完成**：存储域注册修复
✅ **已完成**：数据回传键名统一
✅ **已完成**：错误处理完善
✅ **已完成**：代码编译通过

该修复解决了条件配置保存的根本问题，应该能够彻底解决用户反馈的问题。

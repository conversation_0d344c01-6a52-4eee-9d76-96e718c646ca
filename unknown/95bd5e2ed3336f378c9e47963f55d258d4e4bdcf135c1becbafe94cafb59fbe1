# 强制停止应用任务持久化存储和执行验证计划

## 验证目标
检查配置应用任务中的强制停止应用任务能否正确进行持久化存储和执行。

## 代码分析结果

### 1. 数据模型完整性 ✅
**位置**: `app/src/main/java/com/my/backgroundmanager/model/shared_task_list.kt`

**ApplicationTask数据类包含的强制停止相关字段**:
- `forceStopSelectedApps: List<SimpleAppInfo>` - 选中的应用列表
- `skipForegroundApp: Boolean` - 跳过前台应用
- `skipMusicPlayingApp: Boolean` - 跳过音乐播放应用
- `skipVpnApp: Boolean` - 跳过VPN应用
- `selectedVpnApps: List<SimpleAppInfo>` - VPN应用列表
- `autoIncludeNewApps: Boolean` - 自动包含新安装应用
- `appSortingStrategy: AppSortingStrategy` - 应用排序策略
- `enableMemoryThresholdCheck: Boolean` - 内存阈值检查
- `memoryThreshold: Int` - 内存阈值
- `memoryThresholdIsPercentage: Boolean` - 内存阈值是否为百分比
- 其他高级配置选项

### 2. 序列化机制 ✅
**位置**: `app/src/main/java/com/my/backgroundmanager/model/shared_task_list.kt` (行3489-3595)

**SimpleAppInfo序列化处理**:
```kotlin
forceStopSelectedApps = (params["forceStopSelectedApps"] as? List<Map<String, Any>>)?.map { appMap ->
    SimpleAppInfo(
        packageName = appMap["packageName"] as? String ?: "",
        appName = appMap["appName"] as? String ?: "",
        isSystemApp = appMap["isSystemApp"] as? Boolean ?: false
    )
} ?: emptyList()
```

**关键特点**:
- 使用Map格式序列化SimpleAppInfo，避免Parcelable问题
- 支持createTaskFromJson和createOrUpdateTask两种创建方式
- 包含完整的字段映射和默认值处理

### 3. 持久化存储机制 ✅
**位置**: `app/src/main/java/com/my/backgroundmanager/data/QuickCommandRepository.kt`

**存储流程**:
1. ApplicationTask通过Gson序列化为JSON
2. 使用自定义TaskSerializer处理复杂对象
3. 保存到SharedPreferences中
4. 支持序列化验证和错误处理

### 4. 执行机制 ✅
**位置**: `app/src/main/java/com/my/backgroundmanager/execution/SharedExecutionHandler.kt`

**执行流程**:
1. `executeApplicationTask()` - 应用任务入口 (行7137)
2. `executeForceStopApp()` - 强制停止应用处理 (行7747)
3. `forceStopSelectedAppsWithAdvancedOptions()` - 高级选项处理 (行8060)
4. `forceStopSingleApp()` - 单个应用停止 (行8936)

**执行特点**:
- 支持多种跳过条件（前台、音乐、VPN应用）
- 支持内存阈值检查
- 支持应用排序策略
- 双重权限方案：ActivityManager反射 + Shizuku备用
- 完整的日志记录和错误处理

### 5. 配置界面集成 ✅
**位置**: `app/src/main/java/com/my/backgroundmanager/ui/configuration/ApplicationTaskConfigProvider.kt`

**配置创建**:
```kotlin
val task = ApplicationTask(
    operation = operation,
    forceStopSelectedApps = selectedApps,
    skipForegroundApp = skipForegroundApp,
    skipMusicPlayingApp = skipMusicPlayingApp,
    skipVpnApp = skipVpnApp,
    selectedVpnApps = selectedVpnApps,
    // ... 其他配置选项
)
```

## 验证测试方案

### 测试1: 配置创建和保存
1. 打开应用程序任务配置
2. 选择"强制停止应用"
3. 配置应用选择、跳过选项、高级设置
4. 保存配置
5. **验证点**: 检查SharedPreferences中是否正确保存了JSON数据

### 测试2: 配置加载和恢复
1. 重启应用
2. 打开已保存的快捷指令
3. 编辑强制停止应用任务
4. **验证点**: 检查所有配置选项是否正确恢复

### 测试3: 手动执行测试
1. 创建包含强制停止应用任务的快捷指令
2. 手动触发执行
3. **验证点**: 检查任务是否按配置正确执行

### 测试4: 序列化完整性测试
1. 创建复杂的强制停止应用配置（多个应用、多种跳过条件）
2. 保存并重新加载
3. **验证点**: 检查所有配置细节是否完整保持

## 潜在问题点

### 1. SimpleAppInfo图标处理
- **问题**: Drawable图标无法序列化
- **现状**: 已通过注释说明需要重新加载图标
- **影响**: 不影响核心功能，仅影响显示

### 2. 权限依赖
- **问题**: 强制停止需要系统权限
- **现状**: 已实现双重方案（反射+Shizuku）
- **影响**: 在无权限环境下可能执行失败

### 3. 应用状态检测
- **问题**: 前台应用、音乐播放检测的准确性
- **现状**: 已实现相关检测逻辑
- **影响**: 可能存在检测不准确的边缘情况

## 结论

基于代码分析，强制停止应用任务的持久化存储和执行机制已经实现得相当完整：

✅ **数据模型完整** - 包含所有必要字段
✅ **序列化机制健全** - 正确处理复杂对象序列化
✅ **持久化存储可靠** - 使用成熟的SharedPreferences + Gson方案
✅ **执行逻辑完善** - 支持多种配置选项和权限方案
✅ **错误处理充分** - 包含日志记录和异常处理

**建议**: 可以进行实际的端到端测试来验证功能的完整性，特别是在不同权限环境下的表现。

# 手势录制编辑功能实现指南

## 功能概述

本次更新成功实现了自动点击回放录制完成后支持编辑已录制的手势操作功能，包括：

### 核心功能
1. **录制完成后自动跳转编辑界面** - 录制完成后直接进入编辑模式，无需额外操作
2. **延迟时间调整** - 支持精确调整每个事件的延迟时间，提供预设值和自定义输入
3. **事件删除** - 可以删除不需要的手势事件
4. **延迟插入** - 在任意位置插入延迟等待事件
5. **撤销/重做** - 支持编辑操作的撤销和重做
6. **性能影响提示** - 显示不同延迟设置对性能和耗电的影响

### 界面特性
- **全屏编辑界面** - 遵循Material Design 3设计规范
- **直观的事件列表** - 清晰显示每个手势事件的详细信息
- **实时预览** - 编辑过程中实时显示修改效果
- **智能验证** - 自动验证输入参数的有效性

## 技术实现详情

### 1. 核心文件结构

```
app/src/main/java/com/weinuo/quickcommands/
├── model/
│   └── GestureEditEvent.kt                    # 手势编辑相关数据模型
├── ui/recording/
│   ├── GestureRecordingEditActivity.kt        # 编辑界面Activity
│   ├── GestureRecordingEditScreen.kt          # 编辑界面UI组件
│   ├── GestureRecordingEditViewModel.kt       # 编辑逻辑ViewModel
│   └── DelayAdjustmentDialog.kt               # 延迟调整对话框
└── navigation/
    └── Navigation.kt                          # 导航路由配置
```

### 2. 数据模型设计

#### 手势编辑事件类型
```kotlin
enum class GestureEditEventType {
    ADJUST_DELAY,      // 调整延迟时间
    DELETE_EVENT,      // 删除事件
    MOVE_EVENT,        // 移动事件位置
    DUPLICATE_EVENT,   // 复制事件
    INSERT_DELAY       // 插入延迟
}
```

#### 编辑状态管理
```kotlin
data class GestureEditState(
    val selectedEventIndex: Int = -1,                    // 当前选中的事件索引
    val editHistory: List<GestureEditOperation> = emptyList(), // 编辑历史
    val currentHistoryIndex: Int = -1,                   // 当前历史索引
    val isModified: Boolean = false                      // 是否已修改
)
```

#### 延迟调整配置
```kotlin
data class DelayAdjustmentConfig(
    val minDelay: Long = 0L,        // 最小延迟时间
    val maxDelay: Long = 10000L,    // 最大延迟时间
    val stepSize: Long = 100L,      // 调整步长
    val defaultDelay: Long = 500L   // 默认延迟时间
)
```

### 3. 界面组件架构

#### 主编辑界面 (GestureRecordingEditScreen)
- **顶部工具栏**: 包含返回、撤销、重做、保存按钮
- **录制信息头部**: 显示录制名称、描述、事件数量等基本信息
- **事件列表**: 可滚动的事件卡片列表，支持选择和操作
- **浮动操作按钮**: 快速插入延迟事件

#### 事件编辑卡片 (EventEditCard)
- **事件类型标识**: 图标和颜色区分不同事件类型
- **事件详细信息**: 时间戳、持续时间、触摸点位置等
- **操作按钮**: 调整延迟、删除事件等快捷操作

#### 延迟调整对话框 (DelayAdjustmentDialog)
- **数字输入框**: 支持直接输入延迟时间
- **预设快捷按钮**: 常用延迟时间的快速选择
- **性能影响提示**: 显示不同延迟对性能和耗电的影响
- **实时验证**: 输入验证和错误提示

### 4. 导航流程设计

#### 全屏录制到编辑的流程
```
GestureRecordingActivity (全屏录制)
    ↓ 录制完成
GestureRecordingEditActivity (编辑)
    ↓ 编辑完成
返回调用方 (配置界面)
```

#### 悬浮窗录制到编辑的流程
```
FloatingRecordingService (悬浮窗录制)
    ↓ 录制完成，直接启动编辑界面
GestureRecordingEditActivity (编辑)
    ↓ 编辑完成，更新SharedPreferences
配置界面轮询检测到编辑完成
```

#### 导航路由配置
```kotlin
object GestureRecordingEdit : Screen(
    route = "gesture_recording_edit/{recordingId}",
    titleResId = R.string.gesture_recording_edit,
    selectedIcon = Icons.Filled.Settings,
    unselectedIcon = Icons.Outlined.Settings
) {
    fun createRoute(recordingId: String): String {
        return "gesture_recording_edit/$recordingId"
    }
}
```

### 5. 核心功能实现

#### 悬浮窗录制完成后自动启动编辑界面
```kotlin
// FloatingRecordingService.kt
private fun startEditActivity(recordingId: String) {
    try {
        val editIntent = Intent(this, GestureRecordingEditActivity::class.java).apply {
            putExtra(GestureRecordingEditActivity.EXTRA_RECORDING_ID, recordingId)
            putExtra(GestureRecordingEditActivity.EXTRA_RETURN_RESULT, false)
            // 添加必要的标志以从服务启动Activity
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        }

        startActivity(editIntent)
        Log.d(TAG, "已启动手势录制编辑界面: $recordingId")
    } catch (e: Exception) {
        Log.e(TAG, "启动手势录制编辑界面失败", e)
    }
}
```

#### 编辑完成后更新SharedPreferences
```kotlin
// GestureRecordingEditActivity.kt
onSaveCompleted = {
    if (shouldReturnResult) {
        // 返回编辑后的录制ID
        val resultIntent = Intent().apply {
            putExtra(EXTRA_RECORDING_ID, recordingId)
        }
        setResult(Activity.RESULT_OK, resultIntent)
    } else {
        // 非返回结果模式，更新SharedPreferences供配置界面获取
        val sharedPrefs = getSharedPreferences("floating_recording_result", MODE_PRIVATE)
        sharedPrefs.edit()
            .putString("latest_recording_id", recordingId)
            .putLong("recording_timestamp", System.currentTimeMillis())
            .putBoolean("edit_completed", true)
            .apply()
    }
    finish()
}
```

#### 延迟时间调整
```kotlin
fun adjustEventDelay(eventIndex: Int, newDelay: Long) {
    val config = editConfig.value.delayConfig
    val adjustedDelay = config.adjustToStep(newDelay)

    if (!config.isValidDelay(adjustedDelay)) {
        showError("延迟时间必须在有效范围内")
        return
    }

    // 更新事件并记录历史
    updateEventWithHistory(eventIndex, adjustedDelay)
}
```

#### 撤销/重做机制
```kotlin
private fun updateRecordingWithHistory(
    newRecording: GestureRecording,
    operation: GestureEditOperation
) {
    currentRecording.value = newRecording

    val newHistory = editHistory.take(currentHistoryIndex + 1) + operation
    editState.value = editState.value.copy(
        editHistory = newHistory,
        currentHistoryIndex = newHistory.size - 1,
        isModified = true
    )
}
```

#### 性能影响评估
```kotlin
private fun getPerformanceImpact(delay: Long): Triple<String, String, Color> {
    return when {
        delay < 100 -> Triple("高", "响应快速，但可能增加耗电", MaterialTheme.colorScheme.error)
        delay < 500 -> Triple("中", "平衡响应速度和耗电", MaterialTheme.colorScheme.primary)
        delay < 2000 -> Triple("低", "节省电量，响应较慢", Color(0xFF4CAF50))
        else -> Triple("极低", "最省电，响应很慢", Color(0xFF4CAF50))
    }
}
```

## 使用指南

### 1. 基本使用流程

#### 全屏录制模式
1. **开始录制**: 在自动点击器配置中选择"全屏录制"模式
2. **录制手势**: 在专用录制界面执行需要的手势操作
3. **完成录制**: 点击停止录制按钮
4. **自动跳转**: 系统自动跳转到编辑界面
5. **编辑操作**: 根据需要调整延迟、删除事件等
6. **保存完成**: 点击保存按钮完成编辑

#### 悬浮窗录制模式（推荐）
1. **开始录制**: 在自动点击器配置中选择"悬浮窗录制"模式
2. **录制手势**: 通过悬浮按钮在任意应用中录制手势操作
3. **完成录制**: 点击悬浮按钮的保存功能
4. **自动跳转**: 系统自动启动编辑界面（无需通知）
5. **编辑操作**: 根据需要调整延迟、删除事件等
6. **保存完成**: 点击保存按钮，结果自动返回配置界面

**悬浮窗录制模式的优势**:
- ✅ **无需通知权限**: 直接启动编辑界面，不依赖系统通知
- ✅ **真实环境录制**: 可以在实际应用中录制真实的操作流程
- ✅ **更好的兼容性**: 适用于各种Android定制系统
- ✅ **无缝体验**: 录制完成后立即进入编辑模式

### 2. 延迟时间调整

#### 快捷设置
- **100ms**: 快速响应，适合连续操作
- **500ms**: 平衡选择，适合大多数场景
- **1000ms**: 稳定操作，适合需要等待的场景
- **2000ms**: 慢速操作，适合复杂界面加载
- **5000ms**: 长时间等待，适合网络请求等

#### 自定义输入
- 支持0-10000ms范围内的任意数值
- 自动调整到100ms的倍数
- 实时显示性能影响提示

### 3. 性能优化建议

#### 延迟时间选择原则
- **快速操作** (100-300ms): 适合简单点击，但会增加耗电
- **标准操作** (500-1000ms): 平衡性能和稳定性，推荐使用
- **稳定操作** (1000-3000ms): 适合复杂界面，省电但响应慢
- **等待操作** (3000ms+): 适合网络请求等长时间等待

#### 事件优化建议
- 删除不必要的中间事件
- 合并相近的延迟事件
- 根据实际需要调整延迟时间
- 避免过于频繁的操作

## 扩展性设计

### 1. 支持的扩展功能

- **事件移动**: 调整事件执行顺序
- **事件复制**: 复制现有事件到其他位置
- **批量编辑**: 同时编辑多个事件
- **模板保存**: 保存常用的编辑模板
- **预览功能**: 实时预览编辑效果

### 2. 架构扩展点

- **编辑操作类型**: 可以轻松添加新的编辑操作
- **验证规则**: 支持自定义验证逻辑
- **UI主题**: 支持不同的界面主题
- **存储格式**: 兼容未来的存储格式升级

### 3. 集成其他功能

- **与录制模式集成**: 支持不同录制模式的编辑
- **与回放功能集成**: 编辑后直接预览回放效果
- **与配置系统集成**: 编辑结果自动应用到配置

## 最佳实践

### ✅ 推荐做法

1. **使用全屏界面**: 提供更好的编辑体验
2. **实时验证输入**: 避免无效的编辑操作
3. **提供撤销功能**: 让用户可以安全地尝试编辑
4. **显示性能提示**: 帮助用户做出明智的选择
5. **保持编辑历史**: 支持复杂的编辑流程
6. **自动保存状态**: 避免意外丢失编辑内容

### ❌ 避免的问题

1. **不要使用对话框**: 避免嵌套布局问题
2. **不要忽略验证**: 确保输入参数的有效性
3. **不要丢失编辑历史**: 保持撤销/重做的完整性
4. **不要忽略性能影响**: 向用户说明不同设置的影响
5. **不要强制特定延迟**: 给用户充分的自定义空间

## 故障排除

### 常见问题

**问题**: 编辑界面无法加载录制数据
**解决**: 检查录制ID是否正确，确保录制数据已正确保存

**问题**: 延迟调整无效
**解决**: 确认输入值在有效范围内，检查步长调整逻辑

**问题**: 撤销/重做功能异常
**解决**: 检查编辑历史的记录和索引管理

**问题**: 保存编辑结果失败
**解决**: 检查存储权限和磁盘空间，确认数据格式正确

### 调试技巧

1. **查看日志**: 关注GestureRecordingEditVM标签的日志
2. **检查状态**: 验证editState的各个字段值
3. **测试边界**: 测试最小/最大延迟值的处理
4. **验证导航**: 确认路由参数传递正确

这个实现提供了一个完整、可扩展、用户体验良好的手势录制编辑解决方案，可以作为类似功能实现的参考模板。

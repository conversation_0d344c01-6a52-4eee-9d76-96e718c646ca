---------------------------- PROCESS STARTED (4372) for package com.weinuo.quickcommands ----------------------------
2025-07-28 13:42:45.951  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/os/ServiceManager;->getService(Ljava/lang/String;)Landroid/os/IBinder; (light greylist, reflection)
2025-07-28 13:42:45.952  4372-4372  ShizukuProvider         com.weinuo.quickcommands             D  Initialize Sui: false
2025-07-28 13:42:45.954  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/os/Trace;->TRACE_TAG_APP:J (light greylist, reflection)
2025-07-28 13:42:45.954  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/os/Trace;->isTagEnabled(J)Z (light greylist, reflection)
2025-07-28 13:42:45.976  4372-4372  LanguageManager         com.weinuo.quickcommands             D  Language cache refreshed: system
2025-07-28 13:42:45.976  4372-4372  MainActivity            com.weinuo.quickcommands             D  Language context applied successfully
2025-07-28 13:42:45.995  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/content/Context;->getOpPackageName()Ljava/lang/String; (light greylist, linking)
2025-07-28 13:42:46.037  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  Error checking Shizuku permission
                                                                                                    java.lang.IllegalStateException: binder haven't been received
                                                                                                    	at rikka.shizuku.Shizuku.requireService(Shizuku.java:430)
                                                                                                    	at rikka.shizuku.Shizuku.checkSelfPermission(Shizuku.java:868)
                                                                                                    	at com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission(ShizukuManager.kt:42)
                                                                                                    	at com.weinuo.quickcommands.permission.GlobalPermissionManager.checkPermissionGranted(GlobalPermissionManager.kt:276)
                                                                                                    	at com.weinuo.quickcommands.permission.GlobalPermissionManager.<init>(GlobalPermissionManager.kt:66)
                                                                                                    	at com.weinuo.quickcommands.permission.GlobalPermissionManager.<init>(Unknown Source:0)
                                                                                                    	at com.weinuo.quickcommands.permission.GlobalPermissionManager$Companion.getInstance(GlobalPermissionManager.kt:23)
                                                                                                    	at com.weinuo.quickcommands.MainActivity.onCreate(MainActivity.kt:155)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:7284)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:7275)
                                                                                                    	at android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1271)
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:2938)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:3093)
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:78)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:108)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:68)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1823)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loop(Looper.java:193)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:6834)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-07-28 13:42:46.059  4372-4399  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.069  4372-4398  ThemePerformanceMonitor com.weinuo.quickcommands             D  开始主题性能监控，初始内存使用: 1MB
2025-07-28 13:42:46.072  4372-4399  AppLanguageManager      com.weinuo.quickcommands             D  Language initialized: system
2025-07-28 13:42:46.072  4372-4399  MainActivity            com.weinuo.quickcommands             D  Language settings initialized successfully
2025-07-28 13:42:46.074  4372-4372  ThemePerformanceManager com.weinuo.quickcommands             D  应用进入前台 - 启用性能优化
2025-07-28 13:42:46.075  4372-4399  ThemePerformanceManager com.weinuo.quickcommands             D  开始预热关键组件
2025-07-28 13:42:46.075  4372-4399  ThemePerformanceManager com.weinuo.quickcommands             D  组件预热完成
2025-07-28 13:42:46.084  4372-4398  ThemePerformanceManager com.weinuo.quickcommands             E  性能管理器初始化失败
                                                                                                    java.lang.IllegalStateException: Method addObserver must be called on the main thread
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.enforceMainThreadIfNeeded(LifecycleRegistry.jvm.kt:297)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.addObserver(LifecycleRegistry.jvm.kt:172)
                                                                                                    	at com.weinuo.quickcommands.ui.theme.manager.ThemeManager$Companion.getInstance(ThemeManager.kt:49)
                                                                                                    	at com.weinuo.quickcommands.ui.theme.manager.ThemePerformanceManager$initializeManagers$1.invokeSuspend(ThemePerformanceManager.kt:60)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-28 13:42:46.107  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  Error checking Shizuku permission
                                                                                                    java.lang.IllegalStateException: binder haven't been received
                                                                                                    	at rikka.shizuku.Shizuku.requireService(Shizuku.java:430)
                                                                                                    	at rikka.shizuku.Shizuku.checkSelfPermission(Shizuku.java:868)
                                                                                                    	at com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission(ShizukuManager.kt:42)
                                                                                                    	at com.weinuo.quickcommands.permission.GlobalPermissionManager.checkPermissionGranted(GlobalPermissionManager.kt:276)
                                                                                                    	at com.weinuo.quickcommands.permission.GlobalPermissionManager.refreshPermissionStates(GlobalPermissionManager.kt:383)
                                                                                                    	at com.weinuo.quickcommands.MainActivity.onResume(MainActivity.kt:285)
                                                                                                    	at android.app.Instrumentation.callActivityOnResume(Instrumentation.java:1412)
                                                                                                    	at android.app.Activity.performResume(Activity.java:7440)
                                                                                                    	at android.app.ActivityThread.performResumeActivity(ActivityThread.java:3821)
                                                                                                    	at android.app.ActivityThread.handleResumeActivity(ActivityThread.java:3861)
                                                                                                    	at android.app.servertransaction.ResumeActivityItem.execute(ResumeActivityItem.java:51)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeLifecycleState(TransactionExecutor.java:145)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:70)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1823)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loop(Looper.java:193)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:6834)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-07-28 13:42:46.111  4372-4372  OpenGLRenderer          com.weinuo.quickcommands             D  Skia GL Pipeline
2025-07-28 13:42:46.230  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.231  4372-4372  MainActivity            com.weinuo.quickcommands             D  Starting quick command services...
2025-07-28 13:42:46.240  4372-4372  MainActivity            com.weinuo.quickcommands             D  QuickCommandsService started for quick commands
2025-07-28 13:42:46.249  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: app_state
2025-07-28 13:42:46.250  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: time_based
2025-07-28 13:42:46.250  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: connection_state
2025-07-28 13:42:46.252  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: device_event
2025-07-28 13:42:46.252  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: manual_trigger
2025-07-28 13:42:46.255  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: communication_state
2025-07-28 13:42:46.256  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: sensor_state
2025-07-28 13:42:46.256  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  默认条件适配器注册完成，共注册 7 个适配器
2025-07-28 13:42:46.257  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: phone
2025-07-28 13:42:46.258  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: media
2025-07-28 13:42:46.258  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: application
2025-07-28 13:42:46.259  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: volume
2025-07-28 13:42:46.260  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: connectivity
2025-07-28 13:42:46.260  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: date_time
2025-07-28 13:42:46.260  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: device_action
2025-07-28 13:42:46.261  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: information
2025-07-28 13:42:46.261  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: camera
2025-07-28 13:42:46.262  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: file_operation
2025-07-28 13:42:46.263  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: screen_control
2025-07-28 13:42:46.263  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: notification
2025-07-28 13:42:46.264  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: device_settings
2025-07-28 13:42:46.265  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: location
2025-07-28 13:42:46.265  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  默认任务适配器注册完成，共注册 14 个适配器
2025-07-28 13:42:46.265  4372-4372  QuickComma...oordinator com.weinuo.quickcommands             D  开始加载所有快捷指令
2025-07-28 13:42:46.266  4372-4372  QuickComma...oordinator com.weinuo.quickcommands             D  加载所有快捷指令完成: 0/0
2025-07-28 13:42:46.266  4372-4372  QuickCommandRepository  com.weinuo.quickcommands             D  Loaded 0 commands to flow
2025-07-28 13:42:46.266  4372-4372  QuickCommandRepository  com.weinuo.quickcommands             D  QuickCommandRepository initialized with singleton pattern
2025-07-28 13:42:46.266  4372-4372  QuickCommandRepository  com.weinuo.quickcommands             D  QuickCommandRepository singleton instance created
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: app_state
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: time_based
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: connection_state
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: device_event
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: manual_trigger
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: communication_state
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  已注册条件适配器: sensor_state
2025-07-28 13:42:46.267  4372-4372  ConditionAdapterManager com.weinuo.quickcommands             D  默认条件适配器注册完成，共注册 7 个适配器
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: phone
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: media
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: application
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: volume
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: connectivity
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: date_time
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: device_action
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: information
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: camera
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: file_operation
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: screen_control
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: notification
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: device_settings
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  已注册任务适配器: location
2025-07-28 13:42:46.267  4372-4372  TaskAdapterManager      com.weinuo.quickcommands             D  默认任务适配器注册完成，共注册 14 个适配器
2025-07-28 13:42:46.274  4372-4372  HazeManager             com.weinuo.quickcommands             D  创建全局HazeState，强制启用模糊效果，Android API: 28
2025-07-28 13:42:46.275  4372-4372  HazeManager             com.weinuo.quickcommands             D  模糊实现类型: Experimental RenderScript (Android 11 and below)
2025-07-28 13:42:46.275  4372-4372  HazeManager             com.weinuo.quickcommands             W  Android 28 使用实验性RenderScript模糊，可能存在性能影响
2025-07-28 13:42:46.275  4372-4372  HazeManager             com.weinuo.quickcommands             D  HazeManager实例已创建，Android API: 28
2025-07-28 13:42:46.297  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/graphics/Insets;->left:I (light greylist, linking)
2025-07-28 13:42:46.297  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/graphics/Insets;->top:I (light greylist, linking)
2025-07-28 13:42:46.297  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/graphics/Insets;->right:I (light greylist, linking)
2025-07-28 13:42:46.297  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/graphics/Insets;->bottom:I (light greylist, linking)
2025-07-28 13:42:46.353  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/view/WindowInsets;->CONSUMED:Landroid/view/WindowInsets; (light greylist, reflection)
2025-07-28 13:42:46.354  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/View;->getViewRootImpl()Landroid/view/ViewRootImpl; (light greylist, reflection)
2025-07-28 13:42:46.354  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/view/View$AttachInfo;->mVisibleInsets:Landroid/graphics/Rect; (light greylist, reflection)
2025-07-28 13:42:46.354  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden field Landroid/view/ViewRootImpl;->mAttachInfo:Landroid/view/View$AttachInfo; (light greylist, reflection)
2025-07-28 13:42:46.372  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/text/SpannableStringInternal;->length()I (light greylist, linking)
2025-07-28 13:42:46.431  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.434  4372-4372  chatty                  com.weinuo.quickcommands             I  uid=10058(com.weinuo.quickcommands) identical 1 line
2025-07-28 13:42:46.436  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.440  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  Error checking Shizuku permission
                                                                                                    java.lang.IllegalStateException: binder haven't been received
                                                                                                    	at rikka.shizuku.Shizuku.requireService(Shizuku.java:430)
                                                                                                    	at rikka.shizuku.Shizuku.checkSelfPermission(Shizuku.java:868)
                                                                                                    	at com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission(ShizukuManager.kt:42)
                                                                                                    	at com.weinuo.quickcommands.data.PhoneCheckupRepository.hasShizukuPermission(PhoneCheckupRepository.kt:346)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel$checkPermissions$1.invokeSuspend(PhoneCheckupViewModel.kt:362)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:363)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:26)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable$default(Cancellable.kt:21)
                                                                                                    	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:88)
                                                                                                    	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:123)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:52)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:43)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.checkPermissions(PhoneCheckupViewModel.kt:359)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.<init>(PhoneCheckupViewModel.kt:138)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.oceanblue.OceanBluePhoneCheckupScreenKt.OceanBluePhoneCheckupScreen(OceanBluePhoneCheckupScreen.kt:59)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.themed.ThemedPhoneCheckupScreenKt.ThemedPhoneCheckupScreen(ThemedPhoneCheckupScreen.kt:32)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:617)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:616)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:308)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:306)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:384)
                                                                                                    	at androidx.compose.runtime.saveable.SaveableStateHolderImpl.SaveableStateProvider(SaveableStateHolder.kt:79)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.SaveableStateProvider(NavBackStackEntryProvider.kt:65)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.access$SaveableStateProvider(NavBackStackEntryProvider.kt:1)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:52)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:364)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.LocalOwnersProvider(NavBackStackEntryProvider.kt:47)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:306)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:295)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
2025-07-28 13:42:46.440  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:862)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:852)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedVisibilityKt.AnimatedEnterExitImpl(AnimatedVisibility.kt:755)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:834)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:817)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt.AnimatedContent(AnimatedContent.kt:872)
                                                                                                    	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:273)
                                                                                                    	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:128)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:593)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:591)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:38)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:35)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:261)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:238)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:1042)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:523)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.Utils_jvmKt.invokeComposable(Utils.jvm.kt:27)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.doCompose-aFTiNEg(Composer.kt:3694)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.composeContent--ZbOJvo$runtime_release(Composer.kt:3616)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeContent(Composition.kt:792)
                                                                                                    	at androidx.compose.runtime.Recomposer.composeInitial$runtime_release(Recomposer.kt:1132)
                                                                                                    	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:4034)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeInitial(Composition.kt:677)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.setContent(Composition.kt:616)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcomposeInto(SubcomposeLayout.kt:544)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:514)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:504)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:490)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$Scope.subcompose(SubcomposeLayout.kt:926)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke-0kLqBqw(Scaffold.kt:238)
2025-07-28 13:42:46.440  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke(Scaffold.kt:140)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:754)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.foundation.layout.BoxMeasurePolicy.measure-3p2s80s(Box.kt:145)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:642)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:190)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.ui.layout.RootMeasurePolicy.measure-3p2s80s(RootMeasurePolicy.kt:37)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2496)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1212)
2025-07-28 13:42:46.441  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:364)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureOnly(MeasureAndLayoutDelegate.kt:629)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureOnly(MeasureAndLayoutDelegate.kt:419)
                                                                                                    	at androidx.compose.ui.platform.AndroidComposeView.onMeasure(AndroidComposeView.android.kt:1649)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.internalOnMeasure$ui_release(ComposeView.android.kt:299)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.onMeasure(ComposeView.android.kt:286)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.LinearLayout.measureChildBeforeLayout(LinearLayout.java:1535)
                                                                                                    	at android.widget.LinearLayout.measureVertical(LinearLayout.java:825)
                                                                                                    	at android.widget.LinearLayout.onMeasure(LinearLayout.java:704)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at com.android.internal.policy.DecorView.onMeasure(DecorView.java:716)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewRootImpl.performMeasure(ViewRootImpl.java:2725)
                                                                                                    	at android.view.ViewRootImpl.measureHierarchy(ViewRootImpl.java:1575)
                                                                                                    	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:1858)
                                                                                                    	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:1463)
                                                                                                    	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:7190)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:949)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:761)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:696)
                                                                                                    	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:935)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:873)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loop(Looper.java:193)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:6834)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-07-28 13:42:46.444  4372-4372  KillBgProcessesPermUtil com.weinuo.quickcommands             D  KILL_BACKGROUND_PROCESSES permission check result: true
2025-07-28 13:42:46.445  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  Error checking Shizuku permission
                                                                                                    java.lang.IllegalStateException: binder haven't been received
                                                                                                    	at rikka.shizuku.Shizuku.requireService(Shizuku.java:430)
                                                                                                    	at rikka.shizuku.Shizuku.checkSelfPermission(Shizuku.java:868)
                                                                                                    	at com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission(ShizukuManager.kt:42)
                                                                                                    	at com.weinuo.quickcommands.data.PhoneCheckupRepository.hasShizukuPermission(PhoneCheckupRepository.kt:346)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.checkShizukuTipCard(PhoneCheckupViewModel.kt:385)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.access$checkShizukuTipCard(PhoneCheckupViewModel.kt:50)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel$checkPermissions$1.invokeSuspend(PhoneCheckupViewModel.kt:370)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:363)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:26)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable$default(Cancellable.kt:21)
                                                                                                    	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:88)
                                                                                                    	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:123)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:52)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:43)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.checkPermissions(PhoneCheckupViewModel.kt:359)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.<init>(PhoneCheckupViewModel.kt:138)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.oceanblue.OceanBluePhoneCheckupScreenKt.OceanBluePhoneCheckupScreen(OceanBluePhoneCheckupScreen.kt:59)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.themed.ThemedPhoneCheckupScreenKt.ThemedPhoneCheckupScreen(ThemedPhoneCheckupScreen.kt:32)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:617)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:616)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:308)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:306)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:384)
                                                                                                    	at androidx.compose.runtime.saveable.SaveableStateHolderImpl.SaveableStateProvider(SaveableStateHolder.kt:79)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.SaveableStateProvider(NavBackStackEntryProvider.kt:65)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.access$SaveableStateProvider(NavBackStackEntryProvider.kt:1)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:52)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:364)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.LocalOwnersProvider(NavBackStackEntryProvider.kt:47)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:306)
2025-07-28 13:42:46.445  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:295)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:862)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:852)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedVisibilityKt.AnimatedEnterExitImpl(AnimatedVisibility.kt:755)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:834)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:817)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt.AnimatedContent(AnimatedContent.kt:872)
                                                                                                    	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:273)
                                                                                                    	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:128)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:593)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:591)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:38)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:35)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:261)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:238)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:1042)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:523)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.Utils_jvmKt.invokeComposable(Utils.jvm.kt:27)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.doCompose-aFTiNEg(Composer.kt:3694)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.composeContent--ZbOJvo$runtime_release(Composer.kt:3616)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeContent(Composition.kt:792)
                                                                                                    	at androidx.compose.runtime.Recomposer.composeInitial$runtime_release(Recomposer.kt:1132)
                                                                                                    	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:4034)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeInitial(Composition.kt:677)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.setContent(Composition.kt:616)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcomposeInto(SubcomposeLayout.kt:544)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:514)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:504)
2025-07-28 13:42:46.445  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:490)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$Scope.subcompose(SubcomposeLayout.kt:926)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke-0kLqBqw(Scaffold.kt:238)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke(Scaffold.kt:140)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:754)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.foundation.layout.BoxMeasurePolicy.measure-3p2s80s(Box.kt:145)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:642)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:190)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.ui.layout.RootMeasurePolicy.measure-3p2s80s(RootMeasurePolicy.kt:37)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2496)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
2025-07-28 13:42:46.445  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1212)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:364)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureOnly(MeasureAndLayoutDelegate.kt:629)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureOnly(MeasureAndLayoutDelegate.kt:419)
                                                                                                    	at androidx.compose.ui.platform.AndroidComposeView.onMeasure(AndroidComposeView.android.kt:1649)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.internalOnMeasure$ui_release(ComposeView.android.kt:299)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.onMeasure(ComposeView.android.kt:286)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.LinearLayout.measureChildBeforeLayout(LinearLayout.java:1535)
                                                                                                    	at android.widget.LinearLayout.measureVertical(LinearLayout.java:825)
                                                                                                    	at android.widget.LinearLayout.onMeasure(LinearLayout.java:704)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at com.android.internal.policy.DecorView.onMeasure(DecorView.java:716)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewRootImpl.performMeasure(ViewRootImpl.java:2725)
                                                                                                    	at android.view.ViewRootImpl.measureHierarchy(ViewRootImpl.java:1575)
                                                                                                    	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:1858)
                                                                                                    	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:1463)
                                                                                                    	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:7190)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:949)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:761)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:696)
                                                                                                    	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:935)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:873)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loop(Looper.java:193)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:6834)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-07-28 13:42:46.447  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Permissions checked - UsageStats: true, Shizuku: false, KillBgProcesses: true
2025-07-28 13:42:46.447  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  Error checking Shizuku permission
                                                                                                    java.lang.IllegalStateException: binder haven't been received
                                                                                                    	at rikka.shizuku.Shizuku.requireService(Shizuku.java:430)
                                                                                                    	at rikka.shizuku.Shizuku.checkSelfPermission(Shizuku.java:868)
                                                                                                    	at com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission(ShizukuManager.kt:42)
                                                                                                    	at com.weinuo.quickcommands.data.PhoneCheckupRepository.hasShizukuPermission(PhoneCheckupRepository.kt:346)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.checkShizukuTipCard(PhoneCheckupViewModel.kt:385)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.<init>(PhoneCheckupViewModel.kt:139)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.oceanblue.OceanBluePhoneCheckupScreenKt.OceanBluePhoneCheckupScreen(OceanBluePhoneCheckupScreen.kt:59)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.themed.ThemedPhoneCheckupScreenKt.ThemedPhoneCheckupScreen(ThemedPhoneCheckupScreen.kt:32)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:617)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:616)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:308)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:306)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:384)
                                                                                                    	at androidx.compose.runtime.saveable.SaveableStateHolderImpl.SaveableStateProvider(SaveableStateHolder.kt:79)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.SaveableStateProvider(NavBackStackEntryProvider.kt:65)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.access$SaveableStateProvider(NavBackStackEntryProvider.kt:1)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:52)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:364)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.LocalOwnersProvider(NavBackStackEntryProvider.kt:47)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:306)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:295)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:862)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:852)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedVisibilityKt.AnimatedEnterExitImpl(AnimatedVisibility.kt:755)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:834)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:817)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt.AnimatedContent(AnimatedContent.kt:872)
2025-07-28 13:42:46.447  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:273)
                                                                                                    	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:128)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:593)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:591)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:38)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:35)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:261)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:238)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:1042)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:523)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.Utils_jvmKt.invokeComposable(Utils.jvm.kt:27)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.doCompose-aFTiNEg(Composer.kt:3694)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.composeContent--ZbOJvo$runtime_release(Composer.kt:3616)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeContent(Composition.kt:792)
                                                                                                    	at androidx.compose.runtime.Recomposer.composeInitial$runtime_release(Recomposer.kt:1132)
                                                                                                    	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:4034)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeInitial(Composition.kt:677)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.setContent(Composition.kt:616)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcomposeInto(SubcomposeLayout.kt:544)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:514)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:504)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:490)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$Scope.subcompose(SubcomposeLayout.kt:926)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke-0kLqBqw(Scaffold.kt:238)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke(Scaffold.kt:140)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:754)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
2025-07-28 13:42:46.448  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.foundation.layout.BoxMeasurePolicy.measure-3p2s80s(Box.kt:145)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:642)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:190)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.ui.layout.RootMeasurePolicy.measure-3p2s80s(RootMeasurePolicy.kt:37)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2496)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1212)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:364)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureOnly(MeasureAndLayoutDelegate.kt:629)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureOnly(MeasureAndLayoutDelegate.kt:419)
                                                                                                    	at androidx.compose.ui.platform.AndroidComposeView.onMeasure(AndroidComposeView.android.kt:1649)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.internalOnMeasure$ui_release(ComposeView.android.kt:299)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.onMeasure(ComposeView.android.kt:286)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at android.view.View.measure(View.java:23183)
2025-07-28 13:42:46.448  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.LinearLayout.measureChildBeforeLayout(LinearLayout.java:1535)
                                                                                                    	at android.widget.LinearLayout.measureVertical(LinearLayout.java:825)
                                                                                                    	at android.widget.LinearLayout.onMeasure(LinearLayout.java:704)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at com.android.internal.policy.DecorView.onMeasure(DecorView.java:716)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewRootImpl.performMeasure(ViewRootImpl.java:2725)
                                                                                                    	at android.view.ViewRootImpl.measureHierarchy(ViewRootImpl.java:1575)
                                                                                                    	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:1858)
                                                                                                    	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:1463)
                                                                                                    	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:7190)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:949)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:761)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:696)
                                                                                                    	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:935)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:873)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loop(Looper.java:193)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:6834)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-07-28 13:42:46.449  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  Error checking Shizuku permission
                                                                                                    java.lang.IllegalStateException: binder haven't been received
                                                                                                    	at rikka.shizuku.Shizuku.requireService(Shizuku.java:430)
                                                                                                    	at rikka.shizuku.Shizuku.checkSelfPermission(Shizuku.java:868)
                                                                                                    	at com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission(ShizukuManager.kt:42)
                                                                                                    	at com.weinuo.quickcommands.data.PhoneCheckupRepository.hasShizukuPermission(PhoneCheckupRepository.kt:346)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel$startPermissionStateMonitoring$1$1.emit(PhoneCheckupViewModel.kt:428)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel$startPermissionStateMonitoring$1$1.emit(PhoneCheckupViewModel.kt:421)
                                                                                                    	at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:392)
                                                                                                    	at kotlinx.coroutines.flow.ReadonlyStateFlow.collect(Unknown Source:2)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel$startPermissionStateMonitoring$1.invokeSuspend(PhoneCheckupViewModel.kt:421)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:363)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:26)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable$default(Cancellable.kt:21)
                                                                                                    	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:88)
                                                                                                    	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:123)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:52)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:43)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.startPermissionStateMonitoring(PhoneCheckupViewModel.kt:420)
                                                                                                    	at com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel.<init>(PhoneCheckupViewModel.kt:143)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.oceanblue.OceanBluePhoneCheckupScreenKt.OceanBluePhoneCheckupScreen(OceanBluePhoneCheckupScreen.kt:59)
                                                                                                    	at com.weinuo.quickcommands.ui.screens.themed.ThemedPhoneCheckupScreenKt.ThemedPhoneCheckupScreen(ThemedPhoneCheckupScreen.kt:32)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:617)
                                                                                                    	at com.weinuo.quickcommands.ComposableSingletons$MainActivityKt$lambda-1$1.invoke(MainActivity.kt:616)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:308)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14$1.invoke(NavHost.kt:306)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:384)
                                                                                                    	at androidx.compose.runtime.saveable.SaveableStateHolderImpl.SaveableStateProvider(SaveableStateHolder.kt:79)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.SaveableStateProvider(NavBackStackEntryProvider.kt:65)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt.access$SaveableStateProvider(NavBackStackEntryProvider.kt:1)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:52)
                                                                                                    	at androidx.navigation.compose.NavBackStackEntryProviderKt$LocalOwnersProvider$1.invoke(NavBackStackEntryProvider.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:364)
2025-07-28 13:42:46.449  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.navigation.compose.NavBackStackEntryProviderKt.LocalOwnersProvider(NavBackStackEntryProvider.kt:47)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:306)
                                                                                                    	at androidx.navigation.compose.NavHostKt$NavHost$14.invoke(NavHost.kt:295)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:142)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:862)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$5.invoke(AnimatedContent.kt:852)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedVisibilityKt.AnimatedEnterExitImpl(AnimatedVisibility.kt:755)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:834)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1.invoke(AnimatedContent.kt:817)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt.AnimatedContent(AnimatedContent.kt:872)
                                                                                                    	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:273)
                                                                                                    	at androidx.navigation.compose.NavHostKt.NavHost(NavHost.kt:128)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:593)
                                                                                                    	at com.weinuo.quickcommands.MainActivityKt$MainScreen$2.invoke(MainActivity.kt:591)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:38)
                                                                                                    	at com.weinuo.quickcommands.ui.components.layered.LayeredMainLayoutKt$LayeredMainLayout$2.invoke(LayeredMainLayout.kt:35)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:130)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:261)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.invoke(Scaffold.kt:238)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:1042)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:523)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:121)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.kt:51)
                                                                                                    	at androidx.compose.runtime.internal.Utils_jvmKt.invokeComposable(Utils.jvm.kt:27)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.doCompose-aFTiNEg(Composer.kt:3694)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.composeContent--ZbOJvo$runtime_release(Composer.kt:3616)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeContent(Composition.kt:792)
                                                                                                    	at androidx.compose.runtime.Recomposer.composeInitial$runtime_release(Recomposer.kt:1132)
                                                                                                    	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:4034)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeInitial(Composition.kt:677)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.setContent(Composition.kt:616)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcomposeInto(SubcomposeLayout.kt:544)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:514)
2025-07-28 13:42:46.449  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:504)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:490)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$Scope.subcompose(SubcomposeLayout.kt:926)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke-0kLqBqw(Scaffold.kt:238)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1.invoke(Scaffold.kt:140)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:754)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.foundation.layout.BoxMeasurePolicy.measure-3p2s80s(Box.kt:145)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:642)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:190)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:501)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.measure-BRTryo0(MeasurePassDelegate.kt:450)
                                                                                                    	at androidx.compose.ui.layout.RootMeasurePolicy.measure-3p2s80s(RootMeasurePolicy.kt:37)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:128)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:169)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate$performMeasureBlock$1.invoke(MeasurePassDelegate.kt:168)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2496)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:460)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:244)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:124)
2025-07-28 13:42:46.449  4372-4372  ShizukuManager          com.weinuo.quickcommands             E  	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:107)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.performMeasure-BRTryo0$ui_release(MeasurePassDelegate.kt:422)
                                                                                                    	at androidx.compose.ui.node.MeasurePassDelegate.remeasure-BRTryo0(MeasurePassDelegate.kt:470)
                                                                                                    	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1212)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:364)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureOnly(MeasureAndLayoutDelegate.kt:629)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureOnly(MeasureAndLayoutDelegate.kt:419)
                                                                                                    	at androidx.compose.ui.platform.AndroidComposeView.onMeasure(AndroidComposeView.android.kt:1649)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.internalOnMeasure$ui_release(ComposeView.android.kt:299)
                                                                                                    	at androidx.compose.ui.platform.AbstractComposeView.onMeasure(ComposeView.android.kt:286)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.LinearLayout.measureChildBeforeLayout(LinearLayout.java:1535)
                                                                                                    	at android.widget.LinearLayout.measureVertical(LinearLayout.java:825)
                                                                                                    	at android.widget.LinearLayout.onMeasure(LinearLayout.java:704)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:6781)
                                                                                                    	at android.widget.FrameLayout.onMeasure(FrameLayout.java:185)
                                                                                                    	at com.android.internal.policy.DecorView.onMeasure(DecorView.java:716)
                                                                                                    	at android.view.View.measure(View.java:23183)
                                                                                                    	at android.view.ViewRootImpl.performMeasure(ViewRootImpl.java:2725)
                                                                                                    	at android.view.ViewRootImpl.measureHierarchy(ViewRootImpl.java:1575)
                                                                                                    	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:1858)
                                                                                                    	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:1463)
                                                                                                    	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:7190)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:949)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:761)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:696)
                                                                                                    	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:935)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:873)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loop(Looper.java:193)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:6834)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-07-28 13:42:46.452  4372-4372  KillBgProcessesPermUtil com.weinuo.quickcommands             D  KILL_BACKGROUND_PROCESSES permission check result: true
2025-07-28 13:42:46.453  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  ViewModel: Particle animation state changed to NORMAL
2025-07-28 13:42:46.487  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->create(Ljava/lang/String;Landroid/view/View;)Landroid/view/RenderNode; (light greylist, linking)
2025-07-28 13:42:46.487  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->getScaleX()F (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setClipToBounds(Z)Z (light greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setClipToOutline(Z)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  chatty                  com.weinuo.quickcommands             I  uid=10058(com.weinuo.quickcommands) identical 1 line
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setClipToOutline(Z)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setLayerType(I)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  chatty                  com.weinuo.quickcommands             I  uid=10058(com.weinuo.quickcommands) identical 1 line
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setLayerType(I)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->getMatrix(Landroid/graphics/Matrix;)V (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/DisplayListCanvas;->drawRenderNode(Landroid/view/RenderNode;)V (light greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->isValid()Z (light greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->start(II)Landroid/view/DisplayListCanvas; (light greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->end(Landroid/view/DisplayListCanvas;)V (light greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setAlpha(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setCameraDistance(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setLayerPaint(Landroid/graphics/Paint;)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setOutline(Landroid/graphics/Outline;)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setPivotX(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setPivotX(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setLeftTopRightBottom(IIII)Z (light greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setPivotX(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setRotationX(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setRotationY(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setRotation(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setScaleX(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setScaleY(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setElevation(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setTranslationX(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->setTranslationY(F)Z (dark greylist, linking)
2025-07-28 13:42:46.488  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/view/RenderNode;->getScaleX()F (dark greylist, linking)
2025-07-28 13:42:46.552  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.571  4372-4372  RenderThread            com.weinuo.quickcommands             I  type=1400 audit(0.0:1308): avc: denied { read write } for name="fastpipe" dev="tmpfs" ino=122 scontext=u:r:untrusted_app:s0:c58,c256,c512,c768 tcontext=u:object_r:device:s0 tclass=chr_file permissive=1
2025-07-28 13:42:46.571  4372-4372  RenderThread            com.weinuo.quickcommands             I  type=1400 audit(0.0:1308): avc: denied { open } for path="/dev/fastpipe" dev="tmpfs" ino=122 scontext=u:r:untrusted_app:s0:c58,c256,c512,c768 tcontext=u:object_r:device:s0 tclass=chr_file permissive=1
2025-07-28 13:42:46.579  4372-4403  <no-tag>                com.weinuo.quickcommands             I  fastpipe: Connect success
2025-07-28 13:42:46.580  4372-4403  <no-tag>                com.weinuo.quickcommands             I  fastpipe: Connect success
2025-07-28 13:42:46.580  4372-4403  HostConnection          com.weinuo.quickcommands             D  HostRPC::connect sucess: app=com.weinuo.quickcommands, pid=4372, tid=4403, this=0x7638774699c0
2025-07-28 13:42:46.581  4372-4403  HostConnection          com.weinuo.quickcommands             D  queryAndSetGLESMaxVersion select gles-version: 3.1 hostGLVersion:46 process:com.weinuo.quickcommands
2025-07-28 13:42:46.581  4372-4403  ConfigStore             com.weinuo.quickcommands             I  android::hardware::configstore::V1_0::ISurfaceFlingerConfigs::hasWideColorDisplay retrieved: 0
2025-07-28 13:42:46.581  4372-4403  ConfigStore             com.weinuo.quickcommands             I  android::hardware::configstore::V1_0::ISurfaceFlingerConfigs::hasHDRDisplay retrieved: 0
2025-07-28 13:42:46.581  4372-4403  OpenGLRenderer          com.weinuo.quickcommands             I  Initialized EGL, version 1.4
2025-07-28 13:42:46.581  4372-4403  OpenGLRenderer          com.weinuo.quickcommands             D  Swap behavior 1
2025-07-28 13:42:46.591  4372-4403  EGL_emulation           com.weinuo.quickcommands             D  eglCreateContext: 0x76389273cb00: maj 3 min 1 rcv 4
2025-07-28 13:42:46.618  4372-4403  eglCodecCommon          com.weinuo.quickcommands             E  glUtilsParamSize: unknow param 0x000082da
2025-07-28 13:42:46.618  4372-4403  eglCodecCommon          com.weinuo.quickcommands             E  glUtilsParamSize: unknow param 0x000082e5
2025-07-28 13:42:46.620  4372-4403  HostConnection          com.weinuo.quickcommands             D  ExtendedRCEncoderContext GL_VERSION return OpenGL ES 3.1 v1
2025-07-28 13:42:46.622  4372-4403  eglCodecCommon          com.weinuo.quickcommands             E  glUtilsParamSize: unknow param 0x00008c29
2025-07-28 13:42:46.622  4372-4403  eglCodecCommon          com.weinuo.quickcommands             E  glUtilsParamSize: unknow param 0x000087fe
2025-07-28 13:42:46.632  4372-4403  vndksupport             com.weinuo.quickcommands             D  Loading /vendor/lib64/hw/<EMAIL> from current namespace instead of sphal namespace.
2025-07-28 13:42:46.632  4372-4403  vndksupport             com.weinuo.quickcommands             D  Loading /vendor/lib64/hw/gralloc.default.so from current namespace instead of sphal namespace.
2025-07-28 13:42:46.637  4372-4403  EGL_emulation           com.weinuo.quickcommands             E  tid 4403: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-07-28 13:42:46.637  4372-4403  OpenGLRenderer          com.weinuo.quickcommands             W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x7638775a8480, error=EGL_BAD_MATCH
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  === 当前主题调试信息 ===
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  主题ID: ocean_blue
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  主题名称: 海洋蓝
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  设计方法: LAYERED_DESIGN
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  支持模糊: false
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  支持阴影: true
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  支持透明: false
2025-07-28 13:42:46.638  4372-4372  AppThemeProvider        com.weinuo.quickcommands             D  ========================
2025-07-28 13:42:46.642  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Saved global settings: GlobalSettings(widgetUpdateEnabled=false, widgetUpdateInterval=24, savedNetworkState=null, experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false, appLanguage=system, searchFieldPlaceholderFontWeight=medium, searchFieldIconWeight=medium, topAppBarHeight=55, topAppBarTitleFontSize=25, topAppBarTitleFontWeight=bold, topAppBarTitleVerticalOffset=0, topAppBarTitleHorizontalOffset=0, topAppBarType=standard, screenTitleFontSize=20, formSectionTitleFontSize=17, cardCornerRadius=20, cardDefaultHorizontalPadding=13, cardDefaultVerticalPadding=14, cardSettingsVerticalPadding=6, cardCompactHorizontalPadding=11, cardCompactVerticalPadding=4, cardLargeHorizontalPadding=19, cardLargeVerticalPadding=12, cardItemSpacing=8, cardSectionSpacing=20, cardContentVerticalSpacing=0, cardContentHorizontalSpacing=12, cardSelectedElevation=0, cardSelectedBorderWidth=3, cardTitleFontSize=15, cardTitleFontWeight=medium, cardContentFontSize=13, cardContentFontWeight=medium, cardIconSize=48, pageContentHorizontalPadding=16, pageSearchFieldMargin=16, pageHeaderSpacing=16, pageBottomPadding=88, pageScrollContentSpacing=8, uiSettingsItemVerticalPadding=12, uiDividerHorizontalPadding=0, uiSettingsCardPadding=16, uiSettingsItemSpacing=16, uiSettingsTitleSpacing=8, uiSettingsDescriptionSpacing=4, uiSettingsGroupTitleHorizontalPadding=13, uiSettingsGroupTitleTopPadding=30, uiSettingsGroupTitleBottomPadding=6, uiGlobalSettingsItemSpacing=0, uiDividerVisible=true, checkupDisplayDurationSeconds=5, fixedCheckupScoreEnabled=false, fixedCheckupScore=100, waterBallAdvancedMaterialEnabled=false, dialogOuterPadding=12, dialogIconBottomPadding=16, dialogTitleBottomPadding=16, dialogContentBottomPadding=4, dialogContentVerticalPadding=8, dialogInputBottomPadding=8, dialogButtonTopPadding=2, dialogButtonBottomPadding=14, dialogTitleFontSize=17, dialogDividerHorizontalPadding=12, skyBluePrimary=0xFF0A59F7, skyBlueOnPrimary=0xFFFFFFFF, skyBluePrimaryContainer=0x330A59F7, skyBlueOnPrimaryContainer=0xE5000000, skyBlueSecondary=0x99000000, skyBlueOnSecondary=0x99FFFFFF, skyBlueSecondaryContainer=0xFFF1F3F5, skyBlueOnSecondaryContainer=0x99000000, skyBlueTertiary=0x66000000, skyBlueOnTertiary=0x66FFFFFF, skyBlueTertiaryContainer=0xFFE5E5EA, skyBlueOnTertiaryContainer=0x66000000, skyBlueError=0xFFE84026, skyBlueOnError=0xFFFFFFFF, skyBlueErrorContainer=0xFFED6F21, skyBlueOnErrorContainer=0xFFFFFFFF, skyBlueBackground=0xFFF1F3F5, skyBlueOnBackground=0xE5000000, skyBlueSurface=0xFFF1F3F5, skyBlueOnSurface=0xE5000000, skyBlueSurfaceVariant=0xFFF1F3F5, skyBlueOnSurfaceVariant=0x99000000, skyBlueConfirm=0xFF64BB5C, skyBlueFontEmphasize=0xFF0A59F7, skyBlueIconEmphasize=0xFF0A59F7, skyBlueIconSubEmphasize=0x660A59F7, skyBlueBackgroundEmphasize=0xFF0A59F7, skyBlueBackgroundFocus=0xFFF1F3F5, skyBlueBottomNavBackground=0xFFF1F3F5, skyBlueBottomNavSelectedIcon=0xFF0A59F7, skyBlueBottomNavUnselectedIcon=0x99000000, skyBlueTopBarBackground=0xFFF1F3F5, bottomNavHeight=80, bottomNavHorizontalPadding=16, bottomNavVerticalPadding=7, bottomNavItemCornerRadius=16, bottomNavItemOuterPadding=4, bottomNavItemVerticalPadding=8, bottomNavItemHorizontalPadding=12, bottomNavIconSize=24, bottomNavIconTextSpacing=4, bottomNavTextFontSize=11, bottomNavSelectedFontWeight=medium, bottomNavUnselectedFontWeight=normal, bottomNavColorAnimationDuration=150, bottomNavBackgroundAnimationDuration=150, bottomNavItemArrangement=spaceEvenly, dialogBlurEnabled=true, dialogCornerRadius=28, dialogBlurIntensity=0.6, topAppBarButtonCircleBackgroundEnabled=true, topAppBarButtonCircleBackgroundSize=40, topAppBarButtonCircleBackgroundHorizontalMargin=28, topAppBarButtonCircleBackgroundRightMargin=3)
2025-07-28 13:42:46.642  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Experimental features session state reset
2025-07-28 13:42:46.651  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Initialized 10 particles
2025-07-28 13:42:46.651  4372-4372  System.out              com.weinuo.quickcommands             I  WaterBallComponent: Particles initialized, applying current state: NORMAL
2025-07-28 13:42:46.651  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Resetting to normal state
2025-07-28 13:42:46.651  4372-4372  System.out              com.weinuo.quickcommands             I  WaterBallComponent: Particle animation state changed to NORMAL
2025-07-28 13:42:46.657  4372-4372  Choreographer           com.weinuo.quickcommands             I  Skipped 30 frames!  The application may be doing too much work on its main thread.
2025-07-28 13:42:46.724  4372-4372  QuickCommandsService    com.weinuo.quickcommands             D  QuickCommandsService onCreate started
2025-07-28 13:42:46.724  4372-4372  QuickCommandsService    com.weinuo.quickcommands             D  Starting foreground service immediately...
2025-07-28 13:42:46.728  4372-4372  QuickCommandsService    com.weinuo.quickcommands             D  Quick commands service started as foreground service successfully
2025-07-28 13:42:46.729  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  Starting background initialization...
2025-07-28 13:42:46.729  4372-4398  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.729  4372-4372  QuickCommandsService    com.weinuo.quickcommands             D  QuickCommandsService started
2025-07-28 13:42:46.729  4372-4372  QuickCommandsService    com.weinuo.quickcommands             D  Foreground service already started, skipping
2025-07-28 13:42:46.729  4372-4398  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.729  4372-4398  chatty                  com.weinuo.quickcommands             I  uid=10058(com.weinuo.quickcommands) DefaultDispatch identical 1 line
2025-07-28 13:42:46.729  4372-4398  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.730  4372-4399  QuickCommandsService    com.weinuo.quickcommands             D  No conditions to initialize
2025-07-28 13:42:46.730  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/os/Trace;->asyncTraceBegin(JLjava/lang/String;I)V (light greylist, reflection)
2025-07-28 13:42:46.730  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/os/Trace;->asyncTraceEnd(JLjava/lang/String;I)V (light greylist, reflection)
2025-07-28 13:42:46.730  4372-4372  o.quickcommand          com.weinuo.quickcommands             W  Accessing hidden method Landroid/os/Trace;->traceCounter(JLjava/lang/String;I)V (light greylist, reflection)
2025-07-28 13:42:46.731  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Loaded last checkup data with animation - Score: 92, Time: 1753681075472
2025-07-28 13:42:46.735  4372-4398  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:46.736  4372-4399  AppStateMonitor         com.weinuo.quickcommands             D  Current foreground app: com.weinuo.quickcommands
2025-07-28 13:42:46.736  4372-4399  SharedCond...nEvaluator com.weinuo.quickcommands             D  Force updated app state: current app = com.weinuo.quickcommands
2025-07-28 13:42:46.746  4372-4398  TimeConditionMonitor    com.weinuo.quickcommands             D  Loaded 0 persisted condition states
2025-07-28 13:42:46.751  4372-4398  DeviceEventMonitor      com.weinuo.quickcommands             D  Loaded 0 persisted device event states
2025-07-28 13:42:46.760  4372-4400  QuickCommandsService    com.weinuo.quickcommands             D  Starting conditional command monitoring...
2025-07-28 13:42:46.761  4372-4399  QuickCommandsService    com.weinuo.quickcommands             D  No time conditions found, skipping time condition monitoring
2025-07-28 13:42:46.761  4372-4399  TimeConditionMonitor    com.weinuo.quickcommands             D  Time monitoring already stopped
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Starting enabled reminders, total configs: 8
2025-07-28 13:42:46.761  4372-4399  QuickCommandsService    com.weinuo.quickcommands             D  No memory conditions found, skipping memory state monitoring
2025-07-28 13:42:46.761  4372-4399  MemoryStateMonitor      com.weinuo.quickcommands             D  Memory monitoring already stopped
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: screen_rotation_reminder, isAvailable: false, isEnabled: false, isConfigured: true
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: screen_rotation_reminder (not available)
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: flashlight_reminder, isAvailable: false, isEnabled: false, isConfigured: true
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: flashlight_reminder (not available)
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: new_app_reminder, isAvailable: false, isEnabled: false, isConfigured: true
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: new_app_reminder (not available)
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: music_app_reminder, isAvailable: false, isEnabled: false, isConfigured: false
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: music_app_reminder (not available)
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: shopping_app_reminder, isAvailable: false, isEnabled: false, isConfigured: true
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: shopping_app_reminder (not available)
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: app_link_reminder, isAvailable: false, isEnabled: false, isConfigured: true
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: app_link_reminder (not available)
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: share_url_reminder, isAvailable: false, isEnabled: false, isConfigured: true
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: share_url_reminder (not available)
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Checking config: address_reminder, isAvailable: false, isEnabled: false, isConfigured: false
2025-07-28 13:42:46.761  4372-4408  SmartReminderManager    com.weinuo.quickcommands             D  Skipping reminder: address_reminder (not available)
2025-07-28 13:42:46.761  4372-4400  QuickCommandsService    com.weinuo.quickcommands             D  Conditional command monitoring started successfully
2025-07-28 13:42:46.761  4372-4399  QuickCommandsService    com.weinuo.quickcommands             D  No app state conditions found, stopping app state condition monitoring
2025-07-28 13:42:46.761  4372-4408  QuickCommandsService    com.weinuo.quickcommands             D  No device event conditions found, skipping device event monitoring
2025-07-28 13:42:46.761  4372-4399  AppStateCo...ionMonitor com.weinuo.quickcommands             D  App state monitoring already stopped
2025-07-28 13:42:46.761  4372-4408  DeviceEventMonitor      com.weinuo.quickcommands             D  Device event monitoring already stopped
2025-07-28 13:42:46.761  4372-4399  AppStateCo...ionMonitor com.weinuo.quickcommands             D  Cleared all app state conditions
2025-07-28 13:42:46.761  4372-4400  QuickCommandsService    com.weinuo.quickcommands             D  No battery conditions found, skipping battery state monitoring
2025-07-28 13:42:46.761  4372-4400  BatteryStateMonitor     com.weinuo.quickcommands             D  Battery monitoring already stopped
2025-07-28 13:42:46.761  4372-4400  QuickCommandsService    com.weinuo.quickcommands             D  No communication conditions found, skipping communication state monitoring
2025-07-28 13:42:46.761  4372-4399  QuickCommandsService    com.weinuo.quickcommands             D  No connection conditions found, skipping connection state monitoring
2025-07-28 13:42:46.761  4372-4399  ConnectionStateMonitor  com.weinuo.quickcommands             D  Connection monitoring already stopped
2025-07-28 13:42:46.761  4372-4408  QuickCommandsService    com.weinuo.quickcommands             D  No sensor conditions found, skipping sensor state monitoring
2025-07-28 13:42:46.761  4372-4408  SensorStateMonitor      com.weinuo.quickcommands             D  Sensor monitoring already stopped
2025-07-28 13:42:47.019  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:47.032  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:47.261  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  Reregistering all condition monitors for 0 commands
2025-07-28 13:42:47.261  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  Found 0 enabled commands
2025-07-28 13:42:47.261  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No time conditions found, stopping time condition monitoring
2025-07-28 13:42:47.261  4372-4398  TimeConditionMonitor    com.weinuo.quickcommands             D  Time monitoring already stopped
2025-07-28 13:42:47.261  4372-4398  TimeConditionMonitor    com.weinuo.quickcommands             D  Cleared all time conditions and persisted states
2025-07-28 13:42:47.261  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No memory conditions found, stopping memory state monitoring
2025-07-28 13:42:47.261  4372-4398  MemoryStateMonitor      com.weinuo.quickcommands             D  Memory monitoring already stopped
2025-07-28 13:42:47.261  4372-4398  MemoryStateMonitor      com.weinuo.quickcommands             D  Cleared all memory conditions
2025-07-28 13:42:47.261  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No device event conditions found, stopping device event monitoring
2025-07-28 13:42:47.261  4372-4398  DeviceEventMonitor      com.weinuo.quickcommands             D  Device event monitoring already stopped
2025-07-28 13:42:47.261  4372-4398  DeviceEventMonitor      com.weinuo.quickcommands             D  Cleared all device event conditions
2025-07-28 13:42:47.261  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No app state conditions found, stopping app state condition monitoring
2025-07-28 13:42:47.261  4372-4398  AppStateCo...ionMonitor com.weinuo.quickcommands             D  App state monitoring already stopped
2025-07-28 13:42:47.262  4372-4398  AppStateCo...ionMonitor com.weinuo.quickcommands             D  Cleared all app state conditions
2025-07-28 13:42:47.262  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No connection conditions found, stopping connection state monitoring
2025-07-28 13:42:47.262  4372-4398  ConnectionStateMonitor  com.weinuo.quickcommands             D  Connection monitoring already stopped
2025-07-28 13:42:47.262  4372-4398  ConnectionStateMonitor  com.weinuo.quickcommands             D  Cleared all connection conditions
2025-07-28 13:42:47.262  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No communication conditions found, stopping communication state monitoring
2025-07-28 13:42:47.262  4372-4398  Communicat...ateMonitor com.weinuo.quickcommands             D  Cleared all communication conditions
2025-07-28 13:42:47.262  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No sensor conditions found, stopping sensor state monitoring
2025-07-28 13:42:47.262  4372-4398  SensorStateMonitor      com.weinuo.quickcommands             D  Sensor monitoring already stopped
2025-07-28 13:42:47.262  4372-4398  SensorStateMonitor      com.weinuo.quickcommands             D  Cleared all sensor conditions
2025-07-28 13:42:47.262  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  No battery conditions found, stopping battery state monitoring
2025-07-28 13:42:47.262  4372-4398  BatteryStateMonitor     com.weinuo.quickcommands             D  Battery monitoring already stopped
2025-07-28 13:42:47.262  4372-4398  BatteryStateMonitor     com.weinuo.quickcommands             D  Cleared all battery conditions
2025-07-28 13:42:47.262  4372-4398  QuickCommandsService    com.weinuo.quickcommands             D  All condition monitors reregistered successfully
2025-07-28 13:42:47.733  4372-4372  FloatingButtonManager   com.weinuo.quickcommands             D  Initializing floating button manager
2025-07-28 13:42:47.737  4372-4372  FloatingButtonManager   com.weinuo.quickcommands             W  No overlay permission, skipping floating button initialization
2025-07-28 13:42:47.737  4372-4372  MainActivity            com.weinuo.quickcommands             D  FloatingButtonManager initialized
2025-07-28 13:42:48.001  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:48.018  4372-4372  chatty                  com.weinuo.quickcommands             I  uid=10058(com.weinuo.quickcommands) identical 1 line
2025-07-28 13:42:48.034  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:49.007  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:50.028  4372-4372  chatty                  com.weinuo.quickcommands             I  uid=10058(com.weinuo.quickcommands) identical 4 lines
2025-07-28 13:42:50.049  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:50.118  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Setting particle animation state to CONTRACTING
2025-07-28 13:42:50.118  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  ViewModel: Particle animation state changed to CONTRACTING
2025-07-28 13:42:50.118  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Refreshing health check data
2025-07-28 13:42:50.118  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Manual checkup started
2025-07-28 13:42:50.118  4372-4398  PhoneCheckupRepository  com.weinuo.quickcommands             D  Refreshing app running status
2025-07-28 13:42:50.124  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:50.140  4372-4372  System.out              com.weinuo.quickcommands             I  EnhancedDecorativeParticles: Warning - Expected 10 particles, got 0
2025-07-28 13:42:50.142  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Initialized 10 particles
2025-07-28 13:42:50.142  4372-4372  System.out              com.weinuo.quickcommands             I  WaterBallComponent: Particles initialized, applying current state: NORMAL
2025-07-28 13:42:50.142  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Resetting to normal state
2025-07-28 13:42:50.142  4372-4372  System.out              com.weinuo.quickcommands             I  WaterBallComponent: Particle animation state changed to NORMAL
2025-07-28 13:42:50.269  4372-4408  PhoneCheckupRepository  com.weinuo.quickcommands             D  Running apps count - User: 1, System: 3, Total: 4
2025-07-28 13:42:50.270  4372-4372  PhoneCheckupRepository  com.weinuo.quickcommands             D  Health score calculated: 92 (running apps: 4)
2025-07-28 13:42:50.270  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Health check refreshed - Score: 92, Running apps: 4
2025-07-28 13:42:50.272  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Checkup time saved: 1753681370272
2025-07-28 13:42:50.272  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Checkup score saved: 92
2025-07-28 13:42:50.272  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Setting particle animation state to EXPANDING
2025-07-28 13:42:50.272  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Waiting for 5 seconds to display checkup results
2025-07-28 13:42:50.272  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  ViewModel: Particle animation state changed to EXPANDING
2025-07-28 13:42:51.012  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:51.028  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:51.343  4372-4424  ProfileInstaller        com.weinuo.quickcommands             D  Installing profile for com.weinuo.quickcommands
2025-07-28 13:42:52.020  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:55.025  4372-4372  chatty                  com.weinuo.quickcommands             I  uid=10058(com.weinuo.quickcommands) identical 9 lines
2025-07-28 13:42:55.042  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL
2025-07-28 13:42:55.273  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  ViewModel: Particle animation state changed to NORMAL
2025-07-28 13:42:55.273  4372-4372  PhoneCheckupViewModel   com.weinuo.quickcommands             D  Checkup complete, button state: OPTIMIZE
2025-07-28 13:42:55.290  4372-4372  SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-07-28 13:42:55.306  4372-4372  System.out              com.weinuo.quickcommands             I  EnhancedDecorativeParticles: Warning - Expected 10 particles, got 0
2025-07-28 13:42:55.308  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Initialized 10 particles
2025-07-28 13:42:55.308  4372-4372  System.out              com.weinuo.quickcommands             I  WaterBallComponent: Particles initialized, applying current state: NORMAL
2025-07-28 13:42:55.308  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Resetting to normal state
2025-07-28 13:42:55.308  4372-4372  System.out              com.weinuo.quickcommands             I  WaterBallComponent: Particle animation state changed to NORMAL
2025-07-28 13:42:56.013  4372-4372  System.out              com.weinuo.quickcommands             I  DecorativeParticleManager: Current state: NORMAL

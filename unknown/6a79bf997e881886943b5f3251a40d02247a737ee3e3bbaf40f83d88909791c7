<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 静态快捷方式1 -->
    <shortcut
        android:shortcutId="static_shortcut_1"
        android:enabled="true"
        android:icon="@drawable/ic_shortcut_command"
        android:shortcutShortLabel="@string/shortcut_1_short_label"
        android:shortcutLongLabel="@string/shortcut_1_long_label">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.weinuo.quickcommands"
            android:targetClass="com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity">
            <extra android:name="shortcut_id" android:value="static_shortcut_1" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>

    <!-- 静态快捷方式2 -->
    <shortcut
        android:shortcutId="static_shortcut_2"
        android:enabled="true"
        android:icon="@drawable/ic_shortcut_command"
        android:shortcutShortLabel="@string/shortcut_2_short_label"
        android:shortcutLongLabel="@string/shortcut_2_long_label">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.weinuo.quickcommands"
            android:targetClass="com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity">
            <extra android:name="shortcut_id" android:value="static_shortcut_2" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>

    <!-- 静态快捷方式3 -->
    <shortcut
        android:shortcutId="static_shortcut_3"
        android:enabled="true"
        android:icon="@drawable/ic_shortcut_command"
        android:shortcutShortLabel="@string/shortcut_3_short_label"
        android:shortcutLongLabel="@string/shortcut_3_long_label">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.weinuo.quickcommands"
            android:targetClass="com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity">
            <extra android:name="shortcut_id" android:value="static_shortcut_3" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>

    <!-- 静态快捷方式4 -->
    <shortcut
        android:shortcutId="static_shortcut_4"
        android:enabled="true"
        android:icon="@drawable/ic_shortcut_command"
        android:shortcutShortLabel="@string/shortcut_4_short_label"
        android:shortcutLongLabel="@string/shortcut_4_long_label">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.weinuo.quickcommands"
            android:targetClass="com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity">
            <extra android:name="shortcut_id" android:value="static_shortcut_4" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>
</shortcuts>

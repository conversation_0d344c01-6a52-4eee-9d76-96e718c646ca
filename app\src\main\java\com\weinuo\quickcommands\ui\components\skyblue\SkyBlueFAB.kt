package com.weinuo.quickcommands.ui.components.skyblue

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.weinuo.quickcommands.ui.theme.config.FloatingActionButtonConfig

/**
 * 天空蓝主题专用浮动操作按钮
 *
 * 特点：
 * - 整合设计风格
 * - 无阴影设计（符合整合设计理念）
 * - 完美圆形
 * - 天空蓝主题专有组件
 * - 56dp完美圆形尺寸
 * - 天空蓝主题颜色（#0A59F7背景，白色图标）
 * - 流畅的交互动画（内置水波纹效果）
 * - 圆角端点的加号图标
 * - 符合Material Design 3规范的交互反馈
 *
 * 设计规格：
 * - 容器尺寸：56dp x 56dp（标准FAB尺寸）
 * - 背景色：#0A59F7（天空蓝主题主色）
 * - 图标色：#FFFFFF（纯白色）
 * - 图标尺寸：28dp x 28dp
 * - 图标粗细：3dp stroke
 * - 阴影：0dp（无阴影，符合整合设计风格）
 * - 交互：使用Material Design内置交互动画，避免冲突
 */
@Composable
fun SkyBlueFAB(
    config: FloatingActionButtonConfig,
    modifier: Modifier = Modifier
) {
    // 交互状态管理 - 使用Material Design内置交互源
    val interactionSource = remember { MutableInteractionSource() }

    // 使用Material 3 FloatingActionButton作为基础，应用天空蓝主题的整合设计风格
    FloatingActionButton(
        onClick = config.onClick,
        modifier = modifier
            .size(56.dp) // 标准FAB尺寸，移除手动缩放动画避免冲突
            .zIndex(10f), // 强制设置最高层级，确保FAB始终可见，不被模糊效果或其他层级覆盖
        shape = CircleShape, // 完美圆形
        containerColor = config.containerColor ?: MaterialTheme.colorScheme.primary, // 使用主题primary色
        contentColor = config.contentColor ?: Color.White, // 强制使用白色，确保图标始终可见
        elevation = FloatingActionButtonDefaults.elevation(
            defaultElevation = 0.dp, // 无阴影设计，符合整合设计风格
            pressedElevation = 0.dp, // 按压时也无阴影，避免与模糊效果冲突
            focusedElevation = 0.dp, // 聚焦时无阴影
            hoveredElevation = 0.dp  // 悬停时无阴影
        ),
        interactionSource = interactionSource
    ) {
        // 渲染按钮内容（通常是加号图标）
        config.content()
    }
}

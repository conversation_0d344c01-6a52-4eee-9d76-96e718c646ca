package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem

/**
 * 传感器状态配置数据提供器
 *
 * 提供传感器状态特定的配置项列表，为每个传感器类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object SensorStateConfigProvider {

    /**
     * 获取传感器状态配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 传感器状态配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<SensorStateType>> {
        return listOf(
            // 光线传感器配置项
            ConfigurationCardItem(
                id = "light_sensor",
                title = context.getString(R.string.sensor_light_sensor),
                description = context.getString(R.string.sensor_light_sensor_description),
                operationType = SensorStateType.LIGHT_SENSOR,
                permissionRequired = false,
                content = { type, onComplete ->
                    LightSensorConfigContent(type, onComplete)
                }
            ),

            // 屏幕方向传感器配置项
            ConfigurationCardItem(
                id = "orientation_sensor",
                title = context.getString(R.string.sensor_orientation_sensor),
                description = context.getString(R.string.sensor_orientation_sensor_description),
                operationType = SensorStateType.ORIENTATION_SENSOR,
                permissionRequired = false,
                content = { type, onComplete ->
                    OrientationSensorConfigContent(type, onComplete)
                }
            ),

            // 摇晃检测配置项
            ConfigurationCardItem(
                id = "shake_sensor",
                title = "摇晃检测",
                description = "当设备摇晃时触发条件",
                operationType = SensorStateType.SHAKE_SENSOR,
                permissionRequired = false,
                content = { type, onComplete ->
                    ShakeSensorConfigContent(type, onComplete)
                }
            ),

            // 睡眠检测配置项
            ConfigurationCardItem(
                id = "sleep_sensor",
                title = "睡眠检测",
                description = "当检测到睡眠状态变化时触发条件",
                operationType = SensorStateType.SLEEP_SENSOR,
                permissionRequired = false,
                content = { type, onComplete ->
                    SleepSensorConfigContent(type, onComplete)
                }
            ),

            // 设备翻转检测配置项
            ConfigurationCardItem(
                id = "flip_sensor",
                title = "设备翻转检测",
                description = "当设备翻转时触发条件",
                operationType = SensorStateType.FLIP_SENSOR,
                permissionRequired = false,
                content = { type, onComplete ->
                    FlipSensorConfigContent(type, onComplete)
                }
            ),

            // 接近传感器配置项
            ConfigurationCardItem(
                id = "proximity_sensor",
                title = "接近传感器",
                description = "当物体接近或远离时触发条件",
                operationType = SensorStateType.PROXIMITY_SENSOR,
                permissionRequired = false,
                content = { type, onComplete ->
                    ProximitySensorConfigContent(type, onComplete)
                }
            ),

            // 运动识别配置项
            ConfigurationCardItem(
                id = "activity_recognition",
                title = "运动识别",
                description = "当检测到特定运动状态时触发条件",
                operationType = SensorStateType.ACTIVITY_RECOGNITION,
                permissionRequired = true,
                content = { type, onComplete ->
                    ActivityRecognitionConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * 光线传感器配置内容组件
 *
 * 光线传感器不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun LightSensorConfigContent(
    type: SensorStateType,
    onComplete: (Any) -> Unit
) {
    var thresholdType by rememberSaveable { mutableStateOf(LightThresholdType.DECREASE_TO) }
    var luxValue by rememberSaveable { mutableStateOf(10.0f) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置光线传感器的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 阈值类型选择
        Text(
            text = "触发条件",
            style = MaterialTheme.typography.bodyMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { thresholdType = LightThresholdType.DECREASE_TO },
                label = { Text("减少到") },
                selected = thresholdType == LightThresholdType.DECREASE_TO,
                modifier = Modifier.weight(1f)
            )
            FilterChip(
                onClick = { thresholdType = LightThresholdType.INCREASE_TO },
                label = { Text("增加到") },
                selected = thresholdType == LightThresholdType.INCREASE_TO,
                modifier = Modifier.weight(1f)
            )
        }

        // 光照强度设置
        Text(
            text = "光照强度 (0-1000 lux)",
            style = MaterialTheme.typography.bodyMedium
        )

        OutlinedTextField(
            value = luxValue.toInt().toString(),
            onValueChange = { newValue ->
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 0..1000) {
                    luxValue = intValue.toFloat()
                }
            },
            label = { Text("lux") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = SensorStateCondition(
                    sensorType = type,
                    thresholdType = thresholdType,
                    luxValue = luxValue
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 屏幕方向传感器配置内容组件
 *
 * 屏幕方向传感器不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun OrientationSensorConfigContent(
    type: SensorStateType,
    onComplete: (Any) -> Unit
) {
    var orientationType by rememberSaveable { mutableStateOf(OrientationType.PORTRAIT) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置屏幕方向传感器的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "触发方向",
            style = MaterialTheme.typography.bodyMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { orientationType = OrientationType.PORTRAIT },
                label = { Text("竖屏") },
                selected = orientationType == OrientationType.PORTRAIT,
                modifier = Modifier.weight(1f)
            )
            FilterChip(
                onClick = { orientationType = OrientationType.LANDSCAPE },
                label = { Text("横屏") },
                selected = orientationType == OrientationType.LANDSCAPE,
                modifier = Modifier.weight(1f)
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = SensorStateCondition(
                    sensorType = type,
                    orientationType = orientationType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 摇晃检测配置内容组件
 *
 * 摇晃检测不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ShakeSensorConfigContent(
    type: SensorStateType,
    onComplete: (Any) -> Unit
) {
    var sensitivity by rememberSaveable { mutableStateOf(SensitivityLevel.MEDIUM) }
    var shakeThreshold by rememberSaveable { mutableStateOf(12.0f) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置摇晃检测的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "敏感度级别",
            style = MaterialTheme.typography.bodyMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { sensitivity = SensitivityLevel.LOW },
                label = { Text("低") },
                selected = sensitivity == SensitivityLevel.LOW,
                modifier = Modifier.weight(1f)
            )
            FilterChip(
                onClick = { sensitivity = SensitivityLevel.MEDIUM },
                label = { Text("中") },
                selected = sensitivity == SensitivityLevel.MEDIUM,
                modifier = Modifier.weight(1f)
            )
            FilterChip(
                onClick = { sensitivity = SensitivityLevel.HIGH },
                label = { Text("高") },
                selected = sensitivity == SensitivityLevel.HIGH,
                modifier = Modifier.weight(1f)
            )
        }

        Text(
            text = "摇晃阈值 (5-30)",
            style = MaterialTheme.typography.bodyMedium
        )

        OutlinedTextField(
            value = shakeThreshold.toInt().toString(),
            onValueChange = { newValue ->
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 5..30) {
                    shakeThreshold = intValue.toFloat()
                }
            },
            label = { Text("阈值") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = SensorStateCondition(
                    sensorType = type,
                    sensitivity = sensitivity,
                    shakeThreshold = shakeThreshold
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 睡眠检测配置内容组件
 *
 * 睡眠检测不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SleepSensorConfigContent(
    type: SensorStateType,
    onComplete: (Any) -> Unit
) {
    var sleepStateType by rememberSaveable { mutableStateOf(SleepStateType.FALL_ASLEEP) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置睡眠检测的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "睡眠状态",
            style = MaterialTheme.typography.bodyMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { sleepStateType = SleepStateType.FALL_ASLEEP },
                label = { Text("入睡") },
                selected = sleepStateType == SleepStateType.FALL_ASLEEP,
                modifier = Modifier.weight(1f)
            )
            FilterChip(
                onClick = { sleepStateType = SleepStateType.WAKE_UP },
                label = { Text("醒来") },
                selected = sleepStateType == SleepStateType.WAKE_UP,
                modifier = Modifier.weight(1f)
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = SensorStateCondition(
                    sensorType = type,
                    sleepStateType = sleepStateType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 设备翻转检测配置内容组件
 *
 * 设备翻转检测不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun FlipSensorConfigContent(
    type: SensorStateType,
    onComplete: (Any) -> Unit
) {
    var flipType by rememberSaveable { mutableStateOf(FlipType.FACE_UP_TO_DOWN) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置设备翻转检测的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "翻转类型",
            style = MaterialTheme.typography.bodyMedium
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { flipType = FlipType.FACE_UP_TO_DOWN },
                label = { Text("朝上到朝下") },
                selected = flipType == FlipType.FACE_UP_TO_DOWN,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { flipType = FlipType.FACE_DOWN_TO_UP },
                label = { Text("朝下到朝上") },
                selected = flipType == FlipType.FACE_DOWN_TO_UP,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { flipType = FlipType.ANY_TO_FACE_DOWN },
                label = { Text("任何到朝下") },
                selected = flipType == FlipType.ANY_TO_FACE_DOWN,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = SensorStateCondition(
                    sensorType = type,
                    flipType = flipType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 接近传感器配置内容组件
 *
 * 接近传感器不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ProximitySensorConfigContent(
    type: SensorStateType,
    onComplete: (Any) -> Unit
) {
    var proximityType by rememberSaveable { mutableStateOf(ProximityType.NEAR) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置接近传感器的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "距离类型",
            style = MaterialTheme.typography.bodyMedium
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { proximityType = ProximityType.NEAR },
                label = { Text("接近") },
                selected = proximityType == ProximityType.NEAR,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { proximityType = ProximityType.FAR },
                label = { Text("远离") },
                selected = proximityType == ProximityType.FAR,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { proximityType = ProximityType.SLOW_WAVE },
                label = { Text("慢挥手") },
                selected = proximityType == ProximityType.SLOW_WAVE,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { proximityType = ProximityType.FAST_WAVE },
                label = { Text("快挥手") },
                selected = proximityType == ProximityType.FAST_WAVE,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = SensorStateCondition(
                    sensorType = type,
                    proximityType = proximityType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 运动识别配置内容组件
 *
 * 运动识别需要ACTIVITY_RECOGNITION权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ActivityRecognitionConfigContent(
    type: SensorStateType,
    onComplete: (Any) -> Unit
) {
    var activityType by rememberSaveable { mutableStateOf(ActivityType.WALKING) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置运动识别的触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "运动类型",
            style = MaterialTheme.typography.bodyMedium
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { activityType = ActivityType.DRIVING },
                label = { Text("开车") },
                selected = activityType == ActivityType.DRIVING,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { activityType = ActivityType.CYCLING },
                label = { Text("骑自行车") },
                selected = activityType == ActivityType.CYCLING,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { activityType = ActivityType.RUNNING },
                label = { Text("跑步") },
                selected = activityType == ActivityType.RUNNING,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { activityType = ActivityType.WALKING },
                label = { Text("步行") },
                selected = activityType == ActivityType.WALKING,
                modifier = Modifier.fillMaxWidth()
            )
            FilterChip(
                onClick = { activityType = ActivityType.STILL },
                label = { Text("静止") },
                selected = activityType == ActivityType.STILL,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = SensorStateCondition(
                    sensorType = type,
                    activityType = activityType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

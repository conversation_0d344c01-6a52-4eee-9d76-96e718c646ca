package com.weinuo.quickcommands.ui.theme.config

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 模糊配置类
 *
 * 管理应用中所有模糊效果的配置
 * 支持不同组件的独立模糊设置
 * 支持所有Android版本，包括Android 11及以下的实验性RenderScript模糊
 */
data class BlurConfiguration(
    /**
     * 顶部栏模糊效果开关
     */
    val topBarBlurEnabled: Boolean = true,

    /**
     * 顶部栏模糊样式类型 ("preset" 或 "custom")
     */
    val topBarBlurStyle: String = "preset",

    /**
     * 顶部栏预设材质类型 ("ultraThin", "thin", "regular", "thick")
     */
    val topBarPresetMaterial: String = "regular",

    /**
     * 顶部栏模糊强度 (0.0 - 1.0)
     * 0.0 = 无模糊，1.0 = 最大模糊
     * 仅在自定义模式下使用
     */
    val topBarBlurIntensity: Float = 0.6f,

    /**
     * 顶部栏背景透明度 (0.0 - 1.0)
     * 控制模糊效果下背景颜色的透明度
     * 0.0 = 完全透明，1.0 = 100%透明度
     */
    val topBarBackgroundAlpha: Float = 0.1f,

    /**
     * 顶部栏噪声因子 (0.0 - 1.0)
     * 控制自定义模糊效果的纹理噪声强度
     * 0.0 = 无噪声，1.0 = 最大噪声
     * 仅在自定义模式下使用
     */
    val topBarNoiseFactor: Float = 0.15f,

    /**
     * 顶部栏色调强度 (0.0 - 2.0)
     * 控制自定义模糊效果的色调强度
     * 影响tint颜色的透明度计算
     * 仅在自定义模式下使用
     */
    val topBarTintIntensity: Float = 1.0f,

    /**
     * 底部栏模糊效果开关
     */
    val bottomBarBlurEnabled: Boolean = true,

    /**
     * 底部栏模糊样式类型 ("preset" 或 "custom")
     */
    val bottomBarBlurStyle: String = "preset",

    /**
     * 底部栏预设材质类型 ("ultraThin", "thin", "regular", "thick")
     */
    val bottomBarPresetMaterial: String = "regular",

    /**
     * 底部栏模糊强度 (0.0 - 1.0)
     * 0.0 = 无模糊，1.0 = 最大模糊
     * 仅在自定义模式下使用
     */
    val bottomBarBlurIntensity: Float = 0.6f,

    /**
     * 底部栏背景透明度 (0.0 - 1.0)
     * 控制模糊效果下背景颜色的透明度
     * 0.0 = 完全透明，1.0 = 100%透明度
     */
    val bottomBarBackgroundAlpha: Float = 0.1f,

    /**
     * 底部栏噪声因子 (0.0 - 1.0)
     * 控制自定义模糊效果的纹理噪声强度
     * 0.0 = 无噪声，1.0 = 最大噪声
     * 仅在自定义模式下使用
     */
    val bottomBarNoiseFactor: Float = 0.15f,

    /**
     * 底部栏色调强度 (0.0 - 2.0)
     * 控制自定义模糊效果的色调强度
     * 影响tint颜色的透明度计算
     * 仅在自定义模式下使用
     */
    val bottomBarTintIntensity: Float = 1.0f,

    /**
     * 对话框模糊效果开关
     */
    val dialogBlurEnabled: Boolean = true,

    /**
     * 对话框模糊样式类型 ("preset" 或 "custom")
     */
    val dialogBlurStyle: String = "preset",

    /**
     * 对话框预设材质类型 ("ultraThin", "thin", "regular", "thick")
     */
    val dialogPresetMaterial: String = "regular",

    /**
     * 对话框模糊强度 (0.0 - 1.0)
     * 0.0 = 无模糊，1.0 = 最大模糊
     * 仅在自定义模式下使用
     */
    val dialogBlurIntensity: Float = 0.7f,

    /**
     * 对话框背景透明度 (0.0 - 1.0)
     * 控制模糊效果下背景颜色的透明度
     * 0.0 = 完全透明，1.0 = 100%透明度
     */
    val dialogBackgroundAlpha: Float = 0.2f,

    /**
     * 对话框噪声因子 (0.0 - 1.0)
     * 控制自定义模糊效果的纹理噪声强度
     * 0.0 = 无噪声，1.0 = 最大噪声
     * 仅在自定义模式下使用
     */
    val dialogNoiseFactor: Float = 0.15f,

    /**
     * 对话框色调强度 (0.0 - 2.0)
     * 控制自定义模糊效果的色调强度
     * 影响tint颜色的透明度计算
     * 仅在自定义模式下使用
     */
    val dialogTintIntensity: Float = 1.0f,



    /**
     * 覆盖层模糊效果开关
     */
    val overlayBlurEnabled: Boolean = true,

    /**
     * 覆盖层模糊样式类型 ("preset" 或 "custom")
     */
    val overlayBlurStyle: String = "preset",

    /**
     * 覆盖层预设材质类型 ("ultraThin", "thin", "regular", "thick")
     */
    val overlayPresetMaterial: String = "regular",

    /**
     * 覆盖层模糊强度 (0.0 - 1.0)
     * 0.0 = 无模糊，1.0 = 最大模糊
     * 仅在自定义模式下使用
     */
    val overlayBlurIntensity: Float = 0.6f,

    /**
     * 覆盖层背景透明度 (0.0 - 1.0)
     * 控制模糊效果下背景颜色的透明度
     * 0.0 = 完全透明，1.0 = 100%透明度
     */
    val overlayBackgroundAlpha: Float = 0.1f,

    /**
     * 覆盖层噪声因子 (0.0 - 1.0)
     * 控制自定义模糊效果的纹理噪声强度
     * 0.0 = 无噪声，1.0 = 最大噪声
     * 仅在自定义模式下使用
     */
    val overlayNoiseFactor: Float = 0.15f,

    /**
     * 覆盖层色调强度 (0.0 - 2.0)
     * 控制自定义模糊效果的色调强度
     * 影响tint颜色的透明度计算
     * 仅在自定义模式下使用
     */
    val overlayTintIntensity: Float = 1.0f,

    /**
     * 顶部栏模糊半径 (5.dp - 50.dp)
     * 控制顶部栏的模糊半径大小
     */
    val topBarBlurRadius: Dp = 25.dp,

    /**
     * 底部栏模糊半径 (5.dp - 50.dp)
     * 控制底部栏的模糊半径大小
     */
    val bottomBarBlurRadius: Dp = 25.dp,

    /**
     * 对话框模糊半径 (5.dp - 50.dp)
     * 控制对话框的模糊半径大小
     */
    val dialogBlurRadius: Dp = 25.dp,

    /**
     * 覆盖层模糊半径 (5.dp - 50.dp)
     * 控制覆盖层的模糊半径大小
     */
    val overlayBlurRadius: Dp = 25.dp,

    /**
     * 最大模糊半径（兼容性保留）
     */
    val maxBlurRadius: Dp = 50.dp,

    /**
     * 设备是否支持模糊效果
     * 现在所有Android版本都支持模糊效果，包括Android 11及以下的实验性RenderScript模糊
     */
    val supportedOnDevice: Boolean = true
) {
    /**
     * 获取特定组件的模糊半径
     * 返回组件配置的模糊半径
     */
    fun getComponentBlurRadius(component: BlurComponent): Dp {
        return when (component) {
            BlurComponent.TOP_BAR -> topBarBlurRadius
            BlurComponent.BOTTOM_BAR -> bottomBarBlurRadius
            BlurComponent.DIALOG -> dialogBlurRadius
            BlurComponent.OVERLAY -> overlayBlurRadius
        }
    }

    /**
     * 获取特定组件的实际模糊半径
     * 在自定义模式下使用组件配置的模糊半径，在预设模式下根据强度计算
     */
    fun getActualBlurRadius(component: BlurComponent): Dp {
        return when (getComponentBlurStyle(component)) {
            "custom" -> getComponentBlurRadius(component)
            else -> {
                // 预设模式：根据强度计算模糊半径
                val intensity = getComponentBlurIntensity(component)
                getComponentBlurRadius(component) * intensity
            }
        }
    }

    /**
     * 获取特定组件的模糊强度
     * 根据模糊样式类型返回相应的强度值
     */
    fun getComponentBlurIntensity(component: BlurComponent): Float {
        return when (component) {
            BlurComponent.TOP_BAR -> if (topBarBlurStyle == "custom") topBarBlurIntensity else getPresetIntensity(topBarPresetMaterial)
            BlurComponent.BOTTOM_BAR -> if (bottomBarBlurStyle == "custom") bottomBarBlurIntensity else getPresetIntensity(bottomBarPresetMaterial)
            BlurComponent.DIALOG -> if (dialogBlurStyle == "custom") dialogBlurIntensity else getPresetIntensity(dialogPresetMaterial)
            BlurComponent.OVERLAY -> if (overlayBlurStyle == "custom") overlayBlurIntensity else getPresetIntensity(overlayPresetMaterial)
        }
    }

    /**
     * 获取特定组件的模糊样式类型
     */
    fun getComponentBlurStyle(component: BlurComponent): String {
        return when (component) {
            BlurComponent.TOP_BAR -> topBarBlurStyle
            BlurComponent.BOTTOM_BAR -> bottomBarBlurStyle
            BlurComponent.DIALOG -> dialogBlurStyle
            BlurComponent.OVERLAY -> overlayBlurStyle
        }
    }

    /**
     * 获取特定组件的预设材质类型
     */
    fun getComponentPresetMaterial(component: BlurComponent): String {
        return when (component) {
            BlurComponent.TOP_BAR -> topBarPresetMaterial
            BlurComponent.BOTTOM_BAR -> bottomBarPresetMaterial
            BlurComponent.DIALOG -> dialogPresetMaterial
            BlurComponent.OVERLAY -> overlayPresetMaterial
        }
    }

    /**
     * 根据预设材质类型获取对应的强度值
     */
    private fun getPresetIntensity(material: String): Float {
        return when (material) {
            "ultraThin" -> 0.25f
            "thin" -> 0.5f
            "regular" -> 0.7f
            "thick" -> 1.0f
            else -> 0.6f // 默认值
        }
    }

    /**
     * 获取特定组件的背景透明度
     */
    fun getComponentBackgroundAlpha(component: BlurComponent): Float {
        return when (component) {
            BlurComponent.TOP_BAR -> topBarBackgroundAlpha
            BlurComponent.BOTTOM_BAR -> bottomBarBackgroundAlpha
            BlurComponent.DIALOG -> dialogBackgroundAlpha
            BlurComponent.OVERLAY -> overlayBackgroundAlpha
        }
    }

    /**
     * 获取特定组件的噪声因子
     * 仅在自定义模式下使用
     */
    fun getComponentNoiseFactor(component: BlurComponent): Float {
        return when (component) {
            BlurComponent.TOP_BAR -> topBarNoiseFactor
            BlurComponent.BOTTOM_BAR -> bottomBarNoiseFactor
            BlurComponent.DIALOG -> dialogNoiseFactor
            BlurComponent.OVERLAY -> overlayNoiseFactor
        }
    }

    /**
     * 获取特定组件的色调强度
     * 仅在自定义模式下使用
     */
    fun getComponentTintIntensity(component: BlurComponent): Float {
        return when (component) {
            BlurComponent.TOP_BAR -> topBarTintIntensity
            BlurComponent.BOTTOM_BAR -> bottomBarTintIntensity
            BlurComponent.DIALOG -> dialogTintIntensity
            BlurComponent.OVERLAY -> overlayTintIntensity
        }
    }

    /**
     * 检查模糊效果是否可用
     */
    fun isBlurAvailable(): Boolean = supportedOnDevice

    /**
     * 获取特定组件的模糊半径（兼容性方法）
     */
    fun getBlurRadiusForComponent(component: BlurComponent): Dp {
        return if (isComponentBlurEnabled(component)) {
            getActualBlurRadius(component)
        } else {
            0.dp
        }
    }

    /**
     * 检查特定组件是否启用了模糊效果
     */
    fun isComponentBlurEnabled(component: BlurComponent): Boolean {
        return when (component) {
            BlurComponent.TOP_BAR -> topBarBlurEnabled
            BlurComponent.BOTTOM_BAR -> bottomBarBlurEnabled
            BlurComponent.DIALOG -> dialogBlurEnabled
            BlurComponent.OVERLAY -> overlayBlurEnabled
        }
    }

    /**
     * 获取特定组件模糊强度的百分比表示
     */
    fun getComponentIntensityPercentage(component: BlurComponent): Int =
        (getComponentBlurIntensity(component) * 100).toInt()

    /**
     * 获取特定组件模糊半径的整数表示
     */
    fun getComponentBlurRadiusValue(component: BlurComponent): Int =
        getComponentBlurRadius(component).value.toInt()

    /**
     * 获取特定组件背景透明度的百分比表示
     */
    fun getComponentBackgroundAlphaPercentage(component: BlurComponent): Int =
        (getComponentBackgroundAlpha(component) * 100).toInt()

    /**
     * 获取模糊强度的百分比表示（兼容性方法，使用顶部栏的值）
     */
    fun getIntensityPercentage(): Int = getComponentIntensityPercentage(BlurComponent.TOP_BAR)

    /**
     * 获取背景透明度的百分比表示（兼容性方法，使用顶部栏的值）
     */
    fun getBackgroundAlphaPercentage(): Int = getComponentBackgroundAlphaPercentage(BlurComponent.TOP_BAR)

    /**
     * 检查是否有任何模糊效果启用
     */
    fun hasAnyBlurEnabled(): Boolean {
        return topBarBlurEnabled ||
            bottomBarBlurEnabled ||
            dialogBlurEnabled ||
            overlayBlurEnabled
    }

    /**
     * 创建禁用所有模糊效果的配置
     */
    fun withAllBlurDisabled(): BlurConfiguration {
        return copy(
            topBarBlurEnabled = false,
            bottomBarBlurEnabled = false,
            dialogBlurEnabled = false,
            overlayBlurEnabled = false
        )
    }

    /**
     * 创建启用所有模糊效果的配置
     */
    fun withAllBlurEnabled(): BlurConfiguration {
        return copy(
            topBarBlurEnabled = true,
            bottomBarBlurEnabled = true,
            dialogBlurEnabled = true,
            overlayBlurEnabled = true
        )
    }

    /**
     * 更新特定组件的模糊设置
     */
    fun updateComponentBlur(component: BlurComponent, enabled: Boolean): BlurConfiguration {
        return when (component) {
            BlurComponent.TOP_BAR -> copy(topBarBlurEnabled = enabled)
            BlurComponent.BOTTOM_BAR -> copy(bottomBarBlurEnabled = enabled)
            BlurComponent.DIALOG -> copy(dialogBlurEnabled = enabled)
            BlurComponent.OVERLAY -> copy(overlayBlurEnabled = enabled)
        }
    }

    /**
     * 更新特定组件的模糊强度
     */
    fun updateComponentIntensity(component: BlurComponent, intensity: Float): BlurConfiguration {
        val coercedIntensity = intensity.coerceIn(0f, 1f)
        return when (component) {
            BlurComponent.TOP_BAR -> copy(topBarBlurIntensity = coercedIntensity)
            BlurComponent.BOTTOM_BAR -> copy(bottomBarBlurIntensity = coercedIntensity)
            BlurComponent.DIALOG -> copy(dialogBlurIntensity = coercedIntensity)
            BlurComponent.OVERLAY -> copy(overlayBlurIntensity = coercedIntensity)
        }
    }

    /**
     * 更新特定组件的背景透明度
     */
    fun updateComponentBackgroundAlpha(component: BlurComponent, alpha: Float): BlurConfiguration {
        val coercedAlpha = alpha.coerceIn(0f, 0.5f)
        return when (component) {
            BlurComponent.TOP_BAR -> copy(topBarBackgroundAlpha = coercedAlpha)
            BlurComponent.BOTTOM_BAR -> copy(bottomBarBackgroundAlpha = coercedAlpha)
            BlurComponent.DIALOG -> copy(dialogBackgroundAlpha = coercedAlpha)
            BlurComponent.OVERLAY -> copy(overlayBackgroundAlpha = coercedAlpha)
        }
    }

    /**
     * 更新模糊强度（兼容性方法，更新所有组件）
     */
    fun updateIntensity(intensity: Float): BlurConfiguration {
        val coercedIntensity = intensity.coerceIn(0f, 1f)
        return copy(
            topBarBlurIntensity = coercedIntensity,
            bottomBarBlurIntensity = coercedIntensity,
            dialogBlurIntensity = coercedIntensity,
            overlayBlurIntensity = coercedIntensity
        )
    }

    /**
     * 更新背景透明度（兼容性方法，更新所有组件）
     */
    fun updateBackgroundAlpha(alpha: Float): BlurConfiguration {
        val coercedAlpha = alpha.coerceIn(0f, 1.0f)
        return copy(
            topBarBackgroundAlpha = coercedAlpha,
            bottomBarBackgroundAlpha = coercedAlpha,
            dialogBackgroundAlpha = coercedAlpha,
            overlayBackgroundAlpha = coercedAlpha
        )
    }

    /**
     * 验证配置的有效性
     */
    fun validate(): Boolean {
        return topBarBlurIntensity in 0f..1f &&
               bottomBarBlurIntensity in 0f..1f &&
               dialogBlurIntensity in 0f..1f &&
               overlayBlurIntensity in 0f..1f &&
               topBarBackgroundAlpha in 0f..1f &&
               bottomBarBackgroundAlpha in 0f..1f &&
               dialogBackgroundAlpha in 0f..1f &&
               overlayBackgroundAlpha in 0f..1f &&
               topBarBlurRadius >= 5.dp && topBarBlurRadius <= 50.dp &&
               bottomBarBlurRadius >= 5.dp && bottomBarBlurRadius <= 50.dp &&
               dialogBlurRadius >= 5.dp && dialogBlurRadius <= 50.dp &&
               overlayBlurRadius >= 5.dp && overlayBlurRadius <= 50.dp &&
               maxBlurRadius >= 0.dp
    }

    companion object {
        /**
         * 创建默认配置
         */
        fun default(): BlurConfiguration = BlurConfiguration()

        /**
         * 创建禁用模糊的配置
         */
        fun disabled(): BlurConfiguration = BlurConfiguration().withAllBlurDisabled()

        /**
         * 创建启用所有模糊的配置
         */
        fun allEnabled(): BlurConfiguration = BlurConfiguration().withAllBlurEnabled()

        /**
         * 根据设备能力创建推荐配置
         * 现在所有Android版本都支持模糊效果，推荐启用所有模糊
         */
        fun recommended(): BlurConfiguration {
            return BlurConfiguration(
                topBarBlurEnabled = true,
                topBarBlurIntensity = 0.6f,
                topBarBackgroundAlpha = 0.1f,
                bottomBarBlurEnabled = true,
                bottomBarBlurIntensity = 0.6f,
                bottomBarBackgroundAlpha = 0.1f,
                dialogBlurEnabled = true,
                dialogBlurIntensity = 0.7f,
                dialogBackgroundAlpha = 0.2f,
                overlayBlurEnabled = true,
                overlayBlurIntensity = 0.6f,
                overlayBackgroundAlpha = 0.1f
            )
        }

        /**
         * 强度预设值
         */
        object Intensity {
            const val LIGHT = 0.3f
            const val MEDIUM = 0.5f
            const val STRONG = 0.7f
            const val MAXIMUM = 1.0f
        }

        /**
         * 背景透明度预设值
         */
        object BackgroundAlpha {
            const val TRANSPARENT = 0.0f      // 完全透明
            const val SUBTLE = 0.05f          // 微妙
            const val LIGHT = 0.1f            // 轻度（默认）
            const val MEDIUM = 0.2f           // 中度
            const val STRONG = 0.5f           // 强度
            const val HEAVY = 0.8f            // 重度
            const val MAXIMUM = 1.0f          // 最大（完全不透明）
        }

        /**
         * 半径预设值
         */
        object Radius {
            val SMALL = 10.dp
            val MEDIUM = 20.dp
            val LARGE = 30.dp
            val EXTRA_LARGE = 40.dp
        }
    }
}

/**
 * 模糊组件类型枚举
 */
enum class BlurComponent {
    /**
     * 顶部应用栏
     */
    TOP_BAR,

    /**
     * 底部导航栏
     */
    BOTTOM_BAR,

    /**
     * 对话框
     */
    DIALOG,

    /**
     * 覆盖层
     */
    OVERLAY
}

/**
 * 模糊组件扩展属性
 */
val BlurComponent.displayName: String
    get() = when (this) {
        BlurComponent.TOP_BAR -> "顶部栏"
        BlurComponent.BOTTOM_BAR -> "底部栏"
        BlurComponent.DIALOG -> "对话框"
        BlurComponent.OVERLAY -> "覆盖层"
    }

val BlurComponent.description: String
    get() = when (this) {
        BlurComponent.TOP_BAR -> "为顶部应用栏添加毛玻璃效果"
        BlurComponent.BOTTOM_BAR -> "为底部导航栏添加毛玻璃效果"
        BlurComponent.DIALOG -> "为对话框背景添加模糊效果"
        BlurComponent.OVERLAY -> "为覆盖层添加模糊效果"
    }

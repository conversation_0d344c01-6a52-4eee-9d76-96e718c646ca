package com.weinuo.quickcommands.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 传感器权限工具类
 * 管理传感器相关权限的检查、申请和引导
 * 适用于运动识别、睡眠检测等传感器功能
 */
object SensorPermissionUtil {
    
    /**
     * 运动识别权限
     * Android Q (API 29) 及以上版本需要此权限
     */
    private const val ACTIVITY_RECOGNITION_PERMISSION = Manifest.permission.ACTIVITY_RECOGNITION
    
    /**
     * 检查是否拥有运动识别权限
     * @param context 上下文
     * @return 是否拥有运动识别权限
     */
    fun hasActivityRecognitionPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(
                context, 
                ACTIVITY_RECOGNITION_PERMISSION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android Q以下版本不需要此权限
            true
        }
    }
    
    /**
     * 申请运动识别权限
     * @param launcher 权限申请启动器
     */
    fun requestActivityRecognitionPermission(launcher: ActivityResultLauncher<Array<String>>) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            launcher.launch(arrayOf(ACTIVITY_RECOGNITION_PERMISSION))
        }
    }
    
    /**
     * 打开应用详情设置页面
     * @param context 上下文
     */
    fun openAppDetailsSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 忽略异常，用户可以手动到设置中授权
        }
    }
    
    /**
     * 运动识别权限说明对话框
     * 向用户解释为什么需要运动识别权限
     */
    @Composable
    fun ActivityRecognitionPermissionRationaleDialog(
        onConfirm: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "运动识别权限说明",
            message = """
                为了使用运动识别相关功能，应用需要运动识别权限：

                • 运动状态检测：识别用户当前的运动状态（步行、跑步、骑车、开车、静止等）
                • 睡眠状态检测：通过运动模式推断用户的睡眠状态
                • 活动模式识别：根据设备运动模式触发相应的自动化操作

                隐私保护承诺：
                • 仅用于条件检测，不会收集您的运动数据
                • 不会上传或分享您的活动信息
                • 您可以随时在系统设置中撤销此权限

                注意：此权限仅在Android 10及以上版本需要。
            """.trimIndent(),
            confirmText = "申请权限",
            dismissText = "取消",
            onConfirm = onConfirm,
            onDismiss = onDismiss
        )
    }
    
    /**
     * 运动识别权限被拒绝后的说明对话框
     */
    @Composable
    fun ActivityRecognitionPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "运动识别权限被拒绝",
            message = """
                运动识别功能需要运动识别权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动授予运动识别权限：
                • 运动状态检测（步行、跑步、骑车、开车、静止）
                • 睡眠状态检测
                • 基于活动模式的自动化触发

                您可以点击"打开设置"直接跳转到应用权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }
    
    /**
     * 获取需要的运动识别权限列表
     * @return 运动识别权限数组
     */
    fun getRequiredActivityRecognitionPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            arrayOf(ACTIVITY_RECOGNITION_PERMISSION)
        } else {
            emptyArray()
        }
    }

    /**
     * 获取所需的传感器权限列表（标准方法名）
     * @return 传感器权限数组
     */
    fun getRequiredSensorPermissions(): Array<String> {
        return getRequiredActivityRecognitionPermissions()
    }
    
    /**
     * 检查运动识别权限的详细状态
     * @param context 上下文
     * @return 权限状态描述
     */
    fun getActivityRecognitionPermissionStatus(context: Context): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (hasActivityRecognitionPermission(context)) {
                "已授权"
            } else {
                "未授权"
            }
        } else {
            "不需要权限（Android 10以下）"
        }
    }
    
    /**
     * 检查是否需要运动识别权限
     * @return 是否需要权限
     */
    fun isActivityRecognitionPermissionRequired(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q
    }
}

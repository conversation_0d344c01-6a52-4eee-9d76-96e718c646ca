package com.weinuo.quickcommands.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.ui.activities.RingtoneSelectionActivity
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.screens.LocalNavController
import com.weinuo.quickcommands.utils.FilePickerUtil
import com.weinuo.quickcommands.utils.RingtoneHelper


/**
 * 媒体任务配置数据提供器
 *
 * 提供媒体任务特定的配置项列表，为每个媒体操作类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 *
 * 支持三种媒体操作类型：
 * 1. 多媒体控制 - 模拟媒体按钮、默认播放器控制、音频按钮
 * 2. 播放/停止声音 - 选择文件、选择铃声、停止现有声音
 * 3. 麦克风录音 - 自定义时间、直到被取消、取消录音
 */
object MediaTaskConfigProvider {

    /**
     * 获取媒体任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 媒体任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<MediaOperation>> {
        return listOf(
            // 多媒体控制
            ConfigurationCardItem(
                id = "multimedia_control",
                title = context.getString(R.string.media_multimedia_control),
                description = context.getString(R.string.media_multimedia_control_description),
                operationType = MediaOperation.MULTIMEDIA_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    MultimediaControlConfigContent(operation, onComplete)
                }
            ),

            // 播放/停止声音
            ConfigurationCardItem(
                id = "play_stop_sound",
                title = context.getString(R.string.media_play_stop_sound),
                description = context.getString(R.string.media_play_stop_sound_description),
                operationType = MediaOperation.PLAY_STOP_SOUND,
                permissionRequired = true,
                content = { operation, onComplete ->
                    PlayStopSoundConfigContent(operation, onComplete)
                }
            ),

            // 麦克风录音 - 实验性功能
            ConfigurationCardItem(
                id = "microphone_recording",
                title = context.getString(R.string.media_microphone_recording),
                description = context.getString(R.string.media_microphone_recording_description),
                operationType = MediaOperation.MICROPHONE_RECORDING,
                permissionRequired = true,
                isExperimental = true,
                content = { operation, onComplete ->
                    MicrophoneRecordingConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 媒体任务配置内容组件
 *
 * 统一的媒体任务配置入口，根据操作类型调用相应的配置组件
 */
@Composable
fun MediaTaskConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 默认使用多媒体控制配置
    MultimediaControlConfigContent(MediaOperation.MULTIMEDIA_CONTROL, onComplete)
}

/**
 * 多媒体控制配置内容组件
 */
@Composable
private fun MultimediaControlConfigContent(
    operation: MediaOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 状态变量
    var multimediaControlType by rememberSaveable {
        mutableStateOf(MultimediaControlType.SIMULATE_MEDIA_BUTTON)
    }
    var mediaButtonType by rememberSaveable {
        mutableStateOf(MediaButtonType.PLAY_PAUSE)
    }
    var audioButtonType by rememberSaveable {
        mutableStateOf(AudioButtonType.VOLUME_UP)
    }
    var playerControlType by rememberSaveable {
        mutableStateOf(PlayerControlType.PLAY)
    }



    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 控制类型选择
        Text(
            text = "控制类型",
            style = MaterialTheme.typography.bodyLarge
        )

        Column(modifier = Modifier.selectableGroup()) {
            MultimediaControlType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = multimediaControlType == type,
                            onClick = { multimediaControlType = type },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = multimediaControlType == type,
                        onClick = { multimediaControlType = type }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (type) {
                            MultimediaControlType.SIMULATE_MEDIA_BUTTON -> "模拟媒体按钮"
                            MultimediaControlType.DEFAULT_PLAYER_CONTROL -> "默认播放器控制"
                            MultimediaControlType.SIMULATE_AUDIO_BUTTON -> "模拟音频按钮"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 根据控制类型显示具体选项
        when (multimediaControlType) {
            MultimediaControlType.SIMULATE_MEDIA_BUTTON -> {
                Text(
                    text = "媒体按钮",
                    style = MaterialTheme.typography.bodyMedium
                )

                Column(modifier = Modifier.selectableGroup()) {
                    MediaButtonType.values().forEach { type ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = mediaButtonType == type,
                                    onClick = { mediaButtonType = type },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = mediaButtonType == type,
                                onClick = { mediaButtonType = type }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = when (type) {
                                    MediaButtonType.PLAY_PAUSE -> "播放/暂停"
                                    MediaButtonType.PLAY -> "播放"
                                    MediaButtonType.PAUSE -> "暂停"
                                    MediaButtonType.NEXT_TRACK -> "下一曲"
                                    MediaButtonType.PREVIOUS_TRACK -> "上一曲"
                                    MediaButtonType.STOP -> "停止"
                                    MediaButtonType.FAST_FORWARD -> "快进"
                                    MediaButtonType.REWIND -> "快退"
                                },
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }
            MultimediaControlType.DEFAULT_PLAYER_CONTROL -> {
                Text(
                    text = "播放器控制",
                    style = MaterialTheme.typography.bodyMedium
                )

                Column(modifier = Modifier.selectableGroup()) {
                    PlayerControlType.values().forEach { type ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = playerControlType == type,
                                    onClick = { playerControlType = type },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = playerControlType == type,
                                onClick = { playerControlType = type }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = when (type) {
                                    PlayerControlType.PLAY -> "播放"
                                    PlayerControlType.PAUSE -> "暂停"
                                    PlayerControlType.STOP -> "停止"
                                    PlayerControlType.NEXT -> "下一曲"
                                    PlayerControlType.PREVIOUS -> "上一曲"
                                },
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }
            MultimediaControlType.SIMULATE_AUDIO_BUTTON -> {
                Text(
                    text = "音频按钮",
                    style = MaterialTheme.typography.bodyMedium
                )

                Column(modifier = Modifier.selectableGroup()) {
                    AudioButtonType.values().forEach { type ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = audioButtonType == type,
                                    onClick = { audioButtonType = type },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = audioButtonType == type,
                                onClick = { audioButtonType = type }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = when (type) {
                                    AudioButtonType.VOLUME_UP -> "音量增加"
                                    AudioButtonType.VOLUME_DOWN -> "音量减小"
                                    AudioButtonType.VOLUME_MUTE -> "静音"
                                },
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确定按钮
        Button(
            onClick = {
                val task = MediaTask(
                    operation = operation,
                    multimediaControlType = multimediaControlType,
                    mediaButtonType = mediaButtonType,
                    playerControlType = playerControlType,
                    audioButtonType = audioButtonType
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确定")
        }
    }

}

/**
 * 播放/停止声音配置内容组件
 */
@Composable
private fun PlayStopSoundConfigContent(
    operation: MediaOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    val navController = LocalNavController.current
    val context = LocalContext.current

    // 状态变量
    var soundPlayType by rememberSaveable {
        mutableStateOf(SoundPlayType.SELECT_RINGTONE)
    }
    var selectedFilePath by rememberSaveable {
        mutableStateOf("")
    }
    var selectedFileName by rememberSaveable {
        mutableStateOf("")
    }
    var selectedRingtoneUri by rememberSaveable {
        mutableStateOf("")
    }
    var selectedRingtoneName by rememberSaveable {
        mutableStateOf("")
    }
    var audioStreamType by rememberSaveable {
        mutableStateOf(AudioStreamType.MEDIA_MUSIC)
    }
    var waitForCompletion by rememberSaveable {
        mutableStateOf(false)
    }

    // 文件选择器启动器
    val fileLauncher = FilePickerUtil.rememberFilePickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getRealPath(context, it)
            selectedFilePath = realPath ?: it.toString()
            // 使用显示路径仅用于UI显示
            selectedFileName = FilePickerUtil.getDisplayPath(context, it)
        }
    }

    // 铃声选择启动器
    val ringtoneSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            val ringtoneUri = data?.getStringExtra(RingtoneSelectionActivity.RESULT_RINGTONE_URI)
            val ringtoneName = data?.getStringExtra(RingtoneSelectionActivity.RESULT_RINGTONE_NAME)
            val ringtoneTypeString = data?.getStringExtra(RingtoneSelectionActivity.RESULT_RINGTONE_TYPE)

            if (ringtoneUri != null && ringtoneName != null) {
                selectedRingtoneUri = ringtoneUri
                selectedRingtoneName = ringtoneName

                // 根据铃声类型自动设置音频通道
                val ringtoneType = try {
                    RingtoneHelper.RingtoneType.valueOf(ringtoneTypeString ?: "NOTIFICATION")
                } catch (e: IllegalArgumentException) {
                    RingtoneHelper.RingtoneType.NOTIFICATION
                }

                audioStreamType = when (ringtoneType) {
                    RingtoneHelper.RingtoneType.RINGTONE -> AudioStreamType.RINGTONE
                    RingtoneHelper.RingtoneType.NOTIFICATION -> AudioStreamType.NOTIFICATION
                    RingtoneHelper.RingtoneType.ALARM -> AudioStreamType.ALARM
                }
            }
        }
    }



    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 声音播放类型选择
        Text(
            text = "声音播放类型",
            style = MaterialTheme.typography.bodyLarge
        )

        Column(modifier = Modifier.selectableGroup()) {
            SoundPlayType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = soundPlayType == type,
                            onClick = { soundPlayType = type },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = soundPlayType == type,
                        onClick = { soundPlayType = type }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (type) {
                            SoundPlayType.SELECT_FILE -> "选择文件"
                            SoundPlayType.SELECT_RINGTONE -> "选择铃声"
                            SoundPlayType.STOP_EXISTING_SOUND -> "停止现有声音"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 文件选择（仅在选择文件时显示）
        if (soundPlayType == SoundPlayType.SELECT_FILE) {
            Text(
                text = "选择音频文件",
                style = MaterialTheme.typography.bodyMedium
            )

            // 文件选择按钮
            Button(
                onClick = {
                    FilePickerUtil.launchFilePicker(
                        fileLauncher,
                        arrayOf("audio/*", "video/*", "*/*")
                    )
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    if (selectedFileName.isEmpty()) {
                        "选择音频文件"
                    } else {
                        "已选择: $selectedFileName"
                    }
                )
            }

            // 显示选择的文件信息
            if (selectedFileName.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "选择的文件",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = selectedFileName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        // 铃声选择（仅在选择铃声时显示）
        if (soundPlayType == SoundPlayType.SELECT_RINGTONE) {
            Text(
                text = "选择铃声",
                style = MaterialTheme.typography.bodyMedium
            )

            OutlinedButton(
                onClick = {
                    // 根据当前音频通道类型确定铃声类型
                    val ringtoneType = when (audioStreamType) {
                        AudioStreamType.RINGTONE -> RingtoneHelper.RingtoneType.RINGTONE
                        AudioStreamType.ALARM -> RingtoneHelper.RingtoneType.ALARM
                        AudioStreamType.NOTIFICATION -> RingtoneHelper.RingtoneType.NOTIFICATION
                        else -> RingtoneHelper.RingtoneType.NOTIFICATION
                    }

                    // 启动铃声选择Activity
                    RingtoneSelectionActivity.startForSelection(
                        context = context,
                        ringtoneType = ringtoneType,
                        initialSelectedUri = selectedRingtoneUri
                    )
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    if (selectedRingtoneName.isNotEmpty()) {
                        "已选择: $selectedRingtoneName"
                    } else {
                        "点击选择铃声"
                    }
                )
            }
        }

        // 音频通道选择（不在停止现有声音时显示）
        if (soundPlayType != SoundPlayType.STOP_EXISTING_SOUND) {
            Text(
                text = "音频通道",
                style = MaterialTheme.typography.bodyMedium
            )

            Column(modifier = Modifier.selectableGroup()) {
                AudioStreamType.values().forEach { type ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = audioStreamType == type,
                                onClick = { audioStreamType = type },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = audioStreamType == type,
                            onClick = { audioStreamType = type }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = when (type) {
                                AudioStreamType.RINGTONE -> "铃声"
                                AudioStreamType.ALARM -> "闹钟"
                                AudioStreamType.NOTIFICATION -> "通知"
                                AudioStreamType.MEDIA_MUSIC -> "媒体/音乐"
                                AudioStreamType.SYSTEM -> "系统"
                                AudioStreamType.VOICE_CALL -> "语音通话"
                            },
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 等待完成选项
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Checkbox(
                    checked = waitForCompletion,
                    onCheckedChange = { waitForCompletion = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "完成后才能后续动作",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确定按钮
        Button(
            onClick = {
                val task = MediaTask(
                    operation = operation,
                    soundPlayType = soundPlayType,
                    selectedFilePath = selectedFilePath,
                    selectedFileName = selectedFileName,
                    selectedRingtoneUri = selectedRingtoneUri,
                    selectedRingtoneName = selectedRingtoneName,
                    audioStreamType = audioStreamType,
                    waitForCompletion = waitForCompletion
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (soundPlayType) {
                SoundPlayType.SELECT_FILE -> selectedFilePath.isNotEmpty()
                SoundPlayType.SELECT_RINGTONE -> selectedRingtoneUri.isNotEmpty()
                SoundPlayType.STOP_EXISTING_SOUND -> true
            }
        ) {
            Text("确定")
        }
    }


}

/**
 * 麦克风录音配置内容组件
 */
@Composable
private fun MicrophoneRecordingConfigContent(
    operation: MediaOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    val context = LocalContext.current

    // 状态变量
    var recordingDurationType by rememberSaveable {
        mutableStateOf(RecordingDurationType.CUSTOM_TIME)
    }
    var recordingMinutes by rememberSaveable {
        mutableStateOf("0")
    }
    var recordingSeconds by rememberSaveable {
        mutableStateOf("30")
    }
    var recordingSource by rememberSaveable {
        mutableStateOf(RecordingSource.STANDARD_MIC)
    }
    var recordingFormat by rememberSaveable {
        mutableStateOf(RecordingFormat.THREE_GPP)
    }
    var recordingWaitForCompletion by rememberSaveable {
        mutableStateOf(false)
    }

    // 文件路径相关状态
    var recordingFolderPath by rememberSaveable {
        mutableStateOf("")
    }
    var recordingFileName by rememberSaveable {
        mutableStateOf("")
    }

    // 文件选择器启动器
    val directoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            recordingFolderPath = realPath ?: it.toString()
        }
    }



    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 录音时长类型选择
        Text(
            text = "录音时长类型",
            style = MaterialTheme.typography.bodyLarge
        )

        Column(modifier = Modifier.selectableGroup()) {
            RecordingDurationType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = recordingDurationType == type,
                            onClick = { recordingDurationType = type },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = recordingDurationType == type,
                        onClick = { recordingDurationType = type }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (type) {
                            RecordingDurationType.CUSTOM_TIME -> "自定义时间"
                            RecordingDurationType.UNTIL_CANCELLED -> "直到被取消"
                            RecordingDurationType.CANCEL_RECORDING -> "取消录音"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 自定义时间输入（仅在自定义时间时显示）
        if (recordingDurationType == RecordingDurationType.CUSTOM_TIME) {
            Text(
                text = "录音时长",
                style = MaterialTheme.typography.bodyMedium
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                OutlinedTextField(
                    value = recordingMinutes,
                    onValueChange = { recordingMinutes = it },
                    label = { Text("分钟") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.weight(1f)
                )
                Spacer(modifier = Modifier.width(8.dp))
                OutlinedTextField(
                    value = recordingSeconds,
                    onValueChange = { recordingSeconds = it },
                    label = { Text("秒") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.weight(1f)
                )
            }
        }

        // 录音配置（不在取消录音时显示）
        if (recordingDurationType != RecordingDurationType.CANCEL_RECORDING) {
            Text(
                text = "录音音频源",
                style = MaterialTheme.typography.bodyMedium
            )

            Column(modifier = Modifier.selectableGroup()) {
                RecordingSource.values().forEach { source ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = recordingSource == source,
                                onClick = { recordingSource = source },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = recordingSource == source,
                            onClick = { recordingSource = source }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = when (source) {
                                RecordingSource.STANDARD_MIC -> "标准麦克风"
                                RecordingSource.CAMERA_MIC -> "摄像机麦克风"
                                RecordingSource.UNPROCESSED -> "未处理"
                            },
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "录音格式",
                style = MaterialTheme.typography.bodyMedium
            )

            Column(modifier = Modifier.selectableGroup()) {
                RecordingFormat.values().forEach { format ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = recordingFormat == format,
                                onClick = { recordingFormat = format },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = recordingFormat == format,
                            onClick = { recordingFormat = format }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = when (format) {
                                RecordingFormat.THREE_GPP -> "3GPP"
                                RecordingFormat.MPEG4 -> "MPEG4"
                                RecordingFormat.AAC -> "AAC"
                            },
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 录音文件保存位置配置
            Text(
                text = "录音文件保存位置",
                style = MaterialTheme.typography.bodyMedium
            )

            // 文件夹选择按钮
            Button(
                onClick = {
                    FilePickerUtil.launchDirectoryPicker(directoryLauncher)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(if (recordingFolderPath.isEmpty()) "选择保存文件夹" else "已选择: $recordingFolderPath")
            }

            // 文件名输入
            OutlinedTextField(
                value = recordingFileName,
                onValueChange = { recordingFileName = it },
                label = { Text("录音文件名") },
                placeholder = { Text("例如: recording.3gp（留空使用默认文件名）") },
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 等待完成选项
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Checkbox(
                    checked = recordingWaitForCompletion,
                    onCheckedChange = { recordingWaitForCompletion = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "完成后才能后续动作",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确定按钮
        Button(
            onClick = {
                val task = MediaTask(
                    operation = operation,
                    recordingDurationType = recordingDurationType,
                    recordingMinutes = recordingMinutes.toIntOrNull() ?: 0,
                    recordingSeconds = recordingSeconds.toIntOrNull() ?: 30,
                    recordingSource = recordingSource,
                    recordingFormat = recordingFormat,
                    recordingWaitForCompletion = recordingWaitForCompletion,
                    recordingFolderPath = recordingFolderPath,
                    recordingFileName = recordingFileName
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确定")
        }
    }

}

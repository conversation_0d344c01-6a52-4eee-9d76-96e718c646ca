package com.weinuo.quickcommands.ui.theme.manager

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.weinuo.quickcommands.data.SettingsRepository

/**
 * UI间距配置管理器
 * 
 * 负责管理应用中各种UI元素的间距配置，包括：
 * - 设置项的垂直间距
 * - 分割线的水平间距
 * - 卡片内容的间距等
 */
class UISpacingConfigurationManager private constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {
    
    /**
     * UI间距配置数据类
     */
    data class UISpacingConfiguration(
        val settingsItemVerticalPadding: Int = 12,      // 设置项垂直间距（dp）
        val dividerHorizontalPadding: Int = 0,          // 分割线水平间距（dp）
        val settingsCardPadding: Int = 16,              // 设置卡片内边距（dp）
        val settingsItemSpacing: Int = 16,              // 设置项之间的间距（dp）
        val settingsTitleSpacing: Int = 8,              // 设置标题与内容的间距（dp）
        val settingsDescriptionSpacing: Int = 4,        // 设置描述与标题的间距（dp）
        val settingsGroupTitleHorizontalPadding: Int = 13, // 设置分组标题水平间距（dp）
        val settingsGroupTitleTopPadding: Int = 30,     // 设置分组标题上边距（dp）
        val settingsGroupTitleBottomPadding: Int = 6,   // 设置分组标题下边距（dp）
        val globalSettingsItemSpacing: Int = 0,         // 全局设置界面专用间距（dp）
        val dividerVisible: Boolean = true              // 分割线是否显示
    )
    
    // 当前配置状态
    private var _currentConfiguration by mutableStateOf(loadConfiguration())

    /**
     * 获取当前UI间距配置
     */
    fun getUISpacingConfiguration(): UISpacingConfiguration {
        // 每次获取时重新加载配置，确保获取最新值
        _currentConfiguration = loadConfiguration()
        return _currentConfiguration
    }
    
    /**
     * 从设置中加载配置
     */
    private fun loadConfiguration(): UISpacingConfiguration {
        val settings = settingsRepository.globalSettings.value
        return UISpacingConfiguration(
            settingsItemVerticalPadding = settings.uiSettingsItemVerticalPadding,
            dividerHorizontalPadding = settings.uiDividerHorizontalPadding,
            settingsCardPadding = settings.uiSettingsCardPadding,
            settingsItemSpacing = settings.uiSettingsItemSpacing,
            settingsTitleSpacing = settings.uiSettingsTitleSpacing,
            settingsDescriptionSpacing = settings.uiSettingsDescriptionSpacing,
            settingsGroupTitleHorizontalPadding = settings.uiSettingsGroupTitleHorizontalPadding,
            settingsGroupTitleTopPadding = settings.uiSettingsGroupTitleTopPadding,
            settingsGroupTitleBottomPadding = settings.uiSettingsGroupTitleBottomPadding,
            globalSettingsItemSpacing = settings.uiGlobalSettingsItemSpacing,
            dividerVisible = settings.uiDividerVisible
        )
    }

    /**
     * 保存配置到设置
     */
    private fun saveConfiguration() {
        val currentSettings = settingsRepository.globalSettings.value
        val updatedSettings = currentSettings.copy(
            uiSettingsItemVerticalPadding = _currentConfiguration.settingsItemVerticalPadding,
            uiDividerHorizontalPadding = _currentConfiguration.dividerHorizontalPadding,
            uiSettingsCardPadding = _currentConfiguration.settingsCardPadding,
            uiSettingsItemSpacing = _currentConfiguration.settingsItemSpacing,
            uiSettingsTitleSpacing = _currentConfiguration.settingsTitleSpacing,
            uiSettingsDescriptionSpacing = _currentConfiguration.settingsDescriptionSpacing,
            uiSettingsGroupTitleHorizontalPadding = _currentConfiguration.settingsGroupTitleHorizontalPadding,
            uiSettingsGroupTitleTopPadding = _currentConfiguration.settingsGroupTitleTopPadding,
            uiSettingsGroupTitleBottomPadding = _currentConfiguration.settingsGroupTitleBottomPadding,
            uiGlobalSettingsItemSpacing = _currentConfiguration.globalSettingsItemSpacing,
            uiDividerVisible = _currentConfiguration.dividerVisible
        )
        settingsRepository.saveGlobalSettings(updatedSettings)
    }
    
    /**
     * 更新设置项垂直间距
     */
    fun updateSettingsItemVerticalPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsItemVerticalPadding = padding)
        saveConfiguration()
    }
    
    /**
     * 更新分割线水平间距
     */
    fun updateDividerHorizontalPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(dividerHorizontalPadding = padding)
        saveConfiguration()
    }
    
    /**
     * 更新设置卡片内边距
     */
    fun updateSettingsCardPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsCardPadding = padding)
        saveConfiguration()
    }
    
    /**
     * 更新设置项间距
     */
    fun updateSettingsItemSpacing(spacing: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsItemSpacing = spacing)
        saveConfiguration()
    }
    
    /**
     * 更新设置标题间距
     */
    fun updateSettingsTitleSpacing(spacing: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsTitleSpacing = spacing)
        saveConfiguration()
    }
    
    /**
     * 更新设置描述间距
     */
    fun updateSettingsDescriptionSpacing(spacing: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsDescriptionSpacing = spacing)
        saveConfiguration()
    }

    /**
     * 更新设置分组标题水平间距
     */
    fun updateSettingsGroupTitleHorizontalPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsGroupTitleHorizontalPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新设置分组标题上边距
     */
    fun updateSettingsGroupTitleTopPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsGroupTitleTopPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新设置分组标题下边距
     */
    fun updateSettingsGroupTitleBottomPadding(padding: Int) {
        _currentConfiguration = _currentConfiguration.copy(settingsGroupTitleBottomPadding = padding)
        saveConfiguration()
    }

    /**
     * 更新全局设置界面专用间距
     */
    fun updateGlobalSettingsItemSpacing(spacing: Int) {
        _currentConfiguration = _currentConfiguration.copy(globalSettingsItemSpacing = spacing)
        saveConfiguration()
    }

    /**
     * 更新分割线显示状态
     */
    fun updateDividerVisible(visible: Boolean) {
        _currentConfiguration = _currentConfiguration.copy(dividerVisible = visible)
        saveConfiguration()
    }
    
    /**
     * 重新加载配置
     */
    fun reloadConfiguration() {
        _currentConfiguration = loadConfiguration()
    }

    /**
     * 重置为默认值
     */
    fun resetToDefaults() {
        _currentConfiguration = UISpacingConfiguration()
        saveConfiguration()
    }
    
    /**
     * 批量更新UI间距配置
     */
    fun updateUISpacingConfiguration(
        settingsItemVerticalPadding: Int? = null,
        dividerHorizontalPadding: Int? = null,
        settingsCardPadding: Int? = null,
        settingsItemSpacing: Int? = null,
        settingsTitleSpacing: Int? = null,
        settingsDescriptionSpacing: Int? = null,
        settingsGroupTitleHorizontalPadding: Int? = null,
        settingsGroupTitleTopPadding: Int? = null,
        settingsGroupTitleBottomPadding: Int? = null,
        globalSettingsItemSpacing: Int? = null,
        dividerVisible: Boolean? = null
    ) {
        _currentConfiguration = _currentConfiguration.copy(
            settingsItemVerticalPadding = settingsItemVerticalPadding ?: _currentConfiguration.settingsItemVerticalPadding,
            dividerHorizontalPadding = dividerHorizontalPadding ?: _currentConfiguration.dividerHorizontalPadding,
            settingsCardPadding = settingsCardPadding ?: _currentConfiguration.settingsCardPadding,
            settingsItemSpacing = settingsItemSpacing ?: _currentConfiguration.settingsItemSpacing,
            settingsTitleSpacing = settingsTitleSpacing ?: _currentConfiguration.settingsTitleSpacing,
            settingsDescriptionSpacing = settingsDescriptionSpacing ?: _currentConfiguration.settingsDescriptionSpacing,
            settingsGroupTitleHorizontalPadding = settingsGroupTitleHorizontalPadding ?: _currentConfiguration.settingsGroupTitleHorizontalPadding,
            settingsGroupTitleTopPadding = settingsGroupTitleTopPadding ?: _currentConfiguration.settingsGroupTitleTopPadding,
            settingsGroupTitleBottomPadding = settingsGroupTitleBottomPadding ?: _currentConfiguration.settingsGroupTitleBottomPadding,
            globalSettingsItemSpacing = globalSettingsItemSpacing ?: _currentConfiguration.globalSettingsItemSpacing,
            dividerVisible = dividerVisible ?: _currentConfiguration.dividerVisible
        )
        saveConfiguration()
    }
    
    companion object {
        @Volatile
        private var INSTANCE: UISpacingConfigurationManager? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(context: Context, settingsRepository: SettingsRepository): UISpacingConfigurationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UISpacingConfigurationManager(context.applicationContext, settingsRepository).also { INSTANCE = it }
            }
        }
    }
}

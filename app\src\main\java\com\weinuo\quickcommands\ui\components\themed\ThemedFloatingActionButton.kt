package com.weinuo.quickcommands.ui.components.themed

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import com.weinuo.quickcommands.ui.theme.config.FloatingActionButtonConfig
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的浮动操作按钮组件
 *
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用海洋蓝专用组件（OceanBlueFAB）- 分层设计风格，带阴影效果
 * - 天空蓝主题：使用天空蓝专用组件（SkyBlueFAB）- 整合设计风格，无阴影，大圆角
 * - 未来主题：可以使用各自独有的实现
 *
 * 浮动操作按钮是Material Design的重要组件，
 * 不同主题的实现会体现不同的设计理念。
 * 每个主题都有完全独立的FAB组件实现，确保主题间不会相互影响。
 */
@Composable
fun ThemedFloatingActionButton(
    config: FloatingActionButtonConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current

    // 使用当前主题的组件工厂创建浮动操作按钮
    themeContext.componentFactory.createFloatingActionButton()(
        config.copy(modifier = modifier)
    )
}

/**
 * 主题感知的浮动操作按钮组件 - 便捷版本
 *
 * 提供更简洁的API，自动处理常见的配置
 */
@Composable
fun ThemedFloatingActionButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    shape: Shape? = null,
    containerColor: Color? = null,
    contentColor: Color? = null,
    content: @Composable () -> Unit
) {
    val config = FloatingActionButtonConfig(
        onClick = onClick,
        modifier = modifier,
        shape = shape,
        containerColor = containerColor,
        contentColor = contentColor,
        content = content
    )
    
    // 使用主题感知组件
    ThemedFloatingActionButton(config = config)
}

package com.weinuo.quickcommands.permission

import android.content.Context
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.utils.*

/**
 * 权限注册表 - 统一管理所有操作类型的权限检查逻辑
 *
 * 这个单例对象负责：
 * 1. 为所有操作类型提供统一的权限检查接口
 * 2. 将操作类型映射到相应的权限申请逻辑
 * 3. 支持嵌套权限检查（一个操作可能需要多个权限）
 * 4. 提供可扩展的权限检查架构
 *
 * 使用方法：
 * ```kotlin
 * PermissionRegistry.checkPermissionForOperation(
 *     operation = selectedOperation,
 *     globalPermissionManager = globalPermissionManager,
 *     context = context
 * )
 * ```
 *
 * <AUTHOR>
 * @since 1.0.0
 */
object PermissionRegistry {

    /**
     * 检查指定操作是否已经拥有所需权限
     *
     * @param operation 操作类型（可以是任何枚举类型）
     * @param globalPermissionManager 全局权限管理器实例
     * @param context Android上下文
     * @return true如果已经拥有所需权限，false如果还需要申请权限
     */
    fun hasPermissionForOperation(
        operation: Any,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ): Boolean {
        return when (operation) {
            // 通信相关权限检查
            is CommunicationStateType -> CommunicationPermissionUtil.hasAllCommunicationPermissions(context)
            is PhoneOperation -> CommunicationPermissionUtil.hasAllCommunicationPermissions(context)
            is InformationOperation -> when (operation) {
                InformationOperation.SEND_SMS -> CommunicationPermissionUtil.hasAllCommunicationPermissions(context)
                InformationOperation.SEND_EMAIL -> com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context) // 检查通知权限
                InformationOperation.MESSAGE_RINGTONE -> true // 信息铃声设置不需要特殊权限
            }

            // 位置相关权限检查
            is LocationOperation -> when (operation) {
                LocationOperation.SHARE_LOCATION -> {
                    // 分享位置需要位置权限和通信权限
                    LocationPermissionUtil.hasLocationPermission(context) &&
                    CommunicationPermissionUtil.hasAllCommunicationPermissions(context)
                }
                LocationOperation.TOGGLE_LOCATION_SERVICE,
                LocationOperation.FORCE_LOCATION_UPDATE -> com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                LocationOperation.SET_LOCATION_UPDATE_FREQUENCY -> LocationPermissionUtil.hasLocationPermission(context)
                else -> true // 其他位置操作不需要特殊权限
            }
            is ConnectionType -> when (operation) {
                ConnectionType.WIFI_NETWORK, ConnectionType.CELL_TOWER, ConnectionType.MOBILE_SIGNAL -> LocationPermissionUtil.hasLocationPermission(context)
                ConnectionType.BLUETOOTH_STATE, ConnectionType.BLUETOOTH_DEVICE -> BluetoothPermissionUtil.hasBluetoothPermission(context)
                else -> true // 其他连接类型不需要特殊权限
            }
            is TimeConditionType -> when (operation) {
                TimeConditionType.SUN_EVENT -> LocationPermissionUtil.hasLocationPermission(context)
                else -> true // 其他时间条件不需要特殊权限
            }

            // 媒体相关权限检查
            is MediaOperation -> when (operation) {
                MediaOperation.MICROPHONE_RECORDING -> {
                    // 录音需要录音权限和存储权限，采用逐步检查策略
                    MediaPermissionUtil.hasRecordAudioPermission(context) && CameraPermissionUtil.hasStoragePermission(context)
                }
                else -> true // 其他媒体操作不需要特殊权限
            }
            is CameraOperation -> when (operation) {
                CameraOperation.TAKE_PHOTO -> {
                    // 拍照需要相机权限和存储权限，采用逐步检查策略
                    // 只要有一个权限缺失就返回false，让权限检查组件逐步处理
                    CameraPermissionUtil.hasCameraPermission(context) && CameraPermissionUtil.hasStoragePermission(context)
                }
                CameraOperation.RECORD_VIDEO -> {
                    // 录像需要相机权限、录音权限和存储权限，采用逐步检查策略
                    // 只要有一个权限缺失就返回false，让权限检查组件逐步处理
                    CameraPermissionUtil.hasCameraPermission(context) &&
                    CameraPermissionUtil.hasRecordAudioPermission(context) &&
                    CameraPermissionUtil.hasStoragePermission(context)
                }
                CameraOperation.OPEN_LAST_PHOTO -> CameraPermissionUtil.hasStoragePermission(context)
                CameraOperation.SCREENSHOT -> CameraPermissionUtil.hasStoragePermission(context)
                else -> true
            }

            // 传感器相关权限检查
            is SensorStateType -> when (operation) {
                SensorStateType.ACTIVITY_RECOGNITION -> SensorPermissionUtil.hasActivityRecognitionPermission(context)
                else -> true // 其他传感器不需要特殊权限
            }

            // 应用状态条件权限检查
            is AppStateCategoryType -> when (operation) {
                AppStateCategoryType.INTERFACE_INTERACTION -> {
                    // 界面交互条件需要界面交互无障碍服务
                    hasInterfaceInteractionAccessibilityService(context)
                }
                else -> {
                    // 其他应用状态条件都需要使用情况访问权限
                    UsageStatsPermissionUtil.hasUsageStatsPermission(context)
                }
            }

            // 设备相关权限检查
            is DeviceEventType -> when (operation) {
                DeviceEventType.LOGIN_ATTEMPT_FAILED -> DeviceEventPermissionUtil.hasDeviceAdminPermission(context)
                DeviceEventType.NOTIFICATION_EVENT -> DeviceEventPermissionUtil.hasNotificationListenerPermission(context)
                else -> true // 其他设备事件不需要特殊权限
            }
            is DeviceActionOperation -> when (operation) {
                DeviceActionOperation.TOGGLE_STATUS_BAR -> DeviceEventPermissionUtil.hasAccessibilityPermission(context)
                DeviceActionOperation.VOICE_SEARCH -> MediaPermissionUtil.hasRecordAudioPermission(context)
                DeviceActionOperation.FLASHLIGHT_CONTROL -> CameraPermissionUtil.hasCameraPermission(context)
                DeviceActionOperation.SHIZUKU_COMMAND -> com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                // 系统操作功能需要系统操作无障碍服务
                DeviceActionOperation.QUICK_SETTINGS,
                DeviceActionOperation.POWER_MENU,
                DeviceActionOperation.RECENT_TASKS,
                DeviceActionOperation.APP_DRAWER,
                DeviceActionOperation.ACCESSIBILITY_TOGGLE,
                DeviceActionOperation.BACK_KEY -> hasSystemOperationAccessibilityService(context)
                else -> true // 其他设备操作不需要特殊权限
            }

            // 通知相关权限检查
            is NotificationOperation -> when (operation) {
                NotificationOperation.SHOW_NOTIFICATION -> com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)
                NotificationOperation.CLEAR_NOTIFICATIONS -> DeviceEventPermissionUtil.hasNotificationListenerPermission(context)
                NotificationOperation.SHOW_DIALOG -> OverlayPermissionUtil.hasOverlayPermission(context)
                NotificationOperation.SHOW_BUBBLE -> {
                    // 气泡通知需要先检查通知权限，然后检查气泡通知权限
                    com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context) &&
                    com.weinuo.quickcommands.utils.NotificationPermissionUtil.areBubblesAllowed(context)
                }
                else -> true // 其他通知操作不需要特殊权限
            }

            // 文件相关权限检查
            is FileOperation -> StoragePermissionUtil.hasStoragePermission(context)

            // 设备设置操作权限检查
            is DeviceSettingsOperation -> when (operation) {
                DeviceSettingsOperation.AUTO_ROTATE,
                DeviceSettingsOperation.ACCESSIBILITY_SERVICE,
                DeviceSettingsOperation.INVERT_COLORS,
                DeviceSettingsOperation.DEMO_MODE,
                DeviceSettingsOperation.AMBIENT_DISPLAY,
                DeviceSettingsOperation.SCREEN_LOCK,
                DeviceSettingsOperation.DIGITAL_ASSISTANT,
                DeviceSettingsOperation.DEFAULT_KEYBOARD,
                DeviceSettingsOperation.DRIVING_MODE,
                DeviceSettingsOperation.FONT_SIZE,
                DeviceSettingsOperation.ENTER_SCREENSAVER,
                DeviceSettingsOperation.DISPLAY_DENSITY,
                DeviceSettingsOperation.IMMERSIVE_MODE,
                DeviceSettingsOperation.DARK_THEME,
                DeviceSettingsOperation.POWER_SAVE_MODE,
                DeviceSettingsOperation.SYSTEM_SETTINGS,
                DeviceSettingsOperation.SET_WALLPAPER,
                DeviceSettingsOperation.KEYBOARD_HINT -> com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                else -> true // 其他设备设置操作不需要特殊权限
            }

            // 应用操作权限检查
            is ApplicationOperation -> when (operation) {
                ApplicationOperation.FORCE_STOP_APP -> {
                    // 强制停止应用需要Shizuku权限和使用情况统计权限
                    com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission() &&
                    UsageStatsPermissionUtil.hasUsageStatsPermission(context)
                }
                ApplicationOperation.FREEZE_APP,
                ApplicationOperation.UNFREEZE_APP -> {
                    // 冻结/解冻应用需要Shizuku权限
                    com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                }
                ApplicationOperation.SHELL_SCRIPT,
                ApplicationOperation.EXECUTE_SHELL_SCRIPT -> true // Shell脚本权限检查在具体执行模式中处理
                else -> true // 其他应用操作不需要特殊权限
            }

            // 连接操作权限检查
            is ConnectivityOperation -> when (operation) {
                ConnectivityOperation.CHECK_CONNECTION -> NetworkPermissionUtil.hasNetworkPermissions(context)
                ConnectivityOperation.WIFI_CONTROL,
                ConnectivityOperation.HOTSPOT_CONTROL,
                ConnectivityOperation.MOBILE_DATA_CONTROL,
                ConnectivityOperation.AIRPLANE_MODE_CONTROL,
                ConnectivityOperation.AUTO_SYNC_CONTROL,
                ConnectivityOperation.SYNC_ACCOUNT -> com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                else -> true // 其他连接操作不需要特殊权限
            }

            // 屏幕控制权限检查
            is ScreenControlOperation -> when (operation) {
                ScreenControlOperation.BLOCK_SCREEN_TOUCH -> OverlayPermissionUtil.hasOverlayPermission(context)
                ScreenControlOperation.CHECK_SCREEN_TEXT, ScreenControlOperation.READ_SCREEN_CONTENT -> {
                    // 检查屏幕文字和读取屏幕内容需要界面交互无障碍服务和存储权限
                    hasInterfaceInteractionAccessibilityService(context) && StoragePermissionUtil.hasStoragePermission(context)
                }
                ScreenControlOperation.CHECK_PIXEL_COLOR -> {
                    // 检查像素颜色需要界面交互无障碍服务和存储权限
                    hasInterfaceInteractionAccessibilityService(context) && StoragePermissionUtil.hasStoragePermission(context)
                }
                ScreenControlOperation.AUTO_CLICKER_PLAYBACK -> {
                    // 自动点击器功能需要自动点击器无障碍服务
                    hasAutoClickerAccessibilityService(context)
                }
                ScreenControlOperation.BRIGHTNESS_CONTROL,
                ScreenControlOperation.KEEP_DEVICE_AWAKE,
                ScreenControlOperation.SCREEN_ON_OFF,
                ScreenControlOperation.SCREEN_DIMNESS,
                ScreenControlOperation.FORCE_SCREEN_ROTATION,
                ScreenControlOperation.SET_SCREEN_TIMEOUT -> com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                else -> true // 其他屏幕控制操作不需要特殊权限
            }

            // 音量相关权限检查
            is VolumeOperation -> when (operation) {
                VolumeOperation.DO_NOT_DISTURB_MODE -> {
                    // 勿扰模式需要通知策略访问权限
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                        notificationManager.isNotificationPolicyAccessGranted
                    } else {
                        true // Android 6.0以下版本不需要此权限
                    }
                }
                else -> true // 其他音量操作不需要特殊权限
            }

            // 手动触发权限检查
            is ManualTriggerType -> when (operation) {
                ManualTriggerType.FLOATING_BUTTON -> OverlayPermissionUtil.hasOverlayPermission(context)
                ManualTriggerType.FINGERPRINT_GESTURE -> hasGestureRecognitionAccessibilityService(context)
                else -> true // 其他手动触发方式不需要特殊权限
            }

            // 全局设置操作权限检查
            is GlobalSettingsOperation -> when (operation) {
                GlobalSettingsOperation.WIDGET_UPDATE,
                GlobalSettingsOperation.DEBUG_MODE,
                GlobalSettingsOperation.EXPERIMENTAL_FEATURES -> true // 这些功能无需Shizuku权限
                else -> true // 其他设置也不需要权限
            }

            // Shell脚本Shizuku操作权限检查
            is ShellScriptShizukuOperation -> com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()

            // 智慧提醒操作权限检查
            is SmartReminderOperation -> when (operation) {
                SmartReminderOperation.ENABLE_REMINDER -> {
                    // 智慧提醒需要悬浮窗权限和Shizuku权限
                    OverlayPermissionUtil.hasOverlayPermission(context) &&
                    com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                }
                SmartReminderOperation.ENABLE_FLASHLIGHT_REMINDER -> {
                    // 手电筒提醒需要悬浮窗权限、Shizuku权限和相机权限
                    OverlayPermissionUtil.hasOverlayPermission(context) &&
                    com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission() &&
                    CameraPermissionUtil.hasCameraPermission(context)
                }
            }

            // 默认情况：不需要权限检查的操作
            else -> true
        }
    }

    /**
     * 为指定操作检查并显示权限确认对话框
     *
     * @param operation 操作类型（可以是任何枚举类型）
     * @param globalPermissionManager 全局权限管理器实例
     * @param context Android上下文
     */
    suspend fun checkPermissionForOperation(
        operation: Any,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            // 通信相关权限检查
            is CommunicationStateType -> checkCommunicationPermissions(globalPermissionManager, context)
            is PhoneOperation -> checkPhonePermissions(operation, globalPermissionManager, context)
            is InformationOperation -> checkInformationPermissions(operation, globalPermissionManager, context)

            // 位置相关权限检查
            is LocationOperation -> checkLocationPermissions(operation, globalPermissionManager, context)
            is ConnectionType -> checkConnectionPermissions(operation, globalPermissionManager, context)
            is TimeConditionType -> checkTimeBasedPermissions(operation, globalPermissionManager, context)

            // 媒体相关权限检查
            is MediaOperation -> checkMediaPermissions(operation, globalPermissionManager, context)
            is CameraOperation -> checkCameraPermissions(operation, globalPermissionManager, context)

            // 传感器相关权限检查
            is SensorStateType -> checkSensorPermissions(operation, globalPermissionManager, context)

            // 应用状态条件权限检查
            is AppStateCategoryType -> checkAppStatePermissions(operation, globalPermissionManager, context)

            // 设备相关权限检查
            is DeviceEventType -> checkDeviceEventPermissions(operation, globalPermissionManager, context)
            is DeviceActionOperation -> checkDeviceActionPermissions(operation, globalPermissionManager, context)
            is DeviceSettingsOperation -> checkDeviceSettingsPermissions(operation, globalPermissionManager, context)

            // 通知相关权限检查
            is NotificationOperation -> checkNotificationPermissions(operation, globalPermissionManager, context)

            // 文件相关权限检查
            is FileOperation -> checkFilePermissions(operation, globalPermissionManager, context)

            // 其他权限检查
            is ApplicationOperation -> checkApplicationPermissions(operation, globalPermissionManager, context)
            is ScreenControlOperation -> checkScreenControlPermissions(operation, globalPermissionManager, context)
            is VolumeOperation -> checkVolumePermissions(operation, globalPermissionManager, context)
            is ConnectivityOperation -> checkConnectivityPermissions(operation, globalPermissionManager, context)
            is ManualTriggerType -> checkManualTriggerPermissions(operation, globalPermissionManager, context)

            // 全局设置操作权限检查（仅保留独立功能）
            is GlobalSettingsOperation -> checkGlobalSettingsPermissions(operation, globalPermissionManager, context)

            // Shell脚本Shizuku操作权限检查
            is ShellScriptShizukuOperation -> checkShellScriptShizukuPermissions(operation, globalPermissionManager, context)

            // 智慧提醒操作权限检查
            is SmartReminderOperation -> checkSmartReminderPermissions(operation, globalPermissionManager, context)

            // 默认情况：无需权限检查
            else -> {
                // 对于不需要权限的操作类型，不执行任何操作
            }
        }
    }

    /**
     * 检查通信相关权限
     */
    private suspend fun checkCommunicationPermissions(
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        if (!CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
            globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.COMMUNICATION)
        }
    }

    /**
     * 检查电话操作权限
     */
    private suspend fun checkPhonePermissions(
        operation: PhoneOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        // 所有电话操作都需要通信权限
        if (!CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
            globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.COMMUNICATION)
        }
    }

    /**
     * 检查信息操作权限
     */
    private suspend fun checkInformationPermissions(
        operation: InformationOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            InformationOperation.SEND_SMS -> {
                // 发送短信需要通信权限
                if (!CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.COMMUNICATION)
                }
            }
            InformationOperation.SEND_EMAIL -> {
                // 发送邮件需要通知权限（用于完成和失败通知）
                if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.NOTIFICATION)
                }
            }
            InformationOperation.MESSAGE_RINGTONE -> {
                // 信息铃声设置不需要特殊权限
            }
        }
    }

    /**
     * 检查位置操作权限
     */
    private suspend fun checkLocationPermissions(
        operation: LocationOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            LocationOperation.SHARE_LOCATION -> {
                // 分享位置需要位置权限和通信权限，采用逐步检查策略
                val hasLocationPermission = LocationPermissionUtil.hasLocationPermission(context)
                val hasCommunicationPermission = CommunicationPermissionUtil.hasAllCommunicationPermissions(context)

                // 优先检查位置权限
                if (!hasLocationPermission) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.LOCATION)
                } else if (!hasCommunicationPermission) {
                    // 位置权限已有，检查通信权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.COMMUNICATION)
                }
                // 如果两个权限都有，则不显示任何对话框
            }
            LocationOperation.TOGGLE_LOCATION_SERVICE,
            LocationOperation.FORCE_LOCATION_UPDATE -> {
                // 这些操作需要Shizuku权限
                if (!com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                }
            }
            LocationOperation.SET_LOCATION_UPDATE_FREQUENCY -> {
                // 位置相关操作需要位置权限
                if (!LocationPermissionUtil.hasLocationPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.LOCATION)
                }
            }
        }
    }

    /**
     * 检查连接类型权限
     */
    private suspend fun checkConnectionPermissions(
        operation: ConnectionType,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            ConnectionType.WIFI_NETWORK -> {
                // WiFi网络检测需要位置权限
                if (!LocationPermissionUtil.hasLocationPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.LOCATION)
                }
            }
            ConnectionType.BLUETOOTH_STATE, ConnectionType.BLUETOOTH_DEVICE -> {
                // 蓝牙连接需要蓝牙权限
                if (!BluetoothPermissionUtil.hasBluetoothPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.BLUETOOTH)
                }
            }
            ConnectionType.CELL_TOWER, ConnectionType.MOBILE_SIGNAL -> {
                // 基站连接和手机信号检测需要位置权限
                if (!LocationPermissionUtil.hasLocationPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.LOCATION)
                }
            }
            else -> {
                // 其他连接类型通常不需要特殊权限
            }
        }
    }

    /**
     * 检查时间条件权限
     */
    private suspend fun checkTimeBasedPermissions(
        operation: TimeConditionType,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            TimeConditionType.SUN_EVENT -> {
                // 日出日落功能需要位置权限
                if (!LocationPermissionUtil.hasLocationPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.LOCATION)
                }
            }
            else -> {
                // 其他时间条件不需要特殊权限
            }
        }
    }

    /**
     * 检查媒体操作权限
     */
    private suspend fun checkMediaPermissions(
        operation: MediaOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            MediaOperation.MICROPHONE_RECORDING -> {
                // 录音需要录音权限和存储权限，智能权限检查策略
                val hasRecordAudioPermission = MediaPermissionUtil.hasRecordAudioPermission(context)
                val hasStoragePermission = CameraPermissionUtil.hasStoragePermission(context)

                if (!hasRecordAudioPermission) {
                    // 缺少录音权限，优先显示录音权限对话框
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.MICROPHONE)
                } else if (!hasStoragePermission) {
                    // 只缺存储权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
                }
                // 如果两个权限都有，则不显示任何对话框
            }
            else -> {
                // 其他媒体操作通常不需要特殊权限
            }
        }
    }

    /**
     * 检查相机操作权限
     */
    private suspend fun checkCameraPermissions(
        operation: CameraOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            CameraOperation.TAKE_PHOTO -> {
                // 拍照需要相机权限和存储权限，智能权限检查策略
                val hasCameraPermission = CameraPermissionUtil.hasCameraPermission(context)
                val hasStoragePermission = CameraPermissionUtil.hasStoragePermission(context)

                if (!hasCameraPermission) {
                    // 缺少相机权限，优先显示相机权限对话框
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.CAMERA)
                } else if (!hasStoragePermission) {
                    // 只缺存储权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
                }
                // 如果两个权限都有，则不显示任何对话框
            }
            CameraOperation.RECORD_VIDEO -> {
                // 录像需要相机权限、录音权限和存储权限，智能权限检查策略
                val hasCameraPermission = CameraPermissionUtil.hasCameraPermission(context)
                val hasRecordAudioPermission = CameraPermissionUtil.hasRecordAudioPermission(context)
                val hasStoragePermission = CameraPermissionUtil.hasStoragePermission(context)

                if (!hasCameraPermission) {
                    // 缺少相机权限，优先显示相机权限对话框
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.CAMERA)
                } else if (!hasRecordAudioPermission) {
                    // 缺少录音权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.MICROPHONE)
                } else if (!hasStoragePermission) {
                    // 只缺存储权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
                }
                // 如果所有权限都有，则不显示任何对话框
            }
            CameraOperation.OPEN_LAST_PHOTO -> {
                // 访问最后一张照片需要存储权限
                if (!CameraPermissionUtil.hasStoragePermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
                }
            }
            CameraOperation.SCREENSHOT -> {
                // 截屏需要存储权限
                if (!CameraPermissionUtil.hasStoragePermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
                }
            }
        }
    }

    /**
     * 检查传感器权限
     */
    private suspend fun checkSensorPermissions(
        operation: SensorStateType,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            SensorStateType.ACTIVITY_RECOGNITION -> {
                // 运动识别需要运动识别权限
                if (!SensorPermissionUtil.hasActivityRecognitionPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SENSOR)
                }
            }
            else -> {
                // 其他传感器通常不需要特殊权限
            }
        }
    }

    /**
     * 检查应用状态条件权限
     */
    private suspend fun checkAppStatePermissions(
        operation: AppStateCategoryType,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            AppStateCategoryType.INTERFACE_INTERACTION -> {
                // 界面交互条件需要界面交互无障碍服务
                if (!hasInterfaceInteractionAccessibilityService(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.ACCESSIBILITY)
                }
            }
            else -> {
                // 其他应用状态条件都需要使用情况访问权限
                if (!UsageStatsPermissionUtil.hasUsageStatsPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.USAGE_STATS)
                }
            }
        }
    }

    /**
     * 检查设备事件权限
     */
    private suspend fun checkDeviceEventPermissions(
        operation: DeviceEventType,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            DeviceEventType.LOGIN_ATTEMPT_FAILED -> {
                // 登录失败检测需要设备管理器权限
                if (!DeviceEventPermissionUtil.hasDeviceAdminPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.DEVICE_ADMIN)
                }
            }
            DeviceEventType.NOTIFICATION_EVENT -> {
                // 通知事件监控需要通知使用权限
                if (!DeviceEventPermissionUtil.hasNotificationListenerPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.NOTIFICATION_LISTENER)
                }
            }
            else -> {
                // 其他设备事件通常不需要特殊权限
            }
        }
    }

    /**
     * 检查设备操作权限
     */
    private suspend fun checkDeviceActionPermissions(
        operation: DeviceActionOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            DeviceActionOperation.TOGGLE_STATUS_BAR -> {
                // 状态栏控制需要无障碍服务权限
                if (!DeviceEventPermissionUtil.hasAccessibilityPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.ACCESSIBILITY)
                }
            }
            DeviceActionOperation.VOICE_SEARCH -> {
                // 语音搜索需要录音权限
                if (!MediaPermissionUtil.hasRecordAudioPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.MICROPHONE)
                }
            }
            DeviceActionOperation.FLASHLIGHT_CONTROL -> {
                // 手电筒控制需要相机权限
                if (!CameraPermissionUtil.hasCameraPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.CAMERA)
                }
            }
            DeviceActionOperation.VIBRATION -> {
                // 震动权限是普通权限，在AndroidManifest.xml中声明即可，不需要运行时申请
                // 无需权限检查
            }
            DeviceActionOperation.SHIZUKU_COMMAND -> {
                // Shizuku命令需要Shizuku权限
                if (!com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                }
            }
            // 系统操作功能需要系统操作无障碍服务
            DeviceActionOperation.QUICK_SETTINGS,
            DeviceActionOperation.POWER_MENU,
            DeviceActionOperation.RECENT_TASKS,
            DeviceActionOperation.APP_DRAWER,
            DeviceActionOperation.ACCESSIBILITY_TOGGLE,
            DeviceActionOperation.BACK_KEY -> {
                if (!hasSystemOperationAccessibilityService(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.ACCESSIBILITY)
                }
            }
            else -> {
                // 其他设备操作（分享文本、剪贴板、启动主屏幕、文字转语音、等待延迟）通常不需要特殊权限
            }
        }
    }

    /**
     * 检查设备设置权限
     */
    private suspend fun checkDeviceSettingsPermissions(
        operation: DeviceSettingsOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            DeviceSettingsOperation.AUTO_ROTATE,
            DeviceSettingsOperation.ACCESSIBILITY_SERVICE,
            DeviceSettingsOperation.INVERT_COLORS,
            DeviceSettingsOperation.DEMO_MODE,
            DeviceSettingsOperation.AMBIENT_DISPLAY,
            DeviceSettingsOperation.SCREEN_LOCK,
            DeviceSettingsOperation.DIGITAL_ASSISTANT,
            DeviceSettingsOperation.DEFAULT_KEYBOARD,
            DeviceSettingsOperation.DRIVING_MODE,
            DeviceSettingsOperation.FONT_SIZE,
            DeviceSettingsOperation.ENTER_SCREENSAVER,
            DeviceSettingsOperation.DISPLAY_DENSITY,
            DeviceSettingsOperation.IMMERSIVE_MODE,
            DeviceSettingsOperation.DARK_THEME,
            DeviceSettingsOperation.POWER_SAVE_MODE,
            DeviceSettingsOperation.SYSTEM_SETTINGS,
            DeviceSettingsOperation.SET_WALLPAPER,
            DeviceSettingsOperation.KEYBOARD_HINT -> {
                // 这些设备设置操作需要Shizuku权限
                if (!com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                }
            }
            else -> {
                // 其他设备设置操作不需要特殊权限
            }
        }
    }

    /**
     * 检查通知操作权限
     */
    private suspend fun checkNotificationPermissions(
        operation: NotificationOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            NotificationOperation.SHOW_NOTIFICATION -> {
                // 显示通知需要通知权限（Android 13+）
                if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.NOTIFICATION)
                }
            }
            NotificationOperation.CLEAR_NOTIFICATIONS -> {
                // 清除通知需要通知使用权限
                if (!DeviceEventPermissionUtil.hasNotificationListenerPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.NOTIFICATION_LISTENER)
                }
            }
            NotificationOperation.SHOW_DIALOG -> {
                // 显示对话框需要悬浮窗权限
                if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.OVERLAY)
                }
            }
            NotificationOperation.SHOW_BUBBLE -> {
                // 显示气泡通知需要先检查通知权限，然后检查气泡通知权限，采用逐步检查策略
                val hasNotificationPermission = com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)
                val hasBubblePermission = com.weinuo.quickcommands.utils.NotificationPermissionUtil.areBubblesAllowed(context)

                // 优先检查通知权限
                if (!hasNotificationPermission) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.NOTIFICATION)
                } else if (!hasBubblePermission) {
                    // 通知权限已有，检查气泡通知权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.BUBBLE)
                }
                // 如果两个权限都有，则不显示任何对话框
            }
            else -> {
                // 其他通知操作通常不需要特殊权限
            }
        }
    }

    /**
     * 检查文件操作权限
     */
    private suspend fun checkFilePermissions(
        operation: FileOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        // 所有文件操作都需要存储权限
        if (!StoragePermissionUtil.hasStoragePermission(context)) {
            globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
        }
    }

    /**
     * 检查应用操作权限
     */
    private suspend fun checkApplicationPermissions(
        operation: ApplicationOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            ApplicationOperation.FORCE_STOP_APP -> {
                // 强制停止应用需要Shizuku权限和使用情况统计权限，智能权限检查策略
                val hasShizukuPermission = com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                val hasUsageStatsPermission = UsageStatsPermissionUtil.hasUsageStatsPermission(context)

                if (!hasShizukuPermission) {
                    // 缺少Shizuku权限，优先显示Shizuku权限对话框
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                } else if (!hasUsageStatsPermission) {
                    // 只缺使用情况统计权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.USAGE_STATS)
                }
                // 如果两个权限都有，则不显示任何对话框
            }
            ApplicationOperation.FREEZE_APP,
            ApplicationOperation.UNFREEZE_APP -> {
                // 冻结/解冻应用需要Shizuku权限
                val hasShizukuPermission = com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()

                if (!hasShizukuPermission) {
                    // 缺少Shizuku权限，显示Shizuku权限对话框
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                }
            }
            else -> {
                // 其他应用操作不需要特殊权限
                // Shell脚本权限检查在具体执行模式中处理
            }
        }
    }



    /**
     * 检查屏幕控制权限
     */
    private suspend fun checkScreenControlPermissions(
        operation: ScreenControlOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            ScreenControlOperation.BLOCK_SCREEN_TOUCH -> {
                // 屏蔽屏幕触摸需要悬浮窗权限
                if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.OVERLAY)
                }
            }
            ScreenControlOperation.CHECK_SCREEN_TEXT, ScreenControlOperation.READ_SCREEN_CONTENT -> {
                // 检查屏幕文字和读取屏幕内容需要界面交互无障碍服务和存储权限，智能权限检查策略
                val hasAccessibilityPermission = hasInterfaceInteractionAccessibilityService(context)
                val hasStoragePermission = StoragePermissionUtil.hasStoragePermission(context)

                if (!hasAccessibilityPermission) {
                    // 缺少无障碍服务权限，优先显示无障碍服务权限对话框
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.ACCESSIBILITY)
                } else if (!hasStoragePermission) {
                    // 只缺存储权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
                }
                // 如果两个权限都有，则不显示任何对话框
            }
            ScreenControlOperation.CHECK_PIXEL_COLOR -> {
                // 检查界面元素颜色需要界面交互无障碍服务和存储权限，智能权限检查策略
                val hasAccessibilityPermission = hasInterfaceInteractionAccessibilityService(context)
                val hasStoragePermission = StoragePermissionUtil.hasStoragePermission(context)

                if (!hasAccessibilityPermission) {
                    // 缺少无障碍服务权限，优先显示无障碍服务权限对话框
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.ACCESSIBILITY)
                } else if (!hasStoragePermission) {
                    // 只缺存储权限
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.STORAGE)
                }
                // 如果两个权限都有，则不显示任何对话框
            }
            ScreenControlOperation.AUTO_CLICKER_PLAYBACK -> {
                // 自动点击器功能需要自动点击器无障碍服务
                if (!hasAutoClickerAccessibilityService(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.ACCESSIBILITY)
                }
            }
            ScreenControlOperation.BRIGHTNESS_CONTROL,
            ScreenControlOperation.KEEP_DEVICE_AWAKE,
            ScreenControlOperation.SCREEN_ON_OFF,
            ScreenControlOperation.SCREEN_DIMNESS,
            ScreenControlOperation.FORCE_SCREEN_ROTATION,
            ScreenControlOperation.SET_SCREEN_TIMEOUT -> {
                // 这些屏幕控制操作需要Shizuku权限
                if (!com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                }
            }
            else -> {
                // 其他屏幕控制操作不需要特殊权限
            }
        }
    }

    /**
     * 检查音量操作权限
     */
    private suspend fun checkVolumePermissions(
        operation: VolumeOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            VolumeOperation.DO_NOT_DISTURB_MODE -> {
                // 勿扰模式需要通知策略访问权限
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                    if (!notificationManager.isNotificationPolicyAccessGranted) {
                        globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.DO_NOT_DISTURB)
                    }
                }
                // Android 6.0以下版本不需要此权限
            }
            else -> {
                // 其他音量操作（音量变化、音量调节、免提通话、振动模式、音量弹出窗口）不需要特殊权限
            }
        }
    }

    /**
     * 检查连接操作权限
     */
    private suspend fun checkConnectivityPermissions(
        operation: ConnectivityOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            ConnectivityOperation.CHECK_CONNECTION -> {
                // 连接检查需要网络权限
                if (!NetworkPermissionUtil.hasNetworkPermissions(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.NETWORK)
                }
            }
            ConnectivityOperation.WIFI_CONTROL,
            ConnectivityOperation.HOTSPOT_CONTROL,
            ConnectivityOperation.MOBILE_DATA_CONTROL,
            ConnectivityOperation.AIRPLANE_MODE_CONTROL,
            ConnectivityOperation.AUTO_SYNC_CONTROL,
            ConnectivityOperation.SYNC_ACCOUNT -> {
                // 这些连接操作需要Shizuku权限
                if (!com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                }
            }
            else -> {
                // 其他连接操作不需要特殊权限
            }
        }
    }



    /**
     * 检查手动触发权限
     */
    private suspend fun checkManualTriggerPermissions(
        operation: ManualTriggerType,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            ManualTriggerType.FLOATING_BUTTON -> {
                // 悬浮按钮需要悬浮窗权限
                if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.OVERLAY)
                }
            }
            ManualTriggerType.FINGERPRINT_GESTURE -> {
                // 指纹手势需要手势识别无障碍服务
                if (!hasGestureRecognitionAccessibilityService(context)) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.ACCESSIBILITY)
                }
            }
            else -> {
                // 其他手动触发方式通常不需要特殊权限
            }
        }
    }



    /**
     * 检查全局设置操作权限（仅保留独立功能）
     */
    private suspend fun checkGlobalSettingsPermissions(
        operation: GlobalSettingsOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            GlobalSettingsOperation.WIDGET_UPDATE,
            GlobalSettingsOperation.DEBUG_MODE,
            GlobalSettingsOperation.EXPERIMENTAL_FEATURES -> {
                // 独立功能无需权限检查
            }
            else -> {
                // 主功能已删除，其他设置也不需要权限检查
            }
        }
    }

    /**
     * 检查Shell脚本Shizuku操作权限
     */
    private suspend fun checkShellScriptShizukuPermissions(
        operation: ShellScriptShizukuOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            ShellScriptShizukuOperation.SHIZUKU_SHELL -> {
                // Shizuku Shell脚本执行需要Shizuku权限
                if (!com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
                    globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                }
            }
        }
    }

    /**
     * 检查智慧提醒操作权限
     */
    private suspend fun checkSmartReminderPermissions(
        operation: SmartReminderOperation,
        globalPermissionManager: GlobalPermissionManager,
        context: Context
    ) {
        when (operation) {
            SmartReminderOperation.ENABLE_REMINDER -> {
                // 启用智慧提醒需要悬浮窗权限和Shizuku权限
                val hasOverlayPermission = OverlayPermissionUtil.hasOverlayPermission(context)
                val hasShizukuPermission = com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()

                when {
                    !hasOverlayPermission && !hasShizukuPermission -> {
                        // 两个权限都没有，优先申请悬浮窗权限
                        globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.OVERLAY)
                    }
                    !hasOverlayPermission -> {
                        // 只缺悬浮窗权限
                        globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.OVERLAY)
                    }
                    !hasShizukuPermission -> {
                        // 只缺Shizuku权限
                        globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                    }
                    // 如果两个权限都有，则不显示任何对话框
                }
            }
            SmartReminderOperation.ENABLE_FLASHLIGHT_REMINDER -> {
                // 启用手电筒提醒需要悬浮窗权限、Shizuku权限和相机权限
                val hasOverlayPermission = OverlayPermissionUtil.hasOverlayPermission(context)
                val hasShizukuPermission = com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()
                val hasCameraPermission = CameraPermissionUtil.hasCameraPermission(context)

                when {
                    !hasOverlayPermission -> {
                        // 优先申请悬浮窗权限
                        globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.OVERLAY)
                    }
                    !hasShizukuPermission -> {
                        // 申请Shizuku权限
                        globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.SHIZUKU)
                    }
                    !hasCameraPermission -> {
                        // 申请相机权限
                        globalPermissionManager.showPermissionConfirmationDialog(GlobalPermissionManager.PermissionType.CAMERA)
                    }
                    // 如果所有权限都有，则不显示任何对话框
                }
            }
        }
    }

    /**
     * 检查系统操作无障碍服务是否已启用
     */
    private fun hasSystemOperationAccessibilityService(context: Context): Boolean {
        return com.weinuo.quickcommands.service.SystemOperationAccessibilityService.getInstance() != null
    }

    /**
     * 检查界面交互无障碍服务是否已启用
     */
    private fun hasInterfaceInteractionAccessibilityService(context: Context): Boolean {
        return com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService.getInstance() != null
    }

    /**
     * 检查手势识别无障碍服务是否已启用
     */
    private fun hasGestureRecognitionAccessibilityService(context: Context): Boolean {
        return com.weinuo.quickcommands.service.GestureRecognitionAccessibilityService.getInstance() != null
    }

    /**
     * 检查自动点击器无障碍服务是否已启用
     */
    private fun hasAutoClickerAccessibilityService(context: Context): Boolean {
        return com.weinuo.quickcommands.service.AutoClickerAccessibilityService.getInstance() != null
    }
}

package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.AppListStorageEngine
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation
import com.weinuo.quickcommands.storage.StorageType

/**
 * 传感器状态条件存储适配器
 *
 * 负责SensorStateCondition的原生数据类型存储和重建。
 * 将复杂的传感器状态条件对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、sensorType
 * - 光线传感器参数：thresholdType、luxValue
 * - 屏幕方向传感器参数：orientationType
 * - 摇晃检测参数：sensitivity、shakeThreshold
 * - 睡眠检测参数：sleepStateType
 * - 设备翻转检测参数：flipType
 * - 距离传感器参数：proximityType
 * - 运动识别参数：activityType
 *
 * 存储格式示例：
 * condition_{id}_type = "sensor_state"
 * condition_{id}_sensor_type = "LIGHT_SENSOR"
 * condition_{id}_threshold_type = "DECREASE_TO"
 * condition_{id}_lux_value = 10.0
 * condition_{id}_orientation_type = "PORTRAIT"
 * condition_{id}_sensitivity = "MEDIUM"
 * condition_{id}_shake_threshold = 12.0
 * condition_{id}_sleep_state_type = "FALL_ASLEEP"
 * condition_{id}_flip_type = "FACE_UP_TO_DOWN"
 * condition_{id}_proximity_type = "NEAR"
 * condition_{id}_activity_type = "WALKING"
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class SensorStateConditionAdapter(
    storageManager: NativeTypeStorageManager,
    appListEngine: AppListStorageEngine
) : BaseConditionAdapter<SensorStateCondition>(storageManager, appListEngine) {

    companion object {
        private const val TAG = "SensorStateConditionAdapter"

        // 字段名常量
        private const val FIELD_SENSOR_TYPE = "sensor_type"

        // 光线传感器参数字段
        private const val FIELD_THRESHOLD_TYPE = "threshold_type"
        private const val FIELD_LUX_VALUE = "lux_value"

        // 屏幕方向传感器参数字段
        private const val FIELD_ORIENTATION_TYPE = "orientation_type"

        // 摇晃检测参数字段
        private const val FIELD_SENSITIVITY = "sensitivity"
        private const val FIELD_SHAKE_THRESHOLD = "shake_threshold"

        // 睡眠检测参数字段
        private const val FIELD_SLEEP_STATE_TYPE = "sleep_state_type"

        // 设备翻转检测参数字段
        private const val FIELD_FLIP_TYPE = "flip_type"

        // 距离传感器参数字段
        private const val FIELD_PROXIMITY_TYPE = "proximity_type"

        // 运动识别参数字段
        private const val FIELD_ACTIVITY_TYPE = "activity_type"
    }

    override fun getConditionType() = "sensor_state"

    override fun save(condition: SensorStateCondition): Boolean {
        Log.d(TAG, "开始保存传感器状态条件: ${condition.id}")

        return try {
            val prefix = getPrefix(condition.id)
            val operations = mutableListOf<StorageOperation>()

            // 基础字段
            operations.addAll(saveBaseFields(condition.id, condition))

            // 传感器状态条件特有字段
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SENSOR_TYPE}", condition.sensorType.name, StorageType.STRING),

                // 光线传感器参数
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_THRESHOLD_TYPE}", condition.thresholdType.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_LUX_VALUE}", condition.luxValue, StorageType.FLOAT),

                // 屏幕方向传感器参数
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ORIENTATION_TYPE}", condition.orientationType.name, StorageType.STRING),

                // 摇晃检测参数
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SENSITIVITY}", condition.sensitivity.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SHAKE_THRESHOLD}", condition.shakeThreshold, StorageType.FLOAT),

                // 睡眠检测参数
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SLEEP_STATE_TYPE}", condition.sleepStateType.name, StorageType.STRING),

                // 设备翻转检测参数
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_FLIP_TYPE}", condition.flipType.name, StorageType.STRING),

                // 距离传感器参数
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_PROXIMITY_TYPE}", condition.proximityType.name, StorageType.STRING),

                // 运动识别参数
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ACTIVITY_TYPE}", condition.activityType.name, StorageType.STRING)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "传感器状态条件保存成功: ${condition.id}")
            } else {
                Log.e(TAG, "传感器状态条件保存失败: ${condition.id}")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "保存传感器状态条件时发生异常: ${condition.id}", e)
            false
        }
    }

    override fun load(conditionId: String): SensorStateCondition? {
        Log.d(TAG, "开始加载传感器状态条件: $conditionId")

        return try {
            val prefix = getPrefix(conditionId)

            // 检查条件是否存在
            val conditionType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}type")
            if (conditionType != getConditionType()) {
                Log.w(TAG, "条件类型不匹配或条件不存在: $conditionId, 期望: ${getConditionType()}, 实际: $conditionType")
                return null
            }

            // 加载传感器类型
            val sensorType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SENSOR_TYPE}").let {
                try {
                    SensorStateType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的传感器类型: $it, 使用默认值")
                    SensorStateType.LIGHT_SENSOR
                }
            }

            // 加载光线传感器参数
            val thresholdType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_THRESHOLD_TYPE}").let {
                try {
                    LightThresholdType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的光线阈值类型: $it, 使用默认值")
                    LightThresholdType.DECREASE_TO
                }
            }
            val luxValue = storageManager.loadFloat(StorageDomain.CONDITIONS, "${prefix}${FIELD_LUX_VALUE}", 10.0f)

            // 加载屏幕方向传感器参数
            val orientationType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_ORIENTATION_TYPE}").let {
                try {
                    OrientationType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的屏幕方向类型: $it, 使用默认值")
                    OrientationType.PORTRAIT
                }
            }

            // 加载摇晃检测参数
            val sensitivity = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SENSITIVITY}").let {
                try {
                    SensitivityLevel.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的敏感度级别: $it, 使用默认值")
                    SensitivityLevel.MEDIUM
                }
            }
            val shakeThreshold = storageManager.loadFloat(StorageDomain.CONDITIONS, "${prefix}${FIELD_SHAKE_THRESHOLD}", 12.0f)

            // 加载睡眠检测参数
            val sleepStateType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SLEEP_STATE_TYPE}").let {
                try {
                    SleepStateType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的睡眠状态类型: $it, 使用默认值")
                    SleepStateType.FALL_ASLEEP
                }
            }

            // 加载设备翻转检测参数
            val flipType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_FLIP_TYPE}").let {
                try {
                    FlipType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的翻转类型: $it, 使用默认值")
                    FlipType.FACE_UP_TO_DOWN
                }
            }

            // 加载距离传感器参数
            val proximityType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_PROXIMITY_TYPE}").let {
                try {
                    ProximityType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的距离类型: $it, 使用默认值")
                    ProximityType.NEAR
                }
            }

            // 加载运动识别参数
            val activityType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_ACTIVITY_TYPE}").let {
                try {
                    ActivityType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的运动类型: $it, 使用默认值")
                    ActivityType.WALKING
                }
            }

            // 重建SensorStateCondition对象
            val condition = SensorStateCondition(
                id = conditionId,
                sensorType = sensorType,
                thresholdType = thresholdType,
                luxValue = luxValue,
                orientationType = orientationType,
                sensitivity = sensitivity,
                shakeThreshold = shakeThreshold,
                sleepStateType = sleepStateType,
                flipType = flipType,
                proximityType = proximityType,
                activityType = activityType
            )

            Log.d(TAG, "传感器状态条件加载成功: $conditionId")
            condition

        } catch (e: Exception) {
            Log.e(TAG, "加载传感器状态条件时发生异常: $conditionId", e)
            null
        }
    }
}

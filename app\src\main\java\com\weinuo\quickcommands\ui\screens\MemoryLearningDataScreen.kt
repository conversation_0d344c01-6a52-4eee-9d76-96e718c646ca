package com.weinuo.quickcommands.ui.screens

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

import java.text.SimpleDateFormat
import java.util.*

/**
 * 内存学习数据查看界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MemoryLearningDataScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current

    var learningData by remember { mutableStateOf<List<MemoryLearningRecord>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    // 加载学习数据
    LaunchedEffect(Unit) {
        learningData = loadMemoryLearningData(context)
        isLoading = false
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("内存学习数据") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(
                        onClick = {
                            isLoading = true
                            learningData = loadMemoryLearningData(context)
                            isLoading = false
                        }
                    ) {
                        Icon(Icons.Filled.Refresh, contentDescription = "刷新")
                    }

                    IconButton(
                        onClick = { showDeleteDialog = true }
                    ) {
                        Icon(Icons.Filled.Delete, contentDescription = "清除数据")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                learningData.isEmpty() -> {
                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "暂无学习数据",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Text(
                            text = "启用智能学习模式后，系统会自动收集应用内存使用数据",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
                else -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        item {
                            Text(
                                text = "共收集 ${learningData.size} 条学习记录",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                        }

                        items(learningData) { record ->
                            MemoryLearningRecordCard(record = record)
                        }
                    }
                }
            }
        }
    }

    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("清除学习数据") },
            text = { Text("确定要清除所有内存学习数据吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        clearMemoryLearningData(context)
                        learningData = emptyList()
                        showDeleteDialog = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 内存学习记录卡片
 */
@Composable
private fun MemoryLearningRecordCard(
    record: MemoryLearningRecord
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.appName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )

                Text(
                    text = "样本数: ${record.sampleCount}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "包名: ${record.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.height(4.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "平均内存: ${record.averageMemoryMB}MB",
                    style = MaterialTheme.typography.bodyMedium
                )

                Text(
                    text = "峰值内存: ${record.peakMemoryMB}MB",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "置信度: ${String.format("%.1f", record.confidence * 100)}%",
                    style = MaterialTheme.typography.bodySmall,
                    color = if (record.confidence >= 0.7f) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                Text(
                    text = "最后更新: ${formatTimestamp(record.lastUpdated)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 内存学习记录数据类
 */
data class MemoryLearningRecord(
    val packageName: String,
    val appName: String,
    val sampleCount: Int,
    val averageMemoryMB: Int,
    val peakMemoryMB: Int,
    val confidence: Float,
    val lastUpdated: Long
)

/**
 * 加载内存学习数据
 */
private fun loadMemoryLearningData(context: Context): List<MemoryLearningRecord> {
    val prefs = context.getSharedPreferences("intelligent_memory_detector", Context.MODE_PRIVATE)
    val learningDataPrefix = "memory_learning_"
    val records = mutableListOf<MemoryLearningRecord>()

    // 获取所有键
    val allKeys = prefs.all.keys

    // 找到所有包名
    val packageNames = allKeys
        .filter { it.startsWith(learningDataPrefix) && it.endsWith("_sample_count") }
        .map { it.removePrefix(learningDataPrefix).removeSuffix("_sample_count") }
        .toSet()

    // 为每个包名加载数据
    for (packageName in packageNames) {
        val prefix = "$learningDataPrefix$packageName"
        val sampleCount = prefs.getInt("${prefix}_sample_count", 0)

        if (sampleCount > 0) {
            val averageMemory = prefs.getFloat("${prefix}_average_memory", 0f)
            val peakMemory = prefs.getInt("${prefix}_peak_memory", 0)
            val confidence = prefs.getFloat("${prefix}_confidence", 0f)
            val lastUpdated = prefs.getLong("${prefix}_last_updated", 0)

            // 尝试获取应用名称，如果没有则使用包名
            val appName = try {
                val pm = context.packageManager
                val appInfo = pm.getApplicationInfo(packageName, 0)
                pm.getApplicationLabel(appInfo).toString()
            } catch (e: Exception) {
                packageName
            }

            records.add(
                MemoryLearningRecord(
                    packageName = packageName,
                    appName = appName,
                    sampleCount = sampleCount,
                    averageMemoryMB = averageMemory.toInt(),
                    peakMemoryMB = peakMemory,
                    confidence = confidence,
                    lastUpdated = lastUpdated
                )
            )
        }
    }

    // 按最后更新时间排序
    return records.sortedByDescending { it.lastUpdated }
}

/**
 * 清除内存学习数据
 */
private fun clearMemoryLearningData(context: Context) {
    val prefs = context.getSharedPreferences("intelligent_memory_detector", Context.MODE_PRIVATE)
    val learningDataPrefix = "memory_learning_"
    val editor = prefs.edit()

    // 获取所有键
    val allKeys = prefs.all.keys

    // 删除所有以 "memory_learning_" 开头的键
    for (key in allKeys) {
        if (key.startsWith(learningDataPrefix)) {
            editor.remove(key)
        }
    }

    editor.apply()
}

/**
 * 格式化时间戳
 */
private fun formatTimestamp(timestamp: Long): String {
    val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}

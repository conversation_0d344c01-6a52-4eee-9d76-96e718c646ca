package com.weinuo.quickcommands.ui.settings

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp

/**
 * 设置组件库
 *
 * 提供设置界面常用的UI组件
 */

/**
 * 开关偏好设置组件
 */
@Composable
fun SwitchPreference(
    title: String,
    description: String? = null,
    checked: Boolean,
    onCheckedChange: (<PERSON><PERSON><PERSON>) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                color = if (enabled) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                }
            )
            
            description?.let { desc ->
                Text(
                    text = desc,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (enabled) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    }
                )
            }
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled
        )
    }
}

/**
 * 信息卡片组件
 */
@Composable
fun InfoCard(
    message: String,
    type: InfoType = InfoType.INFO,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (type) {
                InfoType.INFO -> MaterialTheme.colorScheme.surfaceContainerHighest
                InfoType.WARNING -> MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f)
                InfoType.SUCCESS -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (type) {
                    InfoType.INFO -> Icons.Default.Info
                    InfoType.WARNING -> Icons.Default.Warning
                    InfoType.SUCCESS -> Icons.Default.Info
                },
                contentDescription = null,
                tint = when (type) {
                    InfoType.INFO -> MaterialTheme.colorScheme.onSurfaceVariant
                    InfoType.WARNING -> MaterialTheme.colorScheme.error
                    InfoType.SUCCESS -> MaterialTheme.colorScheme.primary
                },
                modifier = Modifier.size(20.dp)
            )
            
            Text(
                text = message,
                style = MaterialTheme.typography.bodySmall,
                color = when (type) {
                    InfoType.INFO -> MaterialTheme.colorScheme.onSurfaceVariant
                    InfoType.WARNING -> MaterialTheme.colorScheme.error
                    InfoType.SUCCESS -> MaterialTheme.colorScheme.primary
                }
            )
        }
    }
}

/**
 * 信息类型枚举
 */
enum class InfoType {
    INFO,
    WARNING,
    SUCCESS
}

/**
 * 设置分组标题
 */
@Composable
fun SettingsGroupTitle(
    title: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        color = MaterialTheme.colorScheme.primary,
        modifier = modifier.padding(horizontal = 16.dp, vertical = 8.dp)
    )
}

/**
 * 设置项分隔符
 */
@Composable
fun SettingsDivider(
    modifier: Modifier = Modifier
) {
    androidx.compose.material3.HorizontalDivider(
        modifier = modifier.padding(horizontal = 16.dp),
        color = MaterialTheme.colorScheme.outlineVariant
    )
}

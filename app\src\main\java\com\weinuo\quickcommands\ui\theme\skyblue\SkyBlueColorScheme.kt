package com.weinuo.quickcommands.ui.theme.skyblue

import androidx.compose.material3.ColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.SkyBlueColorConfigurationManager

/**
 * 天空蓝主题颜色方案
 *
 * 基于提供的颜色Token实现Material Design ColorScheme映射
 * 特点：整合设计风格，统一的视觉体验
 */
object SkyBlueColorScheme {

    /**
     * 天空蓝主题的扩展颜色定义
     * 
     * 这些颜色用于特殊用途，不在标准Material Design ColorScheme中
     */
    object ExtendedColors {
        val confirm = Color(0xFF64BB5C)           // confirm - 确认色
        val fontEmphasize = Color(0xFF0A59F7)     // font_emphasize - 高亮文本
        val iconEmphasize = Color(0xFF0A59F7)     // icon_emphasize - 高亮图标
        val iconSubEmphasize = Color(0x660A59F7)  // icon_sub_emphasize - 高亮辅助图标
        val backgroundEmphasize = Color(0xFF0A59F7) // background_emphasize - 高亮背景
        val backgroundFocus = Color(0xFFF1F3F5)   // comp_background_focus - 获焦态背景色
        
        // 交互式颜色
        val interactiveHover = Color(0x0C000000)  // interactive_hover - 通用悬停交互式颜色
        val interactivePressed = Color(0x19000000) // interactive_pressed - 通用按压交互式颜色
        val interactiveFocus = Color(0xFF0A59F7)  // interactive_focus - 通用获焦交互式颜色
        val interactiveActive = Color(0xFF0A59F7) // interactive_active - 通用激活交互式颜色
        val interactiveSelect = Color(0x33000000) // interactive_select - 通用选择交互式颜色
        val interactiveClick = Color(0x19000000)  // interactive_click - 通用点击交互式颜色
    }

    /**
     * 创建天空蓝主题的ColorScheme（静态版本）
     *
     * 将提供的颜色Token映射到Material Design ColorScheme
     * 使用默认的静态颜色值
     */
    fun createColorScheme(): ColorScheme = lightColorScheme(
        // 主要颜色系统
        primary = Color(0xFF0A59F7),              // brand - 品牌色
        onPrimary = Color(0xFFFFFFFF),            // font_on_primary - 一级文本反色
        primaryContainer = Color(0x330A59F7),     // comp_emphasize_secondary - 20%高亮背景
        onPrimaryContainer = Color(0xE5000000),   // font_primary - 一级文本

        // 次要颜色系统
        secondary = Color(0x99000000),            // font_secondary - 二级文本
        onSecondary = Color(0x99FFFFFF),          // font_on_secondary - 二级文本反色
        secondaryContainer = Color(0xFFF1F3F5),   // background_secondary - 二级背景
        onSecondaryContainer = Color(0x99000000), // font_secondary - 二级文本

        // 第三颜色系统
        tertiary = Color(0x66000000),             // font_tertiary - 三级文本
        onTertiary = Color(0x66FFFFFF),           // font_on_tertiary - 三级文本反色
        tertiaryContainer = Color(0xFFE5E5EA),    // background_tertiary - 三级背景
        onTertiaryContainer = Color(0x66000000),  // font_tertiary - 三级文本

        // 错误颜色系统
        error = Color(0xFFE84026),                // warning - 一级警示色
        onError = Color(0xFFFFFFFF),              // font_on_primary - 一级文本反色
        errorContainer = Color(0xFFED6F21),       // alert - 二级警示色
        onErrorContainer = Color(0xFFFFFFFF),     // font_on_primary - 一级文本反色

        // 表面颜色系统
        background = Color(0xFFF1F3F5),           // background_secondary - 内容背景使用#fff1f3f5
        onBackground = Color(0xE5000000),         // font_primary - 一级文本
        surface = Color(0xFFF1F3F5),              // comp_background_gray - 导航栏背景使用#fff1f3f5
        onSurface = Color(0xE5000000),            // font_primary - 一级文本
        surfaceVariant = Color(0xFFF1F3F5),       // comp_background_gray - 灰色背景
        onSurfaceVariant = Color(0x99000000),     // font_secondary - 二级文本

        // 轮廓和分割线
        outline = Color(0x33000000),              // comp_divider - 分割线颜色
        outlineVariant = Color(0x19000000),       // comp_background_secondary - 二级背景

        // 特殊颜色
        scrim = Color(0xFF000000),                // comp_foreground_primary - 前背景
        inverseSurface = Color(0xFF000000),       // comp_background_neutral - 黑色中性高亮背景
        inverseOnSurface = Color(0xFFFFFFFF),     // comp_common_contrary - 通用反色
        inversePrimary = Color(0xFF0A59F7),       // brand - 品牌色

        // 表面容器层次（Material Design 3新增）
        surfaceDim = Color(0xFFD1D1D6),           // background_fourth - 四级背景
        surfaceBright = Color(0xFFF1F3F5),        // background_secondary - 一级背景使用#fff1f3f5
        surfaceContainerLowest = Color(0xFFFFFFFF), // comp_background_primary_contrary - 常亮背景
        surfaceContainerLow = Color(0xFFFFFFFF),    // comp_background_primary_contrary - 常亮背景（白色）
        surfaceContainer = Color(0xFFE5E5EA),       // background_tertiary - 三级背景
        surfaceContainerHigh = Color(0xFFD1D1D6),   // background_fourth - 四级背景
        surfaceContainerHighest = Color(0x0C000000) // comp_background_tertiary - 三级背景
    )

    /**
     * 创建天空蓝主题的动态ColorScheme
     *
     * 从SettingsRepository中读取用户自定义的颜色配置
     * 支持实时颜色配置更新
     */
    @Composable
    fun createDynamicColorScheme(settingsRepository: SettingsRepository): ColorScheme {
        val context = androidx.compose.ui.platform.LocalContext.current
        val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context, settingsRepository) }
        val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()

        return lightColorScheme(
            // 主要颜色系统
            primary = colorConfig.primary,
            onPrimary = colorConfig.onPrimary,
            primaryContainer = colorConfig.primaryContainer,
            onPrimaryContainer = colorConfig.onPrimaryContainer,

            // 次要颜色系统
            secondary = colorConfig.secondary,
            onSecondary = colorConfig.onSecondary,
            secondaryContainer = colorConfig.secondaryContainer,
            onSecondaryContainer = colorConfig.onSecondaryContainer,

            // 第三颜色系统
            tertiary = colorConfig.tertiary,
            onTertiary = colorConfig.onTertiary,
            tertiaryContainer = colorConfig.tertiaryContainer,
            onTertiaryContainer = colorConfig.onTertiaryContainer,

            // 错误颜色系统
            error = colorConfig.error,
            onError = colorConfig.onError,
            errorContainer = colorConfig.errorContainer,
            onErrorContainer = colorConfig.onErrorContainer,

            // 表面颜色系统
            background = colorConfig.background,
            onBackground = colorConfig.onBackground,
            surface = colorConfig.surface,
            onSurface = colorConfig.onSurface,
            surfaceVariant = colorConfig.surfaceVariant,
            onSurfaceVariant = colorConfig.onSurfaceVariant,

            // 轮廓和分割线（使用动态计算）
            outline = Color(0x33000000),              // comp_divider - 分割线颜色
            outlineVariant = Color(0x19000000),       // comp_background_secondary - 二级背景

            // 特殊颜色（保持静态）
            scrim = Color(0xFF000000),                // comp_foreground_primary - 前背景
            inverseSurface = Color(0xFF000000),       // comp_background_neutral - 黑色中性高亮背景
            inverseOnSurface = Color(0xFFFFFFFF),     // comp_common_contrary - 通用反色
            inversePrimary = colorConfig.primary,     // 使用动态品牌色

            // 表面容器层次（部分使用动态颜色）
            surfaceDim = Color(0xFFD1D1D6),           // background_fourth - 四级背景
            surfaceBright = colorConfig.surface,      // 使用动态表面颜色
            surfaceContainerLowest = Color(0xFFFFFFFF), // comp_background_primary_contrary - 常亮背景
            surfaceContainerLow = Color(0xFFFFFFFF),    // comp_background_primary_contrary - 常亮背景（白色）
            surfaceContainer = colorConfig.tertiaryContainer, // 使用动态三级背景
            surfaceContainerHigh = Color(0xFFD1D1D6),   // background_fourth - 四级背景
            surfaceContainerHighest = Color(0x0C000000) // comp_background_tertiary - 三级背景
        )
    }

    /**
     * 获取动态扩展颜色配置
     *
     * 从SettingsRepository中读取用户自定义的扩展颜色配置
     */
    @Composable
    fun getDynamicExtendedColors(settingsRepository: SettingsRepository): DynamicExtendedColors {
        val context = androidx.compose.ui.platform.LocalContext.current
        val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context, settingsRepository) }
        val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()

        return DynamicExtendedColors(
            confirm = colorConfig.confirm,
            fontEmphasize = colorConfig.fontEmphasize,
            iconEmphasize = colorConfig.iconEmphasize,
            iconSubEmphasize = colorConfig.iconSubEmphasize,
            backgroundEmphasize = colorConfig.backgroundEmphasize,
            backgroundFocus = colorConfig.backgroundFocus,

            // 交互式颜色（保持静态或基于动态颜色计算）
            interactiveHover = Color(0x0C000000),
            interactivePressed = Color(0x19000000),
            interactiveFocus = colorConfig.primary,
            interactiveActive = colorConfig.primary,
            interactiveSelect = Color(0x33000000),
            interactiveClick = Color(0x19000000)
        )
    }

    /**
     * 获取图标颜色
     * 
     * 根据重要性级别返回对应的图标颜色
     */
    fun getIconColor(level: IconLevel): Color = when (level) {
        IconLevel.PRIMARY -> Color(0xE5000000)    // icon_primary - 一级图标
        IconLevel.SECONDARY -> Color(0x99000000)  // icon_secondary - 二级图标
        IconLevel.TERTIARY -> Color(0x66000000)   // icon_tertiary - 三级图标
        IconLevel.FOURTH -> Color(0x33000000)     // icon_fourth - 四级图标
        IconLevel.EMPHASIZE -> Color(0xFF0A59F7)  // icon_emphasize - 高亮图标
        IconLevel.SUB_EMPHASIZE -> Color(0x660A59F7) // icon_sub_emphasize - 高亮辅助图标
    }

    /**
     * 获取反色图标颜色
     * 
     * 用于深色背景上的图标
     */
    fun getOnIconColor(level: IconLevel): Color = when (level) {
        IconLevel.PRIMARY -> Color(0xFFFFFFFF)    // icon_on_primary - 一级图标反色
        IconLevel.SECONDARY -> Color(0x99FFFFFF)  // icon_on_secondary - 二级图标反色
        IconLevel.TERTIARY -> Color(0x66FFFFFF)   // icon_on_tertiary - 三级图标反色
        IconLevel.FOURTH -> Color(0x33FFFFFF)     // icon_on_fourth - 四级图标反色
        IconLevel.EMPHASIZE -> Color(0xFF0A59F7)  // icon_emphasize - 高亮图标
        IconLevel.SUB_EMPHASIZE -> Color(0x660A59F7) // icon_sub_emphasize - 高亮辅助图标
    }

    /**
     * 获取获焦态颜色
     * 
     * 用于组件获得焦点时的颜色
     */
    fun getFocusedColor(level: IconLevel): Color = when (level) {
        IconLevel.PRIMARY -> Color(0xE5000000)    // comp_focused_primary - 获焦态一级反色
        IconLevel.SECONDARY -> Color(0x99000000)  // comp_focused_secondary - 获焦态二级反色
        IconLevel.TERTIARY -> Color(0x66000000)   // comp_focused_tertiary - 获焦态三级反色
        IconLevel.FOURTH -> Color(0x33000000)     // 四级获焦态颜色
        IconLevel.EMPHASIZE -> Color(0xFF0A59F7)  // 高亮获焦态颜色
        IconLevel.SUB_EMPHASIZE -> Color(0x660A59F7) // 高亮辅助获焦态颜色
    }
}

/**
 * 动态扩展颜色配置
 *
 * 包含天空蓝主题的扩展颜色，支持用户自定义配置
 */
data class DynamicExtendedColors(
    val confirm: Color,
    val fontEmphasize: Color,
    val iconEmphasize: Color,
    val iconSubEmphasize: Color,
    val backgroundEmphasize: Color,
    val backgroundFocus: Color,

    // 交互式颜色
    val interactiveHover: Color,
    val interactivePressed: Color,
    val interactiveFocus: Color,
    val interactiveActive: Color,
    val interactiveSelect: Color,
    val interactiveClick: Color
)

/**
 * 图标重要性级别
 *
 * 定义不同重要性级别的图标，用于获取对应的颜色
 */
enum class IconLevel {
    PRIMARY,        // 一级图标
    SECONDARY,      // 二级图标
    TERTIARY,       // 三级图标
    FOURTH,         // 四级图标
    EMPHASIZE,      // 高亮图标
    SUB_EMPHASIZE   // 高亮辅助图标
}

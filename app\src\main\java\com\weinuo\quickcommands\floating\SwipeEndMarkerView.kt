package com.weinuo.quickcommands.floating

import android.content.Context
import android.graphics.*
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.utils.OverlayPermissionUtil

/**
 * 滑动终点标记视图
 *
 * 显示滑动手势的终点，使用坐标标记样式并支持拖拽移动。
 */
class SwipeEndMarkerView(
    private val context: Context,
    private val number: Int,
    private val onPositionChanged: ((Float, Float) -> Unit)? = null
) {
    companion object {
        private const val TAG = "SwipeEndMarkerView"
        private const val MARKER_SIZE_DP = 32
    }

    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var markerView: EndMarkerView? = null
    private var markerParams: WindowManager.LayoutParams? = null
    private var isShowing = false

    /**
     * 显示滑动终点标记
     */
    fun show(x: Float, y: Float): Boolean {
        if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
            return false
        }

        if (isShowing) {
            hide()
        }

        try {
            markerView = EndMarkerView(context, number)

            val density = context.resources.displayMetrics.density
            val sizeInPx = (MARKER_SIZE_DP * density).toInt()

            markerParams = WindowManager.LayoutParams(
                sizeInPx,
                sizeInPx,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = android.view.Gravity.TOP or android.view.Gravity.START
                this.x = (x - sizeInPx / 2).toInt()
                this.y = (y - sizeInPx / 2).toInt()
            }

            // 添加拖拽功能
            addDragFunctionality(markerView!!, markerParams!!)

            windowManager.addView(markerView, markerParams)
            isShowing = true
            return true
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 隐藏滑动终点标记
     */
    fun hide() {
        if (!isShowing) return

        try {
            markerView?.let { view ->
                windowManager.removeView(view)
            }
            markerView = null
            isShowing = false
        } catch (e: Exception) {
            // 忽略异常
        }
    }

    /**
     * 添加拖拽功能
     */
    private fun addDragFunctionality(view: View, params: WindowManager.LayoutParams) {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f

        view.setOnTouchListener { _, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                android.view.MotionEvent.ACTION_MOVE -> {
                    val newX = initialX + (event.rawX - initialTouchX).toInt()
                    val newY = initialY + (event.rawY - initialTouchY).toInt()

                    params.x = newX
                    params.y = newY
                    windowManager.updateViewLayout(view, params)

                    // 通知位置变化
                    val density = context.resources.displayMetrics.density
                    val sizeInPx = (MARKER_SIZE_DP * density).toInt()
                    val centerX = newX + sizeInPx / 2f
                    val centerY = newY + sizeInPx / 2f
                    onPositionChanged?.invoke(centerX, centerY)

                    true
                }
                else -> false
            }
        }
    }

    /**
     * 终点标记视图
     */
    private class EndMarkerView(
        context: Context,
        private val number: Int
    ) : View(context) {

        private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        private val coordinateLinesPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        private val arrowPaint = Paint(Paint.ANTI_ALIAS_FLAG)

        init {
            setupPaints()
        }

        /**
         * 设置画笔
         */
        private fun setupPaints() {
            val density = context.resources.displayMetrics.density

            // 背景画笔 - 使用滑动手势的紫色，但更深一些
            backgroundPaint.apply {
                style = Paint.Style.FILL
                color = Color.argb(220, 106, 27, 154) // 深紫色
            }

            // 坐标线画笔
            coordinateLinesPaint.apply {
                style = Paint.Style.STROKE
                strokeWidth = 3 * density
                color = Color.WHITE
                strokeCap = Paint.Cap.ROUND
            }

            // 文字画笔 - 使用对比色，确保在坐标线上清晰可见
            textPaint.apply {
                color = Color.BLACK
                textSize = 12 * density
                textAlign = Paint.Align.CENTER
                typeface = Typeface.DEFAULT_BOLD
                // 添加白色描边效果，确保在任何背景下都清晰可见
                setShadowLayer(2f, 0f, 0f, Color.WHITE)
            }

            // 箭头画笔 - 使用对比色
            arrowPaint.apply {
                style = Paint.Style.FILL
                color = Color.BLACK
                strokeWidth = 2 * density
                // 添加白色描边效果
                setShadowLayer(2f, 0f, 0f, Color.WHITE)
            }
        }

        override fun onDraw(canvas: Canvas) {
            super.onDraw(canvas)

            val centerX = width / 2f
            val centerY = height / 2f

            // 先绘制坐标线（底层）
            val lineLength = Math.min(width, height) / 2f - 4

            // 水平坐标线
            canvas.drawLine(
                centerX - lineLength, centerY,
                centerX + lineLength, centerY,
                coordinateLinesPaint
            )

            // 垂直坐标线
            canvas.drawLine(
                centerX, centerY - lineLength,
                centerX, centerY + lineLength,
                coordinateLinesPaint
            )

            // 计算数字和箭头在坐标线上方的位置
            val textY = centerY - lineLength / 2f + textPaint.textSize / 3
            val arrowY = centerY - lineLength / 2f

            // 后绘制箭头（顶层），在数字左侧
            val arrowSize = lineLength * 0.2f
            val arrowX = centerX - textPaint.measureText(number.toString()) / 2f - arrowSize * 1.5f
            val path = Path().apply {
                moveTo(arrowX - arrowSize * 0.5f, arrowY - arrowSize * 0.3f)
                lineTo(arrowX + arrowSize * 0.5f, arrowY)
                lineTo(arrowX - arrowSize * 0.5f, arrowY + arrowSize * 0.3f)
                close()
            }
            canvas.drawPath(path, arrowPaint)

            // 最后绘制数字（最顶层），确保数字在坐标线上方显示
            canvas.drawText(number.toString(), centerX, textY, textPaint)
        }
    }
}

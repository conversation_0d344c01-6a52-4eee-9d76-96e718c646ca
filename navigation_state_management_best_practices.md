# 导航状态管理最佳实践

## 问题描述

### 典型场景
在使用 Jetpack Compose 和 Navigation 组件时，经常遇到以下问题：
- 用户在配置界面选择联系人后返回，需要重新点击卡片和选项才能看到选择结果
- 导航过程中组件状态丢失，导致用户体验不佳
- savedStateHandle 的数据传递时机和监听机制不当

### 具体表现
1. 用户点击"收到短信"卡片展开配置
2. 选择"指定联系人"选项
3. 点击"点击选择联系人"按钮，导航到联系人选择界面
4. 选择联系人并返回
5. **问题**：返回后卡片收起，需要重新点击卡片和选项才能看到选择的联系人

## 根本原因分析

### 1. 状态持久化问题
```kotlin
// ❌ 错误：使用 remember，导航时状态可能丢失
var expandedCardId by remember { mutableStateOf(initialExpandedItemId) }

// ✅ 正确：使用 rememberSaveable，确保导航过程中状态保持
var expandedCardId by rememberSaveable { mutableStateOf(initialExpandedItemId) }
```

### 2. 监听时机和层级问题
```kotlin
// ❌ 错误：在子组件中监听，如果组件未渲染则无法接收结果
@Composable
private fun ContactSelector(...) {
    LaunchedEffect(Unit) {
        // 如果卡片收起，这个组件不会被渲染，监听失效
        val selectedContacts = savedStateHandle?.get<List<ContactsHelper.ContactInfo>>("selected_contacts")
    }
}

// ✅ 正确：在父组件中监听，确保能及时处理结果
@Composable
private fun SmsReceivedConfigContent(...) {
    LaunchedEffect(Unit) {
        // 在组件级别监听，无论子组件是否渲染都能处理结果
        val selectedContacts = savedStateHandle?.get<List<ContactsHelper.ContactInfo>>("selected_contacts")
    }
}
```

### 3. 状态同步不完整（自动切换逻辑缺失）
```kotlin
// ❌ 错误：只更新联系人ID，不更新筛选类型
if (selectedContacts != null && selectedContacts.isNotEmpty()) {
    onContactsChanged(selectedContacts.map { it.id })
}

// ✅ 正确：同时更新相关的所有状态，实现自动切换
if (selectedContacts != null && selectedContacts.isNotEmpty()) {
    selectedFilterType = ContactFilterType.SPECIFIC_CONTACTS  // 🔑 自动切换模式
    selectedContactIds = selectedContacts.map { it.id }       // 更新联系人
}

// 🔑 关键原则：选择操作完成后，自动切换到相应的选项
// 适用场景：
// - 联系人选择 → 自动切换到"指定联系人"
// - 应用选择 → 自动切换到"指定应用"
// - 文件选择 → 自动切换到"指定文件"
// - 动态壁纸选择 → 自动切换到"动态壁纸"
```

## 解决方案

### 1. 状态持久化策略

#### 使用 rememberSaveable 替代 remember
```kotlin
// 对于需要在导航过程中保持的状态，使用 rememberSaveable
var expandedCardId by rememberSaveable { mutableStateOf(initialExpandedItemId) }
var selectedFilterType by rememberSaveable { mutableStateOf(ContactFilterType.ANY_CONTACT) }
var selectedContactIds by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }
```

#### 添加必要的导入
```kotlin
import androidx.compose.runtime.saveable.rememberSaveable
```

### 2. 监听层级优化

#### 将结果监听提升到合适的层级
```kotlin
@Composable
private fun SmsReceivedConfigContent(
    type: CommunicationStateType,
    onComplete: (Any) -> Unit
) {
    val navController = LocalNavController.current

    // 状态定义
    var selectedFilterType by rememberSaveable { mutableStateOf(ContactFilterType.ANY_CONTACT) }
    var selectedContactIds by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }

    // 在组件级别监听联系人选择结果
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val selectedContacts = savedStateHandle?.get<List<ContactsHelper.ContactInfo>>("selected_contacts")
        if (selectedContacts != null && selectedContacts.isNotEmpty()) {
            // 关键：自动切换到指定联系人模式
            selectedFilterType = ContactFilterType.SPECIFIC_CONTACTS
            // 更新选中的联系人ID
            selectedContactIds = selectedContacts.map { it.id }
            // 清除结果，避免重复处理
            savedStateHandle.remove<List<ContactsHelper.ContactInfo>>("selected_contacts")
        }
    }

    // UI 渲染逻辑...
}
```

### 4. 自动切换逻辑最佳实践

#### 核心原则：选择即切换
用户完成选择操作后，应该自动切换到相应的选项，避免用户需要手动再次点击。

#### 通用实现模式
```kotlin
@Composable
private fun ConfigContent(/* ... */) {
    // 状态定义
    var selectedMode by rememberSaveable { mutableStateOf(DefaultMode.ANY) }
    var selectedItems by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }

    // 监听选择结果并自动切换
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val selectedData = savedStateHandle?.get<List<DataType>>("selected_data")
        if (selectedData != null && selectedData.isNotEmpty()) {
            // 🔑 关键：自动切换到指定模式
            selectedMode = DefaultMode.SPECIFIC
            // 更新选中的数据
            selectedItems = selectedData.map { it.id }
            // 清除结果，避免重复处理
            savedStateHandle.remove<List<DataType>>("selected_data")
        }
    }
}
```

#### 具体应用场景示例

**1. 动态壁纸应用选择**
```kotlin
// 监听动态壁纸应用选择结果
LaunchedEffect(Unit) {
    val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
    val selectedApps = savedStateHandle?.get<List<SimpleAppInfo>>("selected_apps")
    if (selectedApps != null && selectedApps.isNotEmpty()) {
        // 自动切换到动态壁纸选项
        selectedType = WallpaperType.LIVE_WALLPAPER
        // 更新选中的应用
        val app = selectedApps.first()
        selectedLiveWallpaperPackageName = app.packageName
        selectedLiveWallpaperName = app.appName
        selectedLiveWallpaperIsSystemApp = app.isSystemApp
        // 清除结果
        savedStateHandle.remove<List<SimpleAppInfo>>("selected_apps")
    }
}
```

**2. 应用状态条件选择**
```kotlin
// 监听应用选择结果
LaunchedEffect(Unit) {
    val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
    val selectedApps = savedStateHandle?.get<List<SimpleAppInfo>>("selected_apps")
    if (selectedApps != null && selectedApps.isNotEmpty()) {
        // 自动切换到指定应用模式
        selectedDetectionMode = AppDetectionMode.SPECIFIC_APP
        // 更新选中的应用
        val app = selectedApps.first()
        selectedAppPackageName = app.packageName
        selectedAppName = app.appName
        selectedAppIsSystemApp = app.isSystemApp
        // 清除结果
        savedStateHandle.remove<List<SimpleAppInfo>>("selected_apps")
    }
}
```

**3. 联系人选择**
```kotlin
// 监听联系人选择结果
LaunchedEffect(Unit) {
    val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
    val selectedContacts = savedStateHandle?.get<List<ContactsHelper.ContactInfo>>("selected_contacts")
    if (selectedContacts != null && selectedContacts.isNotEmpty()) {
        // 自动切换到指定联系人模式
        selectedFilterType = ContactFilterType.SPECIFIC_CONTACTS
        // 更新选中的联系人
        selectedContactIds = selectedContacts.map { it.id }
        // 清除结果
        savedStateHandle.remove<List<ContactsHelper.ContactInfo>>("selected_contacts")
    }
}
```

### 3. 避免重复监听

#### 移除子组件中的重复监听逻辑
```kotlin
@Composable
private fun ContactSelector(
    selectedContactIds: List<String>,
    onContactsChanged: (List<String>) -> Unit
) {
    // 移除这里的监听逻辑，因为已经在父组件中处理
    // LaunchedEffect(Unit) { ... } // ❌ 删除

    // 只保留 UI 逻辑和导航逻辑
    OutlinedButton(
        onClick = {
            if (navController != null) {
                val route = Screen.ContactSelection.createMultiSelectionRoute(selectedContactIds)
                navController.navigate(route)
            }
        }
    ) {
        Text(if (selectedContactIds.isNotEmpty()) "已选择 ${selectedContactIds.size} 个联系人" else "点击选择联系人")
    }
}
```

## 最佳实践总结

### 1. 状态管理原则
- **持久化关键状态**：使用 `rememberSaveable` 保存需要在导航过程中保持的状态
- **状态作用域合理化**：将状态定义在合适的组件层级，避免过深或过浅
- **状态同步完整性**：更新相关状态时要考虑所有关联的状态变量

### 2. 导航结果处理原则
- **监听层级选择**：在能够稳定渲染的组件层级监听导航结果
- **避免重复监听**：同一个结果只在一个地方监听和处理
- **及时清理结果**：处理完结果后立即清除，避免重复处理

### 3. 用户体验优化
- **🔑 自动状态切换**：根据用户操作自动切换到合适的模式（核心原则）
- **状态可见性**：确保用户的操作结果能够立即可见
- **操作连续性**：减少用户需要重复的操作步骤
- **学习现有实现**：遇到问题时先查看类似功能的实现方式

### 4. 自动切换逻辑原则
- **选择即切换**：用户完成选择后立即自动切换到相应选项
- **状态同步完整性**：更新所有相关的状态变量，不只是数据本身
- **通用模式复用**：建立标准的自动切换实现模式，在不同场景中复用
- **及时清理结果**：处理完选择结果后立即清除，避免重复处理

## 常见陷阱

### 1. 过度依赖 remember
```kotlin
// ❌ 导航时状态丢失
var importantState by remember { mutableStateOf(defaultValue) }

// ✅ 状态在导航过程中保持
var importantState by rememberSaveable { mutableStateOf(defaultValue) }
```

### 2. 监听位置不当
```kotlin
// ❌ 在可能不渲染的组件中监听
@Composable
fun ConditionalComponent() {
    if (condition) {
        LaunchedEffect(Unit) { /* 监听逻辑 */ }
    }
}

// ✅ 在稳定渲染的组件中监听
@Composable
fun StableComponent() {
    LaunchedEffect(Unit) { /* 监听逻辑 */ }

    if (condition) {
        ConditionalComponent()
    }
}
```

### 3. 状态更新不完整（缺少自动切换）
```kotlin
// ❌ 只更新部分相关状态，忘记自动切换
selectedContactIds = newContactIds

// ✅ 更新所有相关状态，包括自动切换
selectedFilterType = ContactFilterType.SPECIFIC_CONTACTS  // 🔑 自动切换
selectedContactIds = newContactIds
```

### 4. 忘记实现自动切换逻辑
```kotlin
// ❌ 选择后用户需要手动再次点击
LaunchedEffect(Unit) {
    val selectedData = savedStateHandle?.get<List<DataType>>("selected_data")
    if (selectedData != null && selectedData.isNotEmpty()) {
        // 只更新数据，忘记切换模式
        selectedItems = selectedData.map { it.id }
        savedStateHandle.remove<List<DataType>>("selected_data")
    }
}

// ✅ 选择后自动切换到相应选项
LaunchedEffect(Unit) {
    val selectedData = savedStateHandle?.get<List<DataType>>("selected_data")
    if (selectedData != null && selectedData.isNotEmpty()) {
        selectedMode = SpecificMode.SELECTED  // 🔑 自动切换
        selectedItems = selectedData.map { it.id }
        savedStateHandle.remove<List<DataType>>("selected_data")
    }
}
```

## 适用场景

这些最佳实践适用于以下场景：
- 配置界面中的多步骤操作
- 需要导航到其他界面选择数据的场景
- 使用 savedStateHandle 传递导航结果的情况
- 可展开/收起的复杂 UI 组件
- 需要保持用户操作状态的长流程

## 相关文件

本次修复涉及的关键文件：
- `app/src/main/java/com/my/backgroundmanager/ui/screens/DetailConfigurationScreen.kt`
- `app/src/main/java/com/my/backgroundmanager/ui/configuration/CommunicationStateConfigProvider.kt`
- `app/src/main/java/com/my/backgroundmanager/ui/configuration/LocationTaskConfigProvider.kt`
- `app/src/main/java/com/my/backgroundmanager/ui/configuration/DeviceSettingsTaskConfigProvider.kt` (动态壁纸自动切换)
- `app/src/main/java/com/my/backgroundmanager/ui/configuration/AppStateConfigProvider.kt` (应用选择自动切换参考实现)
- `app/src/main/java/com/my/backgroundmanager/ui/configuration/NotificationTaskConfigProvider.kt` (通知任务自动切换修复)

## 验证方法

修复后的验证步骤：
1. 点击配置卡片展开
2. 选择需要导航的选项（如"任何联系人"模式）
3. 点击选择按钮，导航到选择界面
4. 完成选择并返回
5. **🔑 关键验证**：
   - 卡片保持展开状态
   - **自动切换到相应选项**（如"指定联系人"模式）
   - 选择结果立即可见
   - 无需重复操作

## 自动切换逻辑验证清单

**动态壁纸场景**：
- [ ] 选择动态壁纸应用后，自动切换到"动态壁纸"选项
- [ ] 显示选择的应用名称和包名
- [ ] 确认按钮变为可用状态

**应用状态条件场景**：
- [ ] 选择应用后，自动切换到"指定应用"模式
- [ ] 显示选择的应用信息
- [ ] 相关UI状态正确更新

**联系人选择场景**：
- [ ] 选择联系人后，自动切换到"指定联系人"模式
- [ ] 显示选择的联系人数量
- [ ] 配置界面保持展开状态

**通知任务场景**：
- [ ] 清除通知：选择应用后，自动切换到"清除指定应用通知"模式
- [ ] 恢复隐藏通知：选择应用后，自动切换到"恢复指定应用通知"模式
- [ ] 显示选择的应用信息
- [ ] 配置界面保持展开状态

package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands.data.QuickCommandRepository
import com.weinuo.quickcommands.ui.screens.QuickCommandFormContent
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.configuration.ConfigurationMode
import com.weinuo.quickcommands.ui.configuration.ConfigurationItem
import com.weinuo.quickcommands.storage.NavigationDataStorageManager
import com.weinuo.quickcommands.model.SharedTriggerCondition
import com.weinuo.quickcommands.model.SharedTask

/**
 * 快捷指令表单Activity
 * 
 * 独立的Activity，用于创建和编辑快捷指令，不包含底部导航栏。
 * 支持创建和编辑模式：
 * - commandId为null：创建模式
 * - commandId非null：编辑模式
 */
class QuickCommandFormActivity : ComponentActivity() {

    companion object {
        private const val EXTRA_COMMAND_ID = "command_id"

        /**
         * 启动创建新快捷指令的Activity
         */
        fun startForCreate(context: Context) {
            val intent = Intent(context, QuickCommandFormActivity::class.java)
            context.startActivity(intent)
        }

        /**
         * 启动编辑快捷指令的Activity
         */
        fun startForEdit(context: Context, commandId: String) {
            val intent = Intent(context, QuickCommandFormActivity::class.java).apply {
                putExtra(EXTRA_COMMAND_ID, commandId)
            }
            context.startActivity(intent)
        }
    }

    // 配置结果状态 - 使用mutableStateOf使其可观察
    // Triple<navigationKey, editIndex, configMode>
    private val pendingConfigurationResult = mutableStateOf<Triple<String, Int?, String>?>(null)

    // ActivityResultLauncher用于处理配置Activity的结果
    private val configurationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        android.util.Log.d("QuickCommandFormActivity", "ActivityResult received: resultCode=${result.resultCode}")
        if (result.resultCode == Activity.RESULT_OK) {
            val navigationKey = result.data?.getStringExtra("navigation_key")
            val editIndex = result.data?.getIntExtra("edit_index", -1)?.takeIf { it != -1 }
            val configMode = result.data?.getStringExtra("config_mode")

            android.util.Log.d("QuickCommandFormActivity", "ActivityResult data: navigationKey=$navigationKey, editIndex=$editIndex, configMode=$configMode")

            if (navigationKey != null && configMode != null) {
                // 更新State，触发Compose重组
                pendingConfigurationResult.value = Triple(navigationKey, editIndex, configMode)
                android.util.Log.d("QuickCommandFormActivity", "pendingConfigurationResult updated")
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取传入的commandId（如果有）
        val commandId = intent.getStringExtra(EXTRA_COMMAND_ID)

        setContent {
            QuickCommandsTheme {
                QuickCommandFormActivityContent(
                    commandId = commandId,
                    configurationLauncher = configurationLauncher,
                    pendingConfigurationResult = pendingConfigurationResult.value,
                    onConfigurationResultProcessed = {
                        pendingConfigurationResult.value = null
                    },
                    onFinish = { finish() }
                )
            }
        }
    }
}

/**
 * 快捷指令表单Activity的内容组件
 */
@Composable
private fun QuickCommandFormActivityContent(
    commandId: String?,
    configurationLauncher: androidx.activity.result.ActivityResultLauncher<Intent>,
    pendingConfigurationResult: Triple<String, Int?, String>?,
    onConfigurationResultProcessed: () -> Unit,
    onFinish: () -> Unit
) {
    val context = LocalContext.current
    val quickCommandRepository = QuickCommandRepository.getInstance(context)

    // 创建导航数据管理器
    val navigationDataManager = remember { NavigationDataStorageManager(context) }

    QuickCommandFormContent(
        commandId = commandId,
        quickCommandRepository = quickCommandRepository,
        onNavigateBack = onFinish,
        onNavigateToUnifiedConfiguration = { configurationMode, editData, editIndex ->
            // 启动统一配置Activity
            android.util.Log.d("QuickCommandFormActivity", "onNavigateToUnifiedConfiguration called: mode=$configurationMode, editData=$editData, editIndex=$editIndex")
            val intent = when (configurationMode) {
                ConfigurationMode.TRIGGER_CONDITION -> {
                    Intent(context, UnifiedConfigurationActivity::class.java).apply {
                        putExtra("configuration_mode", ConfigurationMode.TRIGGER_CONDITION.name)
                        editData?.let { putExtra("edit_data", it) }
                        editIndex?.let { putExtra("edit_index", it) }
                    }
                }
                ConfigurationMode.ABORT_CONDITION -> {
                    Intent(context, UnifiedConfigurationActivity::class.java).apply {
                        putExtra("configuration_mode", ConfigurationMode.ABORT_CONDITION.name)
                        editData?.let { putExtra("edit_data", it) }
                        editIndex?.let { putExtra("edit_index", it) }
                    }
                }
                ConfigurationMode.TASK -> {
                    Intent(context, UnifiedConfigurationActivity::class.java).apply {
                        putExtra("configuration_mode", ConfigurationMode.TASK.name)
                        editData?.let { putExtra("edit_data", it) }
                        editIndex?.let { putExtra("edit_index", it) }
                    }
                }
            }
            android.util.Log.d("QuickCommandFormActivity", "Launching UnifiedConfigurationActivity with intent: $intent")
            configurationLauncher.launch(intent)
        },
        onNavigateToDetailedConfiguration = { configurationItem, configurationMode, editData, editIndex ->
            // 启动详细配置Activity
            android.util.Log.d("QuickCommandFormActivity", "onNavigateToDetailedConfiguration called: item=${configurationItem.id}, mode=$configurationMode, editData=$editData, editIndex=$editIndex")
            val intent = Intent(context, DetailedConfigurationActivity::class.java).apply {
                putExtra("configuration_mode", configurationMode.name)
                putExtra("item_type", configurationItem.id)
                editData?.let { putExtra("edit_data", it) }
                editIndex?.let { putExtra("edit_index", it) }
            }
            android.util.Log.d("QuickCommandFormActivity", "Launching DetailedConfigurationActivity with intent: $intent")
            configurationLauncher.launch(intent)
        },
        pendingConfigurationResult = pendingConfigurationResult,
        onConfigurationResultProcessed = onConfigurationResultProcessed
    )
}

package com.weinuo.quickcommands.floating

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.GestureRecordingNativeManager
import com.weinuo.quickcommands.utils.OverlayPermissionUtil
import kotlinx.coroutines.*

/**
 * 高级悬浮窗录制服务
 *
 * 提供可视化的手势录制功能，用户可以通过拖拽坐标标记来创建手势序列。
 * 不同于传统悬浮窗录制，此模式不监听用户的实际触摸操作，而是通过
 * 可视化界面让用户直接设置手势坐标和类型。
 *
 * 主要功能：
 * - 显示带加号的悬浮按钮
 * - 提供手势类型选择菜单
 * - 显示可移动的坐标标记
 * - 支持滑动手势的起始点和终点设置
 * - 直接保存到存储并跳转到编辑界面
 */
class AdvancedFloatingRecordingService : Service() {

    companion object {
        private const val TAG = "AdvancedFloatingRecordingService"

        /**
         * 启动高级悬浮窗录制服务
         */
        fun startAdvancedFloatingWindow(context: Context): Boolean {
            return try {
                val intent = Intent(context, AdvancedFloatingRecordingService::class.java)
                context.startService(intent)
                true
            } catch (e: Exception) {
                Log.e(TAG, "启动高级悬浮窗录制服务失败", e)
                false
            }
        }

        /**
         * 停止高级悬浮窗录制服务
         */
        fun stopAdvancedFloatingWindow(context: Context) {
            try {
                val intent = Intent(context, AdvancedFloatingRecordingService::class.java)
                context.stopService(intent)
            } catch (e: Exception) {
                Log.e(TAG, "停止高级悬浮窗录制服务失败", e)
            }
        }
    }

    // 服务组件
    private var advancedFloatingButton: AdvancedFloatingRecordingButton? = null
    private var gestureRecordingManager: GestureRecordingNativeManager? = null
    private val numberMarkers = mutableListOf<NumberMarkerView>()

    // 滑动终点标记列表
    private val swipeEndMarkers = mutableListOf<SwipeEndMarkerView>()

    // 录制状态
    private val coordinateMarkers = mutableListOf<CoordinateMarker>()
    private var recordingConfig = AdvancedRecordingConfig()
    private var isRecording = false

    // 继续录制相关
    private var isContinueRecording = false
    private var existingRecordingId: String? = null
    private var existingEventCount = 0

    // 协程作用域
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "高级悬浮窗录制服务已创建")

        // 初始化手势录制管理器
        gestureRecordingManager = GestureRecordingNativeManager(this)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let {
            // 检查是否是继续录制模式
            isContinueRecording = it.getBooleanExtra("continue_recording", false)
            existingRecordingId = it.getStringExtra("recording_id")
            existingEventCount = it.getIntExtra("existing_event_count", 0)

            if (isContinueRecording) {
                Log.d(TAG, "继续录制模式，现有事件数量: $existingEventCount")
                recordingConfig = recordingConfig.copy(
                    recordingName = "继续录制_${System.currentTimeMillis()}"
                )
            }
        }

        // 启动悬浮按钮
        startAdvancedFloatingButton()

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "高级悬浮窗录制服务已销毁")

        // 停止悬浮按钮
        stopAdvancedFloatingButton()

        // 清除所有数字标记
        clearAllNumberMarkers()

        // 取消协程作用域
        serviceScope.cancel()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * 启动高级悬浮按钮
     */
    private fun startAdvancedFloatingButton() {
        // 检查悬浮窗权限
        if (!OverlayPermissionUtil.hasOverlayPermission(this)) {
            Log.w(TAG, "没有悬浮窗权限，无法显示悬浮按钮")
            stopSelf()
            return
        }

        // 如果按钮已存在，先移除
        if (advancedFloatingButton != null) {
            stopAdvancedFloatingButton()
        }

        try {
            // 创建并显示高级悬浮按钮
            advancedFloatingButton = AdvancedFloatingRecordingButton(
                context = this,
                onAddGesture = { gestureType, x, y -> addGestureMarker(gestureType, x, y) },
                onAddSwipeGesture = { startX, startY, endX, endY -> addSwipeGestureMarker(startX, startY, endX, endY) },
                onSaveRecording = { saveRecording() },
                onClearAll = { clearAllMarkers() },
                onShowSettings = { showRecordingSettings() }
            )

            if (advancedFloatingButton?.show() == true) {
                Log.d(TAG, "高级悬浮按钮已启动")
            } else {
                Log.e(TAG, "显示高级悬浮按钮失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动高级悬浮按钮失败", e)
            stopSelf()
        }
    }

    /**
     * 停止高级悬浮按钮
     */
    private fun stopAdvancedFloatingButton() {
        try {
            advancedFloatingButton?.hide()
            advancedFloatingButton = null
            Log.d(TAG, "高级悬浮按钮已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止高级悬浮按钮失败", e)
        }
    }

    /**
     * 添加数字标记到屏幕
     */
    private fun addNumberMarker(number: Int, x: Float, y: Float, gestureType: AdvancedGestureType) {
        try {
            val marker = NumberMarkerView(this, number, gestureType) { newX, newY ->
                // 当标记位置改变时，更新对应的坐标标记
                updateMarkerPosition(number, newX, newY, gestureType)
            }
            if (marker.show(x, y)) {
                numberMarkers.add(marker)
                Log.d(TAG, "已添加数字标记 $number 在位置 ($x, $y)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "添加数字标记失败", e)
        }
    }

    /**
     * 更新标记位置
     */
    private fun updateMarkerPosition(markerNumber: Int, newX: Float, newY: Float, gestureType: AdvancedGestureType) {
        try {
            // 获取屏幕尺寸
            val screenWidth = resources.displayMetrics.widthPixels.toFloat()
            val screenHeight = resources.displayMetrics.heightPixels.toFloat()

            // 计算相对坐标
            val relativeX = newX / screenWidth
            val relativeY = newY / screenHeight

            // 查找并更新对应的坐标标记
            val markerIndex = if (isContinueRecording) {
                markerNumber - existingEventCount - 1
            } else {
                markerNumber - 1
            }

            if (markerIndex >= 0 && markerIndex < coordinateMarkers.size) {
                val oldMarker = coordinateMarkers[markerIndex]
                val updatedMarker = oldMarker.copy(
                    position = oldMarker.position.copy(
                        startX = relativeX,
                        startY = relativeY
                    )
                )
                coordinateMarkers[markerIndex] = updatedMarker
                Log.d(TAG, "已更新标记 $markerNumber 位置到 ($newX, $newY)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新标记位置失败", e)
        }
    }

    /**
     * 添加滑动终点标记
     */
    private fun addSwipeEndMarker(number: Int, x: Float, y: Float) {
        try {
            val marker = SwipeEndMarkerView(this, number) { newX, newY ->
                // 当滑动终点标记位置改变时，更新对应的坐标标记
                updateSwipeEndMarkerPosition(number, newX, newY)
            }
            if (marker.show(x, y)) {
                swipeEndMarkers.add(marker)
                Log.d(TAG, "已添加滑动终点标记 $number 在位置 ($x, $y)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "添加滑动终点标记失败", e)
        }
    }

    /**
     * 更新滑动终点标记位置
     */
    private fun updateSwipeEndMarkerPosition(markerNumber: Int, newX: Float, newY: Float) {
        try {
            // 获取屏幕尺寸
            val screenWidth = resources.displayMetrics.widthPixels.toFloat()
            val screenHeight = resources.displayMetrics.heightPixels.toFloat()

            // 计算相对坐标
            val relativeX = newX / screenWidth
            val relativeY = newY / screenHeight

            // 查找并更新对应的滑动手势坐标标记的终点
            val markerIndex = if (isContinueRecording) {
                markerNumber - existingEventCount - 1 // 终点数字和起点相同，所以要-1
            } else {
                markerNumber - 1 // 终点数字和起点相同，所以要-1
            }

            if (markerIndex >= 0 && markerIndex < coordinateMarkers.size) {
                val oldMarker = coordinateMarkers[markerIndex]
                if (oldMarker.gestureType == AdvancedGestureType.SWIPE) {
                    val updatedMarker = oldMarker.copy(
                        position = oldMarker.position.copy(
                            endX = relativeX,
                            endY = relativeY
                        )
                    )
                    coordinateMarkers[markerIndex] = updatedMarker
                    Log.d(TAG, "已更新滑动终点标记 $markerNumber 位置到 ($newX, $newY)")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新滑动终点标记位置失败", e)
        }
    }

    /**
     * 清除所有数字标记
     */
    private fun clearAllNumberMarkers() {
        try {
            numberMarkers.forEach { it.hide() }
            numberMarkers.clear()

            // 同时清除滑动终点标记
            swipeEndMarkers.forEach { it.hide() }
            swipeEndMarkers.clear()

            Log.d(TAG, "已清除所有数字标记和滑动终点标记")
        } catch (e: Exception) {
            Log.e(TAG, "清除数字标记失败", e)
        }
    }

    /**
     * 添加手势标记
     */
    private fun addGestureMarker(gestureType: AdvancedGestureType, x: Float, y: Float) {
        Log.d(TAG, "添加手势标记: ${gestureType.displayName} 在位置 ($x, $y)")

        // 获取屏幕尺寸
        val screenWidth = resources.displayMetrics.widthPixels.toFloat()
        val screenHeight = resources.displayMetrics.heightPixels.toFloat()

        // 计算相对坐标
        val relativeX = x / screenWidth
        val relativeY = y / screenHeight

        // 创建新的坐标标记
        val marker = CoordinateMarker(
            gestureType = gestureType,
            position = TouchPosition(
                startX = relativeX,
                startY = relativeY
            ),
            duration = when (gestureType) {
                AdvancedGestureType.TAP -> 50L
                AdvancedGestureType.LONG_PRESS -> 1000L
                AdvancedGestureType.SWIPE -> 300L
                AdvancedGestureType.MULTI_TOUCH -> 100L
            },
            label = "${gestureType.displayName} ${coordinateMarkers.size + 1}"
        )

        coordinateMarkers.add(marker)

        // 添加数字标记到屏幕（如果是继续录制，数字从现有数量往后顺延）
        val markerNumber = if (isContinueRecording) {
            existingEventCount + coordinateMarkers.size
        } else {
            coordinateMarkers.size
        }
        addNumberMarker(markerNumber, x, y, gestureType)

        Log.d(TAG, "已添加手势标记，当前总数: ${coordinateMarkers.size}")
    }

    /**
     * 添加滑动手势标记
     */
    private fun addSwipeGestureMarker(startX: Float, startY: Float, endX: Float, endY: Float) {
        Log.d(TAG, "添加滑动手势标记: 从($startX, $startY)到($endX, $endY)")

        // 获取屏幕尺寸
        val screenWidth = resources.displayMetrics.widthPixels.toFloat()
        val screenHeight = resources.displayMetrics.heightPixels.toFloat()

        // 计算相对坐标
        val relativeStartX = startX / screenWidth
        val relativeStartY = startY / screenHeight
        val relativeEndX = endX / screenWidth
        val relativeEndY = endY / screenHeight

        // 创建滑动手势标记
        val marker = CoordinateMarker(
            gestureType = AdvancedGestureType.SWIPE,
            position = TouchPosition(
                startX = relativeStartX,
                startY = relativeStartY,
                endX = relativeEndX,
                endY = relativeEndY
            ),
            duration = 300L, // 滑动持续时间
            label = "滑动 ${coordinateMarkers.size + 1}"
        )

        coordinateMarkers.add(marker)

        // 计算起始点和终点的数字标记（使用相同数字）
        val markerNumber = if (isContinueRecording) {
            existingEventCount + coordinateMarkers.size
        } else {
            coordinateMarkers.size
        }

        // 添加起始点数字标记
        addNumberMarker(markerNumber, startX, startY, AdvancedGestureType.SWIPE)

        // 添加终点数字标记（使用相同数字，但带箭头区分）
        addSwipeEndMarker(markerNumber, endX, endY)

        Log.d(TAG, "已添加滑动手势标记，当前总数: ${coordinateMarkers.size}")
    }

    /**
     * 清空所有标记
     */
    private fun clearAllMarkers() {
        coordinateMarkers.clear()
        clearAllNumberMarkers()
        Log.d(TAG, "已清空所有标记")
    }

    /**
     * 显示录制设置
     */
    private fun showRecordingSettings() {
        // TODO: 实现录制设置界面
        Log.d(TAG, "显示录制设置")
    }

    /**
     * 保存录制
     */
    private fun saveRecording() {
        if (coordinateMarkers.isEmpty()) {
            Log.w(TAG, "没有手势标记，无法保存")
            return
        }

        serviceScope.launch {
            try {
                if (isContinueRecording && existingRecordingId != null) {
                    // 继续录制模式：加载现有录制并追加新事件
                    Log.d(TAG, "继续录制模式，录制ID: $existingRecordingId，新增标记数: ${coordinateMarkers.size}")

                    val existingRecording = gestureRecordingManager?.loadRecording(existingRecordingId!!)
                    if (existingRecording != null) {
                        Log.d(TAG, "成功加载现有录制，现有事件数: ${existingRecording.events.size}")

                        // 转换新标记为触摸事件
                        val newTouchEvents = coordinateMarkers.map { marker ->
                            Log.d(TAG, "转换标记: ${marker.gestureType.displayName} 位置: (${marker.position.startX}, ${marker.position.startY})")
                            marker.toTouchEvent()
                        }
                        Log.d(TAG, "转换完成，新事件数: ${newTouchEvents.size}")

                        // 合并现有事件和新事件
                        val allEvents = existingRecording.events + newTouchEvents
                        Log.d(TAG, "合并后总事件数: ${allEvents.size}")

                        // 更新录制对象
                        val updatedRecording = existingRecording.copy(
                            events = allEvents,
                            description = "高级悬浮窗录制，包含${allEvents.size}个手势（继续录制）"
                        )

                        // 保存更新后的录制
                        val saveResult = gestureRecordingManager?.saveRecording(updatedRecording, existingRecordingId!!)
                        Log.d(TAG, "保存结果: $saveResult")

                        if (saveResult != null) {
                            Log.d(TAG, "继续录制已保存，总事件数: ${allEvents.size}")

                            // 发送数据更新事件通知
                            notifyRecordingUpdated(existingRecordingId!!)

                            // 启动编辑界面
                            startEditActivity(existingRecordingId!!)
                        } else {
                            Log.e(TAG, "保存继续录制失败")
                        }
                    } else {
                        Log.e(TAG, "无法加载现有录制数据，录制ID: $existingRecordingId")
                    }
                } else {
                    // 新录制模式
                    val touchEvents = coordinateMarkers.map { it.toTouchEvent() }

                    val gestureRecording = GestureRecording(
                        name = recordingConfig.recordingName,
                        description = "高级悬浮窗录制，包含${touchEvents.size}个手势",
                        events = touchEvents,
                        screenWidth = resources.displayMetrics.widthPixels,
                        screenHeight = resources.displayMetrics.heightPixels
                    )

                    val recordingId = "advanced_${System.currentTimeMillis()}"
                    gestureRecordingManager?.saveRecording(gestureRecording, recordingId)

                    Log.d(TAG, "新录制已保存: $recordingId")

                    // 启动编辑界面
                    startEditActivity(recordingId)
                }

                // 清理所有标记
                clearAllNumberMarkers()

                // 停止服务
                stopSelf()

            } catch (e: Exception) {
                Log.e(TAG, "保存录制失败", e)
            }
        }
    }

    /**
     * 通知录制数据已更新
     */
    private fun notifyRecordingUpdated(recordingId: String) {
        try {
            val sharedPrefs = getSharedPreferences("recording_update_events", MODE_PRIVATE)
            sharedPrefs.edit()
                .putLong("recording_updated_$recordingId", System.currentTimeMillis())
                .apply()
            Log.d(TAG, "已发送录制数据更新事件: $recordingId")
        } catch (e: Exception) {
            Log.e(TAG, "发送录制数据更新事件失败", e)
        }
    }

    /**
     * 启动手势录制编辑界面
     */
    private fun startEditActivity(recordingId: String) {
        try {
            val editIntent = Intent(this, com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity::class.java).apply {
                putExtra(com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity.EXTRA_RECORDING_ID, recordingId)
                putExtra(com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity.EXTRA_RETURN_RESULT, false)
                // 添加必要的标志以从服务启动Activity
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }

            startActivity(editIntent)
            Log.d(TAG, "已启动手势录制编辑界面: $recordingId")
        } catch (e: Exception) {
            Log.e(TAG, "启动手势录制编辑界面失败", e)
        }
    }
}

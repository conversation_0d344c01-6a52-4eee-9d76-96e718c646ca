package com.weinuo.quickcommands.utils

import android.content.Context
import android.content.ContextWrapper
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import java.lang.ref.WeakReference
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

/**
 * 高性能本地化助手
 * 
 * 设计原则：
 * 1. 按需包装：只在实际需要多语言的Activity中包装Context
 * 2. 复用Context：缓存已包装的Context，避免重复创建
 * 3. 内存优化：使用WeakReference避免内存泄漏
 * 4. 条件性操作：系统默认语言时直接返回原Context，零性能损耗
 * 5. 线程安全：使用ConcurrentHashMap确保多线程安全
 * 
 * 性能优化特性：
 * - Context缓存机制，避免重复包装
 * - 条件性包装，系统默认时零开销
 * - WeakReference防止内存泄漏
 * - 线程安全的缓存实现
 */
object LocaleHelper {
    private const val TAG = "LocaleHelper"
    
    // Context缓存，使用WeakReference避免内存泄漏
    private val contextCache = ConcurrentHashMap<String, WeakReference<Context>>()
    
    /**
     * 包装Context以应用指定语言
     * 
     * 性能优化：
     * 1. 系统默认语言时直接返回原Context，零性能损耗
     * 2. 缓存已包装的Context，避免重复创建
     * 3. 使用WeakReference防止内存泄漏
     * 
     * @param context 原始Context
     * @param language 语言代码 (system/zh/en)，为null时使用LanguageManager获取
     * @return 包装后的Context
     */
    fun wrapContext(context: Context, language: String? = null): Context {
        val targetLanguage = language ?: LanguageManager.getCurrentLanguage(context)
        
        // 系统默认语言时直接返回原Context，实现零性能损耗
        if (targetLanguage == LanguageManager.LANGUAGE_SYSTEM) {
            Log.d(TAG, "Using system default language, no context wrapping needed")
            return context
        }
        
        try {
            // 生成缓存键
            val cacheKey = "${context.hashCode()}_$targetLanguage"
            
            // 检查缓存
            contextCache[cacheKey]?.get()?.let { cachedContext ->
                Log.d(TAG, "Using cached context for language: $targetLanguage")
                return cachedContext
            }
            
            // 创建新的包装Context
            val wrappedContext = createWrappedContext(context, targetLanguage)
            
            // 缓存包装后的Context
            contextCache[cacheKey] = WeakReference(wrappedContext)
            
            // 清理过期的缓存项
            cleanupCache()
            
            Log.d(TAG, "Created wrapped context for language: $targetLanguage")
            return wrappedContext
            
        } catch (e: Exception) {
            Log.e(TAG, "Error wrapping context for language: $targetLanguage", e)
            // 出错时返回原Context，确保应用正常运行
            return context
        }
    }
    
    /**
     * 检查Context是否需要包装
     * 
     * @param context 上下文
     * @return 是否需要包装
     */
    fun needsContextWrapping(context: Context): Boolean {
        return LanguageManager.needsLanguageOverride(context)
    }
    
    /**
     * 获取指定语言的Locale对象
     * 
     * @param language 语言代码
     * @return Locale对象
     */
    fun getLocaleForLanguage(language: String): Locale {
        return when (language) {
            LanguageManager.LANGUAGE_CHINESE -> Locale.SIMPLIFIED_CHINESE
            LanguageManager.LANGUAGE_ENGLISH -> Locale.ENGLISH
            LanguageManager.LANGUAGE_SYSTEM -> Locale.getDefault()
            else -> {
                Log.w(TAG, "Unknown language code: $language, using default")
                Locale.getDefault()
            }
        }
    }
    
    /**
     * 清除Context缓存
     * 
     * 在语言设置改变时调用，强制重新创建包装Context
     */
    fun clearCache() {
        contextCache.clear()
        Log.d(TAG, "Context cache cleared")
    }
    
    /**
     * 创建包装的Context
     */
    private fun createWrappedContext(context: Context, language: String): Context {
        val locale = getLocaleForLanguage(language)
        val configuration = Configuration(context.resources.configuration)
        
        // 根据Android版本设置Locale
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale)
        } else {
            @Suppress("DEPRECATION")
            configuration.locale = locale
        }
        
        // 创建新的Context
        val newContext = context.createConfigurationContext(configuration)
        
        return ContextWrapper(newContext)
    }
    
    /**
     * 清理过期的缓存项
     * 
     * 定期清理被GC回收的WeakReference，防止内存泄漏
     */
    private fun cleanupCache() {
        val iterator = contextCache.entries.iterator()
        var cleanedCount = 0
        
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (entry.value.get() == null) {
                iterator.remove()
                cleanedCount++
            }
        }
        
        if (cleanedCount > 0) {
            Log.d(TAG, "Cleaned up $cleanedCount expired cache entries")
        }
    }
}

/**
 * Context扩展函数，便于使用
 * 
 * 使用示例：
 * ```kotlin
 * val localizedContext = context.withAppLanguage()
 * val localizedString = localizedContext.getString(R.string.some_text)
 * ```
 */
fun Context.withAppLanguage(language: String? = null): Context {
    return LocaleHelper.wrapContext(this, language)
}

/**
 * 检查Context是否需要语言包装的扩展函数
 */
fun Context.needsLanguageWrapping(): Boolean {
    return LocaleHelper.needsContextWrapping(this)
}

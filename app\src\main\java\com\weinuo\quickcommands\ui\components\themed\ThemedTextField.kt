package com.weinuo.quickcommands.ui.components.themed

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.VisualTransformation
import com.weinuo.quickcommands.ui.theme.config.TextFieldConfig
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的文本输入框组件
 *
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用分层设计风格（LayeredTextField）- 带边框和阴影
 * - 天空蓝主题：使用整合设计风格（IntegratedTextField）- 无边框，大圆角
 * - 未来主题：可以使用各自独有的实现
 *
 * 支持各种文本输入场景，包括单行、多行、密码输入等，
 * 以及丰富的自定义选项。
 */
@Composable
fun ThemedTextField(
    config: TextFieldConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    // 使用当前主题的组件工厂创建文本输入框
    themeContext.componentFactory.createTextField()(
        config.copy(modifier = modifier)
    )
}

/**
 * 主题感知的文本输入框组件 - 便捷版本
 *
 * 提供更简洁的API，自动处理常见的配置
 */
@Composable
fun ThemedTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    label: String? = null,
    placeholder: String? = null,
    leadingIcon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    trailingIcon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    onTrailingIconClick: (() -> Unit)? = null,
    prefix: String? = null,
    suffix: String? = null,
    supportingText: String? = null,
    isError: Boolean = false,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    shape: Shape? = null
) {
    val config = TextFieldConfig(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        enabled = enabled,
        readOnly = readOnly,
        label = label,
        placeholder = placeholder,
        leadingIcon = leadingIcon,
        trailingIcon = trailingIcon,
        onTrailingIconClick = onTrailingIconClick,
        prefix = prefix,
        suffix = suffix,
        supportingText = supportingText,
        isError = isError,
        visualTransformation = visualTransformation,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = singleLine,
        maxLines = maxLines,
        minLines = minLines,
        shape = shape
    )

    // 使用主题感知组件
    ThemedTextField(config = config)
}

/**
 * 主题感知的简单文本输入框 - 最简版本
 *
 * 只需要值和变更回调的最简单版本
 */
@Composable
fun ThemedSimpleTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    label: String? = null,
    placeholder: String? = null
) {
    ThemedTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        label = label,
        placeholder = placeholder,
        singleLine = true
    )
}

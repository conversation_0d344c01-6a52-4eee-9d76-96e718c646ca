package com.weinuo.quickcommands.ui.theme.manager

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.runtime.*
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.config.CardStyleConfig
import com.weinuo.quickcommands.data.SettingsRepository

/**
 * 卡片样式配置管理器
 *
 * 负责管理应用中所有卡片样式的配置，包括：
 * - 从全局设置中获取用户自定义配置
 * - 卡片样式配置的实时更新
 * - 配置的状态管理
 * - 配置的验证和同步
 *
 * 与BlurConfigurationManager不同，卡片样式配置直接使用GlobalSettings进行管理，
 * 确保与其他设置项保持一致的存储和同步机制。
 */
class CardStyleConfigurationManager private constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {

    companion object {
        @Volatile
        private var INSTANCE: CardStyleConfigurationManager? = null

        /**
         * 获取卡片样式配置管理器实例
         */
        fun getInstance(context: Context, settingsRepository: SettingsRepository): CardStyleConfigurationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CardStyleConfigurationManager(context.applicationContext, settingsRepository).also { INSTANCE = it }
            }
        }

        /**
         * 获取卡片样式配置管理器实例（兼容旧版本）
         */
        fun getInstance(context: Context): CardStyleConfigurationManager {
            return getInstance(context, SettingsRepository(context))
        }
    }

    /**
     * 获取当前卡片样式配置
     *
     * 从GlobalSettings中读取用户自定义的卡片样式配置
     */
    @Composable
    fun getCardStyleConfiguration(): CardStyleConfig {
        val globalSettings by settingsRepository.globalSettings.collectAsState()
        
        return CardStyleConfig(
            // 基础卡片样式
            defaultCornerRadius = globalSettings.cardCornerRadius.dp,
            defaultElevation = globalSettings.cardSelectedElevation.dp, // 使用选中阴影作为默认阴影
            defaultHorizontalPadding = globalSettings.cardDefaultHorizontalPadding.dp,
            defaultVerticalPadding = globalSettings.cardDefaultVerticalPadding.dp,
            settingsVerticalPadding = globalSettings.cardSettingsVerticalPadding.dp,

            // 间距配置
            itemSpacing = globalSettings.cardItemSpacing.dp,
            sectionSpacing = globalSettings.cardSectionSpacing.dp,

            // 内容间距配置
            contentVerticalSpacing = globalSettings.cardContentVerticalSpacing.dp,
            contentHorizontalSpacing = globalSettings.cardContentHorizontalSpacing.dp,

            // 选择状态样式
            selectedElevation = globalSettings.cardSelectedElevation.dp,
            selectedBorderWidth = globalSettings.cardSelectedBorderWidth.dp
        )
    }

    /**
     * 更新卡片圆角大小
     */
    fun updateCornerRadius(radius: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardCornerRadius = radius)
        settingsRepository.saveGlobalSettings(newSettings)
    }



    /**
     * 更新卡片默认水平内边距
     */
    fun updateDefaultHorizontalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardDefaultHorizontalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片默认垂直内边距（内容卡片专用）
     */
    fun updateDefaultVerticalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardDefaultVerticalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新设置界面卡片垂直内边距
     */
    fun updateSettingsVerticalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardSettingsVerticalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }



    /**
     * 更新卡片项目间距
     */
    fun updateItemSpacing(spacing: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardItemSpacing = spacing)
        settingsRepository.saveGlobalSettings(newSettings)
    }


    /**
     * 更新卡片区域间距
     */
    fun updateSectionSpacing(spacing: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardSectionSpacing = spacing)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片内容垂直间距
     */
    fun updateContentVerticalSpacing(spacing: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardContentVerticalSpacing = spacing)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片内容水平间距
     */
    fun updateContentHorizontalSpacing(spacing: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardContentHorizontalSpacing = spacing)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片选中阴影
     */
    fun updateSelectedElevation(elevation: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardSelectedElevation = elevation)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片选中边框宽度
     */
    fun updateSelectedBorderWidth(width: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardSelectedBorderWidth = width)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片标题字体大小
     */
    fun updateTitleFontSize(fontSize: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardTitleFontSize = fontSize)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片标题字重
     */
    fun updateTitleFontWeight(fontWeight: String) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardTitleFontWeight = fontWeight)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片内容字体大小
     */
    fun updateContentFontSize(fontSize: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardContentFontSize = fontSize)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新卡片描述字重
     */
    fun updateContentFontWeight(fontWeight: String) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardContentFontWeight = fontWeight)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新快捷指令卡片图标大小
     */
    fun updateIconSize(iconSize: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(cardIconSize = iconSize)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 批量更新卡片样式配置
     *
     * 用于一次性更新多个配置项，避免多次触发设置保存
     */
    fun updateCardStyleConfiguration(
        cornerRadius: Int? = null,
        defaultHorizontalPadding: Int? = null,
        defaultVerticalPadding: Int? = null,
        itemSpacing: Int? = null,
        sectionSpacing: Int? = null,
        contentVerticalSpacing: Int? = null,
        contentHorizontalSpacing: Int? = null,
        selectedElevation: Int? = null,
        selectedBorderWidth: Int? = null
    ) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            cardCornerRadius = cornerRadius ?: currentSettings.cardCornerRadius,
            cardDefaultHorizontalPadding = defaultHorizontalPadding ?: currentSettings.cardDefaultHorizontalPadding,
            cardDefaultVerticalPadding = defaultVerticalPadding ?: currentSettings.cardDefaultVerticalPadding,
            cardItemSpacing = itemSpacing ?: currentSettings.cardItemSpacing,
            cardSectionSpacing = sectionSpacing ?: currentSettings.cardSectionSpacing,
            cardContentVerticalSpacing = contentVerticalSpacing ?: currentSettings.cardContentVerticalSpacing,
            cardContentHorizontalSpacing = contentHorizontalSpacing ?: currentSettings.cardContentHorizontalSpacing,
            cardSelectedElevation = selectedElevation ?: currentSettings.cardSelectedElevation,
            cardSelectedBorderWidth = selectedBorderWidth ?: currentSettings.cardSelectedBorderWidth
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 重置卡片样式配置为默认值
     */
    fun resetToDefaults() {
        updateCardStyleConfiguration(
            cornerRadius = 20,
            defaultHorizontalPadding = 13,
            defaultVerticalPadding = 14,
            itemSpacing = 8,
            sectionSpacing = 20,
            contentVerticalSpacing = 0,
            contentHorizontalSpacing = 12,
            selectedElevation = 0,
            selectedBorderWidth = 3
        )
        // 重置字体大小、字重和图标大小
        updateTitleFontSize(15)
        updateTitleFontWeight("medium")
        updateContentFontSize(13)
        updateContentFontWeight("medium")
        updateIconSize(48)
    }
}

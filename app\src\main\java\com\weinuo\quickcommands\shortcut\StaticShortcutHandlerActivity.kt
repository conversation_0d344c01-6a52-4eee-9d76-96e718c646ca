package com.weinuo.quickcommands.shortcut

import android.content.Context
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.QuickCommandRepository
import com.weinuo.quickcommands.execution.SharedExecutionHandler

/**
 * 静态快捷方式处理活动
 *
 * 当用户点击静态快捷方式时启动，根据快捷方式ID执行对应的一键指令
 * 如果快捷方式尚未配置，则显示提示信息
 */
class StaticShortcutHandlerActivity : ComponentActivity() {

    private lateinit var quickCommandRepository: QuickCommandRepository
    private lateinit var shortcutManager: ShortcutManager
    private lateinit var executionHandler: SharedExecutionHandler

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        quickCommandRepository = QuickCommandRepository.getInstance(applicationContext)
        shortcutManager = ShortcutManager(applicationContext)
        executionHandler = SharedExecutionHandler(applicationContext)

        // 获取快捷方式ID
        val shortcutId = intent.getStringExtra(EXTRA_SHORTCUT_ID)

        if (shortcutId != null) {
            handleStaticShortcut(shortcutId)
        } else {
            Toast.makeText(this, "无效的快捷方式", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    /**
     * 处理静态快捷方式
     *
     * 根据快捷方式ID获取关联的指令并执行
     * 支持一键指令和条件指令两种类型
     * 如果快捷方式尚未配置，则显示提示信息
     */
    private fun handleStaticShortcut(shortcutId: String) {
        // 从快捷方式ID中提取槽位索引
        val slotIndex = when (shortcutId) {
            SHORTCUT_ID_1 -> 0
            SHORTCUT_ID_2 -> 1
            SHORTCUT_ID_3 -> 2
            SHORTCUT_ID_4 -> 3
            else -> -1
        }

        if (slotIndex == -1) {
            Toast.makeText(this, "无效的快捷方式ID", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        val prefs = getSharedPreferences(ShortcutManager.PREFS_NAME, Context.MODE_PRIVATE)

        // 获取快捷指令ID
        val quickCommandId = prefs.getString(ShortcutManager.PREF_STATIC_SHORTCUT_PREFIX + slotIndex, null)

        if (quickCommandId != null) {
            // 执行快捷指令
            executeQuickCommand(quickCommandId)
        } else {
            // 快捷方式尚未配置
            Toast.makeText(
                this,
                getString(R.string.shortcut_not_configured),
                Toast.LENGTH_LONG
            ).show()
            finish()
        }
    }

    /**
     * 执行快捷指令
     */
    private fun executeQuickCommand(commandId: String) {
        // 从内存中的数据流获取快捷指令
        val command = quickCommandRepository.quickCommands.value.find { it.id == commandId }

        if (command != null) {
            // 显示提示信息
            Toast.makeText(
                this,
                getString(R.string.executing_quick_command, command.name, command.tasks.size),
                Toast.LENGTH_LONG
            ).show()

            // 使用共享执行处理器手动执行快捷指令（跳过触发条件检查）
            executionHandler.executeQuickCommandManually(
                command = command,
                onExecutionCompleted = {
                    // 所有任务执行完成时的回调
                    runOnUiThread {
                        Toast.makeText(
                            this,
                            getString(R.string.quick_command_completed, command.name),
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    }
                },
                onExecutionAborted = { abortConditions ->
                    // 执行被中止时的回调
                    val conditionNames = abortConditions.joinToString(", ") { it.displayName }
                    runOnUiThread {
                        Toast.makeText(
                            this,
                            getString(R.string.quick_command_aborted, command.name, conditionNames),
                            Toast.LENGTH_LONG
                        ).show()
                        finish()
                    }
                }
            )
        } else {
            Toast.makeText(this, getString(R.string.quick_command_not_found), Toast.LENGTH_SHORT).show()
            finish()
        }
    }



    companion object {
        const val EXTRA_SHORTCUT_ID = "shortcut_id"

        // 静态快捷方式ID
        const val SHORTCUT_ID_1 = "static_shortcut_1"
        const val SHORTCUT_ID_2 = "static_shortcut_2"
        const val SHORTCUT_ID_3 = "static_shortcut_3"
        const val SHORTCUT_ID_4 = "static_shortcut_4"
    }
}

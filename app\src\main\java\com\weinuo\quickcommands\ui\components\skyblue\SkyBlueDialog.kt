package com.weinuo.quickcommands.ui.components.skyblue

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.effects.rememberHazeManager
import com.weinuo.quickcommands.ui.effects.backgroundBlurEffect
import com.weinuo.quickcommands.ui.theme.config.BlurComponent
import com.weinuo.quickcommands.ui.theme.manager.BlurConfigurationManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueDialogConfigManager
import com.weinuo.quickcommands.ui.theme.manager.DialogSpacingConfigurationManager
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource

/**
 * 键盘高度检测工具函数
 *
 * 使用WindowInsets.ime来检测键盘的显示状态和高度
 * 当键盘显示时返回键盘高度，隐藏时返回0dp
 *
 * @return 当前键盘高度
 */
@Composable
private fun rememberKeyboardHeight(): Dp {
    val keyboardHeight = WindowInsets.ime.asPaddingValues().calculateBottomPadding()
    return keyboardHeight
}

/**
 * 天空蓝主题通用对话框组件
 *
 * 完全自定义实现，不依赖Material Design 3
 * 特点：
 * - 圆角大小可通过全局设置控制
 * - 模糊效果可通过全局设置的"对话框模糊效果"控制
 * - 使用Haze库实现iOS风格的背景模糊效果
 * - 支持滚动内容
 * - 完全符合天空蓝主题的设计风格
 *
 * @param onDismissRequest 对话框关闭回调
 * @param settingsRepository 设置仓库，用于读取全局配置
 * @param confirmButton 确认按钮
 * @param modifier 修饰符
 * @param dismissButton 取消按钮
 * @param icon 图标
 * @param title 标题
 * @param text 内容
 * @param containerColor 容器颜色
 * @param iconContentColor 图标内容颜色
 * @param titleContentColor 标题内容颜色
 * @param textContentColor 文本内容颜色
 * @param properties 对话框属性
 * @param maxHeight 最大高度，超出时启用滚动
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Composable
fun SkyBlueDialog(
    onDismissRequest: () -> Unit,
    settingsRepository: SettingsRepository,
    confirmButton: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    dismissButton: @Composable (() -> Unit)? = null,
    icon: @Composable (() -> Unit)? = null,
    title: @Composable (() -> Unit)? = null,
    text: @Composable (() -> Unit)? = null,
    containerColor: Color = Color(0xFFF9F9F9),
    iconContentColor: Color = Color(0xFF0A59F7),
    titleContentColor: Color = Color(0xE5000000),
    textContentColor: Color = Color(0x99000000),
    properties: DialogProperties = DialogProperties(),
    maxHeight: Dp = 400.dp
) {
    val context = LocalContext.current
    val hazeManager = rememberHazeManager(context)
    val dialogConfigManager = remember { SkyBlueDialogConfigManager(settingsRepository) }
    val dialogConfig = dialogConfigManager.getDynamicDialogStyleConfig()
    val spacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val spacingConfig = spacingConfigManager.getDialogSpacingConfiguration()
    val blurConfigManager = remember { BlurConfigurationManager.getInstance(context) }

    // 键盘避让逻辑
    val keyboardHeight = rememberKeyboardHeight()
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp

    // 计算对话框偏移量，当键盘显示时向上移动
    val dialogOffset by animateDpAsState(
        targetValue = if (keyboardHeight > 0.dp) {
            // 向上移动键盘高度的1/3，但不超过屏幕高度的25%
            val maxOffset = screenHeight * 0.25f
            val calculatedOffset = keyboardHeight / 3
            -minOf(calculatedOffset, maxOffset)
        } else {
            0.dp
        },
        label = "dialogOffset"
    )

    Dialog(
        onDismissRequest = onDismissRequest,
        properties = properties
    ) {
        // 背景内容源（用于模糊）
        Box(
            modifier = Modifier
                .fillMaxSize()
                .hazeSource(hazeManager.globalHazeState)
        )

        // 对话框容器 - 添加键盘避让偏移
        Box(
            modifier = Modifier
                .fillMaxSize()
                .offset(y = dialogOffset),
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = modifier
                    .sizeIn(minWidth = 280.dp, maxWidth = 560.dp)
                    .clip(dialogConfig.shape)
                    .backgroundBlurEffect(
                        hazeState = hazeManager.globalHazeState,
                        style = if (dialogConfig.blurEnabled) {
                            blurConfigManager.getHazeMaterial(BlurComponent.DIALOG)
                        } else {
                            null
                        },
                        backgroundColor = containerColor,
                        component = BlurComponent.DIALOG
                    )
                    .padding(spacingConfig.outerPadding.dp)
            ) {
                Column {
                    // 图标
                    icon?.let { iconContent ->
                        Box(
                            modifier = Modifier
                                .padding(bottom = spacingConfig.iconBottomPadding.dp)
                                .align(Alignment.CenterHorizontally)
                        ) {
                            CompositionLocalProvider(LocalContentColor provides iconContentColor) {
                                iconContent()
                            }
                        }
                    }

                    // 标题 - 始终居中显示
                    title?.let { titleContent ->
                        Box(
                            modifier = Modifier
                                .padding(bottom = if (text != null) spacingConfig.titleBottomPadding.dp else 0.dp)
                                .align(Alignment.CenterHorizontally)
                        ) {
                            CompositionLocalProvider(LocalContentColor provides titleContentColor) {
                                ProvideTextStyle(
                                    androidx.compose.ui.text.TextStyle(
                                        fontSize = spacingConfig.titleFontSize.sp,
                                        fontWeight = FontWeight.SemiBold,
                                        color = titleContentColor,
                                        textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                    )
                                ) {
                                    titleContent()
                                }
                            }
                        }
                    }

                    // 可滚动的内容区域
                    text?.let { textContent ->
                        Column(
                            modifier = Modifier
                                .weight(weight = 1f, fill = false)
                                .heightIn(max = maxHeight)
                                .padding(bottom = spacingConfig.contentBottomPadding.dp)
                                .verticalScroll(rememberScrollState())
                        ) {
                            CompositionLocalProvider(LocalContentColor provides textContentColor) {
                                ProvideTextStyle(
                                    androidx.compose.ui.text.TextStyle(
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.Normal,
                                        color = textContentColor,
                                        lineHeight = 20.sp
                                    )
                                ) {
                                    textContent()
                                }
                            }
                        }
                    }

                    // 按钮区域 - 对称布局
                    if (dismissButton != null) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 取消按钮区域
                            Box(
                                modifier = Modifier.weight(1f),
                                contentAlignment = Alignment.Center
                            ) {
                                dismissButton()
                            }

                            // 分割线 - 与按钮padding对称，确保视觉对齐
                            Box(
                                modifier = Modifier
                                    .width(0.5.dp)
                                    .padding(
                                        top = spacingConfig.buttonTopPadding.dp,
                                        bottom = spacingConfig.buttonBottomPadding.dp
                                    )
                                    .height(24.dp) // 比文字高度稍高，更美观
                                    .background(Color(0xFFE0E0E0))
                            )

                            // 确认按钮区域
                            Box(
                                modifier = Modifier.weight(1f),
                                contentAlignment = Alignment.Center
                            ) {
                                confirmButton()
                            }
                        }
                    } else {
                        // 只有确认按钮时居中显示
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            confirmButton()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 自定义对话框按钮组件
 * 不依赖Material Design 3
 * 按照iOS风格设计：蓝色文字，无背景
 */
@Composable
fun SkyBlueDialogButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isPrimary: Boolean = false,
    enabled: Boolean = true,
    isDestructive: Boolean = false,
    settingsRepository: SettingsRepository
) {
    val textColor = when {
        !enabled -> Color(0xFF0A59F7).copy(alpha = 0.38f)
        isDestructive -> Color(0xFFFF3B30) // 红色用于危险操作
        else -> Color(0xFF0A59F7) // 默认蓝色
    }

    // 获取间距配置
    val context = LocalContext.current
    val spacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val spacingConfig = spacingConfigManager.getDialogSpacingConfiguration()

    Box(
        modifier = modifier
            .clickable(enabled = enabled) { onClick() }
            .padding(
                start = 16.dp,
                top = spacingConfig.buttonTopPadding.dp,
                end = 16.dp,
                bottom = spacingConfig.buttonBottomPadding.dp
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = textColor,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * iOS风格的数字输入框
 * 只显示数字和下方分割线
 */
@Composable
fun IOSStyleNumberInput(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "",
    enabled: Boolean = true,
    settingsRepository: SettingsRepository
) {
    val focusRequester = remember { FocusRequester() }

    Column(
        modifier = modifier
    ) {
        // 输入框区域，包含清除按钮
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            BasicTextField(
            value = value,
            onValueChange = { newValue ->
                // 只允许数字输入
                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                    onValueChange(newValue)
                }
            },
            modifier = Modifier
                .focusRequester(focusRequester)
                .padding(vertical = 8.dp),
            textStyle = androidx.compose.ui.text.TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                textAlign = TextAlign.Start
            ),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number
            ),
            singleLine = true,
            enabled = enabled,
            cursorBrush = SolidColor(Color(0xFF0A59F7)),
            decorationBox = { innerTextField ->
                Box(
                    contentAlignment = Alignment.CenterStart,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (value.isEmpty() && placeholder.isNotEmpty()) {
                        Text(
                            text = placeholder,
                            color = Color.Gray,
                            fontSize = 16.sp,
                            textAlign = TextAlign.Start
                        )
                    }
                    innerTextField()
                }
            }
        )

        // 清除按钮 - 半透明圆圈背景，带黑色叉号，与数字垂直居中对齐
        if (value.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 4.dp)
                    .size(20.dp)
                    .background(
                        color = Color.Black.copy(alpha = 0.05f),
                        shape = CircleShape
                    )
                    .clickable { onValueChange("") },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "清除",
                    tint = Color.Black,
                    modifier = Modifier.size(14.dp)
                )
            }
        }
    }

        // 分割线 - 使用对话框专用的分割线水平间距
        val context = LocalContext.current
        val spacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
        val spacingConfig = spacingConfigManager.getDialogSpacingConfiguration()
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = spacingConfig.dividerHorizontalPadding.dp)
                .height(1.dp)
                .background(Color(0xFFE0E0E0))
        )
    }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
}

/**
 * iOS风格的文本输入框
 * 支持任意文本输入，与IOSStyleNumberInput样式一致
 */
@Composable
fun IOSStyleTextInput(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "",
    enabled: Boolean = true,
    settingsRepository: SettingsRepository
) {
    val focusRequester = remember { FocusRequester() }

    Column(
        modifier = modifier
    ) {
        // 输入框区域，包含清除按钮
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            BasicTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier
                .focusRequester(focusRequester)
                .padding(vertical = 8.dp),
            textStyle = androidx.compose.ui.text.TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                textAlign = TextAlign.Start
            ),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Text
            ),
            singleLine = true,
            enabled = enabled,
            cursorBrush = SolidColor(Color(0xFF0A59F7)),
            decorationBox = { innerTextField ->
                Box(
                    contentAlignment = Alignment.CenterStart,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (value.isEmpty() && placeholder.isNotEmpty()) {
                        Text(
                            text = placeholder,
                            color = Color.Gray,
                            fontSize = 16.sp,
                            textAlign = TextAlign.Start
                        )
                    }
                    innerTextField()
                }
            }
        )

        // 清除按钮 - 半透明圆圈背景，带黑色叉号，与文本垂直居中对齐
        if (value.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 4.dp)
                    .size(20.dp)
                    .background(
                        color = Color.Black.copy(alpha = 0.05f),
                        shape = CircleShape
                    )
                    .clickable { onValueChange("") },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "清除",
                    tint = Color.Black,
                    modifier = Modifier.size(14.dp)
                )
            }
        }
    }

        // 分割线 - 使用对话框专用的分割线水平间距
        val context = LocalContext.current
        val spacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
        val spacingConfig = spacingConfigManager.getDialogSpacingConfiguration()
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = spacingConfig.dividerHorizontalPadding.dp)
                .height(1.dp)
                .background(Color(0xFFE0E0E0))
        )
    }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
}

/**
 * 天空蓝主题对话框组件（简化版本）
 * 直接接受字符串参数，适用于简单的文本对话框
 *
 * @param onDismissRequest 对话框关闭回调
 * @param settingsRepository 设置仓库
 * @param title 标题文本
 * @param message 消息文本
 * @param confirmText 确认按钮文本
 * @param onConfirm 确认回调
 * @param dismissText 取消按钮文本（可选）
 * @param onDismiss 取消回调（可选）
 * @param maxHeight 最大高度，超出时启用滚动
 */
@Composable
fun SkyBlueDialog(
    onDismissRequest: () -> Unit,
    settingsRepository: SettingsRepository,
    title: String,
    message: String,
    confirmText: String,
    onConfirm: () -> Unit,
    dismissText: String? = null,
    onDismiss: (() -> Unit)? = null,
    maxHeight: Dp = 400.dp
) {
    SkyBlueDialog(
        onDismissRequest = onDismissRequest,
        settingsRepository = settingsRepository,
        title = { Text(text = title) },
        text = { Text(text = message) },
        confirmButton = {
            SkyBlueDialogButton(
                text = confirmText,
                onClick = onConfirm,
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = if (dismissText != null && onDismiss != null) {
            {
                SkyBlueDialogButton(
                    text = dismissText,
                    onClick = onDismiss,
                    isPrimary = false,
                    settingsRepository = settingsRepository
                )
            }
        } else null,
        maxHeight = maxHeight
    )
}

/**
 * 天空蓝主题选择对话框
 * 用于显示选项列表，支持滚动和模糊效果
 * 完全不依赖Material Design 3
 *
 * @param onDismissRequest 对话框关闭回调
 * @param settingsRepository 设置仓库
 * @param title 标题
 * @param description 可选的描述文字，显示在标题下方
 * @param items 选项列表
 * @param onItemSelected 选项选择回调
 * @param itemContent 选项内容渲染函数
 * @param maxHeight 最大高度，超出时启用滚动
 */
@Composable
fun <T> SkyBlueSelectionDialog(
    onDismissRequest: () -> Unit,
    settingsRepository: SettingsRepository,
    title: String,
    description: String? = null,
    items: List<T>,
    onItemSelected: (T) -> Unit,
    itemContent: @Composable (T) -> Unit,
    maxHeight: Dp = 400.dp
) {
    SkyBlueDialog(
        onDismissRequest = onDismissRequest,
        settingsRepository = settingsRepository,
        title = { Text(text = title) },
        text = {
            Column {
                // 添加描述文字（如果提供）
                description?.let { desc ->
                    Text(
                        text = desc,
                        color = Color(0x99000000),
                        fontSize = 14.sp,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                }

                // 使用Column来正确渲染Composable列表
                items.forEach { item ->
                    itemContent(item)
                }
            }
        },
        confirmButton = {
            // 空的确认按钮，因为选择对话框通过点击项目来确认
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismissRequest,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        },
        maxHeight = maxHeight
    )
}

package com.weinuo.quickcommands.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 相机权限工具类
 * 管理相机相关权限的检查、申请和引导
 * 适用于拍照、录像、访问照片和视频等功能
 */
object CameraPermissionUtil {
    
    /**
     * 相机权限
     */
    private const val CAMERA_PERMISSION = Manifest.permission.CAMERA
    
    /**
     * 录音权限（录像时需要）
     */
    private const val RECORD_AUDIO_PERMISSION = Manifest.permission.RECORD_AUDIO
    
    /**
     * 读取外部存储权限（访问照片和视频需要）
     */
    private const val READ_EXTERNAL_STORAGE_PERMISSION = Manifest.permission.READ_EXTERNAL_STORAGE
    
    /**
     * 写入外部存储权限（保存照片和视频需要）
     */
    private const val WRITE_EXTERNAL_STORAGE_PERMISSION = Manifest.permission.WRITE_EXTERNAL_STORAGE
    
    /**
     * 相机相关权限列表（拍照）
     */
    private val CAMERA_PERMISSIONS = arrayOf(
        CAMERA_PERMISSION,
        WRITE_EXTERNAL_STORAGE_PERMISSION
    )
    
    /**
     * 录像相关权限列表（录像）
     */
    private val VIDEO_RECORDING_PERMISSIONS = arrayOf(
        CAMERA_PERMISSION,
        RECORD_AUDIO_PERMISSION,
        WRITE_EXTERNAL_STORAGE_PERMISSION
    )
    
    /**
     * 访问照片和视频权限列表
     */
    private val MEDIA_ACCESS_PERMISSIONS = arrayOf(
        READ_EXTERNAL_STORAGE_PERMISSION
    )
    
    /**
     * 检查是否拥有相机权限
     * @param context 上下文
     * @return 是否拥有相机权限
     */
    fun hasCameraPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            CAMERA_PERMISSION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否拥有录音权限
     * @param context 上下文
     * @return 是否拥有录音权限
     */
    fun hasRecordAudioPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            RECORD_AUDIO_PERMISSION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否拥有存储权限
     * @param context 上下文
     * @return 是否拥有存储权限
     */
    fun hasStoragePermission(context: Context): Boolean {
        return StoragePermissionUtil.hasStoragePermission(context)
    }
    
    /**
     * 检查是否拥有拍照所需的权限
     * @param context 上下文
     * @return 是否拥有拍照权限
     */
    fun hasPhotoPermissions(context: Context): Boolean {
        return hasCameraPermission(context) && hasStoragePermission(context)
    }
    
    /**
     * 检查是否拥有录像所需的权限
     * @param context 上下文
     * @return 是否拥有录像权限
     */
    fun hasVideoPermissions(context: Context): Boolean {
        return hasCameraPermission(context) && 
               hasRecordAudioPermission(context) && 
               hasStoragePermission(context)
    }
    
    /**
     * 检查是否拥有访问照片和视频的权限
     * @param context 上下文
     * @return 是否拥有访问权限
     */
    fun hasMediaAccessPermissions(context: Context): Boolean {
        return hasStoragePermission(context)
    }
    
    /**
     * 检查是否拥有所有相机相关权限
     * @param context 上下文
     * @return 是否拥有所有权限
     */
    fun hasAllCameraPermissions(context: Context): Boolean {
        return hasVideoPermissions(context) && hasMediaAccessPermissions(context)
    }
    
    /**
     * 申请拍照权限
     * @param launcher 权限申请启动器
     */
    fun requestPhotoPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(CAMERA_PERMISSIONS)
    }
    
    /**
     * 申请录像权限
     * @param launcher 权限申请启动器
     */
    fun requestVideoPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(VIDEO_RECORDING_PERMISSIONS)
    }
    
    /**
     * 申请访问照片和视频权限
     * @param launcher 权限申请启动器
     */
    fun requestMediaAccessPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(MEDIA_ACCESS_PERMISSIONS)
    }
    
    /**
     * 申请所有相机相关权限
     * @param launcher 权限申请启动器
     */
    fun requestAllCameraPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        val allPermissions = (VIDEO_RECORDING_PERMISSIONS + MEDIA_ACCESS_PERMISSIONS).distinct().toTypedArray()
        launcher.launch(allPermissions)
    }
    
    /**
     * 打开应用权限设置页面
     * @param context 上下文
     */
    fun openAppPermissionSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${context.packageName}")
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开应用设置页面，则打开通用设置页面
            try {
                val intent = Intent(Settings.ACTION_SETTINGS).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } catch (e2: Exception) {
                // 忽略错误
            }
        }
    }
    
    /**
     * 获取需要的相机权限列表
     * @return 相机权限数组
     */
    fun getRequiredCameraPermissions(): Array<String> {
        return VIDEO_RECORDING_PERMISSIONS
    }
    
    /**
     * 相机权限被拒绝后的说明对话框
     */
    @Composable
    fun CameraPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "相机权限被拒绝",
            message = """
                相机功能需要相机权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动授予相机权限：
                • 拍照功能
                • 录像功能

                您可以点击"打开设置"直接跳转到应用权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }
}

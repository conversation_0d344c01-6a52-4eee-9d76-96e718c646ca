package com.weinuo.quickcommands.data

import android.app.AppOpsManager

import android.app.usage.UsageStatsManager
import android.content.Context
import android.content.Intent

import android.content.pm.ApplicationInfo
import android.os.Build
import android.os.Process
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.AppInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * 应用数据仓库，负责获取和管理应用列表
 */
class AppRepository(private val context: Context) {
    private val _userApps = MutableStateFlow<List<AppInfo>>(emptyList())
    private val _systemApps = MutableStateFlow<List<AppInfo>>(emptyList())

    val userApps: Flow<List<AppInfo>> = _userApps.asStateFlow()
    val systemApps: Flow<List<AppInfo>> = _systemApps.asStateFlow()

    private val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager

    /**
     * 加载所有应用
     */
    suspend fun loadApps() {
        withContext(Dispatchers.IO) {
            try {
                val packageManager = context.packageManager
                val installedPackages = packageManager.getInstalledPackages(0)

                val userAppsList = mutableListOf<AppInfo>()
                val systemAppsList = mutableListOf<AppInfo>()

                for (packageInfo in installedPackages) {
                    val applicationInfo = packageInfo.applicationInfo ?: continue
                    val isSystemApp = applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM != 0
                    val packageName = packageInfo.packageName
                    // 安全加载应用图标，避免OOM
                    val icon = applicationInfo.loadIcon(packageManager)

                    val appInfo = AppInfo(
                        packageName = packageName,
                        appName = applicationInfo.loadLabel(packageManager).toString(),
                        icon = icon, // 图标会在UI层使用ImageUtils处理
                        isSystemApp = isSystemApp,
                        isRunning = isAppRunning(packageName)
                    )

                    if (isSystemApp) {
                        systemAppsList.add(appInfo)
                    } else {
                        userAppsList.add(appInfo)
                    }
                }

                // 按运行状态和名称排序
                val sortedUserApps = userAppsList.sortedWith(
                    compareByDescending<AppInfo> { it.isRunning }
                        .thenBy { it.appName }
                )

                val sortedSystemApps = systemAppsList.sortedWith(
                    compareByDescending<AppInfo> { it.isRunning }
                        .thenBy { it.appName }
                )

                _userApps.value = sortedUserApps
                _systemApps.value = sortedSystemApps
            } catch (e: Exception) {
                Log.e("AppRepository", "Error loading apps", e)
            }
        }
    }

    /**
     * 更新应用运行状态
     */
    suspend fun updateRunningStatus() {
        withContext(Dispatchers.IO) {
            val userAppsList = _userApps.value.toMutableList()
            val systemAppsList = _systemApps.value.toMutableList()

            // 更新用户应用运行状态
            for (i in userAppsList.indices) {
                userAppsList[i] = userAppsList[i].copy(
                    isRunning = isAppRunning(userAppsList[i].packageName)
                )
            }

            // 更新系统应用运行状态
            for (i in systemAppsList.indices) {
                systemAppsList[i] = systemAppsList[i].copy(
                    isRunning = isAppRunning(systemAppsList[i].packageName)
                )
            }

            // 重新排序
            val sortedUserApps = userAppsList.sortedWith(
                compareByDescending<AppInfo> { it.isRunning }
                    .thenBy { it.appName }
            )

            val sortedSystemApps = systemAppsList.sortedWith(
                compareByDescending<AppInfo> { it.isRunning }
                    .thenBy { it.appName }
            )

            _userApps.value = sortedUserApps
            _systemApps.value = sortedSystemApps
        }
    }

    // 缓存使用统计数据，避免重复查询
    private var cachedStats: List<android.app.usage.UsageStats>? = null
    private var cachedStatsTimestamp: Long = 0
    private val CACHE_VALID_TIME = 5000L // 缓存有效期5秒

    /**
     * 检查应用是否正在运行
     * 仅使用UsageStatsManager的queryUsageStats方法，提高检测速度
     */
    private fun isAppRunning(packageName: String): Boolean {
        // 使用UsageStatsManager的queryUsageStats
        if (hasUsageStatsPermission()) {
            try {
                val currentTime = System.currentTimeMillis()

                // 使用缓存的统计数据，如果缓存有效
                val stats = if (currentTime - cachedStatsTimestamp < CACHE_VALID_TIME && cachedStats != null) {
                    cachedStats!!
                } else {
                    val endTime = currentTime
                    val startTime = endTime - 30 * 60 * 1000 // 30分钟前

                    // 获取使用统计
                    val newStats = usageStatsManager.queryUsageStats(
                        UsageStatsManager.INTERVAL_DAILY,
                        startTime,
                        endTime
                    )

                    // 更新缓存
                    cachedStats = newStats
                    cachedStatsTimestamp = currentTime

                    newStats
                }

                // 检查应用是否在最近使用的列表中
                val recentTimeThreshold = currentTime - 15 * 60 * 1000 // 15分钟内认为是最近使用
                val appStat = stats.find { it.packageName == packageName }

                // 如果应用在最近使用过，认为它正在运行
                if (appStat != null && appStat.lastTimeUsed >= recentTimeThreshold) {
                    return true
                }

                // 如果应用今天有被使用过，也认为它可能在运行
                if (appStat != null && appStat.totalTimeInForeground > 0) {
                    return true
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking if app is running with UsageStats", e)
            }
        }

        // 如果没有权限或检测失败，返回false
        return false
    }

    /**
     * 检查是否有使用情况访问权限
     */
    private fun hasUsageStatsPermission(): Boolean {
        val appOps = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            appOps.unsafeCheckOpNoThrow(
                AppOpsManager.OPSTR_GET_USAGE_STATS,
                Process.myUid(),
                context.packageName
            )
        } else {
            appOps.checkOpNoThrow(
                AppOpsManager.OPSTR_GET_USAGE_STATS,
                Process.myUid(),
                context.packageName
            )
        }
        return mode == AppOpsManager.MODE_ALLOWED
    }

    /**
     * 打开使用情况访问权限设置页面
     */
    fun openUsageStatsSettings() {
        try {
            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening usage stats settings", e)
            Toast.makeText(
                context,
                context.getString(R.string.usage_stats_permission_required),
                Toast.LENGTH_LONG
            ).show()
        }
    }

    companion object {
        private const val TAG = "AppRepository"
    }
}

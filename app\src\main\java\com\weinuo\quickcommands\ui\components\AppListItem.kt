package com.weinuo.quickcommands.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.model.AppInfo
import com.weinuo.quickcommands.ui.theme.RunningContainer
import com.weinuo.quickcommands.ui.theme.OnRunningContainer
import com.weinuo.quickcommands.utils.ImageUtils

/**
 * 应用列表项组件
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun AppListItem(
    appInfo: AppInfo,
    onItemClick: (AppInfo) -> Unit,
    isInSelectionMode: Boolean = false,
    isSelected: Boolean = false,
    onLongClick: () -> Unit = {},
    onSelectionToggle: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .combinedClickable(
                onClick = {
                    if (isInSelectionMode) {
                        onSelectionToggle()
                    } else {
                        onItemClick(appInfo)
                    }
                },
                onLongClick = onLongClick
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择模式下显示复选框
            if (isInSelectionMode) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = { onSelectionToggle() },
                    modifier = Modifier.padding(end = 8.dp)
                )
            }

            // 应用信息区域
            AppInfoSection(
                appInfo = appInfo,
                isInSelectionMode = isInSelectionMode
            )
        }
    }
}

/**
 * 应用信息区域
 */
@Composable
private fun AppInfoSection(
    appInfo: AppInfo,
    isInSelectionMode: Boolean = false
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 应用图标
        Image(
            bitmap = ImageUtils.safeDrawableToBitmap(appInfo.icon, 96, 96).asImageBitmap(),
            contentDescription = appInfo.appName,
            modifier = Modifier.size(48.dp),
            contentScale = ContentScale.Fit
        )

        Spacer(modifier = Modifier.width(16.dp))

        // 应用名称和包名
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 应用名称
            Text(
                text = appInfo.appName,
                style = MaterialTheme.typography.titleMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 包名
            Text(
                text = appInfo.packageName,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 状态指示器区域（仅保留运行状态）
            if (appInfo.isRunning) {
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 正在运行状态
                    StatusChip(
                        text = "运行中",
                        containerColor = RunningContainer,
                        labelColor = OnRunningContainer
                    )
                }
            }
        }
    }
}

/**
 * 状态指示器Chip组件
 */
@Composable
private fun StatusChip(
    text: String,
    containerColor: androidx.compose.ui.graphics.Color,
    labelColor: androidx.compose.ui.graphics.Color
) {
    AssistChip(
        onClick = { /* 只读，无点击操作 */ },
        label = {
            Text(
                text = text,
                // 使用更小的文字样式
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = MaterialTheme.typography.bodySmall.fontSize * 0.85f
                )
            )
        },
        colors = AssistChipDefaults.assistChipColors(
            containerColor = containerColor,
            labelColor = labelColor
        ),
        border = null,
        modifier = Modifier.wrapContentSize()
    )
}

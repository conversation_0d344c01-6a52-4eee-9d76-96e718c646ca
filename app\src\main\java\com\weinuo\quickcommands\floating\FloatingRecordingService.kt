package com.weinuo.quickcommands.floating

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.annotation.RequiresApi
import com.weinuo.quickcommands.model.TouchEvent
import com.weinuo.quickcommands.service.AutoClickerAccessibilityService
import com.weinuo.quickcommands.utils.OverlayPermissionUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 悬浮窗录制服务
 *
 * 负责管理悬浮窗的生命周期，提供手势录制的触发入口。
 * 用户可以通过悬浮窗开始/停止录制，无需离开当前应用。
 */
class FloatingRecordingService : Service() {

    companion object {
        private const val TAG = "FloatingRecordingService"

        // Intent动作常量
        private const val ACTION_START_FLOATING = "action_start_floating"
        private const val ACTION_STOP_FLOATING = "action_stop_floating"
        private const val ACTION_START_RECORDING = "action_start_recording"
        private const val ACTION_STOP_RECORDING = "action_stop_recording"

        // Intent额外数据
        private const val EXTRA_COMMAND_ID = "command_id"

        /**
         * 启动悬浮窗服务
         */
        fun startFloatingWindow(context: Context) {
            val intent = Intent(context, FloatingRecordingService::class.java).apply {
                action = ACTION_START_FLOATING
            }
            context.startService(intent)
        }

        /**
         * 停止悬浮窗服务
         */
        fun stopFloatingWindow(context: Context) {
            val intent = Intent(context, FloatingRecordingService::class.java).apply {
                action = ACTION_STOP_FLOATING
            }
            context.startService(intent)
        }

        /**
         * 开始录制
         */
        fun startRecording(context: Context, commandId: String) {
            val intent = Intent(context, FloatingRecordingService::class.java).apply {
                action = ACTION_START_RECORDING
                putExtra(EXTRA_COMMAND_ID, commandId)
            }
            context.startService(intent)
        }

        /**
         * 停止录制
         */
        fun stopRecording(context: Context) {
            val intent = Intent(context, FloatingRecordingService::class.java).apply {
                action = ACTION_STOP_RECORDING
            }
            context.startService(intent)
        }
    }

    private var floatingButton: FloatingRecordingButton? = null
    private var recordingOverlay: RecordingOverlay? = null
    private var gestureForwarder: GestureForwarder? = null

    private var currentCommandId: String? = null
    private var isRecording = false
    private var isPaused = false
    private val recordedEvents = mutableListOf<TouchEvent>()
    private var recordingStartTime = 0L

    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "悬浮窗录制服务已创建")

        // 延迟初始化手势转发器，避免在服务启动时立即初始化
        serviceScope.launch {
            delay(1000) // 延迟1秒
            initializeGestureForwarder()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let { handleIntent(it) }
        return START_STICKY
    }

    /**
     * 处理Intent
     */
    private fun handleIntent(intent: Intent) {
        when (intent.action) {
            ACTION_START_FLOATING -> {
                startFloatingButton()
            }

            ACTION_STOP_FLOATING -> {
                stopFloatingButton()
                stopSelf()
            }

            ACTION_START_RECORDING -> {
                val commandId = intent.getStringExtra(EXTRA_COMMAND_ID)
                if (commandId != null) {
                    currentCommandId = commandId
                    startFloatingButton()
                }
            }

            ACTION_STOP_RECORDING -> {
                stopFloatingButton()
            }

            else -> {
                // 默认启动悬浮按钮
                startFloatingButton()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "悬浮窗录制服务已销毁")

        // 清理资源
        stopFloatingButton()
        serviceScope.cancel()

        // 通知FloatingRecordingManager重置状态
        FloatingRecordingManager.resetFloatingWindowState()
    }

    /**
     * 启动悬浮按钮
     */
    private fun startFloatingButton() {
        // 检查悬浮窗权限
        if (!OverlayPermissionUtil.hasOverlayPermission(this)) {
            Log.w(TAG, "没有悬浮窗权限，无法显示悬浮按钮")
            stopSelf()
            return
        }

        // 如果按钮已存在，先移除
        if (floatingButton != null) {
            stopFloatingButton()
        }

        try {
            // 创建并显示悬浮按钮（初始状态为未录制）
            floatingButton = FloatingRecordingButton(
                context = this,
                onStartRecording = { startRecording() },
                onPauseRecording = { pauseRecording() },
                onResumeRecording = { resumeRecording() },
                onSaveRecording = { saveAndStopRecording() }
            )

            if (floatingButton?.show() == true) {
                Log.d(TAG, "悬浮按钮已启动")
            } else {
                Log.e(TAG, "显示悬浮按钮失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动悬浮按钮失败", e)
            stopSelf()
        }
    }

    /**
     * 停止悬浮按钮
     */
    private fun stopFloatingButton() {
        floatingButton?.let { button ->
            try {
                button.hide()
                floatingButton = null
                Log.d(TAG, "悬浮按钮已停止")
            } catch (e: Exception) {
                Log.e(TAG, "停止悬浮按钮失败", e)
            }
        }

        // 同时停止录制覆盖层
        stopRecordingOverlay()

        // 重置状态
        isRecording = false
        isPaused = false
    }

    /**
     * 初始化手势转发器
     */
    private fun initializeGestureForwarder() {
        serviceScope.launch {
            try {
                // 等待AccessibilityService可用
                val accessibilityService = AutoClickerAccessibilityService.getInstance()
                if (accessibilityService != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    gestureForwarder = GestureForwarder(accessibilityService)
                    Log.d(TAG, "手势转发器初始化成功")

                    // 如果录制覆盖层已经显示，更新手势转发器
                    // 注意：这里暂时不需要更新，因为RecordingOverlay在创建时就传入了gestureForwarder
                } else {
                    Log.w(TAG, "AccessibilityService不可用，手势转发功能将被禁用")
                }
            } catch (e: Exception) {
                Log.e(TAG, "初始化手势转发器失败", e)
            }
        }
    }

    /**
     * 开始录制
     */
    private fun startRecording() {
        if (isRecording) {
            Log.w(TAG, "录制已在进行中")
            return
        }

        if (gestureForwarder == null) {
            Log.w(TAG, "手势转发器未初始化，将在初始化完成后启用手势转发功能")
        }

        try {
            // 清空之前的录制数据
            recordedEvents.clear()
            recordingStartTime = System.currentTimeMillis()
            isRecording = true
            isPaused = false

            // 更新悬浮按钮状态
            floatingButton?.setRecordingState(FloatingRecordingButton.RecordingState.RECORDING)

            // 显示透明录制覆盖层
            startRecordingOverlay()

            Log.d(TAG, "录制已开始")
        } catch (e: Exception) {
            Log.e(TAG, "开始录制失败", e)
        }
    }

    /**
     * 暂停录制
     */
    private fun pauseRecording() {
        if (!isRecording || isPaused) {
            Log.w(TAG, "录制未在进行中或已暂停")
            return
        }

        try {
            isPaused = true

            // 隐藏透明录制覆盖层
            stopRecordingOverlay()

            // 更新悬浮按钮状态
            floatingButton?.setRecordingState(FloatingRecordingButton.RecordingState.PAUSED)

            Log.d(TAG, "录制已暂停")
        } catch (e: Exception) {
            Log.e(TAG, "暂停录制失败", e)
        }
    }

    /**
     * 恢复录制
     */
    private fun resumeRecording() {
        if (!isRecording || !isPaused) {
            Log.w(TAG, "录制未在进行中或未暂停")
            return
        }

        try {
            isPaused = false

            // 显示透明录制覆盖层
            startRecordingOverlay()

            // 更新悬浮按钮状态
            floatingButton?.setRecordingState(FloatingRecordingButton.RecordingState.RECORDING)

            Log.d(TAG, "录制已恢复")
        } catch (e: Exception) {
            Log.e(TAG, "恢复录制失败", e)
        }
    }

    /**
     * 保存并停止录制
     */
    private fun saveAndStopRecording() {
        try {
            // 停止录制，回到初始状态
            stopRecording()

            // 异步保存录制数据并在完成后关闭悬浮窗
            serviceScope.launch {
                try {
                    // 保存录制数据
                    saveRecordedData()

                    Log.d(TAG, "录制数据保存完成，准备关闭悬浮窗")

                    // 保存完成后关闭悬浮窗服务
                    stopFloatingButton()
                    stopSelf()

                    Log.d(TAG, "悬浮窗录制服务已自动关闭")
                } catch (e: Exception) {
                    Log.e(TAG, "保存录制数据或关闭悬浮窗失败", e)
                    // 即使保存失败也要关闭悬浮窗
                    stopFloatingButton()
                    stopSelf()
                }
            }

            Log.d(TAG, "录制已停止，正在保存数据...")
        } catch (e: Exception) {
            Log.e(TAG, "保存并停止录制失败", e)
        }
    }

    /**
     * 停止录制
     */
    private fun stopRecording() {
        isRecording = false
        isPaused = false

        // 隐藏透明录制覆盖层
        stopRecordingOverlay()

        // 更新悬浮按钮状态
        floatingButton?.setRecordingState(FloatingRecordingButton.RecordingState.IDLE)
    }



    /**
     * 启动录制覆盖层
     */
    private fun startRecordingOverlay() {
        try {
            recordingOverlay = RecordingOverlay(
                context = this,
                onTouchEvent = { event -> onTouchEventRecorded(event) },
                gestureForwarder = gestureForwarder
            )

            if (recordingOverlay?.show() == true) {
                Log.d(TAG, "录制覆盖层已显示")

                // 确保悬浮按钮在覆盖层之上：重新添加悬浮按钮
                ensureButtonOnTop()
            } else {
                Log.e(TAG, "显示录制覆盖层失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动录制覆盖层失败", e)
        }
    }

    /**
     * 确保悬浮按钮在最顶层
     */
    private fun ensureButtonOnTop() {
        floatingButton?.let { button ->
            try {
                // 先隐藏再显示，确保在最顶层
                button.hide()
                button.show()
                // 恢复当前状态
                when {
                    isPaused -> button.setRecordingState(FloatingRecordingButton.RecordingState.PAUSED)
                    isRecording -> button.setRecordingState(FloatingRecordingButton.RecordingState.RECORDING)
                    else -> button.setRecordingState(FloatingRecordingButton.RecordingState.IDLE)
                }
                Log.d(TAG, "悬浮按钮已置于最顶层")
            } catch (e: Exception) {
                Log.e(TAG, "确保悬浮按钮在最顶层失败", e)
            }
        }
    }

    /**
     * 停止录制覆盖层
     */
    private fun stopRecordingOverlay() {
        recordingOverlay?.let { overlay ->
            try {
                overlay.hide()
                recordingOverlay = null
                Log.d(TAG, "录制覆盖层已隐藏")
            } catch (e: Exception) {
                Log.e(TAG, "停止录制覆盖层失败", e)
            }
        }
    }

    /**
     * 处理录制的触摸事件
     */
    private fun onTouchEventRecorded(event: TouchEvent) {
        if (!isRecording || isPaused) return

        // 直接添加事件
        recordedEvents.add(event)
    }

    /**
     * 保存录制数据
     */
    private suspend fun saveRecordedData() {
        try {
            if (recordedEvents.isNotEmpty()) {
                // 创建手势录制对象
                val recording = com.weinuo.quickcommands.model.GestureRecording(
                    name = "悬浮窗录制_${System.currentTimeMillis()}",
                    description = "通过悬浮窗录制的手势操作",
                    events = recordedEvents.toList(),
                    duration = if (recordedEvents.isNotEmpty()) {
                        System.currentTimeMillis() - recordingStartTime
                    } else 0L,
                    createdTime = System.currentTimeMillis()
                )

                // 使用原生存储管理器保存录制数据
                val gestureRecordingManager = com.weinuo.quickcommands.storage.GestureRecordingNativeManager(this@FloatingRecordingService)
                val recordingId = gestureRecordingManager.saveRecording(recording)

                if (recordingId != null) {
                    Log.d(TAG, "录制数据已保存: $recordingId, ${recordedEvents.size}个事件")

                    // 将录制ID存储到SharedPreferences中，供配置界面获取
                    val sharedPrefs = getSharedPreferences("floating_recording_result", MODE_PRIVATE)
                    sharedPrefs.edit()
                        .putString("latest_recording_id", recordingId)
                        .putLong("recording_timestamp", System.currentTimeMillis())
                        .apply()

                    // 直接启动编辑界面
                    startEditActivity(recordingId)
                } else {
                    Log.e(TAG, "录制数据保存失败")
                    throw Exception("录制数据保存失败")
                }
            } else {
                Log.w(TAG, "没有录制数据需要保存")
                throw Exception("没有录制数据需要保存")
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存录制数据失败", e)
            throw e // 重新抛出异常，让调用方知道保存失败
        }
    }

    /**
     * 启动手势录制编辑界面
     */
    private fun startEditActivity(recordingId: String) {
        try {
            val editIntent = Intent(this, com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity::class.java).apply {
                putExtra(com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity.EXTRA_RECORDING_ID, recordingId)
                putExtra(com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity.EXTRA_RETURN_RESULT, false)
                // 添加必要的标志以从服务启动Activity
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }

            startActivity(editIntent)
            Log.d(TAG, "已启动手势录制编辑界面: $recordingId")
        } catch (e: Exception) {
            Log.e(TAG, "启动手势录制编辑界面失败", e)
            // 如果启动失败，仍然保留原有的SharedPreferences机制作为备用
        }
    }


}

package com.weinuo.quickcommands.floating

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.unit.dp
import kotlin.math.*
import kotlin.random.Random

/**
 * 悬浮加速球拖动动画组件
 *
 * 实现拖动时的动画效果，包括：
 * - 夜空背景
 * - 云朵动画
 * - 水体波动
 * - 鲸鱼游动动画
 */
@Composable
fun FloatingAcceleratorDragAnimation(
    modifier: Modifier = Modifier,
    isVisible: Boolean = false
) {
    // 动画状态
    val animationProgress by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(durationMillis = 500, easing = EaseInOut),
        label = "DragAnimationProgress"
    )

    // 云朵动画
    val cloudOffset by animateFloatAsState(
        targetValue = if (isVisible) 100f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "CloudOffset"
    )

    // 水波动画
    val waveOffset by animateFloatAsState(
        targetValue = if (isVisible) 360f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "WaveOffset"
    )

    // 鲸鱼游动动画
    val whaleOffset by animateFloatAsState(
        targetValue = if (isVisible) 200f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 4000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "WhaleOffset"
    )

    if (animationProgress > 0f) {
        Canvas(
            modifier = modifier
                .fillMaxSize()
                .background(Color.Transparent)
        ) {
            drawDragAnimation(
                progress = animationProgress,
                cloudOffset = cloudOffset,
                waveOffset = waveOffset,
                whaleOffset = whaleOffset,
                size = size
            )
        }
    }
}

/**
 * 绘制拖动动画
 */
private fun DrawScope.drawDragAnimation(
    progress: Float,
    cloudOffset: Float,
    waveOffset: Float,
    whaleOffset: Float,
    size: androidx.compose.ui.geometry.Size
) {
    // 绘制夜空背景
    drawNightSkyBackground(progress, size)
    
    // 绘制云朵
    drawClouds(progress, cloudOffset, size)
    
    // 绘制水体
    drawWaterBody(progress, waveOffset, size)
    
    // 绘制鲸鱼
    drawWhale(progress, whaleOffset, size)
}

/**
 * 绘制夜空背景
 */
private fun DrawScope.drawNightSkyBackground(
    progress: Float,
    size: androidx.compose.ui.geometry.Size
) {
    // 渐变夜空背景
    val nightSkyGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFF0D1B2A).copy(alpha = progress * 0.8f),
            Color(0xFF1B263B).copy(alpha = progress * 0.6f),
            Color(0xFF415A77).copy(alpha = progress * 0.4f)
        ),
        startY = 0f,
        endY = size.height
    )
    
    drawRect(
        brush = nightSkyGradient,
        size = size
    )
    
    // 绘制星星
    repeat(20) { index ->
        val starX = (index * 37 + 50) % size.width.toInt()
        val starY = (index * 23 + 30) % (size.height * 0.6f).toInt()
        val starAlpha = progress * (0.3f + Random.nextFloat() * 0.4f)
        
        drawCircle(
            color = Color.White.copy(alpha = starAlpha),
            radius = 1.5f,
            center = androidx.compose.ui.geometry.Offset(starX.toFloat(), starY.toFloat())
        )
    }
}

/**
 * 绘制云朵
 */
private fun DrawScope.drawClouds(
    progress: Float,
    cloudOffset: Float,
    size: androidx.compose.ui.geometry.Size
) {
    val cloudColor = Color.White.copy(alpha = progress * 0.3f)
    
    // 绘制多个云朵
    repeat(3) { index ->
        val cloudX = (cloudOffset + index * 150) % (size.width + 100)
        val cloudY = size.height * 0.2f + index * 50
        
        drawCloudShape(
            color = cloudColor,
            center = androidx.compose.ui.geometry.Offset(cloudX, cloudY),
            size = 60f + index * 20
        )
    }
}

/**
 * 绘制单个云朵形状
 */
private fun DrawScope.drawCloudShape(
    color: Color,
    center: androidx.compose.ui.geometry.Offset,
    size: Float
) {
    // 简化的云朵形状 - 使用多个圆形组合
    val baseRadius = size / 4
    
    // 主体圆形
    drawCircle(
        color = color,
        radius = baseRadius,
        center = center
    )
    
    // 左侧圆形
    drawCircle(
        color = color,
        radius = baseRadius * 0.8f,
        center = androidx.compose.ui.geometry.Offset(center.x - baseRadius * 0.6f, center.y)
    )
    
    // 右侧圆形
    drawCircle(
        color = color,
        radius = baseRadius * 0.7f,
        center = androidx.compose.ui.geometry.Offset(center.x + baseRadius * 0.7f, center.y)
    )
    
    // 顶部圆形
    drawCircle(
        color = color,
        radius = baseRadius * 0.6f,
        center = androidx.compose.ui.geometry.Offset(center.x, center.y - baseRadius * 0.5f)
    )
}

/**
 * 绘制水体
 */
private fun DrawScope.drawWaterBody(
    progress: Float,
    waveOffset: Float,
    size: androidx.compose.ui.geometry.Size
) {
    val waterStartY = size.height * 0.7f
    val waterColor = Color(0xFF4A90E2).copy(alpha = progress * 0.6f)
    
    // 创建水波路径
    val wavePath = Path().apply {
        moveTo(0f, waterStartY)
        
        // 绘制波浪
        for (x in 0..size.width.toInt() step 10) {
            val waveHeight = sin((x + waveOffset) * 0.02f) * 20f
            lineTo(x.toFloat(), waterStartY + waveHeight)
        }
        
        lineTo(size.width, size.height)
        lineTo(0f, size.height)
        close()
    }
    
    drawPath(
        path = wavePath,
        color = waterColor
    )
}

/**
 * 绘制鲸鱼
 */
private fun DrawScope.drawWhale(
    progress: Float,
    whaleOffset: Float,
    size: androidx.compose.ui.geometry.Size
) {
    val whaleX = size.width * 0.2f + whaleOffset
    val whaleY = size.height * 0.8f
    val whaleColor = Color(0xFF2C3E50).copy(alpha = progress * 0.7f)
    
    // 简化的鲸鱼形状
    val whaleBody = Path().apply {
        // 鲸鱼身体（椭圆形）
        addOval(
            androidx.compose.ui.geometry.Rect(
                left = whaleX - 40,
                top = whaleY - 15,
                right = whaleX + 40,
                bottom = whaleY + 15
            )
        )
    }
    
    drawPath(
        path = whaleBody,
        color = whaleColor
    )
    
    // 鲸鱼尾巴
    val tailPath = Path().apply {
        moveTo(whaleX - 40, whaleY)
        lineTo(whaleX - 60, whaleY - 10)
        lineTo(whaleX - 55, whaleY)
        lineTo(whaleX - 60, whaleY + 10)
        close()
    }
    
    drawPath(
        path = tailPath,
        color = whaleColor
    )
    
    // 鲸鱼眼睛
    drawCircle(
        color = Color.White.copy(alpha = progress * 0.8f),
        radius = 3f,
        center = androidx.compose.ui.geometry.Offset(whaleX + 15, whaleY - 5)
    )
}

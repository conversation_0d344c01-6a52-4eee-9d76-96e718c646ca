package com.weinuo.quickcommands.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.ui.screens.LocalNavController
import com.weinuo.quickcommands.permission.ShellScriptShizukuOperation
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import android.content.pm.PackageManager
import android.util.Log
import com.weinuo.quickcommands.data.SettingsRepository
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.ui.activities.AppSelectionActivity

/**
 * 应用管理任务配置数据提供器
 *
 * 提供应用管理任务特定的配置项列表，为每个操作类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object ApplicationTaskConfigProvider {

    /**
     * 获取应用程序任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 应用程序任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<ApplicationOperation>> {
        return listOf(
            // 启动应用
            ConfigurationCardItem(
                id = "launch_app",
                title = context.getString(R.string.app_launch_app),
                description = context.getString(R.string.app_launch_app_description),
                operationType = ApplicationOperation.LAUNCH_APP,
                permissionRequired = false,
                content = { operation, onComplete ->
                    LaunchAppConfigContent(operation, onComplete)
                }
            ),

            // 强制停止应用
            ConfigurationCardItem(
                id = "force_stop_app",
                title = context.getString(R.string.app_force_stop_app),
                description = context.getString(R.string.app_force_stop_app_description),
                operationType = ApplicationOperation.FORCE_STOP_APP,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ForceStopAppConfigContent(operation, onComplete)
                },
                editableContent = { operation, initialConfig, onComplete ->
                    ForceStopAppConfigContent(operation, onComplete, initialConfig as? ApplicationTask)
                }
            ),





            // 冻结应用
            ConfigurationCardItem(
                id = "freeze_app",
                title = context.getString(R.string.app_freeze_app),
                description = context.getString(R.string.app_freeze_app_description),
                operationType = ApplicationOperation.FREEZE_APP,
                permissionRequired = true,
                content = { operation, onComplete ->
                    FreezeAppConfigContent(operation, onComplete)
                },
                editableContent = { operation, initialConfig, onComplete ->
                    FreezeAppConfigContent(operation, onComplete, initialConfig as? ApplicationTask)
                }
            ),

            // 解冻应用
            ConfigurationCardItem(
                id = "unfreeze_app",
                title = context.getString(R.string.app_unfreeze_app),
                description = context.getString(R.string.app_unfreeze_app_description),
                operationType = ApplicationOperation.UNFREEZE_APP,
                permissionRequired = true,
                content = { operation, onComplete ->
                    UnfreezeAppConfigContent(operation, onComplete)
                },
                editableContent = { operation, initialConfig, onComplete ->
                    UnfreezeAppConfigContent(operation, onComplete, initialConfig as? ApplicationTask)
                }
            ),
            // 打开网站
            ConfigurationCardItem(
                id = "open_website",
                title = context.getString(R.string.app_open_website),
                description = context.getString(R.string.app_open_website_description),
                operationType = ApplicationOperation.OPEN_WEBSITE,
                permissionRequired = false,
                content = { operation, onComplete ->
                    OpenWebsiteConfigContent(operation, onComplete)
                }
            ),

            // 执行JavaScript代码
            ConfigurationCardItem(
                id = "execute_javascript",
                title = context.getString(R.string.app_execute_javascript),
                description = context.getString(R.string.app_execute_javascript_description),
                operationType = ApplicationOperation.EXECUTE_JAVASCRIPT,
                permissionRequired = false,
                content = { operation, onComplete ->
                    ExecuteJavaScriptConfigContent(operation, onComplete)
                }
            ),

            // 执行Shell脚本
            ConfigurationCardItem(
                id = "execute_shell_script",
                title = context.getString(R.string.app_execute_shell_script),
                description = context.getString(R.string.app_execute_shell_script_description),
                operationType = ApplicationOperation.EXECUTE_SHELL_SCRIPT,
                permissionRequired = false, // Shell脚本特殊处理：只有选择Shizuku模式时才检查权限
                content = { operation, onComplete ->
                    ExecuteShellScriptConfigContent(operation, onComplete)
                }
            ),

            // Tasker/Locale插件
            ConfigurationCardItem(
                id = "tasker_locale_plugin",
                title = context.getString(R.string.app_tasker_locale_plugin),
                description = context.getString(R.string.app_tasker_locale_plugin_description),
                operationType = ApplicationOperation.TASKER_LOCALE_PLUGIN,
                permissionRequired = false,
                content = { operation, onComplete ->
                    TaskerLocalePluginConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 启动应用配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun LaunchAppConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit
) {

    var appPackageName by rememberSaveable { mutableStateOf("") }
    var appName by rememberSaveable { mutableStateOf("") }

    val context = LocalContext.current

    // UI状态管理器
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val selectedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (selectedApps.isNotEmpty()) {
                    val selectedApp = selectedApps.first()
                    appPackageName = selectedApp.packageName
                    appName = selectedApp.appName
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "启动应用设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 应用选择
        OutlinedButton(
            onClick = {
                val resultKey = "launch_app_selection_${System.currentTimeMillis()}"
                val intent = AppSelectionActivity.createSingleSelectionIntent(
                    context = context,
                    resultKey = resultKey
                )
                appSelectionLauncher.launch(intent)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (appName.isNotBlank()) {
                    "已选择: $appName"
                } else {
                    "点击选择应用"
                }
            )
        }

        if (appPackageName.isNotBlank()) {
            Text(
                text = "包名: $appPackageName",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = operation,
                    appPackageName = appPackageName,
                    appName = appName
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = appPackageName.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 强制停止应用配置内容组件（增强版）
 *
 * 集成了应用列表显示、筛选功能和所有高级功能
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 *
 * @param operation 应用操作类型
 * @param onComplete 配置完成回调
 * @param initialTask 初始任务数据（编辑模式）
 */
@Composable
private fun ForceStopAppConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit,
    initialTask: ApplicationTask? = null
) {
    // 停止目标选择状态
    var forceStopScope by rememberSaveable { mutableStateOf(initialTask?.forceStopScope ?: ForceStopScope.SELECTED_APP) }

    // 应用选择相关状态（使用原生存储，避免Parcelable序列化问题）
    var selectedApps by remember { mutableStateOf(initialTask?.forceStopSelectedApps ?: emptyList()) }

    // 跳过选项状态
    var skipForegroundApp by rememberSaveable { mutableStateOf(initialTask?.skipForegroundApp ?: true) }
    var skipMusicPlayingApp by rememberSaveable { mutableStateOf(initialTask?.skipMusicPlayingApp ?: true) }
    var skipVpnApp by rememberSaveable { mutableStateOf(initialTask?.skipVpnApp ?: false) }
    var selectedVpnApps by remember { mutableStateOf(initialTask?.selectedVpnApps ?: emptyList()) }

    // 实时前台应用检测选项
    var enableRealtimeForegroundDetection by rememberSaveable { mutableStateOf(initialTask?.enableRealtimeForegroundDetection ?: false) }

    // 智能排序选项
    var sortByBackgroundTime by rememberSaveable { mutableStateOf(initialTask?.sortByBackgroundTime ?: false) }
    var appSortingStrategy by rememberSaveable { mutableStateOf(initialTask?.appSortingStrategy ?: AppSortingStrategy.PRIORITY_THEN_BACKGROUND_TIME) }

    // 智能内存管理配置
    var enableMemoryThresholdCheck by rememberSaveable { mutableStateOf(initialTask?.enableMemoryThresholdCheck ?: false) }
    var memoryThreshold by rememberSaveable { mutableStateOf(initialTask?.memoryThreshold ?: 3) }
    var memoryThresholdIsPercentage by rememberSaveable { mutableStateOf(initialTask?.memoryThresholdIsPercentage ?: false) }
    var memoryCheckFrequency by rememberSaveable { mutableStateOf(initialTask?.memoryCheckFrequency ?: MemoryCheckFrequency.BALANCED) }
    var enableCustomCheckFrequency by rememberSaveable { mutableStateOf(initialTask?.enableCustomCheckFrequency ?: false) }
    var customCheckFrequencySize by rememberSaveable { mutableStateOf(initialTask?.customCheckFrequencySize ?: 5) }

    // 自动包含新安装应用
    var autoIncludeNewApps by rememberSaveable { mutableStateOf(initialTask?.autoIncludeNewApps ?: false) }

    // 自定义强制停止命令配置
    var useCustomForceStopCommand by rememberSaveable { mutableStateOf(initialTask?.useCustomForceStopCommand ?: false) }
    var customForceStopCommand by rememberSaveable { mutableStateOf(initialTask?.customForceStopCommand ?: "") }



    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val uiStateManager = remember { UIStateStorageManager(context) }
    val stateKey = "force_stop_app_config"

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    when {
                        resultKey.startsWith("vpn_app_selection_") -> {
                            // VPN应用选择结果
                            selectedVpnApps = loadedApps
                            // 保存到持久化存储
                            uiStateManager.saveAppListState(stateKey, "selected_vpn_apps", loadedApps)
                        }
                        else -> {
                            // 普通应用选择结果
                            selectedApps = loadedApps
                            // 保存到持久化存储
                            uiStateManager.saveAppListState(stateKey, "selected_apps", loadedApps)
                        }
                    }
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 初始化时加载保存的应用列表
    LaunchedEffect(Unit) {
        selectedApps = uiStateManager.loadAppListState(stateKey, "selected_apps")
        selectedVpnApps = uiStateManager.loadAppListState(stateKey, "selected_vpn_apps")
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "强制停止应用设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 停止目标选择器
        ForceStopTargetSelector(
            selectedScope = forceStopScope,
            onScopeChanged = { forceStopScope = it }
        )

        // 应用选择区域（条件显示）
        if (forceStopScope == ForceStopScope.SELECTED_APP) {
            EnhancedAppSelectionSection(
                selectedApps = selectedApps,
                onAppsChanged = { selectedApps = it },
                onNavigateToAppSelection = { selectedPackageNames ->
                    val resultKey = "force_stop_app_selection_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createMultiSelectionIntent(
                        context = context,
                        selectedAppPackageNames = selectedPackageNames,
                        resultKey = resultKey
                    )
                    appSelectionLauncher.launch(intent)
                }
            )
        }

        // 跳过选项配置
        com.weinuo.quickcommands.ui.components.SkipOptionsSection(
            skipForegroundApp = skipForegroundApp,
            onSkipForegroundAppChanged = { skipForegroundApp = it },
            skipMusicPlayingApp = skipMusicPlayingApp,
            onSkipMusicPlayingAppChanged = { skipMusicPlayingApp = it },
            skipVpnApp = skipVpnApp,
            onSkipVpnAppChanged = { skipVpnApp = it },
            selectedVpnApps = selectedVpnApps,
            onVpnAppsChanged = { selectedVpnApps = it },
            onNavigateToVpnAppSelection = { selectedPackageNames ->
                val resultKey = "vpn_app_selection_${System.currentTimeMillis()}"
                val intent = AppSelectionActivity.createMultiSelectionIntent(
                    context = context,
                    selectedAppPackageNames = selectedPackageNames,
                    resultKey = resultKey
                )
                appSelectionLauncher.launch(intent)
            },
            stateKey = stateKey,
            enableRealtimeForegroundDetection = enableRealtimeForegroundDetection,
            onEnableRealtimeForegroundDetectionChanged = { enableRealtimeForegroundDetection = it },
            showRealtimeForegroundOption = true
        )

        // 自动包含新安装应用
        AutoIncludeNewAppsSection(
            autoIncludeNewApps = autoIncludeNewApps,
            onAutoIncludeNewAppsChanged = { autoIncludeNewApps = it }
        )

        // 智能排序策略
        SmartSortingSection(
            sortingStrategy = appSortingStrategy,
            onSortingStrategyChanged = { appSortingStrategy = it },
            navController = null
        )

        // 智能内存管理配置
        SmartMemoryManagementSection(
            enableMemoryThresholdCheck = enableMemoryThresholdCheck,
            onEnableMemoryThresholdCheckChanged = { enableMemoryThresholdCheck = it },
            memoryThreshold = memoryThreshold,
            onMemoryThresholdChanged = { memoryThreshold = it },
            memoryThresholdIsPercentage = memoryThresholdIsPercentage,
            onMemoryThresholdIsPercentageChanged = { memoryThresholdIsPercentage = it },
            memoryCheckFrequency = memoryCheckFrequency,
            onMemoryCheckFrequencyChanged = { memoryCheckFrequency = it },
            enableCustomCheckFrequency = enableCustomCheckFrequency,
            onEnableCustomCheckFrequencyChanged = { enableCustomCheckFrequency = it },
            customCheckFrequencySize = customCheckFrequencySize,
            onCustomCheckFrequencySizeChanged = { customCheckFrequencySize = it }
        )

        // 自定义强制停止命令配置
        CustomForceStopCommandSection(
            useCustomCommand = useCustomForceStopCommand,
            onUseCustomCommandChanged = { useCustomForceStopCommand = it },
            customCommand = customForceStopCommand,
            onCustomCommandChanged = { customForceStopCommand = it }
        )



        // 确认按钮
        Button(
            onClick = {
                // 预处理自定义命令，计算最终生效的命令
                val effectiveCommand = if (useCustomForceStopCommand && customForceStopCommand.isNotBlank()) {
                    customForceStopCommand
                } else {
                    "am force-stop [package_name]"
                }

                val task = ApplicationTask(
                    operation = operation,
                    forceStopScope = forceStopScope,
                    forceStopSelectedApps = selectedApps,
                    skipForegroundApp = skipForegroundApp,
                    skipMusicPlayingApp = skipMusicPlayingApp,
                    skipVpnApp = skipVpnApp,
                    selectedVpnApps = selectedVpnApps,
                    sortByBackgroundTime = sortByBackgroundTime,
                    appSortingStrategy = appSortingStrategy,
                    enableMemoryThresholdCheck = enableMemoryThresholdCheck,
                    memoryThreshold = memoryThreshold,
                    memoryThresholdIsPercentage = memoryThresholdIsPercentage,
                    memoryCheckFrequency = memoryCheckFrequency,
                    enableCustomCheckFrequency = enableCustomCheckFrequency,
                    customCheckFrequencySize = customCheckFrequencySize,
                    autoIncludeNewApps = autoIncludeNewApps,
                    useCustomForceStopCommand = useCustomForceStopCommand,
                    customForceStopCommand = customForceStopCommand,
                    effectiveForceStopCommand = effectiveCommand,
                    enableRealtimeForegroundDetection = enableRealtimeForegroundDetection
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (forceStopScope) {
                ForceStopScope.SELECTED_APP -> selectedApps.isNotEmpty()
                ForceStopScope.TRIGGER_APP -> true
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 增强的应用选择区域组件
 *
 * 集成了应用列表显示、筛选、搜索等功能
 */
@Composable
private fun EnhancedAppSelectionSection(
    selectedApps: List<SimpleAppInfo>,
    onAppsChanged: (List<SimpleAppInfo>) -> Unit,
    onNavigateToAppSelection: (List<String>) -> Unit,
    title: String = "选择要停止的应用",
    buttonText: String = "点击选择要停止的应用"
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium
        )

        // 应用选择按钮
        OutlinedButton(
            onClick = {
                onNavigateToAppSelection(selectedApps.map { it.packageName })
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.Apps,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = when {
                    selectedApps.isEmpty() -> buttonText
                    selectedApps.size == 1 -> "已选择: ${selectedApps.first().appName}"
                    else -> "已选择 ${selectedApps.size} 个应用"
                }
            )
        }

        // 显示选中的应用列表
        if (selectedApps.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "选中的应用 (${selectedApps.size})",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        // 清除所有按钮
                        TextButton(
                            onClick = { onAppsChanged(emptyList()) },
                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "清除所有",
                                style = MaterialTheme.typography.labelSmall
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 应用列表
                    selectedApps.take(5).forEach { app ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 2.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "• ${app.appName}",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.weight(1f)
                            )

                            // 系统应用标识
                            if (app.isSystemApp) {
                                Text(
                                    text = "系统",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.secondary,
                                    modifier = Modifier
                                        .background(
                                            MaterialTheme.colorScheme.secondaryContainer,
                                            RoundedCornerShape(4.dp)
                                        )
                                        .padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                        }
                    }

                    // 如果应用数量超过5个，显示省略提示
                    if (selectedApps.size > 5) {
                        Text(
                            text = "... 还有 ${selectedApps.size - 5} 个应用",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(start = 8.dp, top = 4.dp)
                        )
                    }
                }
            }
        }
    }
}



/**
 * 智能排序策略区域组件
 */
@Composable
private fun SmartSortingSection(
    sortingStrategy: AppSortingStrategy,
    onSortingStrategyChanged: (AppSortingStrategy) -> Unit,
    navController: NavController?
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "智能排序策略",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "选择应用停止的优先顺序，让重要应用得到更好保护",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            AppSortingStrategy.values().forEach { strategy ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (sortingStrategy == strategy),
                            onClick = { onSortingStrategyChanged(strategy) }
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (sortingStrategy == strategy),
                        onClick = { onSortingStrategyChanged(strategy) }
                    )
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 8.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = strategy.displayName,
                                style = MaterialTheme.typography.bodyMedium
                            )
                            if (strategy == AppSortingStrategy.PRIORITY_THEN_BACKGROUND_TIME) {
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "推荐",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    modifier = Modifier
                                        .background(
                                            MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.1f),
                                            RoundedCornerShape(4.dp)
                                        )
                                        .padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                        }
                        Text(
                            text = strategy.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }

        // 应用优先级管理按钮
        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppImportanceManagement.createRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("管理应用优先级")
        }
    }
}

/**
 * 智能内存管理区域组件（重构版 - 去除嵌套卡片）
 */
@Composable
private fun SmartMemoryManagementSection(
    enableMemoryThresholdCheck: Boolean,
    onEnableMemoryThresholdCheckChanged: (Boolean) -> Unit,
    memoryThreshold: Int,
    onMemoryThresholdChanged: (Int) -> Unit,
    memoryThresholdIsPercentage: Boolean,
    onMemoryThresholdIsPercentageChanged: (Boolean) -> Unit,
    memoryCheckFrequency: MemoryCheckFrequency,
    onMemoryCheckFrequencyChanged: (MemoryCheckFrequency) -> Unit,
    enableCustomCheckFrequency: Boolean,
    onEnableCustomCheckFrequencyChanged: (Boolean) -> Unit,
    customCheckFrequencySize: Int,
    onCustomCheckFrequencySizeChanged: (Int) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 启用内存阈值检查选项
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = enableMemoryThresholdCheck,
                onCheckedChange = onEnableMemoryThresholdCheckChanged
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "当可用内存足够时自动停止清理",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "避免过度清理，当内存达到设定值时自动停止",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 内存管理配置（条件显示，去除嵌套卡片）
        if (enableMemoryThresholdCheck) {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 内存阈值设置标题
                Text(
                    text = "停止清理的内存条件",
                    style = MaterialTheme.typography.titleMedium
                )

                Text(
                    text = "当可用内存达到此值时，将停止继续清理应用",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // 绝对值模式选择（单独一行）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = !memoryThresholdIsPercentage,
                            onClick = { onMemoryThresholdIsPercentageChanged(false) }
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = !memoryThresholdIsPercentage,
                        onClick = { onMemoryThresholdIsPercentageChanged(false) }
                    )
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 8.dp)
                    ) {
                        Text(
                            text = "绝对值模式",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "设置具体的内存大小 (GB)",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // 百分比模式选择（单独一行）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = memoryThresholdIsPercentage,
                            onClick = { onMemoryThresholdIsPercentageChanged(true) }
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = memoryThresholdIsPercentage,
                        onClick = { onMemoryThresholdIsPercentageChanged(true) }
                    )
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 8.dp)
                    ) {
                        Text(
                            text = "百分比模式",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "设置可用内存的百分比 (%)",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // 阈值输入
                var thresholdText by rememberSaveable { mutableStateOf(memoryThreshold.toString()) }
                var thresholdError by rememberSaveable { mutableStateOf(false) }

                OutlinedTextField(
                    value = thresholdText,
                    onValueChange = { newValue ->
                        thresholdText = newValue
                        val threshold = newValue.toIntOrNull()
                        if (threshold != null && threshold > 0) {
                            val maxValue = if (memoryThresholdIsPercentage) 100 else 32
                            if (threshold <= maxValue) {
                                onMemoryThresholdChanged(threshold)
                                thresholdError = false
                            } else {
                                thresholdError = true
                            }
                        } else {
                            thresholdError = true
                        }
                    },
                    label = {
                        Text(
                            if (memoryThresholdIsPercentage) "可用内存百分比阈值" else "可用内存阈值 (GB)"
                        )
                    },
                    suffix = {
                        Text(if (memoryThresholdIsPercentage) "%" else "GB")
                    },
                    isError = thresholdError,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth()
                )

                if (thresholdError) {
                    Text(
                        text = if (memoryThresholdIsPercentage) "请输入1-100之间的数值" else "请输入1-32之间的数值",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 内存检查频率标题
                Text(
                    text = "清理过程中的检查频率",
                    style = MaterialTheme.typography.titleMedium
                )

                Text(
                    text = "控制清理过程中检查内存的频率，频繁检查更精确但稍慢",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // 检查频率选项
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    MemoryCheckFrequency.values().forEach { frequency ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (memoryCheckFrequency == frequency),
                                    onClick = { onMemoryCheckFrequencyChanged(frequency) }
                                ),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (memoryCheckFrequency == frequency),
                                onClick = { onMemoryCheckFrequencyChanged(frequency) }
                            )
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(start = 8.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = frequency.displayName,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    if (frequency == MemoryCheckFrequency.BALANCED) {
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text(
                                            text = "推荐",
                                            style = MaterialTheme.typography.labelSmall,
                                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                                            modifier = Modifier
                                                .background(
                                                    MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.1f),
                                                    RoundedCornerShape(4.dp)
                                                )
                                                .padding(horizontal = 6.dp, vertical = 2.dp)
                                        )
                                    }
                                }
                                Text(
                                    text = frequency.description,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }

                // 自定义检查频率选项
                Text(
                    text = "自定义检查频率",
                    style = MaterialTheme.typography.titleMedium
                )

                Text(
                    text = "设置每清理多少个应用后检查一次内存状态",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = enableCustomCheckFrequency,
                        onCheckedChange = onEnableCustomCheckFrequencyChanged
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "启用自定义检查频率",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                if (enableCustomCheckFrequency) {
                    // 自定义分组大小输入
                    var customGroupSizeText by rememberSaveable { mutableStateOf(customCheckFrequencySize.toString()) }
                    var customGroupSizeError by rememberSaveable { mutableStateOf(false) }

                    OutlinedTextField(
                        value = customGroupSizeText,
                        onValueChange = { newValue ->
                            customGroupSizeText = newValue
                            val groupSize = newValue.toIntOrNull()
                            if (groupSize != null && groupSize > 0 && groupSize <= 50) {
                                onCustomCheckFrequencySizeChanged(groupSize)
                                customGroupSizeError = false
                            } else {
                                customGroupSizeError = true
                            }
                        },
                        label = { Text("每清理多少个应用检查一次") },
                        suffix = { Text("个应用") },
                        isError = customGroupSizeError,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth()
                    )

                    if (customGroupSizeError) {
                        Text(
                            text = "请输入1-50之间的数值",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    Text(
                        text = "提示：启用自定义检查频率后，将优先使用自定义设置而不是上面的预设选项",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
        }
    }
}






/**
 * 执行JavaScript代码配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ExecuteJavaScriptConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit
) {

    var javascriptCode by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "JavaScript代码设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = javascriptCode,
            onValueChange = { javascriptCode = it },
            label = { Text("JavaScript代码") },
            placeholder = { Text("输入要执行的JavaScript代码") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 8
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = operation,
                    javascriptCode = javascriptCode
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = javascriptCode.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 执行Shell脚本配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 * 对于Shell脚本，当用户选择Shizuku模式时会触发权限检查
 */
@Composable
private fun ExecuteShellScriptConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    var shellScript by rememberSaveable { mutableStateOf("") }
    var shellExecutionMode by rememberSaveable { mutableStateOf(ShellExecutionMode.NORMAL) }
    var shellTimeoutMinutes by rememberSaveable { mutableStateOf("0") }
    var shellTimeoutSeconds by rememberSaveable { mutableStateOf("30") }
    var shellWaitForCompletion by rememberSaveable { mutableStateOf(false) }

    // Shell脚本特殊权限检查状态：等待Shizuku权限授予
    var isWaitingForShizukuPermission by rememberSaveable { mutableStateOf(false) }

    // 观察权限状态变化，用于处理权限授予后的逻辑
    val globalPermissionManager = rememberSaveable { com.weinuo.quickcommands.permission.GlobalPermissionManager.getInstance(context) }
    val permissionStates by globalPermissionManager.permissionStates.collectAsState()

    // 观察Shizuku权限状态变化，权限授予后自动设置为Shizuku模式
    LaunchedEffect(permissionStates, isWaitingForShizukuPermission) {
        if (isWaitingForShizukuPermission &&
            com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
            // 权限已授予，设置为Shizuku模式
            shellExecutionMode = ShellExecutionMode.SHIZUKU
            isWaitingForShizukuPermission = false
        }
    }

    // 添加取消等待权限的机制：当用户选择普通模式时取消等待
    LaunchedEffect(shellExecutionMode) {
        if (shellExecutionMode == ShellExecutionMode.NORMAL && isWaitingForShizukuPermission) {
            // 用户选择了普通模式，取消等待Shizuku权限
            isWaitingForShizukuPermission = false
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Shell脚本设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = shellScript,
            onValueChange = { shellScript = it },
            label = { Text("Shell脚本") },
            placeholder = { Text("输入要执行的Shell脚本") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 8
        )

        // 执行模式选择
        Text(
            text = "执行模式",
            style = MaterialTheme.typography.bodyLarge
        )

        ShellExecutionMode.values().forEach { mode ->
            val onModeSelect = {
                if (mode == ShellExecutionMode.SHIZUKU) {
                    // 选择Shizuku模式时先检查权限
                    if (com.weinuo.quickcommands.shizuku.ShizukuManager.checkShizukuPermission()) {
                        // 已有权限，直接设置
                        shellExecutionMode = mode
                    } else {
                        // 没有权限，触发权限检查
                        isWaitingForShizukuPermission = true
                    }
                } else {
                    // 普通模式直接设置
                    shellExecutionMode = mode
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (shellExecutionMode == mode),
                        onClick = onModeSelect
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (shellExecutionMode == mode),
                    onClick = onModeSelect
                )
                Text(
                    text = when (mode) {
                        ShellExecutionMode.NORMAL -> "普通模式"
                        ShellExecutionMode.SHIZUKU -> "Shizuku模式"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 超时设置
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = shellTimeoutMinutes,
                onValueChange = { shellTimeoutMinutes = it },
                label = { Text("超时分钟") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
            OutlinedTextField(
                value = shellTimeoutSeconds,
                onValueChange = { shellTimeoutSeconds = it },
                label = { Text("超时秒数") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
        }

        // 等待完成选项
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = shellWaitForCompletion,
                onCheckedChange = { shellWaitForCompletion = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "等待执行完成",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = operation,
                    shellScript = shellScript,
                    shellExecutionMode = shellExecutionMode,
                    shellTimeoutMinutes = shellTimeoutMinutes.toIntOrNull() ?: 0,
                    shellTimeoutSeconds = shellTimeoutSeconds.toIntOrNull() ?: 30,
                    shellWaitForCompletion = shellWaitForCompletion
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = shellScript.isNotBlank()
        ) {
            Text("确认配置")
        }
    }

    // Shell脚本特殊权限检查：只有等待Shizuku权限时才检查权限
    if (isWaitingForShizukuPermission) {
        com.weinuo.quickcommands.permission.PermissionAwareOperationSelector(
            selectedOperation = ShellScriptShizukuOperation.SHIZUKU_SHELL,
            onPermissionDialogDismissed = {
                // 权限对话框关闭时不清除等待状态，让LaunchedEffect处理权限授予后的逻辑
                // isWaitingForShizukuPermission 保持 true，直到权限授予或用户取消
            },
            context = context
        )
    }
}

/**
 * Tasker/Locale插件配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun TaskerLocalePluginConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit
) {

    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedPluginPackageName by rememberSaveable { mutableStateOf("") }
    var selectedPluginName by rememberSaveable { mutableStateOf("") }
    var selectedPluginIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedPlugin = if (selectedPluginPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedPluginPackageName,
            appName = selectedPluginName,
            isSystemApp = selectedPluginIsSystemApp
        )
    } else {
        null
    }

    var taskerPluginAction by rememberSaveable { mutableStateOf("") }
    var taskerPluginExtras by rememberSaveable { mutableStateOf("") }
    var autoDetectAction by rememberSaveable { mutableStateOf(true) }

    val navController = LocalNavController.current

    // 当选择插件后，自动检测可用的 Action
    LaunchedEffect(selectedPlugin, autoDetectAction) {
        if (selectedPlugin != null && autoDetectAction) {
            val detectedAction = detectPluginActionSuspend(selectedPlugin!!.packageName, navController?.context)
            if (detectedAction.isNotEmpty()) {
                taskerPluginAction = detectedAction
            }
        }
    }

    // 监听应用选择结果
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 使用字段分离的方式获取应用选择结果
        val selectedAppPackageName = savedStateHandle?.get<String>("selected_app_package_name")
        val selectedAppName = savedStateHandle?.get<String>("selected_app_name")
        val selectedAppIsSystemApp = savedStateHandle?.get<Boolean>("selected_app_is_system_app")

        if (!selectedAppPackageName.isNullOrEmpty()) {
            selectedPluginPackageName = selectedAppPackageName
            selectedPluginName = selectedAppName ?: ""
            selectedPluginIsSystemApp = selectedAppIsSystemApp ?: false
            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_app_package_name")
            savedStateHandle.remove<String>("selected_app_name")
            savedStateHandle.remove<Boolean>("selected_app_is_system_app")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Tasker/Locale任务插件设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 插件选择
        Text(
            text = "选择任务插件应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createSingleSelectionRoute(filterType = "TASKER_ACTION_PLUGIN")
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedPlugin != null) {
                    "已选择: ${selectedPlugin!!.appName}"
                } else {
                    "点击选择任务插件应用"
                }
            )
        }

        if (selectedPlugin != null) {
            Text(
                text = "包名: ${selectedPlugin!!.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 显示插件配置说明
        if (selectedPlugin != null) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainerLow
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "插件配置说明",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = "• 插件将通过标准的 Tasker/Locale 协议执行",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "• 执行时会发送广播到插件应用",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "• 插件的具体配置需要在插件应用内完成",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // Action 配置
        if (selectedPlugin != null) {
            Text(
                text = "插件 Action 配置",
                style = MaterialTheme.typography.titleMedium
            )

            // 自动检测开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = autoDetectAction,
                    onCheckedChange = { autoDetectAction = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "自动检测插件 Action",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // Action 输入框
            OutlinedTextField(
                value = taskerPluginAction,
                onValueChange = { taskerPluginAction = it },
                label = { Text("插件 Action (可选)") },
                placeholder = { Text("com.example.plugin.ACTION_EXECUTE") },
                modifier = Modifier.fillMaxWidth(),
                enabled = !autoDetectAction,
                supportingText = {
                    Text(
                        if (autoDetectAction) {
                            "已自动检测插件 Action，如需手动配置请关闭自动检测"
                        } else {
                            "手动指定插件的执行 Action，留空将使用默认值"
                        }
                    )
                }
            )
        }

        OutlinedTextField(
            value = taskerPluginExtras,
            onValueChange = { taskerPluginExtras = it },
            label = { Text("插件配置数据 (可选)") },
            placeholder = { Text("插件特定的配置数据，通常为JSON格式") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4,
            supportingText = {
                Text("此字段用于存储插件的配置数据，具体格式取决于插件要求")
            }
        )

        // 确认按钮
        Button(
            onClick = {
                val finalAction = if (taskerPluginAction.isNotEmpty()) {
                    taskerPluginAction
                } else {
                    // 使用默认的 Tasker 插件执行 Action
                    "com.twofortyfouram.locale.intent.action.FIRE_SETTING"
                }

                val task = ApplicationTask(
                    operation = operation,
                    taskerPluginPackage = selectedPlugin?.packageName ?: "",
                    taskerPluginAction = finalAction,
                    taskerPluginExtras = taskerPluginExtras
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedPlugin != null
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 检测插件的可用 Action (挂起函数版本)
 * 通过查询插件的 Manifest 文件来检测支持的 Action
 */
private suspend fun detectPluginActionSuspend(packageName: String, context: android.content.Context?): String {
    return withContext(Dispatchers.IO) {
        try {
            if (context == null) return@withContext "com.twofortyfouram.locale.intent.action.FIRE_SETTING"

            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES or PackageManager.GET_RECEIVERS)

            // 检查 Activities 中的 Tasker 插件 Action
            packageInfo.activities?.forEach { activityInfo ->
                activityInfo.metaData?.let { metaData ->
                    // 检查是否有 Tasker 插件相关的 meta-data
                    if (metaData.containsKey("com.twofortyfouram.locale.intent.action.EDIT_SETTING")) {
                        return@withContext "com.twofortyfouram.locale.intent.action.FIRE_SETTING"
                    }
                }
            }

            // 检查 BroadcastReceivers 中的 Action
            packageInfo.receivers?.forEach { receiverInfo ->
                receiverInfo.metaData?.let { metaData ->
                    // 检查是否有自定义的执行 Action
                    metaData.keySet().forEach { key ->
                        if (key.contains("action", ignoreCase = true) && key.contains("fire", ignoreCase = true)) {
                            val actionValue = metaData.getString(key)
                            if (!actionValue.isNullOrEmpty()) {
                                return@withContext actionValue
                            }
                        }
                    }
                }
            }

            // 默认返回标准的 Tasker 插件执行 Action
            "com.twofortyfouram.locale.intent.action.FIRE_SETTING"

        } catch (e: Exception) {
            Log.w("ApplicationTaskConfig", "Failed to detect plugin action for $packageName", e)
            "com.twofortyfouram.locale.intent.action.FIRE_SETTING"
        }
    }
}

/**
 * 打开网站配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun OpenWebsiteConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit
) {

    var websiteUrl by rememberSaveable { mutableStateOf("") }
    var urlEncodeParams by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "打开网站设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = websiteUrl,
            onValueChange = { websiteUrl = it },
            label = { Text("网站地址") },
            placeholder = { Text("https://m.baidu.com") },
            modifier = Modifier.fillMaxWidth()
        )

        // URL参数编码选项
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = urlEncodeParams,
                onCheckedChange = { urlEncodeParams = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "URL参数编码",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = operation,
                    websiteUrl = websiteUrl,
                    urlEncodeParams = urlEncodeParams
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = websiteUrl.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}



/**
 * 自动包含新安装应用区域组件
 */
@Composable
private fun AutoIncludeNewAppsSection(
    autoIncludeNewApps: Boolean,
    onAutoIncludeNewAppsChanged: (Boolean) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "自动管理",
            style = MaterialTheme.typography.titleMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = autoIncludeNewApps,
                onCheckedChange = onAutoIncludeNewAppsChanged
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "自动包含新安装应用",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "新安装的应用将自动添加到强制停止列表中",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 强制停止目标选择器组件
 */
@Composable
private fun ForceStopTargetSelector(
    selectedScope: ForceStopScope,
    onScopeChanged: (ForceStopScope) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "停止目标",
            style = MaterialTheme.typography.titleMedium
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 指定应用列表选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedScope == ForceStopScope.SELECTED_APP),
                        onClick = { onScopeChanged(ForceStopScope.SELECTED_APP) }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedScope == ForceStopScope.SELECTED_APP),
                    onClick = { onScopeChanged(ForceStopScope.SELECTED_APP) }
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                ) {
                    Text(
                        text = "指定应用列表",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "停止用户选择的特定应用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 触发条件的应用选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedScope == ForceStopScope.TRIGGER_APP),
                        onClick = { onScopeChanged(ForceStopScope.TRIGGER_APP) }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedScope == ForceStopScope.TRIGGER_APP),
                    onClick = { onScopeChanged(ForceStopScope.TRIGGER_APP) }
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                ) {
                    Text(
                        text = "触发条件的应用",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "停止触发此任务的应用，常与应用状态条件配合使用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }


        }

        // 触发应用模式的说明
        if (selectedScope == ForceStopScope.TRIGGER_APP) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                )
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "将停止触发此任务的应用。例如：在添加条件时选择应用状态→状态变化→后台时间超过阈值，当检测到某应用超过阈值时，会停止该应用。",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 自定义强制停止命令配置区域组件
 */
@Composable
private fun CustomForceStopCommandSection(
    useCustomCommand: Boolean,
    onUseCustomCommandChanged: (Boolean) -> Unit,
    customCommand: String,
    onCustomCommandChanged: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 启用自定义命令选项
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useCustomCommand,
                onCheckedChange = onUseCustomCommandChanged
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "使用自定义强制停止命令",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "自定义强制停止应用的命令模板",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 自定义命令配置（条件显示）
        if (useCustomCommand) {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "命令模板设置",
                    style = MaterialTheme.typography.titleMedium
                )

                Text(
                    text = "使用 [package_name] 作为占位符，执行时会自动替换为实际的应用包名",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // 命令输入框
                OutlinedTextField(
                    value = customCommand,
                    onValueChange = onCustomCommandChanged,
                    label = { Text("强制停止命令") },
                    placeholder = { Text("am force-stop [package_name]") },
                    supportingText = {
                        Text("示例：am force-stop [package_name]")
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = false,
                    maxLines = 3
                )

                // 验证提示
                if (customCommand.isNotBlank() && !customCommand.contains("[package_name]")) {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.error,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "命令中缺少 [package_name] 占位符，可能无法正确执行",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 冻结应用配置内容组件
 *
 * 复用强制停止应用的选择逻辑和UI组件
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 *
 * @param operation 应用操作类型
 * @param onComplete 配置完成回调
 * @param initialTask 初始任务数据（编辑模式）
 */
@Composable
private fun FreezeAppConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit,
    initialTask: ApplicationTask? = null
) {
    // 冻结目标选择状态
    var freezeScope by rememberSaveable { mutableStateOf(initialTask?.freezeScope ?: ForceStopScope.SELECTED_APP) }

    // 应用选择相关状态（使用原生存储，避免Parcelable序列化问题）
    var selectedApps by remember { mutableStateOf(initialTask?.freezeSelectedApps ?: emptyList()) }

    // 获取上下文
    val context = LocalContext.current

    // UI状态管理器
    val uiStateManager = remember { UIStateStorageManager(context) }
    val stateKey = "freeze_app_config"

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    selectedApps = loadedApps
                    // 保存到持久化存储
                    uiStateManager.saveAppListState(stateKey, "selected_apps", loadedApps)
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 初始化时加载保存的应用列表
    LaunchedEffect(Unit) {
        selectedApps = uiStateManager.loadAppListState(stateKey, "selected_apps")
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "冻结应用设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 冻结目标选择器
        FreezeTargetSelector(
            selectedScope = freezeScope,
            onScopeChanged = { freezeScope = it }
        )

        // 应用选择区域（条件显示）
        if (freezeScope == ForceStopScope.SELECTED_APP) {
            EnhancedAppSelectionSection(
                selectedApps = selectedApps,
                onAppsChanged = { selectedApps = it },
                onNavigateToAppSelection = { selectedPackageNames ->
                    val resultKey = "freeze_app_selection_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createMultiSelectionIntent(
                        context = context,
                        selectedAppPackageNames = selectedPackageNames,
                        resultKey = resultKey
                    )
                    appSelectionLauncher.launch(intent)
                },
                title = "选择要冻结的应用",
                buttonText = "点击选择要冻结的应用"
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = operation,
                    freezeScope = freezeScope,
                    freezeSelectedApps = selectedApps
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (freezeScope) {
                ForceStopScope.SELECTED_APP -> selectedApps.isNotEmpty()
                ForceStopScope.TRIGGER_APP -> true
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 解冻应用配置内容组件
 *
 * 复用强制停止应用的选择逻辑和UI组件，并增加解冻后打开应用的选项
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 *
 * @param operation 应用操作类型
 * @param onComplete 配置完成回调
 * @param initialTask 初始任务数据（编辑模式）
 */
@Composable
private fun UnfreezeAppConfigContent(
    operation: ApplicationOperation,
    onComplete: (Any) -> Unit,
    initialTask: ApplicationTask? = null
) {
    // 解冻目标选择状态
    var unfreezeScope by rememberSaveable { mutableStateOf(initialTask?.unfreezeScope ?: ForceStopScope.SELECTED_APP) }

    // 应用选择相关状态（使用原生存储，避免Parcelable序列化问题）
    var selectedApps by remember { mutableStateOf(initialTask?.unfreezeSelectedApps ?: emptyList()) }

    // 解冻后打开应用选项
    var openAppAfterUnfreeze by rememberSaveable { mutableStateOf(initialTask?.openAppAfterUnfreeze ?: false) }

    // 获取上下文
    val context = LocalContext.current

    // UI状态管理器
    val uiStateManager = remember { UIStateStorageManager(context) }
    val stateKey = "unfreeze_app_config"

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    selectedApps = loadedApps
                    // 保存到持久化存储
                    uiStateManager.saveAppListState(stateKey, "selected_apps", loadedApps)
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 初始化时加载保存的应用列表
    LaunchedEffect(Unit) {
        selectedApps = uiStateManager.loadAppListState(stateKey, "selected_apps")
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "解冻应用设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 解冻目标选择器
        UnfreezeTargetSelector(
            selectedScope = unfreezeScope,
            onScopeChanged = { unfreezeScope = it }
        )

        // 应用选择区域（条件显示）
        if (unfreezeScope == ForceStopScope.SELECTED_APP) {
            EnhancedAppSelectionSection(
                selectedApps = selectedApps,
                onAppsChanged = { selectedApps = it },
                onNavigateToAppSelection = { selectedPackageNames ->
                    val resultKey = "unfreeze_app_selection_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createMultiSelectionIntent(
                        context = context,
                        selectedAppPackageNames = selectedPackageNames,
                        resultKey = resultKey
                    )
                    appSelectionLauncher.launch(intent)
                },
                title = "选择要解冻的应用",
                buttonText = "点击选择要解冻的应用"
            )
        }

        // 解冻后打开应用选项
        Text(
            text = "解冻后操作",
            style = MaterialTheme.typography.titleMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = openAppAfterUnfreeze,
                onCheckedChange = { openAppAfterUnfreeze = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "解冻后打开应用",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "解冻应用后自动启动该应用",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = operation,
                    unfreezeScope = unfreezeScope,
                    unfreezeSelectedApps = selectedApps,
                    openAppAfterUnfreeze = openAppAfterUnfreeze
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (unfreezeScope) {
                ForceStopScope.SELECTED_APP -> selectedApps.isNotEmpty()
                ForceStopScope.TRIGGER_APP -> true
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 冻结目标选择器组件
 */
@Composable
private fun FreezeTargetSelector(
    selectedScope: ForceStopScope,
    onScopeChanged: (ForceStopScope) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "冻结目标",
            style = MaterialTheme.typography.titleMedium
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 指定应用列表选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedScope == ForceStopScope.SELECTED_APP),
                        onClick = { onScopeChanged(ForceStopScope.SELECTED_APP) }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedScope == ForceStopScope.SELECTED_APP),
                    onClick = { onScopeChanged(ForceStopScope.SELECTED_APP) }
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                ) {
                    Text(
                        text = "指定应用列表",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "冻结用户选择的特定应用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 触发条件的应用选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedScope == ForceStopScope.TRIGGER_APP),
                        onClick = { onScopeChanged(ForceStopScope.TRIGGER_APP) }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedScope == ForceStopScope.TRIGGER_APP),
                    onClick = { onScopeChanged(ForceStopScope.TRIGGER_APP) }
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                ) {
                    Text(
                        text = "触发条件的应用",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "冻结触发此任务的应用，常与应用状态条件配合使用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 触发应用模式的说明
        if (selectedScope == ForceStopScope.TRIGGER_APP) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                )
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "将冻结触发此任务的应用。例如：在添加条件时选择应用状态→状态变化→后台时间超过阈值，当检测到某应用超过阈值时，会冻结该应用。",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 解冻目标选择器组件
 */
@Composable
private fun UnfreezeTargetSelector(
    selectedScope: ForceStopScope,
    onScopeChanged: (ForceStopScope) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "解冻目标",
            style = MaterialTheme.typography.titleMedium
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 指定应用列表选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedScope == ForceStopScope.SELECTED_APP),
                        onClick = { onScopeChanged(ForceStopScope.SELECTED_APP) }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedScope == ForceStopScope.SELECTED_APP),
                    onClick = { onScopeChanged(ForceStopScope.SELECTED_APP) }
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                ) {
                    Text(
                        text = "指定应用列表",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "解冻用户选择的特定应用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 触发条件的应用选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedScope == ForceStopScope.TRIGGER_APP),
                        onClick = { onScopeChanged(ForceStopScope.TRIGGER_APP) }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedScope == ForceStopScope.TRIGGER_APP),
                    onClick = { onScopeChanged(ForceStopScope.TRIGGER_APP) }
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                ) {
                    Text(
                        text = "触发条件的应用",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "解冻触发此任务的应用，常与应用状态条件配合使用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 触发应用模式的说明
        if (selectedScope == ForceStopScope.TRIGGER_APP) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                )
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "将解冻触发此任务的应用。例如：在添加条件时选择应用状态→状态变化→后台时间超过阈值，当检测到某应用超过阈值时，会解冻该应用。",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}
package com.weinuo.quickcommands.ui.components.integrated

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.compose.material3.MaterialTheme
import androidx.navigation.NavController
import com.weinuo.quickcommands.ui.theme.config.BottomNavigationConfig
import com.weinuo.quickcommands.ui.theme.config.BlurComponent
import com.weinuo.quickcommands.ui.theme.manager.BlurConfigurationManager
import com.weinuo.quickcommands.ui.effects.HazeManager
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.hazeEffect
import com.weinuo.quickcommands.ui.effects.backgroundBlurEffect

/**
 * 整合设计的主布局组件
 *
 * 特点：
 * - iOS风格模糊效果，内容延伸到全屏
 * - 导航栏覆盖在内容上方，应用模糊效果
 * - 使用Box布局实现覆盖效果
 * - 支持模糊背景和透明度
 * - 正确处理状态栏WindowInsets，保持内容可见性
 * - 统一处理TopAppBar的布局策略（覆盖式）
 */
@Composable
fun IntegratedMainLayout(
    navController: NavController,
    bottomNavConfig: BottomNavigationConfig,
    hazeState: HazeState,
    content: @Composable (PaddingValues) -> Unit
) {
    val context = LocalContext.current
    val hazeManager = remember { HazeManager.getInstance(context) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background) // 设置天空蓝主题背景颜色
    ) {
        // 主要内容区域 - 作为模糊源，延伸到全屏，实现沉浸式状态栏
        // 整合设计的特点：内容延伸到全屏，页面内部自己处理TopAppBar和BottomNavigation的遮挡
        Box(
            modifier = Modifier
                .fillMaxSize()
                .hazeSource(
                    state = hazeState,
                    zIndex = 0f // 背景层级，会被标题栏(1f)和底部导航栏(2f)模糊
                )
        ) {
            content(PaddingValues(0.dp))
        }

        // 顶部应用栏 - 覆盖在内容上方，支持模糊效果
        // 使用页面级配置机制获取TopAppBar配置
        val topAppBarConfig = rememberIntegratedTopAppBarConfig()
        if (topAppBarConfig != null) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .fillMaxWidth()
                    .zIndex(1f) // 确保在内容上方
            ) {
                IntegratedTopAppBar(
                    config = topAppBarConfig,
                    hazeState = hazeState
                )
            }
        }

        // 底部导航栏 - 覆盖在内容上方，应用模糊效果
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .zIndex(2f) // 确保在内容和顶部应用栏上方
        ) {
            IntegratedBottomNavigation(
                config = bottomNavConfig,
                hazeState = hazeState,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 全局模糊遮罩层 - 当上下文菜单显示时覆盖整个屏幕，包括底部导航栏
        if (hazeManager.showContextMenu) {
            GlobalBlurredContextMenuOverlay(
                hazeState = hazeState,
                onDismiss = { hazeManager.hideContextMenu() },
                modifier = Modifier.zIndex(10f) // 确保在所有组件上方
            )
        }
    }
}

/**
 * 全局模糊上下文菜单遮罩层
 *
 * 为上下文菜单提供全屏模糊背景效果，覆盖所有组件包括底部导航栏
 * 位于IntegratedMainLayout的最顶层，确保能够模糊所有下层内容
 * 现在支持用户自定义的覆盖层模糊配置
 */
@Composable
private fun GlobalBlurredContextMenuOverlay(
    hazeState: HazeState,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val blurConfigManager = remember { BlurConfigurationManager.getInstance(context) }
    val blurConfig = blurConfigManager.getBlurConfiguration()

    // 获取覆盖层模糊配置
    val overlayBlurEnabled = blurConfig.overlayBlurEnabled

    Box(
        modifier = modifier
            .fillMaxSize()
            .backgroundBlurEffect(
                hazeState = hazeState,
                style = if (overlayBlurEnabled) {
                    blurConfigManager.getHazeMaterial(BlurComponent.OVERLAY)
                } else {
                    null
                },
                backgroundColor = if (overlayBlurEnabled) {
                    MaterialTheme.colorScheme.surface
                } else {
                    Color.Transparent
                },
                component = BlurComponent.OVERLAY
            )
            .clickable { onDismiss() } // 点击遮罩关闭菜单
    )
}

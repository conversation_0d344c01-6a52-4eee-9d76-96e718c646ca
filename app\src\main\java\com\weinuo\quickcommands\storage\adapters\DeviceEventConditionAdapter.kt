package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.AppListStorageEngine
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation
import com.weinuo.quickcommands.storage.StorageType

/**
 * 设备事件条件存储适配器
 *
 * 负责DeviceEventCondition的原生数据类型存储和重建。
 * 将复杂的设备事件条件对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、eventType
 * - GPS状态参数：gpsStateType
 * - Logcat参数：logcatComponent、logcatText、logcatBufferTypes、logcatCaseSensitive
 * - 剪贴板参数：clipboardText、clipboardUseRegex、clipboardCaseSensitive
 * - 屏幕事件参数：screenEventType
 * - 底座状态参数：dockStateType
 * - Intent参数：intentAction、intentCategory、intentDataUri、intentExtras
 * - SIM卡参数：simCardStateType、simSlotIndex
 * - 系统设置参数：settingKey、settingValue、settingType
 * - 通知事件参数：notificationPackageName、notificationTitle、notificationText、notificationSoundMode
 * - 铃声模式参数：ringerModeType
 * - 音乐播放参数：musicPlaybackType
 * - 飞行模式参数：airplaneModeType
 * - 音量参数：volumeStreamType、volumeThreshold
 * - 内存状态参数：memoryStateType、memoryThreshold、isPercentageMode、memoryChangeThreshold
 *
 * 存储格式示例：
 * condition_{id}_type = "device_event"
 * condition_{id}_event_type = "GPS_STATE"
 * condition_{id}_gps_state_type = "ENABLED"
 * condition_{id}_logcat_component = "ActivityManager"
 * condition_{id}_logcat_text = "Process started"
 * condition_{id}_logcat_buffer_types_count = 2
 * condition_{id}_logcat_buffer_types_0 = "MAIN"
 * condition_{id}_logcat_buffer_types_1 = "SYSTEM"
 * condition_{id}_logcat_case_sensitive = true
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class DeviceEventConditionAdapter(
    storageManager: NativeTypeStorageManager,
    appListEngine: AppListStorageEngine
) : BaseConditionAdapter<DeviceEventCondition>(storageManager, appListEngine) {

    companion object {
        private const val TAG = "DeviceEventConditionAdapter"

        // 字段名常量
        private const val FIELD_EVENT_TYPE = "event_type"

        // GPS状态参数字段
        private const val FIELD_GPS_STATE_TYPE = "gps_state_type"

        // Logcat参数字段
        private const val FIELD_LOGCAT_COMPONENT = "logcat_component"
        private const val FIELD_LOGCAT_TEXT = "logcat_text"
        private const val FIELD_LOGCAT_BUFFER_TYPES = "logcat_buffer_types"
        private const val FIELD_LOGCAT_CASE_SENSITIVE = "logcat_case_sensitive"

        // 剪贴板参数字段
        private const val FIELD_CLIPBOARD_TEXT = "clipboard_text"
        private const val FIELD_CLIPBOARD_USE_REGEX = "clipboard_use_regex"
        private const val FIELD_CLIPBOARD_CASE_SENSITIVE = "clipboard_case_sensitive"

        // 屏幕事件参数字段
        private const val FIELD_SCREEN_EVENT_TYPE = "screen_event_type"

        // 底座状态参数字段
        private const val FIELD_DOCK_STATE_TYPE = "dock_state_type"

        // Intent参数字段
        private const val FIELD_INTENT_ACTION = "intent_action"
        private const val FIELD_INTENT_CATEGORY = "intent_category"
        private const val FIELD_INTENT_DATA_URI = "intent_data_uri"
        private const val FIELD_INTENT_EXTRAS = "intent_extras"

        // SIM卡参数字段
        private const val FIELD_SIM_CARD_STATE_TYPE = "sim_card_state_type"
        private const val FIELD_SIM_SLOT_INDEX = "sim_slot_index"

        // 系统设置参数字段
        private const val FIELD_SETTING_KEY = "setting_key"
        private const val FIELD_SETTING_VALUE = "setting_value"
        private const val FIELD_SETTING_TYPE = "setting_type"

        // 通知事件参数字段
        private const val FIELD_NOTIFICATION_PACKAGE_NAME = "notification_package_name"
        private const val FIELD_NOTIFICATION_TITLE = "notification_title"
        private const val FIELD_NOTIFICATION_TEXT = "notification_text"
        private const val FIELD_NOTIFICATION_SOUND_MODE = "notification_sound_mode"

        // 铃声模式参数字段
        private const val FIELD_RINGER_MODE_TYPE = "ringer_mode_type"

        // 音乐播放参数字段
        private const val FIELD_MUSIC_PLAYBACK_TYPE = "music_playback_type"

        // 飞行模式参数字段
        private const val FIELD_AIRPLANE_MODE_TYPE = "airplane_mode_type"

        // 音量参数字段
        private const val FIELD_VOLUME_STREAM_TYPE = "volume_stream_type"
        private const val FIELD_VOLUME_THRESHOLD = "volume_threshold"

        // 内存状态参数字段
        private const val FIELD_MEMORY_STATE_TYPE = "memory_state_type"
        private const val FIELD_MEMORY_THRESHOLD = "memory_threshold"
        private const val FIELD_IS_PERCENTAGE_MODE = "is_percentage_mode"
        private const val FIELD_MEMORY_CHANGE_THRESHOLD = "memory_change_threshold"

        // 检测频率字段
        private const val FIELD_CHECK_FREQUENCY = "check_frequency"
        private const val FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY = "enable_custom_check_frequency"
        private const val FIELD_CUSTOM_CHECK_FREQUENCY_SECONDS = "custom_check_frequency_seconds"

        // 内存检测模式字段
        private const val FIELD_MEMORY_CHECK_MODE = "memory_check_mode"

        // 事件驱动配置字段
        private const val FIELD_TRIGGER_ON_APP_FOREGROUND = "trigger_on_app_foreground"
        private const val FIELD_TRIGGER_ON_APP_LAUNCH = "trigger_on_app_launch"
        private const val FIELD_TRIGGER_ON_MEMORY_PRESSURE = "trigger_on_memory_pressure"
        private const val FIELD_APP_LAUNCH_DELAY_SECONDS = "app_launch_delay_seconds"
        private const val FIELD_FOREGROUND_DELAY_SECONDS = "foreground_delay_seconds"
        private const val FIELD_ENABLE_SMART_DELAY = "enable_smart_delay"
        private const val FIELD_MONITOR_DURATION_SECONDS = "monitor_duration_seconds"
        private const val FIELD_MONITOR_INTERVAL_SECONDS = "monitor_interval_seconds"
        private const val FIELD_COOLDOWN_SECONDS = "cooldown_seconds"

        // 自适应配置字段
        private const val FIELD_ADAPTIVE_STRATEGY = "adaptive_strategy"
        private const val FIELD_ENABLE_MEMORY_PRESSURE_ADAPTATION = "enable_memory_pressure_adaptation"
        private const val FIELD_ENABLE_APP_ACTIVITY_ADAPTATION = "enable_app_activity_adaptation"
        private const val FIELD_MEMORY_ABUNDANT_FREQUENCY = "memory_abundant_frequency"
        private const val FIELD_MEMORY_TIGHT_FREQUENCY = "memory_tight_frequency"

        // 智能学习配置字段
        private const val FIELD_ENABLE_LEARNING = "enable_learning"
        private const val FIELD_MIN_SAMPLES_FOR_PREDICTION = "min_samples_for_prediction"
        private const val FIELD_CONFIDENCE_THRESHOLD = "confidence_threshold"
        private const val FIELD_STABILITY_CHECK_INTERVAL = "stability_check_interval"
        private const val FIELD_STABILITY_THRESHOLD_MB = "stability_threshold_mb"
        private const val FIELD_REQUIRED_STABLE_CHECKS = "required_stable_checks"
        private const val FIELD_MAX_HISTORY_RECORDS = "max_history_records"
        private const val FIELD_DATA_RETENTION_DAYS = "data_retention_days"

        // 混合模式配置字段
        private const val FIELD_ENABLE_EVENT_DRIVEN = "enable_event_driven"
        private const val FIELD_ENABLE_ADAPTIVE = "enable_adaptive"
        private const val FIELD_ENABLE_INTELLIGENT = "enable_intelligent"
        private const val FIELD_EVENT_DRIVEN_WEIGHT = "event_driven_weight"
        private const val FIELD_ADAPTIVE_WEIGHT = "adaptive_weight"
        private const val FIELD_INTELLIGENT_WEIGHT = "intelligent_weight"
    }

    override fun getConditionType() = "device_event"

    override fun save(condition: DeviceEventCondition): Boolean {
        Log.d(TAG, "开始保存设备事件条件: ${condition.id}")

        return try {
            val prefix = getPrefix(condition.id)
            val operations = mutableListOf<StorageOperation>()

            // 基础字段
            operations.addAll(saveBaseFields(condition.id, condition))

            // 设备事件条件特有字段
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_EVENT_TYPE}", condition.eventType.value, StorageType.STRING))

            // GPS状态参数
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_GPS_STATE_TYPE}", condition.gpsStateType.value, StorageType.STRING))

            // Logcat参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_LOGCAT_COMPONENT}", condition.logcatComponent, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_LOGCAT_TEXT}", condition.logcatText, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_LOGCAT_CASE_SENSITIVE}", condition.logcatCaseSensitive, StorageType.BOOLEAN)
            ))

            // 保存Logcat缓冲区类型列表
            operations.addAll(saveLogcatBufferTypesList("${prefix}${FIELD_LOGCAT_BUFFER_TYPES}", condition.logcatBufferTypes))

            // 剪贴板参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_CLIPBOARD_TEXT}", condition.clipboardText, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_CLIPBOARD_USE_REGEX}", condition.clipboardUseRegex, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_CLIPBOARD_CASE_SENSITIVE}", condition.clipboardCaseSensitive, StorageType.BOOLEAN)
            ))

            // 屏幕事件参数
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SCREEN_EVENT_TYPE}", condition.screenEventType.value, StorageType.STRING))

            // 底座状态参数
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_DOCK_STATE_TYPE}", condition.dockStateType.value, StorageType.STRING))

            // Intent参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTENT_ACTION}", condition.intentAction, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTENT_CATEGORY}", condition.intentExtraKey, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTENT_DATA_URI}", condition.intentExtraValue, StorageType.STRING)
            ))

            // SIM卡参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SIM_CARD_STATE_TYPE}", condition.simCardStateType.value, StorageType.STRING)
            ))

            // 系统设置参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SETTING_KEY}", condition.systemSettingKey, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SETTING_VALUE}", condition.systemSettingValue, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SETTING_TYPE}", condition.systemSettingTypes.firstOrNull()?.value ?: "", StorageType.STRING)
            ))

            // 通知事件参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_PACKAGE_NAME}", condition.notificationSelectedApps.joinToString(","), StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_TITLE}", condition.notificationTitle, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_TEXT}", condition.notificationText, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_SOUND_MODE}", condition.notificationSoundMode.value, StorageType.STRING)
            ))

            // 铃声模式参数
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_RINGER_MODE_TYPE}", condition.ringerModeType.value, StorageType.STRING))

            // 音乐播放参数
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MUSIC_PLAYBACK_TYPE}", condition.musicPlaybackType.value, StorageType.STRING))

            // 飞行模式参数
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_AIRPLANE_MODE_TYPE}", condition.airplaneModeType.value, StorageType.STRING))

            // 音量参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_STREAM_TYPE}", condition.volumeStreamType.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_THRESHOLD}", condition.volumeThreshold, StorageType.INT)
            ))

            // 内存状态参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_STATE_TYPE}", condition.memoryStateType.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_THRESHOLD}", condition.memoryThreshold, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_IS_PERCENTAGE_MODE}", condition.isPercentageMode, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_CHANGE_THRESHOLD}", condition.memoryChangeThreshold, StorageType.INT)
            ))

            // 检测频率参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_CHECK_FREQUENCY}", condition.checkFrequency.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY}", condition.enableCustomCheckFrequency, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_CUSTOM_CHECK_FREQUENCY_SECONDS}", condition.customCheckFrequencySeconds, StorageType.INT)
            ))

            // 内存检测模式参数
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_CHECK_MODE}", condition.memoryCheckMode.name, StorageType.STRING))

            // 事件驱动配置参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_ON_APP_FOREGROUND}", condition.triggerOnAppForeground, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_ON_APP_LAUNCH}", condition.triggerOnAppLaunch, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_ON_MEMORY_PRESSURE}", condition.triggerOnMemoryPressure, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_APP_LAUNCH_DELAY_SECONDS}", condition.appLaunchDelaySeconds, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_FOREGROUND_DELAY_SECONDS}", condition.foregroundDelaySeconds, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_SMART_DELAY}", condition.enableSmartDelay, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MONITOR_DURATION_SECONDS}", condition.monitorDurationSeconds, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MONITOR_INTERVAL_SECONDS}", condition.monitorIntervalSeconds, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_COOLDOWN_SECONDS}", condition.cooldownSeconds, StorageType.INT)
            ))

            // 自适应配置参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ADAPTIVE_STRATEGY}", condition.adaptiveStrategy.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_MEMORY_PRESSURE_ADAPTATION}", condition.enableMemoryPressureAdaptation, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_APP_ACTIVITY_ADAPTATION}", condition.enableAppActivityAdaptation, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_ABUNDANT_FREQUENCY}", condition.memoryAbundantFrequency, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_TIGHT_FREQUENCY}", condition.memoryTightFrequency, StorageType.INT)
            ))

            // 智能学习配置参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_LEARNING}", condition.enableLearning, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MIN_SAMPLES_FOR_PREDICTION}", condition.minSamplesForPrediction, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_CONFIDENCE_THRESHOLD}", condition.confidenceThreshold, StorageType.FLOAT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_STABILITY_CHECK_INTERVAL}", condition.stabilityCheckInterval, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_STABILITY_THRESHOLD_MB}", condition.stabilityThresholdMB, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_REQUIRED_STABLE_CHECKS}", condition.requiredStableChecks, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MAX_HISTORY_RECORDS}", condition.maxHistoryRecords, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_DATA_RETENTION_DAYS}", condition.dataRetentionDays, StorageType.INT)
            ))

            // 混合模式配置参数
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_EVENT_DRIVEN}", condition.enableEventDriven, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_ADAPTIVE}", condition.enableAdaptive, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_INTELLIGENT}", condition.enableIntelligent, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_EVENT_DRIVEN_WEIGHT}", condition.eventDrivenWeight, StorageType.FLOAT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_ADAPTIVE_WEIGHT}", condition.adaptiveWeight, StorageType.FLOAT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTELLIGENT_WEIGHT}", condition.intelligentWeight, StorageType.FLOAT)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "设备事件条件保存成功: ${condition.id}")
            } else {
                Log.e(TAG, "设备事件条件保存失败: ${condition.id}")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "保存设备事件条件时发生异常: ${condition.id}", e)
            false
        }
    }

    override fun load(conditionId: String): DeviceEventCondition? {
        Log.d(TAG, "开始加载设备事件条件: $conditionId")

        return try {
            val prefix = getPrefix(conditionId)

            // 检查条件是否存在
            val conditionType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}type")
            if (conditionType != getConditionType()) {
                Log.w(TAG, "条件类型不匹配或条件不存在: $conditionId, 期望: ${getConditionType()}, 实际: $conditionType")
                return null
            }

            // 加载事件类型
            val eventType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_EVENT_TYPE}").let {
                try {
                    DeviceEventType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的设备事件类型: $it, 使用默认值")
                    DeviceEventType.GPS_STATE
                }
            }

            // 加载GPS状态类型
            val gpsStateType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_GPS_STATE_TYPE}").let {
                try {
                    GpsStateType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的GPS状态类型: $it, 使用默认值")
                    GpsStateType.ENABLED
                }
            }

            // 加载Logcat参数
            val logcatComponent = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_LOGCAT_COMPONENT}")
            val logcatText = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_LOGCAT_TEXT}")
            val logcatCaseSensitive = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_LOGCAT_CASE_SENSITIVE}")
            val logcatBufferTypes = loadLogcatBufferTypesList("${prefix}${FIELD_LOGCAT_BUFFER_TYPES}")

            // 加载剪贴板参数
            val clipboardText = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_CLIPBOARD_TEXT}")
            val clipboardUseRegex = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_CLIPBOARD_USE_REGEX}")
            val clipboardCaseSensitive = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_CLIPBOARD_CASE_SENSITIVE}")

            // 加载屏幕事件类型
            val screenEventType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SCREEN_EVENT_TYPE}").let {
                try {
                    ScreenEventType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的屏幕事件类型: $it, 使用默认值")
                    ScreenEventType.ON
                }
            }

            // 加载底座状态类型
            val dockStateType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_DOCK_STATE_TYPE}").let {
                try {
                    DockStateType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的底座状态类型: $it, 使用默认值")
                    DockStateType.ANY_DOCK
                }
            }

            // 加载Intent参数
            val intentAction = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTENT_ACTION}")
            val intentExtraKey = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTENT_CATEGORY}")
            val intentExtraValue = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTENT_DATA_URI}")

            // 加载SIM卡参数
            val simCardStateType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SIM_CARD_STATE_TYPE}").let {
                try {
                    SimCardStateType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的SIM卡状态类型: $it, 使用默认值")
                    SimCardStateType.STATE_CHANGED
                }
            }

            // 加载系统设置参数
            val systemSettingKey = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SETTING_KEY}")
            val systemSettingValue = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SETTING_VALUE}")
            val systemSettingTypes = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SETTING_TYPE}").let {
                try {
                    if (it.isNotEmpty()) {
                        listOf(SystemSettingType.fromValue(it))
                    } else {
                        listOf(SystemSettingType.SYSTEM)
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "无效的系统设置类型: $it, 使用默认值")
                    listOf(SystemSettingType.SYSTEM)
                }
            }

            // 加载通知事件参数
            val notificationSelectedApps = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_PACKAGE_NAME}").let {
                if (it.isNotEmpty()) it.split(",") else emptyList()
            }
            val notificationTitle = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_TITLE}")
            val notificationText = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_TEXT}")
            val notificationSoundMode = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_NOTIFICATION_SOUND_MODE}").let {
                try {
                    NotificationSoundMode.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的通知声音模式: $it, 使用默认值")
                    NotificationSoundMode.ANY
                }
            }

            // 加载铃声模式类型
            val ringerModeType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_RINGER_MODE_TYPE}").let {
                try {
                    RingerModeType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的铃声模式类型: $it, 使用默认值")
                    RingerModeType.NORMAL
                }
            }

            // 加载音乐播放类型
            val musicPlaybackType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_MUSIC_PLAYBACK_TYPE}").let {
                try {
                    MusicPlaybackType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的音乐播放类型: $it, 使用默认值")
                    MusicPlaybackType.STARTED
                }
            }

            // 加载飞行模式类型
            val airplaneModeType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_AIRPLANE_MODE_TYPE}").let {
                try {
                    AirplaneModeType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的飞行模式类型: $it, 使用默认值")
                    AirplaneModeType.ENABLED
                }
            }

            // 加载音量参数
            val volumeStreamType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_STREAM_TYPE}").let { value ->
                try {
                    VolumeStreamType.valueOf(value)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的音量流类型: $value, 使用默认值")
                    VolumeStreamType.MEDIA_MUSIC
                }
            }
            val volumeThreshold = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_THRESHOLD}")

            // 加载内存状态参数
            val memoryStateType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_STATE_TYPE}").let {
                try {
                    MemoryStateType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的内存状态类型: $it, 使用默认值")
                    MemoryStateType.BELOW_THRESHOLD
                }
            }
            val memoryThreshold = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_THRESHOLD}")
            val isPercentageMode = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_IS_PERCENTAGE_MODE}")
            val memoryChangeThreshold = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_CHANGE_THRESHOLD}")

            // 加载检测频率参数
            val checkFrequency = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_CHECK_FREQUENCY}").let {
                try {
                    ConditionCheckFrequency.valueOf(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的检测频率: $it, 使用默认值")
                    ConditionCheckFrequency.BALANCED
                }
            }
            val enableCustomCheckFrequency = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY}")
            val customCheckFrequencySeconds = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_CUSTOM_CHECK_FREQUENCY_SECONDS}", 5)

            // 加载内存检测模式参数
            val memoryCheckMode = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_CHECK_MODE}").let {
                try {
                    MemoryCheckMode.valueOf(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的内存检测模式: $it, 使用默认值")
                    MemoryCheckMode.TRADITIONAL
                }
            }

            // 加载事件驱动配置参数
            val triggerOnAppForeground = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_ON_APP_FOREGROUND}", true)
            val triggerOnAppLaunch = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_ON_APP_LAUNCH}", true)
            val triggerOnMemoryPressure = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_ON_MEMORY_PRESSURE}", true)
            val appLaunchDelaySeconds = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_APP_LAUNCH_DELAY_SECONDS}", 10)
            val foregroundDelaySeconds = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_FOREGROUND_DELAY_SECONDS}", 5)
            val enableSmartDelay = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_SMART_DELAY}", true)
            val monitorDurationSeconds = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MONITOR_DURATION_SECONDS}", 30)
            val monitorIntervalSeconds = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MONITOR_INTERVAL_SECONDS}", 3)
            val cooldownSeconds = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_COOLDOWN_SECONDS}", 60)

            // 加载自适应配置参数
            val adaptiveStrategy = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_ADAPTIVE_STRATEGY}").let {
                try {
                    AdaptiveStrategy.valueOf(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的自适应策略: $it, 使用默认值")
                    AdaptiveStrategy.BALANCED
                }
            }
            val enableMemoryPressureAdaptation = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_MEMORY_PRESSURE_ADAPTATION}", true)
            val enableAppActivityAdaptation = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_APP_ACTIVITY_ADAPTATION}", true)
            val memoryAbundantFrequency = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_ABUNDANT_FREQUENCY}", 120)
            val memoryTightFrequency = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEMORY_TIGHT_FREQUENCY}", 5)

            // 加载智能学习配置参数
            val enableLearning = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_LEARNING}", true)
            val minSamplesForPrediction = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MIN_SAMPLES_FOR_PREDICTION}", 5)
            val confidenceThreshold = storageManager.loadFloat(StorageDomain.CONDITIONS, "${prefix}${FIELD_CONFIDENCE_THRESHOLD}", 0.7f)
            val stabilityCheckInterval = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_STABILITY_CHECK_INTERVAL}", 2)
            val stabilityThresholdMB = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_STABILITY_THRESHOLD_MB}", 50)
            val requiredStableChecks = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_REQUIRED_STABLE_CHECKS}", 3)
            val maxHistoryRecords = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MAX_HISTORY_RECORDS}", 30)
            val dataRetentionDays = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_DATA_RETENTION_DAYS}", 30)

            // 加载混合模式配置参数
            val enableEventDriven = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_EVENT_DRIVEN}", true)
            val enableAdaptive = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_ADAPTIVE}", true)
            val enableIntelligent = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_ENABLE_INTELLIGENT}", false)
            val eventDrivenWeight = storageManager.loadFloat(StorageDomain.CONDITIONS, "${prefix}${FIELD_EVENT_DRIVEN_WEIGHT}", 0.4f)
            val adaptiveWeight = storageManager.loadFloat(StorageDomain.CONDITIONS, "${prefix}${FIELD_ADAPTIVE_WEIGHT}", 0.4f)
            val intelligentWeight = storageManager.loadFloat(StorageDomain.CONDITIONS, "${prefix}${FIELD_INTELLIGENT_WEIGHT}", 0.2f)

            // 重建DeviceEventCondition对象
            val condition = DeviceEventCondition(
                id = conditionId,
                eventType = eventType,
                gpsStateType = gpsStateType,
                logcatComponent = logcatComponent,
                logcatText = logcatText,
                logcatBufferTypes = logcatBufferTypes,
                logcatCaseSensitive = logcatCaseSensitive,
                clipboardText = clipboardText,
                clipboardUseRegex = clipboardUseRegex,
                clipboardCaseSensitive = clipboardCaseSensitive,
                screenEventType = screenEventType,
                dockStateType = dockStateType,
                intentAction = intentAction,
                intentExtraKey = intentExtraKey,
                intentExtraValue = intentExtraValue,
                simCardStateType = simCardStateType,
                systemSettingTypes = systemSettingTypes,
                systemSettingKey = systemSettingKey,
                systemSettingValue = systemSettingValue,
                notificationSelectedApps = notificationSelectedApps,
                notificationTitle = notificationTitle,
                notificationText = notificationText,
                notificationSoundMode = notificationSoundMode,
                ringerModeType = ringerModeType,
                musicPlaybackType = musicPlaybackType,
                airplaneModeType = airplaneModeType,
                volumeStreamType = volumeStreamType,
                volumeThreshold = volumeThreshold,
                memoryStateType = memoryStateType,
                memoryThreshold = memoryThreshold,
                isPercentageMode = isPercentageMode,
                memoryChangeThreshold = memoryChangeThreshold,
                checkFrequency = checkFrequency,
                enableCustomCheckFrequency = enableCustomCheckFrequency,
                customCheckFrequencySeconds = customCheckFrequencySeconds,
                memoryCheckMode = memoryCheckMode,
                triggerOnAppForeground = triggerOnAppForeground,
                triggerOnAppLaunch = triggerOnAppLaunch,
                triggerOnMemoryPressure = triggerOnMemoryPressure,
                appLaunchDelaySeconds = appLaunchDelaySeconds,
                foregroundDelaySeconds = foregroundDelaySeconds,
                enableSmartDelay = enableSmartDelay,
                monitorDurationSeconds = monitorDurationSeconds,
                monitorIntervalSeconds = monitorIntervalSeconds,
                cooldownSeconds = cooldownSeconds,
                adaptiveStrategy = adaptiveStrategy,
                enableMemoryPressureAdaptation = enableMemoryPressureAdaptation,
                enableAppActivityAdaptation = enableAppActivityAdaptation,
                memoryAbundantFrequency = memoryAbundantFrequency,
                memoryTightFrequency = memoryTightFrequency,
                enableLearning = enableLearning,
                minSamplesForPrediction = minSamplesForPrediction,
                confidenceThreshold = confidenceThreshold,
                stabilityCheckInterval = stabilityCheckInterval,
                stabilityThresholdMB = stabilityThresholdMB,
                requiredStableChecks = requiredStableChecks,
                maxHistoryRecords = maxHistoryRecords,
                dataRetentionDays = dataRetentionDays,
                enableEventDriven = enableEventDriven,
                enableAdaptive = enableAdaptive,
                enableIntelligent = enableIntelligent,
                eventDrivenWeight = eventDrivenWeight,
                adaptiveWeight = adaptiveWeight,
                intelligentWeight = intelligentWeight
            )

            Log.d(TAG, "设备事件条件加载成功: $conditionId")
            condition

        } catch (e: Exception) {
            Log.e(TAG, "加载设备事件条件时发生异常: $conditionId", e)
            null
        }
    }

    /**
     * 保存Logcat缓冲区类型列表
     */
    private fun saveLogcatBufferTypesList(key: String, list: List<LogcatBufferType>): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        // 保存列表大小
        operations.add(StorageOperation(StorageDomain.CONDITIONS, "${key}_count", list.size, StorageType.INT))

        // 保存每个缓冲区类型
        list.forEachIndexed { index, bufferType ->
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${key}_${index}", bufferType.value, StorageType.STRING))
        }

        return operations
    }

    /**
     * 加载Logcat缓冲区类型列表
     */
    private fun loadLogcatBufferTypesList(key: String): List<LogcatBufferType> {
        val count = storageManager.loadInt(StorageDomain.CONDITIONS, "${key}_count")
        val list = mutableListOf<LogcatBufferType>()

        for (index in 0 until count) {
            val bufferTypeValue = storageManager.loadString(StorageDomain.CONDITIONS, "${key}_${index}")
            if (bufferTypeValue.isNotEmpty()) {
                try {
                    list.add(LogcatBufferType.fromValue(bufferTypeValue))
                } catch (e: Exception) {
                    Log.w(TAG, "无效的Logcat缓冲区类型: $bufferTypeValue")
                }
            }
        }

        return list
    }
}

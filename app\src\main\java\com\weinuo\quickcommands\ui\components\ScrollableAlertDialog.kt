package com.weinuo.quickcommands.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

/**
 * 纯ScrollableAlertDialog组件
 * 基于Dialog直接实现，内置滚动支持，无嵌套AlertDialog
 *
 * @param onDismissRequest 对话框关闭回调
 * @param confirmButton 确认按钮
 * @param modifier 修饰符
 * @param dismissButton 取消按钮
 * @param icon 图标
 * @param title 标题
 * @param text 内容
 * @param shape 形状
 * @param containerColor 容器颜色
 * @param iconContentColor 图标内容颜色
 * @param titleContentColor 标题内容颜色
 * @param textContentColor 文本内容颜色
 * @param tonalElevation 色调高度
 * @param properties 对话框属性
 * @param maxHeight 最大高度，超出时启用滚动（默认400dp）
 */
@Composable
fun ScrollableAlertDialog(
    onDismissRequest: () -> Unit,
    confirmButton: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    dismissButton: @Composable (() -> Unit)? = null,
    icon: @Composable (() -> Unit)? = null,
    title: @Composable (() -> Unit)? = null,
    text: @Composable (() -> Unit)? = null,
    shape: Shape = RoundedCornerShape(28.dp),
    containerColor: Color = AlertDialogDefaults.containerColor,
    iconContentColor: Color = AlertDialogDefaults.iconContentColor,
    titleContentColor: Color = AlertDialogDefaults.titleContentColor,
    textContentColor: Color = AlertDialogDefaults.textContentColor,
    tonalElevation: Dp = AlertDialogDefaults.TonalElevation,
    properties: DialogProperties = DialogProperties(),
    maxHeight: Dp = 400.dp
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = properties
    ) {
        Surface(
            modifier = modifier,
            shape = shape,
            color = containerColor,
            tonalElevation = tonalElevation,
        ) {
            Column(
                modifier = Modifier
                    .sizeIn(minWidth = 280.dp, maxWidth = 560.dp)
                    .padding(24.dp)
            ) {
                // 图标
                icon?.let { iconContent ->
                    CompositionLocalProvider(LocalContentColor provides iconContentColor) {
                        Box(
                            Modifier
                                .padding(bottom = 16.dp)
                                .align(Alignment.CenterHorizontally)
                        ) {
                            iconContent()
                        }
                    }
                }

                // 标题
                title?.let { titleContent ->
                    CompositionLocalProvider(LocalContentColor provides titleContentColor) {
                        val textStyle = MaterialTheme.typography.headlineSmall
                        ProvideTextStyle(textStyle) {
                            Box(
                                Modifier
                                    .padding(bottom = if (text != null) 16.dp else 0.dp)
                                    .align(
                                        if (icon == null) Alignment.Start
                                        else Alignment.CenterHorizontally
                                    )
                            ) {
                                titleContent()
                            }
                        }
                    }
                }

                // 可滚动的内容区域
                text?.let { textContent ->
                    CompositionLocalProvider(LocalContentColor provides textContentColor) {
                        val textStyle = MaterialTheme.typography.bodyMedium
                        ProvideTextStyle(textStyle) {
                            Column(
                                modifier = Modifier
                                    .weight(weight = 1f, fill = false)
                                    .heightIn(max = maxHeight)
                                    .padding(bottom = 24.dp)
                                    .verticalScroll(rememberScrollState())
                            ) {
                                textContent()
                            }
                        }
                    }
                }

                // 按钮区域
                Row(
                    horizontalArrangement = Arrangement.End,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    dismissButton?.let {
                        dismissButton()
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    confirmButton()
                }
            }
        }
    }
}

/**
 * 可滚动的AlertDialog组件（简化版本）
 * 直接接受字符串参数，适用于简单的文本对话框
 *
 * @param onDismissRequest 对话框关闭回调
 * @param title 标题文本
 * @param message 消息文本
 * @param confirmText 确认按钮文本
 * @param onConfirm 确认回调
 * @param dismissText 取消按钮文本（可选）
 * @param onDismiss 取消回调（可选）
 * @param maxHeight 最大高度，超出时启用滚动（默认400dp）
 */
@Composable
fun ScrollableAlertDialog(
    onDismissRequest: () -> Unit,
    title: String,
    message: String,
    confirmText: String,
    onConfirm: () -> Unit,
    dismissText: String? = null,
    onDismiss: (() -> Unit)? = null,
    maxHeight: Dp = 400.dp
) {
    ScrollableAlertDialog(
        onDismissRequest = onDismissRequest,
        title = { Text(text = title) },
        text = { Text(text = message) },
        confirmButton = {
            TextButton(onClick = onConfirm) {
                Text(confirmText)
            }
        },
        dismissButton = if (dismissText != null && onDismiss != null) {
            {
                TextButton(onClick = onDismiss) {
                    Text(dismissText)
                }
            }
        } else null,
        maxHeight = maxHeight
    )
}

/**
 * 纯可滚动的选择对话框
 * 基于Dialog直接实现，用于显示选项列表，支持滚动
 *
 * 注意：这个组件使用Column来正确渲染Composable列表
 * ScrollableAlertDialog内部已经有滚动容器，所以不会有嵌套滚动问题
 *
 * @param onDismissRequest 对话框关闭回调
 * @param title 标题
 * @param description 可选的描述文字，显示在标题下方
 * @param items 选项列表
 * @param onItemSelected 选项选择回调
 * @param maxHeight 最大高度，超出时启用滚动（默认400dp）
 */
@Composable
fun <T> ScrollableSelectionDialog(
    onDismissRequest: () -> Unit,
    title: String,
    description: String? = null,
    items: List<T>,
    onItemSelected: (T) -> Unit,
    itemContent: @Composable (T) -> Unit,
    maxHeight: Dp = 400.dp
) {
    ScrollableAlertDialog(
        onDismissRequest = onDismissRequest,
        title = { Text(text = title) },
        text = {
            Column {
                // 添加描述文字（如果提供）
                description?.let { desc ->
                    Text(
                        text = desc,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                }

                // 使用Column来正确渲染Composable列表
                items.forEach { item ->
                    itemContent(item)
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onDismissRequest) {
                Text("取消")
            }
        },
        maxHeight = maxHeight
    )
}

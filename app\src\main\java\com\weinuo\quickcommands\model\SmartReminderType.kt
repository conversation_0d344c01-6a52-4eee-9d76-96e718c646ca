package com.weinuo.quickcommands.model

import android.content.Context
import com.weinuo.quickcommands.R

/**
 * 智慧提醒类型枚举
 *
 * 定义了所有可用的智慧提醒功能类型。每个类型包含：
 * - 唯一标识符
 * - 本地化的标题和描述
 * - 配置需求类型
 * - 图标资源
 *
 * 设计原则：
 * - 高可扩展性：便于添加新的提醒类型
 * - 国际化支持：所有文本通过字符串资源管理
 * - 配置类型明确：清晰标识每种提醒的配置需求
 */
enum class SmartReminderType(
    val id: String,
    val titleResId: Int,
    val descriptionResId: Int,
    val configType: SmartReminderConfigType
) {

    /**
     * 屏幕旋转提醒
     * 检测到设备旋转时，建议用户开启屏幕旋转
     */
    SCREEN_ROTATION_REMINDER(
        id = "screen_rotation_reminder",
        titleResId = R.string.screen_rotation_reminder_title,
        descriptionResId = R.string.screen_rotation_reminder_description,
        configType = SmartReminderConfigType.NONE
    ),

    /**
     * 手电筒提醒
     * 检测到手电筒开启时，建议用户关闭手电筒
     */
    FLASHLIGHT_REMINDER(
        id = "flashlight_reminder",
        titleResId = R.string.flashlight_reminder_title,
        descriptionResId = R.string.flashlight_reminder_description,
        configType = SmartReminderConfigType.NONE
    ),

    /**
     * 打开新应用提醒
     * 检测到新应用安装时，建议用户打开新应用
     */
    NEW_APP_REMINDER(
        id = "new_app_reminder",
        titleResId = R.string.new_app_reminder_title,
        descriptionResId = R.string.new_app_reminder_description,
        configType = SmartReminderConfigType.NONE
    ),

    /**
     * 音乐应用提醒
     * 连接耳机时，建议打开音乐应用
     */
    MUSIC_APP_REMINDER(
        id = "music_app_reminder",
        titleResId = R.string.music_app_reminder_title,
        descriptionResId = R.string.music_app_reminder_description,
        configType = SmartReminderConfigType.APP_SELECTION
    ),

    /**
     * 购物应用提醒
     * 复制商品链接时，建议打开对应购物应用
     */
    SHOPPING_APP_REMINDER(
        id = "shopping_app_reminder",
        titleResId = R.string.shopping_app_reminder_title,
        descriptionResId = R.string.shopping_app_reminder_description,
        configType = SmartReminderConfigType.NONE
    ),

    /**
     * 应用链接提醒
     * 复制应用链接时，建议打开对应应用
     */
    APP_LINK_REMINDER(
        id = "app_link_reminder",
        titleResId = R.string.app_link_reminder_title,
        descriptionResId = R.string.app_link_reminder_description,
        configType = SmartReminderConfigType.NONE
    ),

    /**
     * 分享网址提醒
     * 复制网址链接时，建议分享网址
     */
    SHARE_URL_REMINDER(
        id = "share_url_reminder",
        titleResId = R.string.share_url_reminder_title,
        descriptionResId = R.string.share_url_reminder_description,
        configType = SmartReminderConfigType.NONE
    ),

    /**
     * 地址提醒
     * 复制地址时，建议打开地图应用
     */
    ADDRESS_REMINDER(
        id = "address_reminder",
        titleResId = R.string.address_reminder_title,
        descriptionResId = R.string.address_reminder_description,
        configType = SmartReminderConfigType.APP_SELECTION
    );

    /**
     * 获取本地化标题
     */
    fun getLocalizedTitle(context: Context): String {
        return context.getString(titleResId)
    }

    /**
     * 获取本地化描述
     */
    fun getLocalizedDescription(context: Context): String {
        return context.getString(descriptionResId)
    }

    companion object {
        /**
         * 根据ID获取智慧提醒类型
         */
        fun fromId(id: String): SmartReminderType? {
            return values().find { it.id == id }
        }

        /**
         * 获取所有可用的智慧提醒类型
         */
        fun getAllTypes(): List<SmartReminderType> {
            return values().toList()
        }
    }
}

/**
 * 智慧提醒配置类型
 *
 * 定义了不同智慧提醒功能的配置需求：
 * - NONE: 无需配置，可直接开启
 * - APP_SELECTION: 需要选择应用
 * - CUSTOM_SETTINGS: 需要自定义设置
 * - PERMISSION_REQUIRED: 需要权限配置
 */
enum class SmartReminderConfigType {
    /**
     * 无需配置，可直接开启
     */
    NONE,

    /**
     * 需要选择应用（如音乐应用提醒）
     */
    APP_SELECTION,

    /**
     * 需要自定义设置（如检测灵敏度、延迟时间等）
     */
    CUSTOM_SETTINGS,

    /**
     * 需要权限配置（如特殊权限申请）
     */
    PERMISSION_REQUIRED
}

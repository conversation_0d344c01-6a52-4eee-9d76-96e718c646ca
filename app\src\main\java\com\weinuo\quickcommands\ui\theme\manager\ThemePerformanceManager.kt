package com.weinuo.quickcommands.ui.theme.manager

import android.content.Context
import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.weinuo.quickcommands.ui.effects.HazeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 主题性能管理器
 * 
 * 负责协调主题系统的性能优化，确保：
 * 1. 应用在后台时最小化资源消耗
 * 2. 前台时提供最佳用户体验
 * 3. 智能管理缓存和内存使用
 */
class ThemePerformanceManager private constructor(
    private val context: Context
) : DefaultLifecycleObserver {
    
    companion object {
        private const val TAG = "ThemePerformanceManager"
        private const val BACKGROUND_CLEANUP_DELAY = 30000L // 30秒
        
        @Volatile
        private var INSTANCE: ThemePerformanceManager? = null
        
        fun getInstance(context: Context): ThemePerformanceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ThemePerformanceManager(context.applicationContext).also { 
                    INSTANCE = it
                    ProcessLifecycleOwner.get().lifecycle.addObserver(it)
                }
            }
        }
    }
    
    private val performanceScope = CoroutineScope(Dispatchers.Default)
    private var backgroundCleanupJob: Job? = null
    private var isAppInForeground = true
    
    // 管理的组件
    private lateinit var themeManager: ThemeManager
    private lateinit var hazeManager: HazeManager
    
    init {
        initializeManagers()
    }
    
    private fun initializeManagers() {
        performanceScope.launch {
            try {
                themeManager = ThemeManager.getInstance(context)
                hazeManager = HazeManager.getInstance(context)

                Log.d(TAG, "性能管理器初始化完成")
            } catch (e: Exception) {
                Log.e(TAG, "性能管理器初始化失败", e)
            }
        }
    }
    
    // ========== 生命周期回调 ==========
    
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        Log.d(TAG, "应用进入前台 - 启用性能优化")
        isAppInForeground = true
        
        // 取消后台清理任务
        backgroundCleanupJob?.cancel()
        backgroundCleanupJob = null
        
        // 通知各组件应用进入前台
        notifyForegroundState(true)
        
        // 预热关键组件
        performanceScope.launch {
            preWarmComponents()
        }
    }
    
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Log.d(TAG, "应用进入后台 - 启用后台优化")
        isAppInForeground = false
        
        // 通知各组件应用进入后台
        notifyForegroundState(false)
        
        // 启动后台清理任务
        scheduleBackgroundCleanup()
    }
    
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        Log.d(TAG, "应用销毁 - 清理所有资源")
        cleanup()
    }
    
    // ========== 性能优化方法 ==========
    
    /**
     * 通知前后台状态变化
     */
    private fun notifyForegroundState(isInForeground: Boolean) {
        try {
            // 新的Haze模糊系统不需要前后台状态通知
            // HazeManager会自动处理性能优化

            // 可以添加更多组件的状态通知

        } catch (e: Exception) {
            Log.e(TAG, "通知前后台状态失败", e)
        }
    }
    
    /**
     * 预热关键组件
     */
    private suspend fun preWarmComponents() {
        try {
            Log.d(TAG, "开始预热关键组件")
            
            // 预加载当前主题
            if (::themeManager.isInitialized) {
                themeManager.preloadThemes()
            }
            
            Log.d(TAG, "组件预热完成")
        } catch (e: Exception) {
            Log.e(TAG, "组件预热失败", e)
        }
    }
    
    /**
     * 安排后台清理任务
     */
    private fun scheduleBackgroundCleanup() {
        backgroundCleanupJob = performanceScope.launch {
            delay(BACKGROUND_CLEANUP_DELAY)
            
            if (!isAppInForeground) {
                performBackgroundCleanup()
            }
        }
    }
    
    /**
     * 执行后台清理
     */
    private suspend fun performBackgroundCleanup() {
        try {
            Log.d(TAG, "开始后台清理")
            
            // 清理主题缓存（保留当前主题）
            if (::themeManager.isInitialized) {
                val currentThemeId = themeManager.getCurrentThemeId()
                // 这里可以调用ThemeCache的智能清理方法
                // themeCache.performSmartCleanup(currentThemeId)
            }
            
            // 建议系统进行垃圾回收
            System.gc()

            // 记录后台清理
            ThemePerformanceMonitor.recordBackgroundCleanup()

            Log.d(TAG, "后台清理完成")
        } catch (e: Exception) {
            Log.e(TAG, "后台清理失败", e)
        }
    }
    
    /**
     * 获取性能统计信息
     */
    fun getPerformanceStats(): PerformanceStats {
        return PerformanceStats(
            isAppInForeground = isAppInForeground,
            backgroundCleanupActive = backgroundCleanupJob?.isActive == true,
            managersInitialized = ::themeManager.isInitialized &&
                                ::hazeManager.isInitialized
        )
    }
    
    /**
     * 手动触发性能优化
     */
    fun optimizePerformance() {
        if (isAppInForeground) {
            performanceScope.launch {
                preWarmComponents()
            }
        } else {
            performanceScope.launch {
                performBackgroundCleanup()
            }
        }
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            backgroundCleanupJob?.cancel()
            performanceScope.cancel()
            
            ProcessLifecycleOwner.get().lifecycle.removeObserver(this)
            
            Log.d(TAG, "性能管理器清理完成")
        } catch (e: Exception) {
            Log.e(TAG, "性能管理器清理失败", e)
        }
    }
}

/**
 * 性能统计数据类
 */
data class PerformanceStats(
    val isAppInForeground: Boolean,
    val backgroundCleanupActive: Boolean,
    val managersInitialized: Boolean
)

package com.weinuo.quickcommands.floating

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.utils.OverlayPermissionUtil

/**
 * 悬浮加速球管理器
 *
 * 统一管理悬浮加速球功能，提供简单的API接口。
 * 负责权限检查、服务管理和状态跟踪。
 */
object FloatingAcceleratorManager {

    private const val TAG = "FloatingAcceleratorManager"

    private var isFloatingAcceleratorActive = false

    /**
     * 启动悬浮加速球
     *
     * @param context 上下文
     * @return 是否成功启动
     */
    fun startFloatingAccelerator(context: Context): Boolean {
        Log.d(TAG, "尝试启动悬浮加速球")

        // 检查悬浮窗权限
        if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
            Log.w(TAG, "没有悬浮窗权限，无法启动悬浮加速球")
            return false
        }

        try {
            // 启动悬浮窗服务
            FloatingAcceleratorService.startFloatingWindow(context)
            isFloatingAcceleratorActive = true
            Log.d(TAG, "悬浮加速球启动成功")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "启动悬浮加速球失败", e)
            isFloatingAcceleratorActive = false
            return false
        }
    }

    /**
     * 停止悬浮加速球
     *
     * @param context 上下文
     * @return 是否成功停止
     */
    fun stopFloatingAccelerator(context: Context): Boolean {
        Log.d(TAG, "尝试停止悬浮加速球")

        try {
            // 停止悬浮窗服务
            FloatingAcceleratorService.stopFloatingWindow(context)
            isFloatingAcceleratorActive = false
            Log.d(TAG, "悬浮加速球停止成功")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "停止悬浮加速球失败", e)
            return false
        }
    }

    /**
     * 检查悬浮加速球是否正在运行
     *
     * @return 是否正在运行
     */
    fun isFloatingAcceleratorActive(): Boolean {
        return isFloatingAcceleratorActive
    }

    /**
     * 检查是否具备启动悬浮加速球的条件
     *
     * @param context 上下文
     * @return 检查结果
     */
    fun checkFloatingAcceleratorRequirements(context: Context): FloatingAcceleratorRequirement {
        // 检查悬浮窗权限
        if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
            return FloatingAcceleratorRequirement.OVERLAY_PERMISSION_REQUIRED
        }

        // 所有条件满足
        return FloatingAcceleratorRequirement.READY
    }

    /**
     * 请求必要的权限
     *
     * @param context 上下文
     * @param requirement 需要的权限
     */
    fun requestRequiredPermission(context: Context, requirement: FloatingAcceleratorRequirement) {
        when (requirement) {
            FloatingAcceleratorRequirement.OVERLAY_PERMISSION_REQUIRED -> {
                OverlayPermissionUtil.requestOverlayPermission(context)
            }
            FloatingAcceleratorRequirement.READY -> {
                // 无需请求权限
            }
        }
    }
}

/**
 * 悬浮加速球启动要求枚举
 */
enum class FloatingAcceleratorRequirement {
    /**
     * 需要悬浮窗权限
     */
    OVERLAY_PERMISSION_REQUIRED,

    /**
     * 准备就绪，可以启动
     */
    READY
}

package com.weinuo.quickcommands.execution

import android.app.ActivityManager
import android.app.AlarmManager
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.usage.UsageStatsManager
import android.app.WallpaperManager
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.hardware.camera2.CameraManager
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.media.RingtoneManager
import android.os.Vibrator
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.speech.RecognizerIntent
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.telephony.SmsManager
import android.text.Html
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.core.app.NotificationCompat
import androidx.core.content.FileProvider
import java.io.File
import java.io.IOException
import java.util.Locale
import com.weinuo.quickcommands.data.AppRepository
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.model.DateTimeTask
import com.weinuo.quickcommands.model.DateTimeTaskType
import com.weinuo.quickcommands.model.StopwatchOperation
import com.weinuo.quickcommands.model.StopwatchManager
import com.weinuo.quickcommands.model.QuickCommand
import com.weinuo.quickcommands.model.SharedTask
import com.weinuo.quickcommands.model.SharedTriggerCondition
import com.weinuo.quickcommands.model.CoordinateType

import com.weinuo.quickcommands.model.VolumeTask
import com.weinuo.quickcommands.model.VolumeOperation
import com.weinuo.quickcommands.model.SpeakerphoneOperation
import com.weinuo.quickcommands.model.VibrationModeType

import com.weinuo.quickcommands.model.DoNotDisturbMode
import com.weinuo.quickcommands.model.VolumeMode
import com.weinuo.quickcommands.model.VolumeAdjustOperation


import com.weinuo.quickcommands.model.ConnectivityTask
import com.weinuo.quickcommands.model.ConnectivityOperation
import com.weinuo.quickcommands.model.SwitchOperation

import com.weinuo.quickcommands.model.IntentTargetType
import com.weinuo.quickcommands.model.IntentParamType
import com.weinuo.quickcommands.model.PhoneTask
import com.weinuo.quickcommands.model.PhoneOperation
import com.weinuo.quickcommands.model.MakeCallType
import com.weinuo.quickcommands.model.AnswerCallDelayType
import com.weinuo.quickcommands.model.ClearCallLogType
import com.weinuo.quickcommands.model.MediaTask
import com.weinuo.quickcommands.model.MediaOperation
import com.weinuo.quickcommands.model.MultimediaControlType
import com.weinuo.quickcommands.model.MediaButtonType
import com.weinuo.quickcommands.model.AudioButtonType
import com.weinuo.quickcommands.model.PlayerControlType
import com.weinuo.quickcommands.model.SoundPlayType
import com.weinuo.quickcommands.model.AudioStreamType
import com.weinuo.quickcommands.model.RecordingDurationType
import com.weinuo.quickcommands.model.RecordingSource
import com.weinuo.quickcommands.model.RecordingFormat
import com.weinuo.quickcommands.model.ScreenControlTask
import com.weinuo.quickcommands.model.ScreenControlOperation
import com.weinuo.quickcommands.model.BrightnessControlType
import com.weinuo.quickcommands.model.ScreenOnOffOperation
import com.weinuo.quickcommands.model.ScreenDimnessSensorMode
import com.weinuo.quickcommands.model.KeepAwakeOperation
import com.weinuo.quickcommands.model.TouchBlockOperation
import com.weinuo.quickcommands.model.ForceRotationMode
import com.weinuo.quickcommands.model.ScreenTimeoutUnit
import com.weinuo.quickcommands.model.DeviceActionTask
import com.weinuo.quickcommands.model.DeviceActionOperation
import com.weinuo.quickcommands.model.FlashlightControlType
import com.weinuo.quickcommands.model.StatusBarOperation
import com.weinuo.quickcommands.model.TTSAudioStream
import com.weinuo.quickcommands.model.DeviceVibrationPattern
import com.weinuo.quickcommands.model.DeviceSettingsTask
import com.weinuo.quickcommands.model.DeviceSettingsOperation
import com.weinuo.quickcommands.model.TriStateOperation
import com.weinuo.quickcommands.model.SystemSettingsValueType
import com.weinuo.quickcommands.model.WallpaperType
import com.weinuo.quickcommands.model.WallpaperLocation
import com.weinuo.quickcommands.model.ImmersiveModeType
import com.weinuo.quickcommands.model.AmbientDisplayMode
import com.weinuo.quickcommands.model.NotificationTask
import com.weinuo.quickcommands.model.NotificationOperation
import com.weinuo.quickcommands.model.NotificationSoundType
import com.weinuo.quickcommands.model.NotificationClearMode
import com.weinuo.quickcommands.model.NotificationRestoreMode
import com.weinuo.quickcommands.model.LocationTask
import com.weinuo.quickcommands.model.LocationOperation
import com.weinuo.quickcommands.model.LocationShareMethod
import com.weinuo.quickcommands.model.LocationServiceControlMethod
import com.weinuo.quickcommands.model.LocationUpdateFrequencyUnit
import com.weinuo.quickcommands.model.FileOperationTask
import com.weinuo.quickcommands.model.FileOperation
import com.weinuo.quickcommands.model.FileWriteMode

import com.weinuo.quickcommands.model.FileOperationType
import com.weinuo.quickcommands.model.FileSelectionMode
import com.weinuo.quickcommands.model.CompressionLocation
import com.weinuo.quickcommands.model.CameraTask
import com.weinuo.quickcommands.model.CameraOperation
import com.weinuo.quickcommands.model.CameraType
import com.weinuo.quickcommands.model.VideoRecordingOperation
import com.weinuo.quickcommands.model.SaveLocation
import com.weinuo.quickcommands.model.InformationTask
import com.weinuo.quickcommands.model.InformationOperation
import com.weinuo.quickcommands.model.SimCardSelection
import com.weinuo.quickcommands.model.ApplicationTask
import com.weinuo.quickcommands.model.ApplicationOperation
import com.weinuo.quickcommands.model.ShellExecutionMode
import com.weinuo.quickcommands.model.ForceStopScope
import com.weinuo.quickcommands.model.CleanupStrategy
import com.weinuo.quickcommands.model.CleanupRule
import com.weinuo.quickcommands.model.CleanupRuleType
import com.weinuo.quickcommands.model.AppImportance
import com.weinuo.quickcommands.model.AppInfo
import com.weinuo.quickcommands.model.AppSortingStrategy
import com.weinuo.quickcommands.model.MemoryCheckFrequency
import com.weinuo.quickcommands.model.SimpleAppInfo
import com.weinuo.quickcommands.model.NetworkState
import com.weinuo.quickcommands.service.AlarmOverlayService
import com.weinuo.quickcommands.shizuku.ShizukuManager
import com.weinuo.quickcommands.utils.StoragePermissionUtil
import com.weinuo.quickcommands.utils.VibrationManager
import com.weinuo.quickcommands.utils.CommunicationPermissionUtil
import com.weinuo.quickcommands.utils.RingtoneHelper

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 共享执行处理器
 *
 * 此类负责执行任务序列，并在执行过程中检查中止条件。
 * 它为一键指令和条件指令提供统一的执行逻辑。
 */
class SharedExecutionHandler(private val context: Context) {
    private val TAG = "SharedExecutionHandler"
    private val executionScope = CoroutineScope(Dispatchers.Default + Job())
    private val settingsRepository = SettingsRepository(context)
    private val appRepository = AppRepository(context)
    private val conditionEvaluator = SharedConditionEvaluator(context)
    private val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager

    // 媒体播放器和录音器实例
    private var mediaPlayer: MediaPlayer? = null
    private var mediaRecorder: MediaRecorder? = null

    /**
     * 执行快捷指令（用于自动触发，会检查触发条件）
     *
     * @param command 要执行的快捷指令
     * @param triggerEventData 触发事件数据（可选）
     * @param onTaskStarted 任务开始执行时的回调
     * @param onTaskCompleted 任务完成时的回调
     * @param onExecutionCompleted 所有任务执行完成时的回调
     * @param onExecutionAborted 执行被中止时的回调
     */
    fun executeQuickCommand(
        command: QuickCommand,
        triggerEventData: Map<String, Any>? = null,
        onTaskStarted: ((SharedTask) -> Unit)? = null,
        onTaskCompleted: ((SharedTask, Boolean) -> Unit)? = null,
        onExecutionCompleted: (() -> Unit)? = null,
        onExecutionAborted: ((List<SharedTriggerCondition>) -> Unit)? = null
    ) {
        // 检查指令是否启用
        if (!command.isEnabled) {
            onExecutionCompleted?.invoke()
            return
        }

        // 检查是否在生效时间范围内
        if (!isCommandEffective(command)) {
            onExecutionCompleted?.invoke()
            return
        }

        executeTasks(
            tasks = command.tasks,
            abortConditions = command.abortConditions,
            requireAllAbortConditions = command.requireAllAbortConditions,
            commandName = command.name,
            triggerEventData = triggerEventData,
            onTaskStarted = onTaskStarted,
            onTaskCompleted = onTaskCompleted,
            onExecutionCompleted = onExecutionCompleted,
            onExecutionAborted = onExecutionAborted
        )
    }

    /**
     * 手动执行快捷指令（用于手动触发，跳过触发条件检查）
     * 适用于动态快捷方式、静态快捷方式、桌面小组件、悬浮按钮等手动触发场景
     *
     * @param command 要执行的快捷指令
     * @param onTaskStarted 任务开始执行时的回调
     * @param onTaskCompleted 任务完成时的回调
     * @param onExecutionCompleted 所有任务执行完成时的回调
     * @param onExecutionAborted 执行被中止时的回调
     */
    fun executeQuickCommandManually(
        command: QuickCommand,
        onTaskStarted: ((SharedTask) -> Unit)? = null,
        onTaskCompleted: ((SharedTask, Boolean) -> Unit)? = null,
        onExecutionCompleted: (() -> Unit)? = null,
        onExecutionAborted: ((List<SharedTriggerCondition>) -> Unit)? = null
    ) {
        // 检查指令是否启用
        if (!command.isEnabled) {
            onExecutionCompleted?.invoke()
            return
        }

        // 检查是否在生效时间范围内
        if (!isCommandEffective(command)) {
            onExecutionCompleted?.invoke()
            return
        }

        executeTasks(
            tasks = command.tasks,
            abortConditions = command.abortConditions,
            requireAllAbortConditions = command.requireAllAbortConditions,
            commandName = command.name,
            triggerEventData = null, // 手动执行时没有触发事件数据
            onTaskStarted = onTaskStarted,
            onTaskCompleted = onTaskCompleted,
            onExecutionCompleted = onExecutionCompleted,
            onExecutionAborted = onExecutionAborted
        )
    }

    /**
     * 执行任务序列
     *
     * @param tasks 要执行的任务列表
     * @param abortConditions 中止条件列表
     * @param requireAllAbortConditions 是否需要所有中止条件都满足才中止执行
     * @param commandName 指令名称（用于日志）
     * @param triggerEventData 触发事件数据（可选）
     * @param onTaskStarted 任务开始执行时的回调
     * @param onTaskCompleted 任务完成时的回调
     * @param onExecutionCompleted 所有任务执行完成时的回调
     * @param onExecutionAborted 执行被中止时的回调
     */
    private fun executeTasks(
        tasks: List<SharedTask>,
        abortConditions: List<SharedTriggerCondition>,
        requireAllAbortConditions: Boolean,
        commandName: String,
        triggerEventData: Map<String, Any>? = null,
        onTaskStarted: ((SharedTask) -> Unit)? = null,
        onTaskCompleted: ((SharedTask, Boolean) -> Unit)? = null,
        onExecutionCompleted: (() -> Unit)? = null,
        onExecutionAborted: ((List<SharedTriggerCondition>) -> Unit)? = null
    ) {
        if (tasks.isEmpty()) {
            onExecutionCompleted?.invoke()
            return
        }

        executionScope.launch {

            var successfulTasks = 0
            var failedTasks = 0

            // 执行任务序列
            for ((index, task) in tasks.withIndex()) {
                try {
                    // 检查中止条件
                    if (abortConditions.isNotEmpty()) {
                        val satisfiedConditions = conditionEvaluator.checkConditions(abortConditions)
                        if (shouldAbort(satisfiedConditions, requireAllAbortConditions, abortConditions.size)) {
                            onExecutionAborted?.invoke(satisfiedConditions)
                            return@launch
                        }
                    }

                    // 开始执行任务
                    onTaskStarted?.invoke(task)

                    // 执行任务
                    val success = executeTaskInternal(task, triggerEventData)

                    if (success) {
                        successfulTasks++
                    } else {
                        failedTasks++
                    }

                    // 任务完成回调
                    onTaskCompleted?.invoke(task, success)

                } catch (e: Exception) {
                    failedTasks++

                    // 通知任务完成（失败）
                    onTaskCompleted?.invoke(task, false)
                }
            }

            // 所有任务执行完成

            onExecutionCompleted?.invoke()
        }
    }

    /**
     * 执行单个任务（公共方法，供测试使用）
     *
     * @param task 要执行的任务
     * @param eventData 事件数据（可选）
     * @return 任务是否成功执行
     */
    suspend fun executeTask(task: SharedTask, eventData: Map<String, Any>? = null): Boolean {
        return executeTaskInternal(task, eventData)
    }

    /**
     * 执行单个任务（内部方法）
     *
     * @param task 要执行的任务
     * @param eventData 事件数据（可选）
     * @return 任务是否成功执行
     */
    private suspend fun executeTaskInternal(task: SharedTask, eventData: Map<String, Any>? = null): Boolean {
        return try {
            val result = when (task) {
                is VolumeTask -> executeVolumeTask(task)
                is DateTimeTask -> executeDateTimeTask(task)
                is ConnectivityTask -> executeConnectivityTask(task)
                is PhoneTask -> executePhoneTask(task)
                is MediaTask -> executeMediaTask(task)
                is ScreenControlTask -> executeScreenControlTask(task)
                is DeviceActionTask -> executeDeviceActionTask(task)
                is DeviceSettingsTask -> executeDeviceSettingsTask(task)
                is NotificationTask -> executeNotificationTask(task)
                is LocationTask -> executeLocationTask(task)
                is FileOperationTask -> executeFileOperationTask(task)
                is CameraTask -> executeCameraTask(task)
                is InformationTask -> executeInformationTask(task)
                is ApplicationTask -> executeApplicationTask(task, eventData) // 只有应用任务需要事件数据
                else -> {
                    false
                }
            }

            result

        } catch (e: Exception) {
            false
        }
    }



    /**
     * 执行音量任务
     *
     * @param task 音量任务
     * @return 任务是否成功执行
     */
    private suspend fun executeVolumeTask(task: VolumeTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

                val success = when (task.operation) {
                    VolumeOperation.SPEAKERPHONE_CONTROL -> executeSpeakerphoneControl(task, audioManager)
                    VolumeOperation.VIBRATION_MODE_CONTROL -> executeVibrationModeControl(task, audioManager)
                    VolumeOperation.SHOW_VOLUME_POPUP -> executeShowVolumePopup(task, audioManager)
                    VolumeOperation.DO_NOT_DISTURB_MODE -> executeDoNotDisturbMode(task, audioManager)
                    VolumeOperation.VOLUME_CHANGE -> executeVolumeChange(task, audioManager)
                    VolumeOperation.VOLUME_ADJUST -> executeVolumeAdjust(task, audioManager)
                }



                Log.d(TAG, "Volume task completed: operation=${task.operation.name}, success=$success")
                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行免提通话控制
     */
    private fun executeSpeakerphoneControl(task: VolumeTask, audioManager: AudioManager): Boolean {
        return try {
            when (task.speakerphoneOperation) {
                SpeakerphoneOperation.TURN_ON -> {
                    audioManager.isSpeakerphoneOn = true
                    Log.d(TAG, "Speakerphone turned on")
                }
                SpeakerphoneOperation.TURN_OFF -> {
                    audioManager.isSpeakerphoneOn = false
                    Log.d(TAG, "Speakerphone turned off")
                }
                SpeakerphoneOperation.TOGGLE -> {
                    audioManager.isSpeakerphoneOn = !audioManager.isSpeakerphoneOn
                    Log.d(TAG, "Speakerphone toggled to: ${audioManager.isSpeakerphoneOn}")
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error controlling speakerphone", e)
            false
        }
    }

    /**
     * 执行振动模式控制
     */
    private fun executeVibrationModeControl(task: VolumeTask, audioManager: AudioManager): Boolean {
        return try {
            when (task.vibrationModeType) {
                VibrationModeType.SILENT_WITH_VIBRATION -> {
                    audioManager.ringerMode = AudioManager.RINGER_MODE_VIBRATE
                    Log.d(TAG, "Set to silent with vibration mode")
                }
                VibrationModeType.NORMAL_WITHOUT_VIBRATION -> {
                    audioManager.ringerMode = AudioManager.RINGER_MODE_NORMAL
                    // 注意：这里可能需要额外的设置来禁用振动，具体取决于系统版本
                    Log.d(TAG, "Set to normal without vibration mode")
                }
                VibrationModeType.RING_WITH_VIBRATION -> {
                    audioManager.ringerMode = AudioManager.RINGER_MODE_NORMAL
                    // 启用响铃时振动（通常是默认行为）
                    Log.d(TAG, "Set to ring with vibration mode")
                }
                VibrationModeType.RING_WITHOUT_VIBRATION -> {
                    audioManager.ringerMode = AudioManager.RINGER_MODE_NORMAL
                    // 禁用响铃时振动
                    Log.d(TAG, "Set to ring without vibration mode")
                }
                VibrationModeType.RING_VIBRATION_TOGGLE -> {
                    // 切换响铃时振动设置
                    audioManager.ringerMode = AudioManager.RINGER_MODE_NORMAL
                    Log.d(TAG, "Toggled ring vibration setting")
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error controlling vibration mode", e)
            false
        }
    }

    /**
     * 执行显示音量弹出窗口
     */
    private fun executeShowVolumePopup(task: VolumeTask, audioManager: AudioManager): Boolean {
        return try {
            val streamType = task.volumePopupType.streamType
            val currentVolume = audioManager.getStreamVolume(streamType)

            // 显示音量弹出窗口（通过调整音量触发）
            audioManager.setStreamVolume(streamType, currentVolume, AudioManager.FLAG_SHOW_UI)

            Log.d(TAG, "Showed volume popup for stream: ${task.volumePopupType.displayName}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error showing volume popup", e)
            false
        }
    }



    /**
     * 执行勿扰模式控制
     */
    private fun executeDoNotDisturbMode(task: VolumeTask, audioManager: AudioManager): Boolean {
        return try {
            when (task.doNotDisturbMode) {
                DoNotDisturbMode.NORMAL -> {
                    audioManager.ringerMode = AudioManager.RINGER_MODE_NORMAL
                    Log.d(TAG, "Set to normal mode")
                }
                DoNotDisturbMode.CALLS_ONLY -> {
                    // 尝试使用NotificationManager设置勿扰模式
                    try {
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                            if (notificationManager.isNotificationPolicyAccessGranted) {
                                notificationManager.setInterruptionFilter(NotificationManager.INTERRUPTION_FILTER_PRIORITY)
                                Log.d(TAG, "Set to calls only mode using NotificationManager")
                            } else {
                                // 回退到基本的铃声模式设置
                                audioManager.ringerMode = AudioManager.RINGER_MODE_VIBRATE
                                Log.d(TAG, "Set to calls only mode (fallback to vibrate mode)")
                            }
                        } else {
                            audioManager.ringerMode = AudioManager.RINGER_MODE_VIBRATE
                            Log.d(TAG, "Set to calls only mode (older Android version)")
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not set calls only mode, using fallback", e)
                        audioManager.ringerMode = AudioManager.RINGER_MODE_VIBRATE
                    }
                }
                DoNotDisturbMode.COMPLETE_SILENCE -> {
                    audioManager.ringerMode = AudioManager.RINGER_MODE_SILENT

                    // 如果启用了禁用振动选项，尝试禁用振动
                    if (task.disableVibrationWhenSilent) {
                        try {
                            // 尝试通过系统设置禁用振动
                            val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
                            if (vibrator?.hasVibrator() == true) {
                                // 注意：这里只能取消当前的振动，无法完全禁用系统振动
                                // 完全禁用振动需要系统级权限
                                vibrator.cancel()
                                Log.d(TAG, "Cancelled current vibrations")
                            }
                        } catch (e: Exception) {
                            Log.w(TAG, "Could not disable vibration completely", e)
                        }
                    }

                    Log.d(TAG, "Set to complete silence mode${if (task.disableVibrationWhenSilent) " with vibration disabled" else ""}")
                }
                DoNotDisturbMode.ALARMS_ONLY -> {
                    // 尝试使用NotificationManager设置仅闹钟模式
                    try {
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                            if (notificationManager.isNotificationPolicyAccessGranted) {
                                notificationManager.setInterruptionFilter(NotificationManager.INTERRUPTION_FILTER_ALARMS)
                                Log.d(TAG, "Set to alarms only mode using NotificationManager")
                            } else {
                                // 回退到静音模式
                                audioManager.ringerMode = AudioManager.RINGER_MODE_SILENT
                                Log.d(TAG, "Set to alarms only mode (fallback to silent mode)")
                            }
                        } else {
                            audioManager.ringerMode = AudioManager.RINGER_MODE_SILENT
                            Log.d(TAG, "Set to alarms only mode (older Android version)")
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not set alarms only mode, using fallback", e)
                        audioManager.ringerMode = AudioManager.RINGER_MODE_SILENT
                    }
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error controlling do not disturb mode", e)
            false
        }
    }

    /**
     * 执行音量变化
     */
    private fun executeVolumeChange(task: VolumeTask, audioManager: AudioManager): Boolean {
        return try {
            val streamType = task.volumeStreamType.streamType
            val maxVolume = audioManager.getStreamMaxVolume(streamType)

            // 计算目标音量
            val targetVolume = when (task.volumeMode) {
                VolumeMode.PERCENTAGE -> {
                    // 百分比模式：将百分比转换为绝对值
                    (maxVolume * task.volumeValue / 100.0).toInt().coerceIn(0, maxVolume)
                }
                VolumeMode.ABSOLUTE -> {
                    // 绝对值模式：直接使用指定的值，但要确保不超过最大值
                    task.volumeValue.coerceIn(0, maxVolume)
                }
            }

            // 设置音量
            audioManager.setStreamVolume(streamType, targetVolume, 0)

            Log.d(TAG, "Volume changed: stream=${task.volumeStreamType.displayName}, mode=${task.volumeMode.displayName}, value=${task.volumeValue}, actual=$targetVolume/$maxVolume")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error changing volume", e)
            false
        }
    }

    /**
     * 执行音量调节
     */
    private fun executeVolumeAdjust(task: VolumeTask, audioManager: AudioManager): Boolean {
        return try {
            val streamType = task.volumeAdjustStreamType.streamType
            val direction = when (task.volumeAdjustOperation) {
                VolumeAdjustOperation.VOLUME_UP -> AudioManager.ADJUST_RAISE
                VolumeAdjustOperation.VOLUME_DOWN -> AudioManager.ADJUST_LOWER
            }

            // 调节音量
            audioManager.adjustStreamVolume(streamType, direction, 0)

            Log.d(TAG, "Volume adjusted: stream=${task.volumeAdjustStreamType.displayName}, operation=${task.volumeAdjustOperation.displayName}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adjusting volume", e)
            false
        }
    }







    /**
     * 获取当前前台应用包名
     */
    private fun getForegroundPackage(): String? {
        try {
            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 30 * 60 * 1000 // 30分钟前

            // 获取使用统计
            val stats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY, // 使用DAILY间隔获取更多数据
                startTime,
                endTime
            )

            if (stats.isNotEmpty()) {
                // 按最后使用时间排序，找出最近使用的应用
                val sortedApps = stats.sortedByDescending { it.lastTimeUsed }

                // 如果最近使用的应用在15分钟内有活动，认为它是前台应用
                val recentTimeThreshold = endTime - 15 * 60 * 1000 // 15分钟内
                val mostRecentApp = sortedApps.firstOrNull()

                if (mostRecentApp != null && mostRecentApp.lastTimeUsed >= recentTimeThreshold) {
                    return mostRecentApp.packageName
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting foreground package with UsageStats", e)
        }

        return null
    }



    /**
     * 检查是否有VPN应用激活（快捷指令专用版本）
     * 使用简化的VPN检测逻辑，不依赖已删除的主功能字段
     */
    private fun hasVpnActive(apps: List<com.weinuo.quickcommands.model.AppInfo>): Boolean {
        // 简化的VPN检测：检查包名或应用名包含"vpn"关键字的应用是否在运行
        return apps.any { app ->
            (app.packageName.lowercase().contains("vpn") ||
             app.appName.lowercase().contains("vpn")) &&
            isAppCurrentlyRunning(app.packageName)
        }
    }

    /**
     * 检查应用是否正在运行（快捷指令专用版本）
     * 使用UsageStatsManager检测应用运行状态
     */
    private fun isAppCurrentlyRunning(packageName: String): Boolean {
        return try {
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val currentTime = System.currentTimeMillis()
            val startTime = currentTime - 15 * 60 * 1000 // 15分钟内

            val stats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                currentTime
            )

            val appStat = stats.find { it.packageName == packageName }
            appStat != null && appStat.lastTimeUsed >= startTime
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if app is running: $packageName", e)
            false
        }
    }

    /**
     * 检查是否应该中止执行
     *
     * @param satisfiedConditions 满足的条件列表
     * @param requireAllConditions 是否需要所有条件都满足
     * @param totalConditions 条件总数
     * @return 是否应该中止执行
     */
    private fun shouldAbort(
        satisfiedConditions: List<SharedTriggerCondition>,
        requireAllConditions: Boolean,
        totalConditions: Int
    ): Boolean {
        if (satisfiedConditions.isEmpty()) {
            return false
        }

        return if (requireAllConditions) {
            // 所有条件都满足时中止
            satisfiedConditions.size == totalConditions
        } else {
            // 任一条件满足时中止
            satisfiedConditions.isNotEmpty()
        }
    }

    /**
     * 检查快捷指令是否在生效时间范围内
     *
     * @param command 快捷指令
     * @return 是否在生效时间范围内
     */
    private fun isCommandEffective(command: QuickCommand): Boolean {
        // 如果设置为全天生效，直接返回true
        if (command.isAllDayEffective) {
            return true
        }

        // 获取当前时间
        val calendar = java.util.Calendar.getInstance()
        val currentHour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
        val currentMinute = calendar.get(java.util.Calendar.MINUTE)
        val currentTimeMinutes = currentHour * 60 + currentMinute

        // 解析生效时间范围
        val startTimeParts = command.effectiveStartTime.split(":")
        val endTimeParts = command.effectiveEndTime.split(":")

        if (startTimeParts.size != 2 || endTimeParts.size != 2) {
            Log.e(TAG, "Invalid time format in command ${command.name}")
            return false
        }

        val startHour = startTimeParts[0].toIntOrNull() ?: 0
        val startMinute = startTimeParts[1].toIntOrNull() ?: 0
        val startTimeMinutes = startHour * 60 + startMinute

        val endHour = endTimeParts[0].toIntOrNull() ?: 23
        val endMinute = endTimeParts[1].toIntOrNull() ?: 59
        val endTimeMinutes = endHour * 60 + endMinute

        // 检查当前时间是否在生效时间范围内
        return if (endTimeMinutes >= startTimeMinutes) {
            // 正常情况：开始时间 <= 结束时间
            currentTimeMinutes in startTimeMinutes..endTimeMinutes
        } else {
            // 跨天情况：开始时间 > 结束时间
            currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes
        }
    }





    /**
     * 执行日期与时间任务
     *
     * @param task 日期与时间任务
     * @return 任务是否成功执行
     */
    private suspend fun executeDateTimeTask(task: DateTimeTask): Boolean {
        return try {
            when (task.taskType) {
                DateTimeTaskType.STOPWATCH -> executeStopwatchTask(task)
                DateTimeTaskType.ALARM -> executeAlarmTask(task)
                DateTimeTaskType.SYSTEM_ALARM_RINGTONE -> executeSystemAlarmRingtoneTask(task)
                DateTimeTaskType.VOICE_TIME_ANNOUNCEMENT -> executeVoiceTimeAnnouncementTask(task)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing date time task", e)



            false
        }
    }

    /**
     * 执行秒表任务
     *
     * @param task 日期与时间任务（秒表类型）
     * @return 任务是否成功执行
     */
    private suspend fun executeStopwatchTask(task: DateTimeTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val stopwatchId = if (task.stopwatchId.isNotEmpty()) {
                    task.stopwatchId
                } else if (task.stopwatchName.isNotEmpty()) {
                    // 创建新秒表
                    val newStopwatch = StopwatchManager.createStopwatch(context, task.stopwatchName)
                    newStopwatch.id
                } else {
                    Log.e(TAG, "No stopwatch ID or name provided")
                    return@withContext false
                }

                val success = when (task.stopwatchOperation) {
                    StopwatchOperation.START -> StopwatchManager.startStopwatch(context, stopwatchId)
                    StopwatchOperation.PAUSE -> StopwatchManager.pauseStopwatch(context, stopwatchId)
                    StopwatchOperation.STOP -> StopwatchManager.stopStopwatch(context, stopwatchId)
                    StopwatchOperation.LAP -> StopwatchManager.lapStopwatch(context, stopwatchId)
                    StopwatchOperation.RESET -> StopwatchManager.resetStopwatch(context, stopwatchId)
                    StopwatchOperation.RESET_AND_START -> StopwatchManager.resetAndStartStopwatch(context, stopwatchId)
                }

                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行闹钟任务
     *
     * @param task 日期与时间任务（闹钟类型）
     * @return 任务是否成功执行
     */
    private suspend fun executeAlarmTask(task: DateTimeTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 权限检查已在配置对话框初始化时进行，此处直接执行任务

                when (task.alarmTimeType) {
                    com.weinuo.quickcommands.model.AlarmTimeType.ABSOLUTE -> {
                        // 固定时间闹钟：检查是否有重复天数设置
                        if (task.alarmDays.isEmpty()) {
                            // 无重复天数：立即启动闹钟悬浮窗服务
                            startAlarmOverlayService(task)
                        } else {
                            // 有重复天数：使用AlarmManager安排重复闹钟
                            scheduleAbsoluteAlarm(task)
                        }
                    }
                    com.weinuo.quickcommands.model.AlarmTimeType.RELATIVE -> {
                        // 相对时间闹钟：使用AlarmManager延迟启动
                        scheduleRelativeAlarm(task)
                    }
                }



                Log.d(TAG, "Alarm task started: timeType=${task.alarmTimeType}, requireTask=${task.requireTask}, enableSound=${task.enableSound}, message='${task.reminderMessage}'")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing alarm task", e)



                false
            }
        }
    }

    /**
     * 启动闹钟悬浮窗服务
     *
     * @param task 闹钟任务
     */
    private fun startAlarmOverlayService(task: DateTimeTask) {
        val intent = Intent(context, AlarmOverlayService::class.java).apply {
            putExtra("requireTask", task.requireTask)
            putExtra("enableSound", task.enableSound)
            putExtra("reminderMessage", task.reminderMessage)
            putExtra("enableVibration", task.enableVibration)
            putExtra("vibrationMode", task.vibrationMode)
            putExtra("vibrationIntensity", task.vibrationIntensity)
            putExtra("clicksRequired", task.clicksRequired)
            putExtra("selectedRingtoneUri", task.selectedRingtoneUri)
            putExtra("selectedRingtoneName", task.selectedRingtoneName)
        }
        context.startService(intent)
    }

    /**
     * 安排绝对时间重复闹钟
     *
     * @param task 闹钟任务
     */
    private fun scheduleAbsoluteAlarm(task: DateTimeTask) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        // 计算今天的闹钟时间
        val calendar = java.util.Calendar.getInstance().apply {
            set(java.util.Calendar.HOUR_OF_DAY, task.alarmHour)
            set(java.util.Calendar.MINUTE, task.alarmMinute)
            set(java.util.Calendar.SECOND, 0)
            set(java.util.Calendar.MILLISECOND, 0)
        }

        // 如果今天的时间已经过了，从明天开始
        if (calendar.timeInMillis <= System.currentTimeMillis()) {
            calendar.add(java.util.Calendar.DAY_OF_MONTH, 1)
        }

        // 为每个选中的星期几设置闹钟
        task.alarmDays.forEach { dayOfWeek ->
            val alarmCalendar = calendar.clone() as java.util.Calendar

            // 找到下一个指定的星期几
            val currentDayOfWeek = alarmCalendar.get(java.util.Calendar.DAY_OF_WEEK) - 1 // 转换为0-6
            val targetDayOfWeek = dayOfWeek

            var daysToAdd = targetDayOfWeek - currentDayOfWeek
            if (daysToAdd < 0) {
                daysToAdd += 7
            }

            alarmCalendar.add(java.util.Calendar.DAY_OF_MONTH, daysToAdd)

            // 创建PendingIntent
            val intent = Intent(context, AlarmOverlayService::class.java).apply {
                putExtra("requireTask", task.requireTask)
                putExtra("enableSound", task.enableSound)
                putExtra("reminderMessage", task.reminderMessage)
                putExtra("enableVibration", task.enableVibration)
                putExtra("vibrationMode", task.vibrationMode)
                putExtra("vibrationIntensity", task.vibrationIntensity)
                putExtra("clicksRequired", task.clicksRequired)
                putExtra("selectedRingtoneUri", task.selectedRingtoneUri)
                putExtra("selectedRingtoneName", task.selectedRingtoneName)
            }

            val pendingIntent = PendingIntent.getService(
                context,
                (task.id.hashCode() + dayOfWeek), // 使用任务ID+星期几作为唯一请求码
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 设置重复闹钟
            try {
                alarmManager.setRepeating(
                    AlarmManager.RTC_WAKEUP,
                    alarmCalendar.timeInMillis,
                    AlarmManager.INTERVAL_DAY * 7, // 每周重复
                    pendingIntent
                )

                Log.d(TAG, "Absolute alarm scheduled for day $dayOfWeek: ${alarmCalendar.time}")
            } catch (e: Exception) {
                Log.e(TAG, "Error scheduling absolute alarm for day $dayOfWeek", e)
            }
        }
    }

    /**
     * 安排相对时间闹钟
     *
     * @param task 闹钟任务
     */
    private fun scheduleRelativeAlarm(task: DateTimeTask) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        // 计算触发时间
        val delayMillis = (task.alarmRelativeHours * 60 * 60 * 1000L) + (task.alarmRelativeMinutes * 60 * 1000L)
        val triggerTime = System.currentTimeMillis() + delayMillis

        // 创建PendingIntent来启动闹钟服务
        val intent = Intent(context, AlarmOverlayService::class.java).apply {
            putExtra("requireTask", task.requireTask)
            putExtra("enableSound", task.enableSound)
            putExtra("reminderMessage", task.reminderMessage)
            putExtra("enableVibration", task.enableVibration)
            putExtra("vibrationMode", task.vibrationMode)
            putExtra("vibrationIntensity", task.vibrationIntensity)
            putExtra("clicksRequired", task.clicksRequired)
            putExtra("selectedRingtoneUri", task.selectedRingtoneUri)
            putExtra("selectedRingtoneName", task.selectedRingtoneName)
        }

        val pendingIntent = PendingIntent.getService(
            context,
            task.id.hashCode(), // 使用任务ID的hashCode作为请求码
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 设置精确闹钟
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            }

            Log.d(TAG, "Relative alarm scheduled: delay=${delayMillis}ms, triggerTime=$triggerTime")
        } catch (e: Exception) {
            Log.e(TAG, "Error scheduling relative alarm", e)
            // 如果设置闹钟失败，立即启动服务作为备用方案
            startAlarmOverlayService(task)
        }
    }

    /**
     * 执行系统闹钟铃声设置任务
     *
     * @param task 日期与时间任务（系统闹钟铃声类型）
     * @return 任务是否成功执行
     */
    private suspend fun executeSystemAlarmRingtoneTask(task: DateTimeTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查铃声URI是否有效
                if (task.selectedRingtoneUri.isEmpty()) {
                    Log.e(TAG, "No ringtone URI provided for system alarm ringtone task")
                    return@withContext false
                }

                // 使用RingtoneHelper设置系统闹钟铃声
                val success = RingtoneHelper.setRingtone(
                    context = context,
                    ringtoneUri = task.selectedRingtoneUri,
                    ringtoneType = RingtoneHelper.RingtoneType.ALARM
                )

                if (success) {

                    Log.d(TAG, "System alarm ringtone set successfully: ${task.selectedRingtoneName}")
                } else {
                    Log.e(TAG, "Failed to set system alarm ringtone: ${task.selectedRingtoneName}")
                }

                success
            } catch (e: Exception) {
                Log.e(TAG, "Error setting system alarm ringtone", e)



                false
            }
        }
    }

    /**
     * 执行语音报时任务
     *
     * @param task 日期与时间任务（语音报时类型）
     * @return 任务是否成功执行
     */
    private suspend fun executeVoiceTimeAnnouncementTask(task: DateTimeTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 获取当前时间
                val calendar = java.util.Calendar.getInstance()
                val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
                val minute = calendar.get(java.util.Calendar.MINUTE)

                // 根据时间格式生成语音文本
                val timeText = if (task.voiceTimeFormat == "12") {
                    // 12小时制
                    val hour12 = if (hour == 0) 12 else if (hour > 12) hour - 12 else hour
                    val amPm = if (hour < 12) "上午" else "下午"
                    when (task.voiceLanguage) {
                        "en-US" -> {
                            val amPmEn = if (hour < 12) "AM" else "PM"
                            "Current time is $hour12:${minute.toString().padStart(2, '0')} $amPmEn"
                        }
                        else -> "$amPm${hour12}点${minute}分"
                    }
                } else {
                    // 24小时制
                    when (task.voiceLanguage) {
                        "en-US" -> "Current time is $hour:${minute.toString().padStart(2, '0')}"
                        else -> "现在时间是${hour}点${minute}分"
                    }
                }

                // 创建TTS实例并播报时间
                var ttsInstance: android.speech.tts.TextToSpeech? = null
                ttsInstance = android.speech.tts.TextToSpeech(context) { status ->
                    if (status == android.speech.tts.TextToSpeech.SUCCESS) {
                        try {
                            ttsInstance?.let { tts ->
                                // 设置语言
                                val locale = when (task.voiceLanguage) {
                                    "zh-CN" -> java.util.Locale.SIMPLIFIED_CHINESE
                                    "en-US" -> java.util.Locale.US
                                    else -> java.util.Locale.getDefault()
                                }

                                val langResult = tts.setLanguage(locale)
                                if (langResult == android.speech.tts.TextToSpeech.LANG_MISSING_DATA ||
                                    langResult == android.speech.tts.TextToSpeech.LANG_NOT_SUPPORTED) {
                                    Log.w(TAG, "Language not supported: ${task.voiceLanguage}, using default")
                                }

                                // 设置音调和语速
                                tts.setPitch(task.voicePitch)
                                tts.setSpeechRate(task.voiceSpeed)

                                // 设置音频属性
                                val audioAttributes = android.media.AudioAttributes.Builder()
                                    .setUsage(
                                        when (task.voiceAudioStream) {
                                            "ALARM" -> android.media.AudioAttributes.USAGE_ALARM
                                            "NOTIFICATION" -> android.media.AudioAttributes.USAGE_NOTIFICATION
                                            else -> android.media.AudioAttributes.USAGE_MEDIA
                                        }
                                    )
                                    .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                                    .build()

                                tts.setAudioAttributes(audioAttributes)

                                // 播报时间
                                val result = tts.speak(timeText, android.speech.tts.TextToSpeech.QUEUE_FLUSH, null, "voice_time_announcement")

                                if (result == android.speech.tts.TextToSpeech.SUCCESS) {
                                    Log.d(TAG, "Voice time announcement initiated: $timeText")

                                    // 延迟关闭TTS，给语音播报一些时间
                                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                        tts.shutdown()
                                    }, 5000) // 5秒后关闭
                                } else {
                                    Log.e(TAG, "Voice time announcement failed: result=$result")
                                    tts.shutdown()
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error in TTS initialization callback for voice time announcement", e)
                            ttsInstance?.shutdown()
                        }
                    } else {
                        Log.e(TAG, "TTS initialization failed for voice time announcement: status=$status")
                    }
                }

                Log.d(TAG, "Voice time announcement TTS initialization started")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing voice time announcement", e)
                false
            }
        }
    }

    /**
     * 获取正在播放音乐的应用包名集合
     *
     * 通过媒体会话检测正在播放音乐的应用
     * 使用dumpsys media_session命令检查媒体会话状态
     *
     * @return 正在播放音乐的应用包名集合，如果没有则返回空集合
     */
    private suspend fun getMusicPlayingPackages(): Set<String> = withContext(Dispatchers.IO) {
        try {
            // 使用dumpsys media_session命令检查媒体会话状态
            val result = ShizukuManager.executeCommand("dumpsys media_session")

            if (result.isNotEmpty()) {
                // 解析媒体会话输出，提取正在播放音乐的应用包名
                val packages = parseMediaSessionPackages(result)

                Log.d(TAG, "Music playing packages: ${packages.joinToString(", ")}")
                return@withContext packages
            } else {
                Log.w(TAG, "Empty result from dumpsys media_session command")
                return@withContext emptySet()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting music playing packages", e)
            return@withContext emptySet()
        }
    }

    /**
     * 解析媒体会话输出，提取正在播放音乐的应用包名
     *
     * @param output dumpsys media_session命令的输出
     * @return 正在播放音乐的应用包名集合
     */
    private fun parseMediaSessionPackages(output: String): Set<String> {
        val packages = mutableSetOf<String>()

        try {
            val lines = output.split("\n")
            var currentPackage: String? = null
            var isPlaying = false

            for (line in lines) {
                val trimmedLine = line.trim()

                // 查找包名
                if (trimmedLine.startsWith("package=")) {
                    currentPackage = trimmedLine.substringAfter("package=").trim()
                    isPlaying = false
                }

                // 查找播放状态
                if (trimmedLine.contains("state=3") || // STATE_PLAYING
                    trimmedLine.contains("playing") ||
                    trimmedLine.contains("PLAYING")) {
                    isPlaying = true
                }

                // 如果找到了包名和播放状态，添加到结果中
                if (currentPackage != null && isPlaying) {
                    packages.add(currentPackage)
                    Log.d(TAG, "Added music playing package: $currentPackage")
                    currentPackage = null
                    isPlaying = false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing media session output", e)
        }

        return packages
    }



    /**
     * 执行连接任务
     *
     * @param task 连接任务
     * @return 任务是否成功执行
     */
    private suspend fun executeConnectivityTask(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 执行时权限检查：蓝牙/位置权限检查（针对需要权限的操作）
                when (task.operation) {
                    ConnectivityOperation.WIFI_CONTROL,
                    ConnectivityOperation.HOTSPOT_CONTROL,
                    ConnectivityOperation.MOBILE_DATA_CONTROL,
                    ConnectivityOperation.AIRPLANE_MODE_CONTROL -> {
                        if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                            Log.e(TAG, "Location permission not granted for connectivity task execution")

                            return@withContext false
                        }
                    }

                    ConnectivityOperation.AUTO_SYNC_CONTROL,
                    ConnectivityOperation.SYNC_ACCOUNT -> {
                        if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
                            Log.e(TAG, "Communication permissions not granted for connectivity task execution")

                            return@withContext false
                        }
                    }
                    ConnectivityOperation.SEND_INTENT,
                    ConnectivityOperation.CHECK_CONNECTION -> {
                        // 这些操作不需要特殊权限检查
                    }
                    ConnectivityOperation.NETWORK_STATE_SAVE,
                    ConnectivityOperation.NETWORK_STATE_RESTORE -> {
                        // 网络状态操作需要位置权限来检测网络状态
                        if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                            return@withContext false
                        }
                    }
                }

                val success = when (task.operation) {
                    ConnectivityOperation.WIFI_CONTROL -> executeWifiControl(task)
                    ConnectivityOperation.HOTSPOT_CONTROL -> executeHotspotControl(task)
                    ConnectivityOperation.MOBILE_DATA_CONTROL -> executeMobileDataControl(task)
                    ConnectivityOperation.AUTO_SYNC_CONTROL -> executeAutoSyncControl(task)
                    ConnectivityOperation.AIRPLANE_MODE_CONTROL -> executeAirplaneModeControl(task)
                    ConnectivityOperation.SEND_INTENT -> executeSendIntent(task)
                    ConnectivityOperation.SYNC_ACCOUNT -> executeSyncAccount(task)
                    ConnectivityOperation.CHECK_CONNECTION -> executeCheckConnection(task)
                    ConnectivityOperation.NETWORK_STATE_SAVE -> executeNetworkStateSave(task)
                    ConnectivityOperation.NETWORK_STATE_RESTORE -> executeNetworkStateRestore(task)
                }

                // 处理waitForCompletion逻辑
                if (success && task.waitForCompletion &&
                    (task.operation == ConnectivityOperation.CHECK_CONNECTION ||
                     task.operation == ConnectivityOperation.SYNC_ACCOUNT)) {

                    // 对于需要等待完成的操作，添加额外的等待时间
                    when (task.operation) {
                        ConnectivityOperation.CHECK_CONNECTION -> {
                            // 连接检查已经在executeCheckConnection中处理了等待
                            Log.d(TAG, "Connection check completed with waitForCompletion")
                        }
                        ConnectivityOperation.SYNC_ACCOUNT -> {
                            // 账号同步需要额外等待时间确保同步完成
                            delay(2000) // 等待2秒确保同步操作完成
                            Log.d(TAG, "Account sync completed with waitForCompletion")
                        }
                        else -> { /* 其他操作不需要额外等待 */ }
                    }
                }

                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行WiFi控制
     */
    private suspend fun executeWifiControl(task: ConnectivityTask): Boolean {
        return try {
            // 检查位置权限（WiFi SSID检测可能需要）
            if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                Log.e(TAG, "Location permissions not granted for WiFi control")

                return false
            }

            val command = when (task.wifiOperation) {
                SwitchOperation.TOGGLE -> {
                    // 切换模式：检测当前WiFi状态并执行相反操作
                    val currentWifiEnabled = isWifiEnabled()
                    if (currentWifiEnabled) {
                        "svc wifi disable"
                    } else {
                        "svc wifi enable"
                    }
                }
                SwitchOperation.ENABLE -> "svc wifi enable"
                SwitchOperation.DISABLE -> "svc wifi disable"
            }

            // 执行Shizuku命令
            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "WiFi control completed: operation=${task.wifiOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing WiFi control", e)
            false
        }
    }

    /**
     * 执行热点控制
     */
    private suspend fun executeHotspotControl(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检查位置权限（热点控制可能需要）
                if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                    Log.e(TAG, "Location permissions not granted for hotspot control")

                    return@withContext false
                }

                val command = when (task.hotspotOperation) {
                    SwitchOperation.TOGGLE -> {
                        // 切换模式：检测当前热点状态并执行相反操作
                        val currentHotspotEnabled = isHotspotEnabled()
                        if (currentHotspotEnabled) {
                            "cmd wifi set-wifi-ap-enabled false"
                        } else {
                            "cmd wifi set-wifi-ap-enabled true"
                        }
                    }
                    SwitchOperation.ENABLE -> "cmd wifi set-wifi-ap-enabled true"
                    SwitchOperation.DISABLE -> "cmd wifi set-wifi-ap-enabled false"
                }

                // 执行Shizuku命令
                val result = ShizukuManager.executeCommand(command)
                val success = !result.contains("Error:") && !result.contains("错误")

                Log.d(TAG, "Hotspot control completed: operation=${task.hotspotOperation}, command=$command, result=$result")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing hotspot control", e)
                false
            }
        }
    }

    /**
     * 执行移动数据控制
     */
    private suspend fun executeMobileDataControl(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检查位置权限（移动数据状态检测可能需要）
                if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                    Log.e(TAG, "Location permissions not granted for mobile data control")

                    return@withContext false
                }

                val command = when (task.mobileDataOperation) {
                    SwitchOperation.TOGGLE -> {
                        // 切换模式：检测当前移动数据状态并执行相反操作
                        val currentMobileDataEnabled = isMobileDataEnabled()
                        if (currentMobileDataEnabled) {
                            "svc data disable"
                        } else {
                            "svc data enable"
                        }
                    }
                    SwitchOperation.ENABLE -> "svc data enable"
                    SwitchOperation.DISABLE -> "svc data disable"
                }

                // 执行Shizuku命令
                val result = ShizukuManager.executeCommand(command)
                val success = !result.contains("Error:") && !result.contains("错误")

                Log.d(TAG, "Mobile data control completed: operation=${task.mobileDataOperation}, command=$command, result=$result")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing mobile data control", e)
                false
            }
        }
    }

    /**
     * 执行自动同步控制
     */
    private suspend fun executeAutoSyncControl(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检查通讯录权限（自动同步控制可能涉及账号同步）
                if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
                    Log.e(TAG, "Communication permissions not granted for auto sync control")

                    return@withContext false
                }

                val command = when (task.autoSyncOperation) {
                    SwitchOperation.TOGGLE -> {
                        // 切换模式：检测当前自动同步状态并执行相反操作
                        val currentAutoSyncEnabled = isAutoSyncEnabled()
                        if (currentAutoSyncEnabled) {
                            "settings put global auto_sync 0"
                        } else {
                            "settings put global auto_sync 1"
                        }
                    }
                    SwitchOperation.ENABLE -> "settings put global auto_sync 1"
                    SwitchOperation.DISABLE -> "settings put global auto_sync 0"
                }

                // 执行Shizuku命令
                val result = ShizukuManager.executeCommand(command)
                val success = !result.contains("Error:") && !result.contains("错误")

                Log.d(TAG, "Auto sync control completed: operation=${task.autoSyncOperation}, command=$command, result=$result")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing auto sync control", e)
                false
            }
        }
    }

    /**
     * 执行飞行模式控制
     */
    private suspend fun executeAirplaneModeControl(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检查位置权限（飞行模式控制可能影响位置服务）
                if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                    Log.e(TAG, "Location permissions not granted for airplane mode control")

                    return@withContext false
                }

                val command = when (task.airplaneModeOperation) {
                    SwitchOperation.TOGGLE -> {
                        // 切换模式：检测当前飞行模式状态并执行相反操作
                        val currentAirplaneModeEnabled = isAirplaneModeEnabled()
                        if (currentAirplaneModeEnabled) {
                            "settings put global airplane_mode_on 0"
                        } else {
                            "settings put global airplane_mode_on 1"
                        }
                    }
                    SwitchOperation.ENABLE -> "settings put global airplane_mode_on 1"
                    SwitchOperation.DISABLE -> "settings put global airplane_mode_on 0"
                }

                // 执行Shizuku命令
                val result = ShizukuManager.executeCommand(command)
                val success = !result.contains("Error:") && !result.contains("错误")

                // 如果设置成功，发送广播通知系统状态变化
                if (success) {
                    try {
                        val broadcastCommand = when (task.airplaneModeOperation) {
                            SwitchOperation.TOGGLE -> {
                                val currentAirplaneModeEnabled = isAirplaneModeEnabled()
                                if (currentAirplaneModeEnabled) {
                                    "am broadcast -a android.intent.action.AIRPLANE_MODE --ez state true"
                                } else {
                                    "am broadcast -a android.intent.action.AIRPLANE_MODE --ez state false"
                                }
                            }
                            SwitchOperation.ENABLE -> "am broadcast -a android.intent.action.AIRPLANE_MODE --ez state true"
                            SwitchOperation.DISABLE -> "am broadcast -a android.intent.action.AIRPLANE_MODE --ez state false"
                        }
                        ShizukuManager.executeCommand(broadcastCommand)
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to send airplane mode broadcast", e)
                    }
                }

                Log.d(TAG, "Airplane mode control completed: operation=${task.airplaneModeOperation}, command=$command, result=$result")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing airplane mode control", e)
                false
            }
        }
    }



    /**
     * 执行发送Intent
     */
    private suspend fun executeSendIntent(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val intent = Intent().apply {
                    // 设置动作
                    if (task.intentAction.isNotEmpty()) {
                        action = task.intentAction
                    }

                    // 设置包名和类名
                    if (task.intentPackageName.isNotEmpty() && task.intentClassName.isNotEmpty()) {
                        setClassName(task.intentPackageName, task.intentClassName)
                    } else if (task.intentPackageName.isNotEmpty()) {
                        `package` = task.intentPackageName
                    }

                    // 设置数据URL
                    if (task.intentDataUrl.isNotEmpty()) {
                        data = android.net.Uri.parse(task.intentDataUrl)
                    }

                    // 设置MIME类型
                    if (task.intentMimeType.isNotEmpty()) {
                        type = task.intentMimeType
                    }

                    // 设置旗帜
                    if (task.intentFlags != 0) {
                        flags = task.intentFlags
                    }

                    // 添加附加参数
                    task.intentParams.forEach { param ->
                        when (param.type) {
                            IntentParamType.STRING -> putExtra(param.name, param.value)
                            IntentParamType.BOOLEAN -> putExtra(param.name, param.value.toBoolean())
                            IntentParamType.INTEGER -> {
                                try {
                                    putExtra(param.name, param.value.toInt())
                                } catch (e: NumberFormatException) {
                                    Log.w(TAG, "Invalid integer value for ${param.name}: ${param.value}")
                                }
                            }
                            IntentParamType.LONG -> {
                                try {
                                    putExtra(param.name, param.value.toLong())
                                } catch (e: NumberFormatException) {
                                    Log.w(TAG, "Invalid long value for ${param.name}: ${param.value}")
                                }
                            }
                            IntentParamType.FLOAT -> {
                                try {
                                    putExtra(param.name, param.value.toFloat())
                                } catch (e: NumberFormatException) {
                                    Log.w(TAG, "Invalid float value for ${param.name}: ${param.value}")
                                }
                            }
                            IntentParamType.DOUBLE -> {
                                try {
                                    putExtra(param.name, param.value.toDouble())
                                } catch (e: NumberFormatException) {
                                    Log.w(TAG, "Invalid double value for ${param.name}: ${param.value}")
                                }
                            }
                        }
                    }

                    // 根据目标类型设置不同的标志
                    when (task.intentTargetType) {
                        IntentTargetType.ACTIVITY -> {
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        IntentTargetType.BROADCAST -> {
                            // 广播不需要额外标志
                        }
                        IntentTargetType.SERVICE -> {
                            // 服务不需要额外标志
                        }
                    }
                }

                // 根据目标类型发送Intent
                when (task.intentTargetType) {
                    IntentTargetType.ACTIVITY -> {
                        context.startActivity(intent)
                        Log.d(TAG, "Activity Intent sent: action=${task.intentAction}, package=${task.intentPackageName}")
                    }
                    IntentTargetType.BROADCAST -> {
                        context.sendBroadcast(intent)
                        Log.d(TAG, "Broadcast Intent sent: action=${task.intentAction}, package=${task.intentPackageName}")
                    }
                    IntentTargetType.SERVICE -> {
                        context.startService(intent)
                        Log.d(TAG, "Service Intent sent: action=${task.intentAction}, package=${task.intentPackageName}")
                    }
                }

                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing send Intent", e)
                false
            }
        }
    }

    /**
     * 执行同步账号
     */
    private suspend fun executeSyncAccount(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检查通讯录权限
                if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
                    Log.e(TAG, "Communication permissions not granted for account sync")

                    return@withContext false
                }

                // 根据账号类型构建同步命令
                val command = when {
                    task.accountType.isNotEmpty() -> {
                        // 同步指定类型的账号
                        "cmd account sync ${task.accountType}"
                    }
                    else -> {
                        // 同步所有账号
                        "cmd account sync"
                    }
                }

                // 执行Shizuku命令
                val result = ShizukuManager.executeCommand(command)
                val success = !result.contains("Error:") && !result.contains("错误")

                Log.d(TAG, "Account sync completed: accountType=${task.accountType}, command=$command, result=$result")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing sync account", e)
                false
            }
        }
    }

    /**
     * 执行检查连接
     */
    private suspend fun executeCheckConnection(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (task.checkUrl.isEmpty()) {
                    Log.e(TAG, "Check URL is empty")
                    return@withContext false
                }

                // 检查网络权限
                if (context.checkSelfPermission(android.Manifest.permission.INTERNET) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "INTERNET permission not granted")
                    return@withContext false
                }

                val url = java.net.URL(task.checkUrl)
                val connection = url.openConnection() as java.net.HttpURLConnection

                // 设置超时时间（checkTimeout已经是毫秒）
                val timeoutMs = if (task.checkTimeout > 0) task.checkTimeout else 10000 // 默认10秒
                connection.connectTimeout = timeoutMs
                connection.readTimeout = timeoutMs

                // 设置请求方法为HEAD，减少数据传输
                connection.requestMethod = "HEAD"
                connection.instanceFollowRedirects = true

                val startTime = System.currentTimeMillis()
                val responseCode = connection.responseCode
                val endTime = System.currentTimeMillis()
                val responseTime = endTime - startTime

                connection.disconnect()

                val success = responseCode in 200..299

                Log.d(TAG, "Connection check completed: url=${task.checkUrl}, responseCode=$responseCode, responseTime=${responseTime}ms, success=$success")



                success
            } catch (e: java.net.SocketTimeoutException) {
                Log.e(TAG, "Connection check timeout: url=${task.checkUrl}", e)

                false
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 检测当前WiFi是否已启用
     *
     * @return WiFi是否已启用
     */
    private suspend fun isWifiEnabled(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 使用dumpsys wifi命令检查WiFi状态
                val result = ShizukuManager.executeCommand("dumpsys wifi | grep 'Wi-Fi is'")
                val enabled = result.contains("enabled") || result.contains("已启用")
                enabled
            } catch (e: Exception) {
                // 如果检测失败，默认返回false（保守处理）
                false
            }
        }
    }

    /**
     * 检测当前热点是否已启用
     *
     * @return 热点是否已启用
     */
    private suspend fun isHotspotEnabled(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 使用cmd wifi命令检查热点状态
                val result = ShizukuManager.executeCommand("cmd wifi get-wifi-ap-state")
                val enabled = result.contains("enabled") || result.contains("11") || result.contains("13")
                Log.d(TAG, "Hotspot status check: $result, enabled: $enabled")
                enabled
            } catch (e: Exception) {
                Log.e(TAG, "Error checking hotspot status", e)
                false
            }
        }
    }

    /**
     * 检测当前移动数据是否已启用
     *
     * @return 移动数据是否已启用
     */
    private suspend fun isMobileDataEnabled(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 使用settings命令检查移动数据状态
                val result = ShizukuManager.executeCommand("settings get global mobile_data")
                val enabled = result.trim() == "1"
                Log.d(TAG, "Mobile data status check: $result, enabled: $enabled")
                enabled
            } catch (e: Exception) {
                Log.e(TAG, "Error checking mobile data status", e)
                false
            }
        }
    }

    /**
     * 检测当前自动同步是否已启用
     *
     * @return 自动同步是否已启用
     */
    private suspend fun isAutoSyncEnabled(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 使用settings命令检查自动同步状态
                val result = ShizukuManager.executeCommand("settings get global auto_sync")
                val enabled = result.trim() == "1"
                Log.d(TAG, "Auto sync status check: $result, enabled: $enabled")
                enabled
            } catch (e: Exception) {
                Log.e(TAG, "Error checking auto sync status", e)
                false
            }
        }
    }

    /**
     * 检测当前飞行模式是否已启用
     *
     * @return 飞行模式是否已启用
     */
    private suspend fun isAirplaneModeEnabled(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 使用settings命令检查飞行模式状态
                val result = ShizukuManager.executeCommand("settings get global airplane_mode_on")
                val enabled = result.trim() == "1"
                Log.d(TAG, "Airplane mode status check: $result, enabled: $enabled")
                enabled
            } catch (e: Exception) {
                Log.e(TAG, "Error checking airplane mode status", e)
                false
            }
        }
    }

    /**
     * 执行网络状态保存
     *
     * @param task 连接任务
     * @return 任务是否成功执行
     */
    private suspend fun executeNetworkStateSave(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检测当前WiFi和移动数据状态
                val wifiEnabled = isWifiEnabled()
                val mobileDataEnabled = isMobileDataEnabled()

                // 创建网络状态对象
                val networkState = NetworkState(
                    wifiEnabled = wifiEnabled,
                    mobileDataEnabled = mobileDataEnabled,
                    saveTime = System.currentTimeMillis()
                )

                // 保存到全局设置
                val currentSettings = settingsRepository.globalSettings.first()
                val updatedSettings = currentSettings.copy(savedNetworkState = networkState)
                settingsRepository.saveGlobalSettings(updatedSettings)



                Log.d(TAG, "Network state saved: WiFi=$wifiEnabled, MobileData=$mobileDataEnabled")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error saving network state", e)

                false
            }
        }
    }

    /**
     * 执行网络状态恢复
     *
     * @param task 连接任务
     * @return 任务是否成功执行
     */
    private suspend fun executeNetworkStateRestore(task: ConnectivityTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val currentSettings = settingsRepository.globalSettings.first()
                val savedNetworkState = currentSettings.savedNetworkState

                // 检查是否有保存的网络状态
                if (savedNetworkState == null) {
                    Log.w(TAG, "No saved network state found, skipping restore")

                    return@withContext true // 没有状态也算成功，避免阻塞后续任务
                }

                // 检查状态是否过期
                if (savedNetworkState.isExpired()) {
                    Log.w(TAG, "Saved network state is expired, clearing and skipping restore")
                    // 清除过期状态
                    val clearedSettings = currentSettings.copy(savedNetworkState = null)
                    settingsRepository.saveGlobalSettings(clearedSettings)


                    return@withContext true
                }

                var success = true

                // 恢复WiFi状态
                val currentWifiEnabled = isWifiEnabled()
                if (currentWifiEnabled != savedNetworkState.wifiEnabled) {
                    val wifiCommand = if (savedNetworkState.wifiEnabled) {
                        task.effectiveWifiEnableCommand
                    } else {
                        task.effectiveWifiDisableCommand
                    }
                    val wifiResult = ShizukuManager.executeCommand(wifiCommand)
                    if (wifiResult.contains("Error:") || wifiResult.contains("错误")) {
                        success = false
                        Log.e(TAG, "Failed to restore WiFi state: $wifiResult")
                    } else {
                        Log.d(TAG, "WiFi state restored to: ${savedNetworkState.wifiEnabled} using command: $wifiCommand")
                    }
                }

                // 恢复移动数据状态
                val currentMobileDataEnabled = isMobileDataEnabled()
                if (currentMobileDataEnabled != savedNetworkState.mobileDataEnabled) {
                    val dataCommand = if (savedNetworkState.mobileDataEnabled) {
                        task.effectiveMobileDataEnableCommand
                    } else {
                        task.effectiveMobileDataDisableCommand
                    }
                    val dataResult = ShizukuManager.executeCommand(dataCommand)
                    if (dataResult.contains("Error:") || dataResult.contains("错误")) {
                        success = false
                        Log.e(TAG, "Failed to restore mobile data state: $dataResult")
                    } else {
                        Log.d(TAG, "Mobile data state restored to: ${savedNetworkState.mobileDataEnabled} using command: $dataCommand")
                    }
                }

                // 恢复完成后清除保存的状态
                val clearedSettings = currentSettings.copy(savedNetworkState = null)
                settingsRepository.saveGlobalSettings(clearedSettings)



                Log.d(TAG, "Network state restore completed: success=$success")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error restoring network state", e)

                false
            }
        }
    }

    /**
     * 执行电话任务
     *
     * @param task 电话任务
     * @return 任务是否成功执行
     */
    private suspend fun executePhoneTask(task: PhoneTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 执行时权限检查：通信权限检查
                when (task.operation) {
                    PhoneOperation.OPEN_CALL_LOG -> {
                        if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasReadCallLogPermission(context)) {
                            Log.e(TAG, "READ_CALL_LOG permission not granted for phone task execution")

                            return@withContext false
                        }
                    }
                    PhoneOperation.MAKE_CALL -> {
                        if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasCallPhonePermission(context)) {
                            Log.e(TAG, "CALL_PHONE permission not granted for phone task execution")

                            return@withContext false
                        }
                    }
                    PhoneOperation.CLEAR_CALL_LOG -> {
                        if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasWriteCallLogPermission(context)) {
                            return@withContext false
                        }
                    }
                    PhoneOperation.REJECT_CALL,
                    PhoneOperation.ANSWER_CALL,
                    PhoneOperation.RINGTONE_SETTINGS -> {
                        // 这些操作不需要特殊权限检查
                    }
                }



                val success = when (task.operation) {
                    PhoneOperation.OPEN_CALL_LOG -> executeOpenCallLog()
                    PhoneOperation.REJECT_CALL -> executeRejectCall()
                    PhoneOperation.MAKE_CALL -> executeMakeCall(task)
                    PhoneOperation.ANSWER_CALL -> executeAnswerCall(task)
                    PhoneOperation.CLEAR_CALL_LOG -> executeClearCallLog(task)
                    PhoneOperation.RINGTONE_SETTINGS -> executePhoneRingtoneSettings(task)
                }



                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 打开通话记录
     */
    private suspend fun executeOpenCallLog(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查通话记录读取权限
                if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasReadCallLogPermission(context)) {
                    return@withContext false
                }

                val intent = Intent(Intent.ACTION_VIEW).apply {
                    type = "vnd.android.cursor.dir/calls"
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 拒接电话
     */
    private suspend fun executeRejectCall(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 使用Shizuku命令拒接电话
                val result = ShizukuManager.executeCommand("service call phone 5")
                Log.d(TAG, "Reject call result: $result")
                !result.contains("Error") && !result.contains("错误")
            } catch (e: Exception) {
                Log.e(TAG, "Error rejecting call", e)
                false
            }
        }
    }

    /**
     * 拨打电话
     */
    private suspend fun executeMakeCall(task: PhoneTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查拨打电话权限
                if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasCallPhonePermission(context)) {
                    Log.e(TAG, "CALL_PHONE permission not granted")

                    return@withContext false
                }

                val phoneNumber = when (task.makeCallType) {
                    MakeCallType.MANUAL_INPUT -> task.phoneNumber
                    MakeCallType.CONTACT -> {
                        // 根据联系人ID或姓名查找电话号码
                        getPhoneNumberFromContact(task)
                    }
                    MakeCallType.RECENT_CALL -> {
                        // 查询最近拨打的号码
                        getRecentCallNumber()
                    }
                }

                if (phoneNumber.isEmpty()) {
                    Log.e(TAG, "Phone number is empty")
                    return@withContext false
                }

                // 构建拨号Intent，支持SIM卡选择
                val intent = createCallIntent(phoneNumber, task.simCardSelection)

                context.startActivity(intent)
                Log.d(TAG, "Call initiated to: $phoneNumber with SIM: ${task.simCardSelection}")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error making call", e)
                false
            }
        }
    }

    /**
     * 接听电话
     */
    private suspend fun executeAnswerCall(task: PhoneTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 根据延迟设置等待
                val delayMillis = when (task.answerDelayType) {
                    AnswerCallDelayType.NO_DELAY -> 0L
                    AnswerCallDelayType.CUSTOM_DELAY -> task.answerDelaySeconds * 1000L
                }

                if (delayMillis > 0) {
                    delay(delayMillis)
                }

                // 使用Shizuku命令接听电话
                val result = ShizukuManager.executeCommand("service call phone 2")
                Log.d(TAG, "Answer call result: $result")
                !result.contains("Error") && !result.contains("错误")
            } catch (e: Exception) {
                Log.e(TAG, "Error answering call", e)
                false
            }
        }
    }

    /**
     * 清除通话记录
     */
    private suspend fun executeClearCallLog(task: PhoneTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检查通话记录写入权限
                if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasWriteCallLogPermission(context)) {
                    Log.e(TAG, "WRITE_CALL_LOG permission not granted")

                    return@withContext false
                }

                // 根据清除类型构建不同的命令
                val command = when (task.clearLogType) {
                    ClearCallLogType.ALL -> "content delete --uri content://call_log/calls"
                    ClearCallLogType.INCOMING -> "content delete --uri content://call_log/calls --where \"type=1\""
                    ClearCallLogType.OUTGOING -> "content delete --uri content://call_log/calls --where \"type=2\""
                    ClearCallLogType.MISSED -> "content delete --uri content://call_log/calls --where \"type=3\""
                    ClearCallLogType.VOICEMAIL -> "content delete --uri content://call_log/calls --where \"type=4\""
                    ClearCallLogType.REJECTED -> "content delete --uri content://call_log/calls --where \"type=5\""
                    ClearCallLogType.BLOCKED -> "content delete --uri content://call_log/calls --where \"type=6\""
                }

                val result = ShizukuManager.executeCommand(command)
                Log.d(TAG, "Clear call log result: $result")
                !result.contains("Error") && !result.contains("错误")
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing call log", e)
                false
            }
        }
    }

    /**
     * 执行电话铃声设置
     */
    private suspend fun executePhoneRingtoneSettings(task: PhoneTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查铃声URI是否有效
                if (task.selectedRingtoneUri.isEmpty()) {
                    Log.e(TAG, "No ringtone URI provided for phone ringtone task")
                    return@withContext false
                }

                // 使用RingtoneHelper设置电话铃声
                val success = RingtoneHelper.setRingtone(
                    context = context,
                    ringtoneUri = task.selectedRingtoneUri,
                    ringtoneType = RingtoneHelper.RingtoneType.RINGTONE
                )

                if (success) {

                    Log.d(TAG, "Phone ringtone set successfully: ${task.selectedRingtoneName}")
                } else {
                    Log.e(TAG, "Failed to set phone ringtone: ${task.selectedRingtoneName}")
                }

                success
            } catch (e: Exception) {
                Log.e(TAG, "Error setting phone ringtone", e)



                false
            }
        }
    }

    /**
     * 从联系人信息获取电话号码
     */
    private suspend fun getPhoneNumberFromContact(task: PhoneTask): String {
        return withContext(Dispatchers.IO) {
            try {
                // 检查联系人权限
                if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasContactsPermission(context)) {
                    Log.w(TAG, "No contacts permission, cannot get phone number from contact")
                    return@withContext ""
                }

                // 优先使用联系人ID列表
                if (task.contactIds.isNotEmpty()) {
                    // 获取第一个联系人的电话号码
                    val contactId = task.contactIds.first()
                    val phoneNumber = getPhoneNumberByContactId(contactId)
                    if (phoneNumber.isNotEmpty()) {
                        Log.d(TAG, "Found phone number by contact ID: $contactId -> $phoneNumber")
                        return@withContext phoneNumber
                    }
                }

                // 如果没有联系人ID，尝试通过联系人姓名查找
                if (task.contactName.isNotEmpty()) {
                    val contact = com.weinuo.quickcommands.utils.ContactsHelper.getContactByName(context, task.contactName)
                    if (contact != null) {
                        Log.d(TAG, "Found phone number by contact name: ${task.contactName} -> ${contact.phoneNumber}")
                        return@withContext contact.phoneNumber
                    }
                }

                Log.w(TAG, "No phone number found for contact")
                ""
            } catch (e: Exception) {
                Log.e(TAG, "Error getting phone number from contact", e)
                ""
            }
        }
    }

    /**
     * 根据联系人ID获取电话号码
     */
    private suspend fun getPhoneNumberByContactId(contactId: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val cursor = context.contentResolver.query(
                    android.provider.ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                    arrayOf(android.provider.ContactsContract.CommonDataKinds.Phone.NUMBER),
                    "${android.provider.ContactsContract.CommonDataKinds.Phone.CONTACT_ID} = ?",
                    arrayOf(contactId),
                    null
                )

                cursor?.use { c ->
                    if (c.moveToFirst()) {
                        val numberIndex = c.getColumnIndex(android.provider.ContactsContract.CommonDataKinds.Phone.NUMBER)
                        val phoneNumber = c.getString(numberIndex) ?: ""
                        return@withContext com.weinuo.quickcommands.utils.ContactsHelper.cleanPhoneNumber(phoneNumber)
                    }
                }

                ""
            } catch (e: Exception) {
                Log.e(TAG, "Error getting phone number by contact ID: $contactId", e)
                ""
            }
        }
    }

    /**
     * 获取最近拨打的电话号码
     */
    private suspend fun getRecentCallNumber(): String {
        return withContext(Dispatchers.IO) {
            try {
                // 检查通话记录权限
                if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasReadCallLogPermission(context)) {
                    return@withContext ""
                }

                val cursor = context.contentResolver.query(
                    android.provider.CallLog.Calls.CONTENT_URI,
                    arrayOf(android.provider.CallLog.Calls.NUMBER),
                    "${android.provider.CallLog.Calls.TYPE} = ?",
                    arrayOf(android.provider.CallLog.Calls.OUTGOING_TYPE.toString()),
                    "${android.provider.CallLog.Calls.DATE} DESC LIMIT 1"
                )

                cursor?.use { c ->
                    if (c.moveToFirst()) {
                        val numberIndex = c.getColumnIndex(android.provider.CallLog.Calls.NUMBER)
                        val phoneNumber = c.getString(numberIndex) ?: ""
                        Log.d(TAG, "Found recent call number: $phoneNumber")
                        return@withContext com.weinuo.quickcommands.utils.ContactsHelper.cleanPhoneNumber(phoneNumber)
                    }
                }

                Log.w(TAG, "No recent call found")
                ""
            } catch (e: Exception) {
                Log.e(TAG, "Error getting recent call number", e)
                ""
            }
        }
    }

    /**
     * 创建拨号Intent，支持SIM卡选择
     */
    private fun createCallIntent(phoneNumber: String, simCardSelection: SimCardSelection): Intent {
        return when (simCardSelection) {
            SimCardSelection.SIM1 -> {
                // 使用SIM卡1拨号
                Intent(Intent.ACTION_CALL).apply {
                    data = android.net.Uri.parse("tel:$phoneNumber")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    // 添加SIM卡选择参数（适用于支持的设备）
                    putExtra("com.android.phone.extra.slot", 0)
                    putExtra("Cdma_Supp", false)
                }
            }
            SimCardSelection.SIM2 -> {
                // 使用SIM卡2拨号
                Intent(Intent.ACTION_CALL).apply {
                    data = android.net.Uri.parse("tel:$phoneNumber")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    // 添加SIM卡选择参数（适用于支持的设备）
                    putExtra("com.android.phone.extra.slot", 1)
                    putExtra("Cdma_Supp", false)
                }
            }
            SimCardSelection.ASK_EACH_TIME -> {
                // 让系统询问用户选择SIM卡
                Intent(Intent.ACTION_CALL).apply {
                    data = android.net.Uri.parse("tel:$phoneNumber")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    // 不添加SIM卡选择参数，让系统弹出选择对话框
                }
            }
        }
    }

    /**
     * 执行媒体任务
     *
     * @param task 媒体任务
     * @return 任务是否成功执行
     */
    private suspend fun executeMediaTask(task: MediaTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 执行时权限检查：录音权限检查（针对录音操作）
                if (task.operation == MediaOperation.MICROPHONE_RECORDING) {
                    if (!com.weinuo.quickcommands.utils.MediaPermissionUtil.hasRecordAudioPermission(context)) {
                        Log.e(TAG, "RECORD_AUDIO permission not granted for media task execution")

                        return@withContext false
                    }

                    // 检查存储权限（录音文件需要保存到存储）
                    if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasStoragePermission(context)) {
                        return@withContext false
                    }
                }

                val success = when (task.operation) {
                    MediaOperation.MULTIMEDIA_CONTROL -> executeMultimediaControl(task)
                    MediaOperation.PLAY_STOP_SOUND -> executePlayStopSound(task)
                    MediaOperation.MICROPHONE_RECORDING -> executeMicrophoneRecording(task)
                }



                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行多媒体控制
     */
    private suspend fun executeMultimediaControl(task: MediaTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                when (task.multimediaControlType) {
                    MultimediaControlType.SIMULATE_MEDIA_BUTTON -> {
                        // 模拟媒体按钮
                        val keyCode = when (task.mediaButtonType) {
                            MediaButtonType.PLAY_PAUSE -> android.view.KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE
                            MediaButtonType.PLAY -> android.view.KeyEvent.KEYCODE_MEDIA_PLAY
                            MediaButtonType.PAUSE -> android.view.KeyEvent.KEYCODE_MEDIA_PAUSE
                            MediaButtonType.NEXT_TRACK -> android.view.KeyEvent.KEYCODE_MEDIA_NEXT
                            MediaButtonType.PREVIOUS_TRACK -> android.view.KeyEvent.KEYCODE_MEDIA_PREVIOUS
                            MediaButtonType.STOP -> android.view.KeyEvent.KEYCODE_MEDIA_STOP
                            MediaButtonType.FAST_FORWARD -> android.view.KeyEvent.KEYCODE_MEDIA_FAST_FORWARD
                            MediaButtonType.REWIND -> android.view.KeyEvent.KEYCODE_MEDIA_REWIND
                        }

                        // 发送媒体按键事件
                        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager
                        val keyEvent = android.view.KeyEvent(android.view.KeyEvent.ACTION_DOWN, keyCode)
                        audioManager.dispatchMediaKeyEvent(keyEvent)

                        val keyEventUp = android.view.KeyEvent(android.view.KeyEvent.ACTION_UP, keyCode)
                        audioManager.dispatchMediaKeyEvent(keyEventUp)

                        Log.d(TAG, "Media button simulated: ${task.mediaButtonType}")
                        true
                    }
                    MultimediaControlType.DEFAULT_PLAYER_CONTROL -> {
                        // 默认播放器控制
                        // 这里可以通过MediaController或Intent来控制默认播放器
                        // 为简化实现，使用媒体按键事件
                        val keyCode = when (task.playerControlType) {
                            PlayerControlType.PLAY -> android.view.KeyEvent.KEYCODE_MEDIA_PLAY
                            PlayerControlType.PAUSE -> android.view.KeyEvent.KEYCODE_MEDIA_PAUSE
                            PlayerControlType.STOP -> android.view.KeyEvent.KEYCODE_MEDIA_STOP
                            PlayerControlType.NEXT -> android.view.KeyEvent.KEYCODE_MEDIA_NEXT
                            PlayerControlType.PREVIOUS -> android.view.KeyEvent.KEYCODE_MEDIA_PREVIOUS
                        }

                        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager
                        val keyEvent = android.view.KeyEvent(android.view.KeyEvent.ACTION_DOWN, keyCode)
                        audioManager.dispatchMediaKeyEvent(keyEvent)

                        val keyEventUp = android.view.KeyEvent(android.view.KeyEvent.ACTION_UP, keyCode)
                        audioManager.dispatchMediaKeyEvent(keyEventUp)

                        Log.d(TAG, "Player control executed: ${task.playerControlType}")
                        true
                    }
                    MultimediaControlType.SIMULATE_AUDIO_BUTTON -> {
                        // 模拟音频按钮
                        val keyCode = when (task.audioButtonType) {
                            AudioButtonType.VOLUME_UP -> android.view.KeyEvent.KEYCODE_VOLUME_UP
                            AudioButtonType.VOLUME_DOWN -> android.view.KeyEvent.KEYCODE_VOLUME_DOWN
                            AudioButtonType.VOLUME_MUTE -> android.view.KeyEvent.KEYCODE_VOLUME_MUTE
                        }

                        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager
                        val keyEvent = android.view.KeyEvent(android.view.KeyEvent.ACTION_DOWN, keyCode)
                        audioManager.dispatchMediaKeyEvent(keyEvent)

                        val keyEventUp = android.view.KeyEvent(android.view.KeyEvent.ACTION_UP, keyCode)
                        audioManager.dispatchMediaKeyEvent(keyEventUp)

                        Log.d(TAG, "Audio button simulated: ${task.audioButtonType}")
                        true
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing multimedia control", e)
                false
            }
        }
    }

    /**
     * 执行播放/停止声音
     */
    private suspend fun executePlayStopSound(task: MediaTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                when (task.soundPlayType) {
                    SoundPlayType.SELECT_FILE -> {
                        // 播放选择的文件
                        if (task.selectedFilePath.isEmpty()) {
                            Log.e(TAG, "No file selected for sound playback")
                            return@withContext false
                        }

                        // 实现文件播放逻辑
                        try {
                            // 释放之前的MediaPlayer实例
                            mediaPlayer?.release()

                            // 创建新的MediaPlayer实例
                            mediaPlayer = MediaPlayer().apply {
                                // 设置数据源
                                setDataSource(task.selectedFilePath)

                                // 设置音频流类型
                                setAudioStreamType(when (task.audioStreamType) {
                                    AudioStreamType.MEDIA_MUSIC -> AudioManager.STREAM_MUSIC
                                    AudioStreamType.RINGTONE -> AudioManager.STREAM_RING
                                    AudioStreamType.ALARM -> AudioManager.STREAM_ALARM
                                    AudioStreamType.NOTIFICATION -> AudioManager.STREAM_NOTIFICATION
                                    AudioStreamType.SYSTEM -> AudioManager.STREAM_SYSTEM
                                    AudioStreamType.VOICE_CALL -> AudioManager.STREAM_VOICE_CALL
                                    else -> AudioManager.STREAM_MUSIC
                                })

                                // 准备播放
                                prepare()

                                // 开始播放
                                start()

                                // 设置播放完成监听器
                                setOnCompletionListener {
                                    Log.d(TAG, "File playback completed: ${task.selectedFilePath}")
                                    release()
                                    mediaPlayer = null
                                }

                                // 设置错误监听器
                                setOnErrorListener { _, what, extra ->
                                    Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                                    release()
                                    mediaPlayer = null
                                    false
                                }
                            }

                            Log.d(TAG, "Started playing file: ${task.selectedFilePath}")
                            true
                        } catch (e: IOException) {
                            Log.e(TAG, "Error playing file: ${task.selectedFilePath}", e)
                            mediaPlayer?.release()
                            mediaPlayer = null
                            false
                        } catch (e: Exception) {
                            Log.e(TAG, "Unexpected error playing file: ${task.selectedFilePath}", e)
                            mediaPlayer?.release()
                            mediaPlayer = null
                            false
                        }
                    }
                    SoundPlayType.SELECT_RINGTONE -> {
                        // 播放系统铃声
                        val ringtoneManager = android.media.RingtoneManager(context)
                        val ringtoneUri = when (task.audioStreamType) {
                            AudioStreamType.RINGTONE -> android.media.RingtoneManager.getDefaultUri(android.media.RingtoneManager.TYPE_RINGTONE)
                            AudioStreamType.ALARM -> android.media.RingtoneManager.getDefaultUri(android.media.RingtoneManager.TYPE_ALARM)
                            AudioStreamType.NOTIFICATION -> android.media.RingtoneManager.getDefaultUri(android.media.RingtoneManager.TYPE_NOTIFICATION)
                            else -> android.media.RingtoneManager.getDefaultUri(android.media.RingtoneManager.TYPE_NOTIFICATION)
                        }

                        val ringtone = android.media.RingtoneManager.getRingtone(context, ringtoneUri)
                        ringtone?.play()

                        Log.d(TAG, "Playing ringtone: ${task.audioStreamType}")
                        true
                    }
                    SoundPlayType.STOP_EXISTING_SOUND -> {
                        // 停止现有声音
                        // 这里可以通过AudioManager停止所有音频播放
                        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager

                        // 发送暂停媒体按键
                        val keyEvent = android.view.KeyEvent(android.view.KeyEvent.ACTION_DOWN, android.view.KeyEvent.KEYCODE_MEDIA_PAUSE)
                        audioManager.dispatchMediaKeyEvent(keyEvent)

                        val keyEventUp = android.view.KeyEvent(android.view.KeyEvent.ACTION_UP, android.view.KeyEvent.KEYCODE_MEDIA_PAUSE)
                        audioManager.dispatchMediaKeyEvent(keyEventUp)

                        Log.d(TAG, "Stopped existing sound")
                        true
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing play/stop sound", e)
                false
            }
        }
    }

    /**
     * 执行麦克风录音
     */
    private suspend fun executeMicrophoneRecording(task: MediaTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 检查录音权限
                if (!com.weinuo.quickcommands.utils.MediaPermissionUtil.hasRecordAudioPermission(context)) {
                    Log.e(TAG, "RECORD_AUDIO permission not granted")
                    return@withContext false
                }

                // 检查存储权限（录音文件需要保存到存储）
                if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for recording")
                    return@withContext false
                }

                when (task.recordingDurationType) {
                    RecordingDurationType.CUSTOM_TIME -> {
                        // 自定义时间录音
                        val durationMs = (task.recordingMinutes * 60 + task.recordingSeconds) * 1000L
                        if (durationMs <= 0) {
                            Log.e(TAG, "Invalid recording duration")
                            return@withContext false
                        }

                        // 实现录音逻辑
                        try {
                            // 释放之前的MediaRecorder实例
                            mediaRecorder?.release()

                            // 创建录音文件目录
                            val recordingDir = if (task.recordingFolderPath.isNotEmpty()) {
                                File(task.recordingFolderPath)
                            } else {
                                File(context.getExternalFilesDir(Environment.DIRECTORY_MUSIC), "recordings")
                            }

                            if (!recordingDir.exists()) {
                                recordingDir.mkdirs()
                            }

                            // 生成文件名
                            val fileName = if (task.recordingFileName.isNotEmpty()) {
                                // 使用用户指定的文件名，确保有正确的扩展名
                                val userFileName = task.recordingFileName
                                val expectedExtension = getFileExtension(task.recordingFormat)
                                if (userFileName.endsWith(".$expectedExtension")) {
                                    userFileName
                                } else {
                                    "$userFileName.$expectedExtension"
                                }
                            } else {
                                // 使用默认文件名
                                val timestamp = System.currentTimeMillis()
                                "recording_$timestamp.${getFileExtension(task.recordingFormat)}"
                            }

                            val recordingFile = File(recordingDir, fileName)

                            // 创建新的MediaRecorder实例
                            mediaRecorder = MediaRecorder().apply {
                                // 设置音频源
                                setAudioSource(when (task.recordingSource) {
                                    RecordingSource.STANDARD_MIC -> MediaRecorder.AudioSource.MIC
                                    RecordingSource.CAMERA_MIC -> MediaRecorder.AudioSource.CAMCORDER
                                    RecordingSource.UNPROCESSED -> MediaRecorder.AudioSource.UNPROCESSED
                                    else -> MediaRecorder.AudioSource.MIC
                                })

                                // 设置输出格式
                                setOutputFormat(when (task.recordingFormat) {
                                    RecordingFormat.THREE_GPP -> MediaRecorder.OutputFormat.THREE_GPP
                                    RecordingFormat.MPEG4 -> MediaRecorder.OutputFormat.MPEG_4
                                    RecordingFormat.AAC -> MediaRecorder.OutputFormat.AAC_ADTS
                                    else -> MediaRecorder.OutputFormat.THREE_GPP
                                })

                                // 设置音频编码器
                                setAudioEncoder(when (task.recordingFormat) {
                                    RecordingFormat.THREE_GPP -> MediaRecorder.AudioEncoder.AMR_NB
                                    RecordingFormat.MPEG4 -> MediaRecorder.AudioEncoder.AAC
                                    RecordingFormat.AAC -> MediaRecorder.AudioEncoder.AAC
                                    else -> MediaRecorder.AudioEncoder.AMR_NB
                                })

                                // 设置输出文件
                                setOutputFile(recordingFile.absolutePath)

                                // 准备录音
                                prepare()

                                // 开始录音
                                start()
                            }

                            Log.d(TAG, "Started recording to: ${recordingFile.absolutePath}")

                            // 等待录音完成
                            if (task.recordingWaitForCompletion) {
                                delay(durationMs)

                                // 停止录音
                                mediaRecorder?.apply {
                                    stop()
                                    release()
                                }
                                mediaRecorder = null

                                Log.d(TAG, "Recording completed: ${recordingFile.absolutePath}")
                            }

                            true
                        } catch (e: IOException) {
                            Log.e(TAG, "Error during recording", e)
                            mediaRecorder?.release()
                            mediaRecorder = null
                            false
                        } catch (e: Exception) {
                            Log.e(TAG, "Unexpected error during recording", e)
                            mediaRecorder?.release()
                            mediaRecorder = null
                            false
                        }
                    }
                    RecordingDurationType.UNTIL_CANCELLED -> {
                        // 直到被取消的录音
                        try {
                            // 释放之前的MediaRecorder实例
                            mediaRecorder?.release()

                            // 创建录音文件目录
                            val recordingDir = if (task.recordingFolderPath.isNotEmpty()) {
                                File(task.recordingFolderPath)
                            } else {
                                File(context.getExternalFilesDir(Environment.DIRECTORY_MUSIC), "recordings")
                            }

                            if (!recordingDir.exists()) {
                                recordingDir.mkdirs()
                            }

                            // 生成文件名
                            val fileName = if (task.recordingFileName.isNotEmpty()) {
                                // 使用用户指定的文件名，确保有正确的扩展名
                                val userFileName = task.recordingFileName
                                val expectedExtension = getFileExtension(task.recordingFormat)
                                if (userFileName.endsWith(".$expectedExtension")) {
                                    userFileName
                                } else {
                                    "$userFileName.$expectedExtension"
                                }
                            } else {
                                // 使用默认文件名
                                val timestamp = System.currentTimeMillis()
                                "continuous_recording_$timestamp.${getFileExtension(task.recordingFormat)}"
                            }

                            val recordingFile = File(recordingDir, fileName)

                            // 创建新的MediaRecorder实例
                            mediaRecorder = MediaRecorder().apply {
                                // 设置音频源
                                setAudioSource(when (task.recordingSource) {
                                    RecordingSource.STANDARD_MIC -> MediaRecorder.AudioSource.MIC
                                    RecordingSource.CAMERA_MIC -> MediaRecorder.AudioSource.CAMCORDER
                                    RecordingSource.UNPROCESSED -> MediaRecorder.AudioSource.UNPROCESSED
                                    else -> MediaRecorder.AudioSource.MIC
                                })

                                // 设置输出格式
                                setOutputFormat(when (task.recordingFormat) {
                                    RecordingFormat.THREE_GPP -> MediaRecorder.OutputFormat.THREE_GPP
                                    RecordingFormat.MPEG4 -> MediaRecorder.OutputFormat.MPEG_4
                                    RecordingFormat.AAC -> MediaRecorder.OutputFormat.AAC_ADTS
                                    else -> MediaRecorder.OutputFormat.THREE_GPP
                                })

                                // 设置音频编码器
                                setAudioEncoder(when (task.recordingFormat) {
                                    RecordingFormat.THREE_GPP -> MediaRecorder.AudioEncoder.AMR_NB
                                    RecordingFormat.MPEG4 -> MediaRecorder.AudioEncoder.AAC
                                    RecordingFormat.AAC -> MediaRecorder.AudioEncoder.AAC
                                    else -> MediaRecorder.AudioEncoder.AMR_NB
                                })

                                // 设置输出文件
                                setOutputFile(recordingFile.absolutePath)

                                // 准备录音
                                prepare()

                                // 开始录音
                                start()
                            }

                            Log.d(TAG, "Started continuous recording to: ${recordingFile.absolutePath}")
                            true
                        } catch (e: IOException) {
                            Log.e(TAG, "Error starting continuous recording", e)
                            mediaRecorder?.release()
                            mediaRecorder = null
                            false
                        } catch (e: Exception) {
                            Log.e(TAG, "Unexpected error starting continuous recording", e)
                            mediaRecorder?.release()
                            mediaRecorder = null
                            false
                        }
                    }
                    RecordingDurationType.CANCEL_RECORDING -> {
                        // 取消录音
                        try {
                            mediaRecorder?.apply {
                                stop()
                                release()
                                Log.d(TAG, "Recording cancelled and stopped")
                            }
                            mediaRecorder = null
                            true
                        } catch (e: Exception) {
                            Log.e(TAG, "Error cancelling recording", e)
                            mediaRecorder?.release()
                            mediaRecorder = null
                            false
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing microphone recording", e)
                false
            }
        }
    }

    /**
     * 根据录音格式获取文件扩展名
     */
    private fun getFileExtension(format: RecordingFormat): String {
        return when (format) {
            RecordingFormat.THREE_GPP -> "3gp"
            RecordingFormat.MPEG4 -> "mp4"
            RecordingFormat.AAC -> "aac"
            else -> "3gp"
        }
    }

    /**
     * 释放媒体资源
     */
    fun releaseMediaResources() {
        try {
            mediaPlayer?.release()
            mediaPlayer = null

            mediaRecorder?.apply {
                stop()
                release()
            }
            mediaRecorder = null

            Log.d(TAG, "Media resources released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing media resources", e)
        }
    }

    /**
     * 执行屏幕控制任务
     *
     * @param task 屏幕控制任务
     * @return 任务是否成功执行
     */
    private suspend fun executeScreenControlTask(task: ScreenControlTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {


                val success = when (task.operation) {
                    ScreenControlOperation.BRIGHTNESS_CONTROL -> executeBrightnessControl(task)
                    ScreenControlOperation.KEEP_DEVICE_AWAKE -> executeKeepDeviceAwake(task)
                    ScreenControlOperation.SCREEN_ON_OFF -> executeScreenOnOff(task)
                    ScreenControlOperation.SCREEN_DIMNESS -> executeScreenDimness(task)
                    ScreenControlOperation.BLOCK_SCREEN_TOUCH -> executeBlockScreenTouch(task)
                    ScreenControlOperation.FORCE_SCREEN_ROTATION -> executeForceScreenRotation(task)
                    ScreenControlOperation.SET_SCREEN_TIMEOUT -> executeSetScreenTimeout(task)
                    ScreenControlOperation.CHECK_SCREEN_TEXT -> executeCheckScreenText(task)
                    ScreenControlOperation.READ_SCREEN_CONTENT -> executeReadScreenContent(task)
                    ScreenControlOperation.CHECK_PIXEL_COLOR -> executeCheckPixelColor(task)
                    ScreenControlOperation.AUTO_CLICKER_PLAYBACK -> executeAutoClickerPlayback(task)
                }

                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行亮度控制
     */
    private suspend fun executeBrightnessControl(task: ScreenControlTask): Boolean {
        return try {
            val brightnessValue = when (task.brightnessControlType) {
                BrightnessControlType.PERCENTAGE -> {
                    // 百分比模式：0-100转换为0-255
                    (task.brightnessValue * 255 / 100).coerceIn(0, 255)
                }
                BrightnessControlType.ABSOLUTE_VALUE -> {
                    // 绝对值模式：直接使用0-255的值
                    task.brightnessValue.coerceIn(0, 255)
                }
            }

            // 构建亮度控制命令
            val commands = mutableListOf<String>()

            // 设置亮度值
            commands.add("settings put system screen_brightness $brightnessValue")

            // 设置自动亮度
            if (task.enableAutoBrightness) {
                commands.add("settings put system screen_brightness_mode 1")
            } else {
                commands.add("settings put system screen_brightness_mode 0")
            }

            // 执行命令
            var allSuccessful = true
            for (command in commands) {
                val result = ShizukuManager.executeCommand(command)
                Log.d(TAG, "Brightness control command result: $result")
                if (result.contains("Error:") || result.contains("错误")) {
                    allSuccessful = false
                }
            }

            Log.d(TAG, "Brightness control completed: type=${task.brightnessControlType}, value=${task.brightnessValue}, auto=${task.enableAutoBrightness}")
            allSuccessful
        } catch (e: Exception) {
            Log.e(TAG, "Error executing brightness control", e)
            false
        }
    }

    /**
     * 执行保持设备唤醒
     */
    private suspend fun executeKeepDeviceAwake(task: ScreenControlTask): Boolean {
        return try {
            // 使用Shizuku命令控制设备唤醒
            val command = when (task.keepAwakeOperation) {
                KeepAwakeOperation.ENABLE -> "settings put global stay_on_while_plugged_in 7"  // 7 = USB + AC + Wireless
                KeepAwakeOperation.DISABLE -> "settings put global stay_on_while_plugged_in 0"  // 0 = 禁用
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Keep device awake completed: operation=${task.keepAwakeOperation}, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing keep device awake", e)
            false
        }
    }

    /**
     * 执行屏幕开关
     */
    private suspend fun executeScreenOnOff(task: ScreenControlTask): Boolean {
        return try {
            when (task.screenOnOffImplementation) {
                null -> {
                    Log.e(TAG, "屏幕开关实现方式未选择")
                    false
                }
                com.weinuo.quickcommands.model.ScreenOnOffImplementation.SHIZUKU -> {
                    // 使用Shizuku方式
                    val command = when (task.screenOnOffOperation) {
                        ScreenOnOffOperation.TURN_ON -> "input keyevent KEYCODE_WAKEUP"
                        ScreenOnOffOperation.TURN_OFF -> "input keyevent KEYCODE_POWER"
                    }

                    val result = ShizukuManager.executeCommand(command)
                    val success = !result.contains("Error:") && !result.contains("错误")

                    Log.d(TAG, "Screen on/off via Shizuku completed: operation=${task.screenOnOffOperation}, result=$result")
                    success
                }
                com.weinuo.quickcommands.model.ScreenOnOffImplementation.ACCESSIBILITY_SERVICE -> {
                    // 使用无障碍服务方式
                    val systemOperationService = com.weinuo.quickcommands.service.SystemOperationAccessibilityService.getInstance()

                    if (systemOperationService == null) {
                        Log.e(TAG, "系统操作无障碍服务未启用，无法执行屏幕开关操作")
                        return false
                    }

                    val operationType = when (task.screenOnOffOperation) {
                        ScreenOnOffOperation.TURN_ON -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_SCREEN_ON
                        ScreenOnOffOperation.TURN_OFF -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_SCREEN_OFF
                    }

                    val success = systemOperationService.executeSystemOperation(operationType)
                    Log.d(TAG, "Screen on/off via accessibility service completed: operation=${task.screenOnOffOperation}, success=$success")
                    success
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing screen on/off", e)
            false
        }
    }

    /**
     * 执行屏幕暗度控制
     */
    private suspend fun executeScreenDimness(task: ScreenControlTask): Boolean {
        return try {
            val commands = mutableListOf<String>()

            when (task.screenDimnessSensorMode) {
                ScreenDimnessSensorMode.SENSOR_ON -> {
                    // 开启光传感器
                    commands.add("settings put system screen_brightness_mode 1")
                    // 设置暗度百分比（这里使用自适应亮度的调节值）
                    val dimnessValue = (task.screenDimnessPercentage * 255 / 100).coerceIn(0, 255)
                    commands.add("settings put system screen_auto_brightness_adj ${(dimnessValue - 127) / 127.0}")
                }
                ScreenDimnessSensorMode.SENSOR_OFF -> {
                    // 关闭光传感器
                    commands.add("settings put system screen_brightness_mode 0")
                }
            }

            // 执行命令
            var allSuccessful = true
            for (command in commands) {
                val result = ShizukuManager.executeCommand(command)
                Log.d(TAG, "Screen dimness command result: $result")
                if (result.contains("Error:") || result.contains("错误")) {
                    allSuccessful = false
                }
            }

            Log.d(TAG, "Screen dimness completed: sensorMode=${task.screenDimnessSensorMode}, percentage=${task.screenDimnessPercentage}")
            allSuccessful
        } catch (e: Exception) {
            Log.e(TAG, "Error executing screen dimness", e)
            false
        }
    }

    /**
     * 执行屏幕触摸屏蔽
     */
    private suspend fun executeBlockScreenTouch(task: ScreenControlTask): Boolean {
        return try {
            when (task.touchBlockOperation) {
                TouchBlockOperation.ENABLE -> {
                    // 启用触摸屏蔽 - 启动悬浮窗服务
                    com.weinuo.quickcommands.service.TouchBlockOverlayService.startTouchBlock(
                        context = context,
                        emergencyCloseEnabled = task.emergencyCloseEnabled,
                        emergencyClickCount = task.emergencyClickCount,
                        emergencyTimeWindowSeconds = task.emergencyTimeWindowSeconds
                    )
                    Log.d(TAG, "Touch block overlay service started")
                    true
                }
                TouchBlockOperation.DISABLE -> {
                    // 禁用触摸屏蔽 - 停止悬浮窗服务
                    com.weinuo.quickcommands.service.TouchBlockOverlayService.stopTouchBlock(context)
                    Log.d(TAG, "Touch block overlay service stopped")
                    true
                }
                TouchBlockOperation.TOGGLE -> {
                    // 切换触摸屏蔽状态 - 简化处理，直接启用
                    com.weinuo.quickcommands.service.TouchBlockOverlayService.startTouchBlock(
                        context = context,
                        emergencyCloseEnabled = task.emergencyCloseEnabled,
                        emergencyClickCount = task.emergencyClickCount,
                        emergencyTimeWindowSeconds = task.emergencyTimeWindowSeconds
                    )
                    Log.d(TAG, "Touch block overlay service toggled (started)")
                    true
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing block screen touch", e)
            false
        }
    }

    /**
     * 执行强制屏幕旋转
     */
    private suspend fun executeForceScreenRotation(task: ScreenControlTask): Boolean {
        return try {
            val rotationValue = when (task.forceRotationMode) {
                ForceRotationMode.FORCE_PORTRAIT -> 1  // 强制竖屏
                ForceRotationMode.FORCE_LANDSCAPE -> 2  // 强制横屏
                ForceRotationMode.STOP_FORCE_ROTATION -> 0  // 停止强制旋转（自动旋转）
                ForceRotationMode.FORCE_KEEP_CURRENT -> 3  // 强制保持当前方向
                ForceRotationMode.TOGGLE_KEEP_CURRENT -> 0  // 切换（简化处理）
                ForceRotationMode.FORCE_REVERSE_PORTRAIT -> 9  // 强制反向竖屏
                ForceRotationMode.FORCE_REVERSE_LANDSCAPE -> 8  // 强制反向横屏
                ForceRotationMode.FORCE_SENSOR_LANDSCAPE -> 6  // 强制传感器横屏
                ForceRotationMode.FORCE_SENSOR_PORTRAIT -> 7  // 强制传感器竖屏
                ForceRotationMode.FORCE_SENSOR_ROTATION -> 4  // 强制按传感器方向旋转
            }

            val commands = mutableListOf<String>()

            if (task.forceRotationMode == ForceRotationMode.STOP_FORCE_ROTATION) {
                // 启用自动旋转
                commands.add("settings put system accelerometer_rotation 1")
            } else {
                // 禁用自动旋转并设置固定方向
                commands.add("settings put system accelerometer_rotation 0")
                commands.add("settings put system user_rotation $rotationValue")
            }

            // 执行命令
            var allSuccessful = true
            for (command in commands) {
                val result = ShizukuManager.executeCommand(command)
                Log.d(TAG, "Force screen rotation command result: $result")
                if (result.contains("Error:") || result.contains("错误")) {
                    allSuccessful = false
                }
            }

            Log.d(TAG, "Force screen rotation completed: mode=${task.forceRotationMode}, rotationValue=$rotationValue")
            allSuccessful
        } catch (e: Exception) {
            Log.e(TAG, "Error executing force screen rotation", e)
            false
        }
    }

    /**
     * 执行设置屏幕超时
     */
    private suspend fun executeSetScreenTimeout(task: ScreenControlTask): Boolean {
        return try {
            // 将时间转换为毫秒
            val timeoutMillis = when (task.screenTimeoutUnit) {
                ScreenTimeoutUnit.SECONDS -> task.screenTimeoutValue * 1000L
                ScreenTimeoutUnit.MINUTES -> task.screenTimeoutValue * 60 * 1000L
            }

            val command = "settings put system screen_off_timeout $timeoutMillis"
            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Set screen timeout completed: value=${task.screenTimeoutValue}, unit=${task.screenTimeoutUnit}, timeoutMillis=$timeoutMillis, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing set screen timeout", e)
            false
        }
    }

    /**
     * 执行设备动作任务
     *
     * @param task 设备动作任务
     * @return 任务是否成功执行
     */
    private suspend fun executeDeviceActionTask(task: DeviceActionTask): Boolean {
        return try {

            val success = when (task.operation) {
                DeviceActionOperation.SHARE_TEXT -> executeShareText(task)
                DeviceActionOperation.CLIPBOARD_REFRESH -> executeClipboardRefresh()
                DeviceActionOperation.LAUNCH_HOME -> executeLaunchHome()
                DeviceActionOperation.SET_CLIPBOARD -> executeSetClipboard(task)
                DeviceActionOperation.TOGGLE_STATUS_BAR -> executeToggleStatusBar(task)
                DeviceActionOperation.VOICE_SEARCH -> executeVoiceSearch()
                DeviceActionOperation.FLASHLIGHT_CONTROL -> executeFlashlightControl(task)
                DeviceActionOperation.VIBRATION -> executeDeviceVibration(task)
                DeviceActionOperation.TEXT_TO_SPEECH -> executeTextToSpeech(task)
                DeviceActionOperation.SHIZUKU_COMMAND -> executeShizukuCommandFromDeviceAction(task)
                DeviceActionOperation.WAIT_DELAY -> executeWaitDelayFromDeviceAction(task)
                // 系统操作功能
                DeviceActionOperation.QUICK_SETTINGS -> executeSystemOperation(task)
                DeviceActionOperation.POWER_MENU -> executeSystemOperation(task)
                DeviceActionOperation.RECENT_TASKS -> executeSystemOperation(task)
                DeviceActionOperation.APP_DRAWER -> executeSystemOperation(task)
                DeviceActionOperation.ACCESSIBILITY_TOGGLE -> executeSystemOperation(task)
                DeviceActionOperation.BACK_KEY -> executeSystemOperation(task)
            }

            success
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 执行分享文本
     */
    private suspend fun executeShareText(task: DeviceActionTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                if (task.shareText.isEmpty()) {
                    Log.e(TAG, "Share text is empty")
                    return@withContext false
                }

                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    type = "text/plain"
                    putExtra(Intent.EXTRA_TEXT, task.shareText)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }

                val finalIntent = if (task.shareAppPackage.isNotEmpty()) {
                    if (task.shareTargetActivityName.isNotEmpty()) {
                        // 指定特定的分享目标（应用+Activity）
                        shareIntent.apply {
                            component = android.content.ComponentName(task.shareAppPackage, task.shareTargetActivityName)
                        }
                    } else {
                        // 指定特定应用（但不指定Activity）
                        shareIntent.setPackage(task.shareAppPackage)
                        shareIntent
                    }
                } else {
                    // 显示应用选择器
                    Intent.createChooser(shareIntent, "分享文本").apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                }

                context.startActivity(finalIntent)

                val targetInfo = if (task.shareTargetLabel.isNotEmpty() && task.shareTargetLabel != task.shareAppName) {
                    "${task.shareAppName} - ${task.shareTargetLabel}"
                } else {
                    task.shareAppName
                }
                Log.d(TAG, "Share text completed: text=${task.shareText}, target=$targetInfo")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing share text", e)
                false
            }
        }
    }

    /**
     * 执行剪贴板刷新（Android 10+）
     */
    private suspend fun executeClipboardRefresh(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    // Android 10+ 需要创建临时叠加层来读取剪贴板
                    val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

                    // 创建一个临时的不可见叠加层
                    val intent = Intent(context, com.weinuo.quickcommands.service.ClipboardRefreshOverlayService::class.java)
                    context.startService(intent)

                    Log.d(TAG, "Clipboard refresh initiated for Android 10+")
                } else {
                    // Android 10以下可以直接读取剪贴板
                    val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val clipData = clipboardManager.primaryClip
                    Log.d(TAG, "Clipboard refresh completed: hasClip=${clipData != null}")
                }
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing clipboard refresh", e)
                false
            }
        }
    }

    /**
     * 执行启动主屏幕
     */
    private suspend fun executeLaunchHome(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val homeIntent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory(Intent.CATEGORY_HOME)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(homeIntent)

                Log.d(TAG, "Launch home completed")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing launch home", e)
                false
            }
        }
    }

    /**
     * 执行设置剪贴板
     */
    private suspend fun executeSetClipboard(task: DeviceActionTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                if (task.clipboardText.isEmpty()) {
                    Log.e(TAG, "Clipboard text is empty")
                    return@withContext false
                }

                val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clipData = ClipData.newPlainText("设备动作", task.clipboardText)
                clipboardManager.setPrimaryClip(clipData)

                Log.d(TAG, "Set clipboard completed: text=${task.clipboardText}")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing set clipboard", e)
                false
            }
        }
    }

    /**
     * 执行状态栏控制
     */
    private suspend fun executeToggleStatusBar(task: DeviceActionTask): Boolean {
        return try {
            val command = when (task.statusBarOperation) {
                StatusBarOperation.EXPAND -> "cmd statusbar expand-notifications"
                StatusBarOperation.COLLAPSE -> "cmd statusbar collapse"
                StatusBarOperation.TOGGLE -> {
                    // 简化处理：直接展开通知栏
                    "cmd statusbar expand-notifications"
                }
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Toggle status bar completed: operation=${task.statusBarOperation}, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing toggle status bar", e)
            false
        }
    }

    /**
     * 执行语音搜索
     */
    private suspend fun executeVoiceSearch(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val voiceIntent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                    putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                    putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                    putExtra(RecognizerIntent.EXTRA_PROMPT, "请说话...")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }

                // 检查是否有应用可以处理语音识别
                val packageManager = context.packageManager
                if (voiceIntent.resolveActivity(packageManager) != null) {
                    context.startActivity(voiceIntent)
                    Log.d(TAG, "Voice search completed")
                    true
                } else {
                    Log.e(TAG, "No app can handle voice recognition")
                    false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing voice search", e)
                false
            }
        }
    }

    /**
     * 执行手电筒控制
     */
    private suspend fun executeFlashlightControl(task: DeviceActionTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
                val cameraId = cameraManager.cameraIdList.firstOrNull()

                if (cameraId == null) {
                    Log.e(TAG, "No camera found for flashlight")
                    return@withContext false
                }

                when (task.flashlightOperation) {
                    FlashlightControlType.TURN_ON -> {
                        cameraManager.setTorchMode(cameraId, true)
                        Log.d(TAG, "Flashlight turned on")
                    }
                    FlashlightControlType.TURN_OFF -> {
                        cameraManager.setTorchMode(cameraId, false)
                        Log.d(TAG, "Flashlight turned off")
                    }
                    FlashlightControlType.TOGGLE -> {
                        // 获取当前手电筒状态并切换
                        try {
                            // 尝试获取当前状态，如果失败则默认开启
                            val currentState = try {
                                // 这里可以通过其他方式获取当前状态，暂时简化处理
                                false // 默认假设关闭状态
                            } catch (e: Exception) {
                                false
                            }
                            val newState = !currentState
                            cameraManager.setTorchMode(cameraId, newState)
                            Log.d(TAG, "Flashlight toggled: ${if (newState) "turned on" else "turned off"}")
                        } catch (e: Exception) {
                            // 如果切换失败，尝试直接开启
                            cameraManager.setTorchMode(cameraId, true)
                            Log.d(TAG, "Flashlight toggle failed, defaulted to turn on")
                        }
                    }
                }
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing flashlight control", e)
                false
            }
        }
    }

    /**
     * 执行设备震动
     */
    private suspend fun executeDeviceVibration(task: DeviceActionTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 将设备动作震动模式转换为VibrationManager的配置
                val vibrationConfig = when (task.vibrationPattern) {
                    DeviceVibrationPattern.WAVE -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 500L,
                        interval = 200L,
                        repeatCount = 3
                    )
                    DeviceVibrationPattern.SHORT_BUZZ -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.SINGLE,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 200L
                    )
                    DeviceVibrationPattern.LONG_BUZZ -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.SINGLE,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 1000L
                    )
                    DeviceVibrationPattern.QUICK -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.STRONG,
                        duration = 100L,
                        interval = 100L,
                        repeatCount = 5
                    )
                    DeviceVibrationPattern.SLOW -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 800L,
                        interval = 800L,
                        repeatCount = 3
                    )
                    DeviceVibrationPattern.INCREASING -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.ESCALATING,
                        intensity = VibrationManager.VibrationIntensity.LIGHT,
                        duration = 2000L
                    )
                    DeviceVibrationPattern.CONSTANT -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.CONTINUOUS,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 3000L
                    )
                    DeviceVibrationPattern.DECREASING -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.ESCALATING,
                        intensity = VibrationManager.VibrationIntensity.STRONG,
                        duration = 2000L
                    )
                    DeviceVibrationPattern.MINIMAL -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.SINGLE,
                        intensity = VibrationManager.VibrationIntensity.LIGHT,
                        duration = 50L
                    )
                    DeviceVibrationPattern.LIGHT -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.SINGLE,
                        intensity = VibrationManager.VibrationIntensity.LIGHT,
                        duration = 300L
                    )
                    DeviceVibrationPattern.FANTASY -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 150L,
                        interval = 100L,
                        repeatCount = 8
                    )
                    DeviceVibrationPattern.GAME_OVER -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.STRONG,
                        duration = 300L,
                        interval = 200L,
                        repeatCount = 4
                    )
                    DeviceVibrationPattern.DOUBLE_BUZZ -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 200L,
                        interval = 100L,
                        repeatCount = 2
                    )
                    DeviceVibrationPattern.TRIPLE_BUZZ -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 200L,
                        interval = 100L,
                        repeatCount = 3
                    )
                    DeviceVibrationPattern.HEARTBEAT -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.INTERMITTENT,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 100L,
                        interval = 500L,
                        repeatCount = 10
                    )
                    DeviceVibrationPattern.NOTIFICATION -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.SINGLE,
                        intensity = VibrationManager.VibrationIntensity.LIGHT,
                        duration = 250L
                    )
                    DeviceVibrationPattern.CUSTOM -> VibrationManager.VibrationConfig(
                        mode = VibrationManager.VibrationMode.SINGLE,
                        intensity = VibrationManager.VibrationIntensity.MEDIUM,
                        duration = 500L
                    )
                }

                VibrationManager.startVibration(context, vibrationConfig)
                Log.d(TAG, "Device vibration completed: pattern=${task.vibrationPattern}")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing device vibration", e)
                false
            }
        }
    }

    /**
     * 执行文字转语音
     */
    private suspend fun executeTextToSpeech(task: DeviceActionTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                if (task.ttsText.isEmpty()) {
                    Log.e(TAG, "TTS text is empty")
                    return@withContext false
                }

                // 创建TTS实例
                var ttsInstance: TextToSpeech? = null
                ttsInstance = TextToSpeech(context) { status ->
                    if (status == TextToSpeech.SUCCESS) {
                        try {
                            ttsInstance?.let { tts ->
                                // 设置语言
                                val locale = when (task.ttsLanguage) {
                                    "zh-CN" -> Locale.SIMPLIFIED_CHINESE
                                    "zh-TW" -> Locale.TRADITIONAL_CHINESE
                                    "en-US" -> Locale.US
                                    "en-GB" -> Locale.UK
                                    "ja-JP" -> Locale.JAPAN
                                    "ko-KR" -> Locale.KOREA
                                    else -> Locale.getDefault()
                                }
                                tts.language = locale

                                // 设置音调和速度
                                tts.setPitch(task.ttsPitch)
                                tts.setSpeechRate(task.ttsSpeed)

                                // 设置音频属性
                                val audioAttributes = AudioAttributes.Builder()
                                    .setUsage(
                                        when (task.ttsAudioStream) {
                                            TTSAudioStream.RING -> AudioAttributes.USAGE_NOTIFICATION_RINGTONE
                                            TTSAudioStream.NOTIFICATION -> AudioAttributes.USAGE_NOTIFICATION
                                            TTSAudioStream.ALARM -> AudioAttributes.USAGE_ALARM
                                            TTSAudioStream.MUSIC -> AudioAttributes.USAGE_MEDIA
                                            TTSAudioStream.SYSTEM -> AudioAttributes.USAGE_ASSISTANCE_SONIFICATION
                                            TTSAudioStream.VOICE_CALL -> AudioAttributes.USAGE_VOICE_COMMUNICATION
                                        }
                                    )
                                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                                    .build()

                                tts.setAudioAttributes(audioAttributes)

                                // 处理文本（如果需要逐个说出数字）
                                val processedText = if (task.ttsSpellOutNumbers) {
                                    task.ttsText.replace(Regex("\\d")) { matchResult ->
                                        " ${matchResult.value} "
                                    }
                                } else {
                                    task.ttsText
                                }

                                // 开始朗读
                                val queueMode = if (task.ttsQueueMode) {
                                    TextToSpeech.QUEUE_ADD
                                } else {
                                    TextToSpeech.QUEUE_FLUSH
                                }

                                val utteranceId = "device_action_tts_${System.currentTimeMillis()}"

                                if (task.ttsWaitForCompletion) {
                                    // 如果需要等待完成，设置监听器
                                    tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                                        override fun onStart(utteranceId: String?) {
                                            Log.d(TAG, "TTS started: $utteranceId")
                                        }

                                        override fun onDone(utteranceId: String?) {
                                            Log.d(TAG, "TTS completed: $utteranceId")
                                            tts.shutdown()
                                        }

                                        override fun onError(utteranceId: String?) {
                                            Log.e(TAG, "TTS error: $utteranceId")
                                            tts.shutdown()
                                        }
                                    })
                                }

                                val result = tts.speak(processedText, queueMode, null, utteranceId)

                                if (result == TextToSpeech.SUCCESS) {
                                    Log.d(TAG, "TTS speak initiated: text=${task.ttsText}, language=${task.ttsLanguage}")

                                    // 如果不需要等待完成，立即关闭TTS
                                    if (!task.ttsWaitForCompletion) {
                                        // 延迟关闭，给TTS一些时间开始播放
                                        Handler(Looper.getMainLooper()).postDelayed({
                                            tts.shutdown()
                                        }, 1000)
                                    }
                                } else {
                                    Log.e(TAG, "TTS speak failed: result=$result")
                                    tts.shutdown()
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error in TTS initialization callback", e)
                            ttsInstance?.shutdown()
                        }
                    } else {
                        Log.e(TAG, "TTS initialization failed: status=$status")
                    }
                }

                Log.d(TAG, "TTS initialization started")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing text to speech", e)
                false
            }
        }
    }

    /**
     * 执行Shizuku命令（从设备动作任务）
     */
    private suspend fun executeShizukuCommandFromDeviceAction(task: DeviceActionTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (task.shizukuCommands.isEmpty()) {
                    Log.e(TAG, "Shizuku commands are empty")
                    return@withContext false
                }

                // 分割多个命令（以分号分隔）
                val commands = task.shizukuCommands.split(";").filter { it.isNotBlank() }

                var allSuccessful = true
                for (command in commands) {
                    val trimmedCommand = command.trim()
                    if (trimmedCommand.isNotEmpty()) {
                        val result = ShizukuManager.executeCommand(trimmedCommand)
                        Log.d(TAG, "Shizuku command result: $result")

                        // 如果结果包含错误信息，认为命令执行失败
                        if (result.contains("Error:") || result.contains("错误") || result.contains("失败") ||
                            result.contains("Exception") || result.contains("not found") || result.contains("permission denied")) {
                            allSuccessful = false
                            Log.w(TAG, "Shizuku command failed: $trimmedCommand, result: $result")
                        }
                    }
                }

                Log.d(TAG, "Shizuku commands from device action completed: commands='${task.shizukuCommands}', success=$allSuccessful")
                allSuccessful
            } catch (e: Exception) {
                Log.e(TAG, "Error executing Shizuku commands from device action", e)
                false
            }
        }
    }

    /**
     * 执行等待延迟（从设备动作任务）
     */
    private suspend fun executeWaitDelayFromDeviceAction(task: DeviceActionTask): Boolean {
        return try {
            val totalMillis = (task.waitMinutes * 60 + task.waitSeconds) * 1000L
            if (totalMillis > 0) {
                delay(totalMillis)
                Log.d(TAG, "Wait delay from device action completed: ${task.waitMinutes}分${task.waitSeconds}秒")
            } else {
                Log.d(TAG, "Wait delay from device action: 0秒，立即完成")
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error executing wait delay from device action", e)
            false
        }
    }

    /**
     * 执行设备设置任务
     *
     * @param task 设备设置任务
     * @return 任务是否成功执行
     */
    private suspend fun executeDeviceSettingsTask(task: DeviceSettingsTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {

                val success = when (task.operation) {
                    DeviceSettingsOperation.INVERT_COLORS -> executeInvertColors(task)
                    DeviceSettingsOperation.FONT_SIZE -> executeFontSize(task)
                    DeviceSettingsOperation.ENTER_SCREENSAVER -> executeEnterScreensaver()
                    DeviceSettingsOperation.AUTO_ROTATE -> executeAutoRotate(task)
                    DeviceSettingsOperation.ACCESSIBILITY_SERVICE -> executeAccessibilityService(task)
                    DeviceSettingsOperation.DISPLAY_DENSITY -> executeDisplayDensity(task)
                    DeviceSettingsOperation.IMMERSIVE_MODE -> executeImmersiveMode(task)
                    DeviceSettingsOperation.DARK_THEME -> executeDarkTheme(task)
                    DeviceSettingsOperation.DEMO_MODE -> executeDemoMode(task)
                    DeviceSettingsOperation.AMBIENT_DISPLAY -> executeAmbientDisplay(task)
                    DeviceSettingsOperation.POWER_SAVE_MODE -> executePowerSaveMode(task)
                    DeviceSettingsOperation.SYSTEM_SETTINGS -> executeSystemSettings(task)
                    DeviceSettingsOperation.SET_WALLPAPER -> executeSetWallpaper(task)
                    DeviceSettingsOperation.SCREEN_LOCK -> executeScreenLock(task)
                    DeviceSettingsOperation.DIGITAL_ASSISTANT -> executeDigitalAssistant(task)
                    DeviceSettingsOperation.DEFAULT_KEYBOARD -> executeDefaultKeyboard(task)
                    DeviceSettingsOperation.KEYBOARD_HINT -> executeKeyboardHint()
                    DeviceSettingsOperation.DRIVING_MODE -> executeDrivingMode(task)
                }



                Log.d(TAG, "Device settings task completed: operation=${task.operation}, result=$success")
                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行反色设置
     */
    private suspend fun executeInvertColors(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.invertColorsOperation) {
                TriStateOperation.TOGGLE -> {
                    // 检查当前状态并切换
                    val currentState = ShizukuManager.executeCommand("settings get secure accessibility_display_inversion_enabled")
                    val isEnabled = currentState.trim() == "1"
                    if (isEnabled) {
                        "settings put secure accessibility_display_inversion_enabled 0"
                    } else {
                        "settings put secure accessibility_display_inversion_enabled 1"
                    }
                }
                TriStateOperation.ENABLE -> "settings put secure accessibility_display_inversion_enabled 1"
                TriStateOperation.DISABLE -> "settings put secure accessibility_display_inversion_enabled 0"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Invert colors completed: operation=${task.invertColorsOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing invert colors", e)
            false
        }
    }

    /**
     * 执行字体大小设置
     */
    private suspend fun executeFontSize(task: DeviceSettingsTask): Boolean {
        return try {
            // 字体大小范围通常是 0.85 到 1.3，100% 对应 1.0
            val fontScale = task.fontSizePercentage / 100.0f
            val command = "settings put system font_scale $fontScale"

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Font size completed: percentage=${task.fontSizePercentage}, scale=$fontScale, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing font size", e)
            false
        }
    }

    /**
     * 执行进入屏保模式
     */
    private suspend fun executeEnterScreensaver(): Boolean {
        return try {
            // 启动屏保
            val command = "am start -a android.intent.action.MAIN -c android.intent.category.DESK_DOCK"
            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Enter screensaver completed: result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing enter screensaver", e)
            false
        }
    }

    /**
     * 执行屏幕自动旋转设置
     */
    private suspend fun executeAutoRotate(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.autoRotateOperation) {
                TriStateOperation.TOGGLE -> {
                    // 检查当前状态并切换
                    val currentState = ShizukuManager.executeCommand("settings get system accelerometer_rotation")
                    val isEnabled = currentState.trim() == "1"
                    if (isEnabled) {
                        "settings put system accelerometer_rotation 0"
                    } else {
                        "settings put system accelerometer_rotation 1"
                    }
                }
                TriStateOperation.ENABLE -> "settings put system accelerometer_rotation 1"
                TriStateOperation.DISABLE -> "settings put system accelerometer_rotation 0"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Auto rotate completed: operation=${task.autoRotateOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing auto rotate", e)
            false
        }
    }

    /**
     * 执行无障碍服务设置
     */
    private suspend fun executeAccessibilityService(task: DeviceSettingsTask): Boolean {
        return try {
            when (task.accessibilityServiceOperation) {
                TriStateOperation.TOGGLE -> {
                    // 切换模式：获取所有已启用的无障碍服务并切换状态
                    val enabledServices = ShizukuManager.executeCommand("settings get secure enabled_accessibility_services")
                    val hasAnyEnabled = enabledServices.isNotEmpty() && enabledServices != "null"

                    val command = if (hasAnyEnabled) {
                        // 禁用所有无障碍服务
                        "settings put secure enabled_accessibility_services null"
                    } else {
                        // 启用指定的无障碍服务（如果有指定）
                        if (task.accessibilityServicePackage.isNotEmpty()) {
                            "settings put secure enabled_accessibility_services ${task.accessibilityServicePackage}"
                        } else {
                            // 没有指定服务，无法启用
                            Log.w(TAG, "No accessibility service package specified for toggle operation")
                            return false
                        }
                    }

                    val result = ShizukuManager.executeCommand(command)
                    !result.contains("Error:") && !result.contains("错误")
                }
                TriStateOperation.ENABLE -> {
                    if (task.accessibilityServicePackage.isNotEmpty()) {
                        val command = "settings put secure enabled_accessibility_services ${task.accessibilityServicePackage}"
                        val result = ShizukuManager.executeCommand(command)
                        !result.contains("Error:") && !result.contains("错误")
                    } else {
                        Log.w(TAG, "No accessibility service package specified for enable operation")
                        false
                    }
                }
                TriStateOperation.DISABLE -> {
                    val command = "settings put secure enabled_accessibility_services null"
                    val result = ShizukuManager.executeCommand(command)
                    !result.contains("Error:") && !result.contains("错误")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing accessibility service", e)
            false
        }
    }

    /**
     * 执行显示密度设置
     */
    private suspend fun executeDisplayDensity(task: DeviceSettingsTask): Boolean {
        return try {
            // 获取默认密度
            val defaultDensity = ShizukuManager.executeCommand("wm density").let { result ->
                // 解析 "Physical density: 420" 格式
                val regex = "Physical density: (\\d+)".toRegex()
                regex.find(result)?.groupValues?.get(1)?.toIntOrNull() ?: 420
            }

            // 计算新密度
            val newDensity = (defaultDensity * task.displayDensityPercentage / 100.0).toInt()
            val command = "wm density $newDensity"

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Display density completed: percentage=${task.displayDensityPercentage}, density=$newDensity, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing display density", e)
            false
        }
    }

    /**
     * 执行沉浸模式设置
     */
    private suspend fun executeImmersiveMode(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.immersiveModeType) {
                ImmersiveModeType.DISABLE -> "settings put global policy_control null"
                ImmersiveModeType.HIDE_NAVIGATION -> "settings put global policy_control immersive.navigation=*"
                ImmersiveModeType.HIDE_STATUS_BAR -> "settings put global policy_control immersive.status=*"
                ImmersiveModeType.FULL_IMMERSIVE -> "settings put global policy_control immersive.full=*"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Immersive mode completed: type=${task.immersiveModeType}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing immersive mode", e)
            false
        }
    }

    /**
     * 执行深色主题设置
     */
    private suspend fun executeDarkTheme(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.darkThemeOperation) {
                TriStateOperation.TOGGLE -> {
                    // 检查当前状态并切换
                    val currentState = ShizukuManager.executeCommand("settings get secure ui_night_mode")
                    val isDarkMode = currentState.trim() == "2"
                    if (isDarkMode) {
                        "settings put secure ui_night_mode 1"
                    } else {
                        "settings put secure ui_night_mode 2"
                    }
                }
                TriStateOperation.ENABLE -> "settings put secure ui_night_mode 2"
                TriStateOperation.DISABLE -> "settings put secure ui_night_mode 1"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Dark theme completed: operation=${task.darkThemeOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing dark theme", e)
            false
        }
    }

    /**
     * 执行演示模式设置
     */
    private suspend fun executeDemoMode(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.demoModeOperation) {
                TriStateOperation.TOGGLE -> {
                    // 检查当前状态并切换
                    val currentState = ShizukuManager.executeCommand("settings get global sysui_demo_allowed")
                    val isEnabled = currentState.trim() == "1"
                    if (isEnabled) {
                        "settings put global sysui_demo_allowed 0"
                    } else {
                        "settings put global sysui_demo_allowed 1"
                    }
                }
                TriStateOperation.ENABLE -> "settings put global sysui_demo_allowed 1"
                TriStateOperation.DISABLE -> "settings put global sysui_demo_allowed 0"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Demo mode completed: operation=${task.demoModeOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing demo mode", e)
            false
        }
    }

    /**
     * 执行环境显示设置
     */
    private suspend fun executeAmbientDisplay(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.ambientDisplayMode) {
                AmbientDisplayMode.NOTIFICATION_WAKE -> "settings put secure doze_enabled 1"
                AmbientDisplayMode.ALWAYS_ON -> "settings put secure doze_always_on 1"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Ambient display completed: mode=${task.ambientDisplayMode}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing ambient display", e)
            false
        }
    }

    /**
     * 执行省电模式设置
     */
    private suspend fun executePowerSaveMode(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.powerSaveModeOperation) {
                TriStateOperation.TOGGLE -> {
                    // 检查当前状态并切换
                    val currentState = ShizukuManager.executeCommand("settings get global low_power")
                    val isEnabled = currentState.trim() == "1"
                    if (isEnabled) {
                        "settings put global low_power 0"
                    } else {
                        "settings put global low_power 1"
                    }
                }
                TriStateOperation.ENABLE -> "settings put global low_power 1"
                TriStateOperation.DISABLE -> "settings put global low_power 0"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Power save mode completed: operation=${task.powerSaveModeOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing power save mode", e)
            false
        }
    }

    /**
     * 执行系统设置
     */
    private suspend fun executeSystemSettings(task: DeviceSettingsTask): Boolean {
        return try {
            if (task.systemSettingsKey.isEmpty()) {
                Log.w(TAG, "System settings key is empty")
                return false
            }

            val value = when (task.systemSettingsValueType) {
                SystemSettingsValueType.INTEGER -> task.systemSettingsValue.toIntOrNull()?.toString() ?: task.systemSettingsValue
                SystemSettingsValueType.FLOAT -> task.systemSettingsValue.toFloatOrNull()?.toString() ?: task.systemSettingsValue
                SystemSettingsValueType.LONG -> task.systemSettingsValue.toLongOrNull()?.toString() ?: task.systemSettingsValue
                SystemSettingsValueType.STRING -> task.systemSettingsValue
            }

            val command = "settings put ${task.systemSettingsTable.tableName} ${task.systemSettingsKey} $value"
            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "System settings completed: table=${task.systemSettingsTable.tableName}, key=${task.systemSettingsKey}, value=$value, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing system settings", e)
            false
        }
    }

    /**
     * 执行设置壁纸
     */
    private suspend fun executeSetWallpaper(task: DeviceSettingsTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                when (task.wallpaperType) {
                    WallpaperType.IMAGE -> {
                        if (task.wallpaperImagePath.isEmpty()) {
                            Log.w(TAG, "Wallpaper image path is empty")
                            return@withContext false
                        }

                        val wallpaperManager = WallpaperManager.getInstance(context)
                        val bitmap = android.graphics.BitmapFactory.decodeFile(task.wallpaperImagePath)

                        if (bitmap == null) {
                            Log.e(TAG, "Failed to decode wallpaper image: ${task.wallpaperImagePath}")
                            return@withContext false
                        }

                        when (task.wallpaperLocation) {
                            WallpaperLocation.HOME_SCREEN -> wallpaperManager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_SYSTEM)
                            WallpaperLocation.LOCK_SCREEN -> wallpaperManager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_LOCK)
                            WallpaperLocation.BOTH -> wallpaperManager.setBitmap(bitmap)
                        }

                        Log.d(TAG, "Set wallpaper completed: path=${task.wallpaperImagePath}, location=${task.wallpaperLocation}")
                        true
                    }
                    WallpaperType.LIVE_WALLPAPER -> {
                        if (task.liveWallpaperPackage.isNotEmpty()) {
                            // 如果指定了动态壁纸包名，尝试直接启动该应用的动态壁纸预览
                            try {
                                val intent = Intent(WallpaperManager.ACTION_CHANGE_LIVE_WALLPAPER)
                                intent.putExtra(WallpaperManager.EXTRA_LIVE_WALLPAPER_COMPONENT,
                                    android.content.ComponentName(task.liveWallpaperPackage, "${task.liveWallpaperPackage}.LiveWallpaperService"))
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                context.startActivity(intent)

                                Log.d(TAG, "Live wallpaper preview started for package: ${task.liveWallpaperPackage}")
                                true
                            } catch (e: Exception) {
                                Log.w(TAG, "Failed to start live wallpaper preview, falling back to chooser", e)
                                // 如果直接启动失败，回退到壁纸选择器
                                val intent = Intent(WallpaperManager.ACTION_LIVE_WALLPAPER_CHOOSER)
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                context.startActivity(intent)

                                Log.d(TAG, "Live wallpaper chooser started as fallback")
                                true
                            }
                        } else {
                            // 如果没有指定包名，启动壁纸选择器
                            val intent = Intent(WallpaperManager.ACTION_LIVE_WALLPAPER_CHOOSER)
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            context.startActivity(intent)

                            Log.d(TAG, "Live wallpaper chooser started")
                            true
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing set wallpaper", e)
                false
            }
        }
    }

    /**
     * 执行屏幕锁定设置
     */
    private suspend fun executeScreenLock(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.screenLockOperation) {
                TriStateOperation.ENABLE -> "input keyevent 26" // 电源键锁屏
                TriStateOperation.DISABLE -> {
                    // 禁用锁屏需要设置锁屏类型为无
                    "settings put secure lockscreen.disabled 1"
                }
                TriStateOperation.TOGGLE -> {
                    // 切换模式：检查当前锁屏状态
                    val currentState = ShizukuManager.executeCommand("settings get secure lockscreen.disabled")
                    val isDisabled = currentState.trim() == "1"
                    if (isDisabled) {
                        "settings put secure lockscreen.disabled 0"
                    } else {
                        "settings put secure lockscreen.disabled 1"
                    }
                }
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Screen lock completed: operation=${task.screenLockOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing screen lock", e)
            false
        }
    }

    /**
     * 执行数字助理设置
     */
    private suspend fun executeDigitalAssistant(task: DeviceSettingsTask): Boolean {
        return try {
            if (task.digitalAssistantPackage.isEmpty()) {
                Log.w(TAG, "Digital assistant package is empty")
                return false
            }

            // 数字助理设置 - 主要设置assistant设置项
            // 其他服务设置项可能因应用而异，所以只设置主要的assistant设置
            val commands = listOf(
                "settings put secure assistant ${task.digitalAssistantPackage}"
            )

            var allSuccess = true
            val results = mutableListOf<String>()

            for (command in commands) {
                try {
                    val result = ShizukuManager.executeCommand(command)
                    results.add("Command: $command, Result: $result")

                    if (result.contains("Error:") || result.contains("错误") || result.contains("Exception") ||
                        result.contains("Permission denied") || result.contains("not found") ||
                        result.contains("failed") || result.contains("Failed")) {
                        allSuccess = false
                        Log.w(TAG, "Digital assistant command failed: $command, result: $result")
                    }
                } catch (e: Exception) {
                    allSuccess = false
                    Log.e(TAG, "Error executing digital assistant command: $command", e)
                    results.add("Command: $command, Error: ${e.message}")
                }
            }

            Log.d(TAG, "Digital assistant completed: package=${task.digitalAssistantPackage}, success=$allSuccess")
            Log.d(TAG, "Digital assistant results: ${results.joinToString("; ")}")
            allSuccess
        } catch (e: Exception) {
            Log.e(TAG, "Error executing digital assistant", e)
            false
        }
    }

    /**
     * 执行默认键盘设置
     */
    private suspend fun executeDefaultKeyboard(task: DeviceSettingsTask): Boolean {
        return try {
            if (task.keyboardPackage.isEmpty()) {
                Log.w(TAG, "Keyboard package is empty")
                return false
            }

            // 键盘设置需要先启用输入法，然后设置为默认
            val commands = listOf(
                "ime enable ${task.keyboardPackage}",  // 先启用输入法
                "ime set ${task.keyboardPackage}"      // 然后设置为默认
            )

            var allSuccess = true
            val results = mutableListOf<String>()

            for (command in commands) {
                try {
                    val result = ShizukuManager.executeCommand(command)
                    results.add("Command: $command, Result: $result")

                    if (result.contains("Error:") || result.contains("错误") || result.contains("Exception") ||
                        result.contains("Permission denied") || result.contains("not found") ||
                        result.contains("failed") || result.contains("Failed")) {
                        // 对于ime enable命令，如果输入法已经启用，可能会返回错误，但这不是真正的失败
                        if (command.startsWith("ime enable") && (result.contains("already enabled") || result.contains("已启用"))) {
                            Log.d(TAG, "Keyboard already enabled: ${task.keyboardPackage}")
                        } else {
                            allSuccess = false
                            Log.w(TAG, "Keyboard command failed: $command, result: $result")
                        }
                    }
                } catch (e: Exception) {
                    allSuccess = false
                    Log.e(TAG, "Error executing keyboard command: $command", e)
                    results.add("Command: $command, Error: ${e.message}")
                }
            }

            Log.d(TAG, "Default keyboard completed: package=${task.keyboardPackage}, success=$allSuccess")
            Log.d(TAG, "Default keyboard results: ${results.joinToString("; ")}")
            allSuccess
        } catch (e: Exception) {
            Log.e(TAG, "Error executing default keyboard", e)
            false
        }
    }

    /**
     * 执行键盘提示
     */
    private suspend fun executeKeyboardHint(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 显示输入法选择器
                val inputMethodManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                inputMethodManager.showInputMethodPicker()

                Log.d(TAG, "Keyboard hint (input method picker) shown")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error executing keyboard hint", e)
                false
            }
        }
    }

    /**
     * 执行驾驶模式设置
     */
    private suspend fun executeDrivingMode(task: DeviceSettingsTask): Boolean {
        return try {
            val command = when (task.drivingModeOperation) {
                TriStateOperation.TOGGLE -> {
                    // 检查当前状态并切换
                    val currentState = ShizukuManager.executeCommand("settings get global zen_mode")
                    val isEnabled = currentState.trim() != "0"
                    if (isEnabled) {
                        "settings put global zen_mode 0"
                    } else {
                        "settings put global zen_mode 1"
                    }
                }
                TriStateOperation.ENABLE -> "settings put global zen_mode 1"
                TriStateOperation.DISABLE -> "settings put global zen_mode 0"
            }

            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Driving mode completed: operation=${task.drivingModeOperation}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing driving mode", e)
            false
        }
    }

    /**
     * 执行通知任务
     */
    private suspend fun executeNotificationTask(task: NotificationTask): Boolean {
        return try {
            // 执行时权限检查：通知权限检查（针对需要权限的操作）
            when (task.operation) {
                NotificationOperation.SHOW_NOTIFICATION,
                NotificationOperation.CLEAR_NOTIFICATIONS,
                NotificationOperation.FLOATING_NOTIFICATION_CONTROL,
                NotificationOperation.REPLY_NOTIFICATION,
                NotificationOperation.INTERACT_NOTIFICATION -> {
                    if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)) {
                        Log.e(TAG, "Notification permission not granted for notification task execution")
                        return false
                    }
                }
                NotificationOperation.SHOW_DIALOG -> {
                    if (!com.weinuo.quickcommands.utils.OverlayPermissionUtil.hasOverlayPermission(context)) {
                        Log.e(TAG, "Overlay permission not granted for dialog notification task execution")
                        return false
                    }
                }
                NotificationOperation.SHOW_BUBBLE -> {
                    // 检查通知权限和气泡通知权限
                    if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)) {
                        Log.e(TAG, "Notification permission not granted for bubble notification task execution")
                        return false
                    }
                    if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.areBubblesAllowed(context)) {
                        Log.e(TAG, "Bubble notification permission not granted for bubble notification task execution")
                        return false
                    }
                }
                NotificationOperation.SHOW_TOAST,
                NotificationOperation.RESTORE_HIDDEN_NOTIFICATIONS,
                NotificationOperation.RINGTONE_SETTINGS -> {
                    // 这些操作不需要特殊权限检查
                }
            }

            when (task.operation) {
                NotificationOperation.SHOW_TOAST -> {
                    // 显示Toast消息
                    withContext(Dispatchers.Main) {
                        if (task.toastMessage.isEmpty()) {
                            Log.e(TAG, "Toast message is empty")
                            return@withContext false
                        }

                        // 根据持续时间确定Toast长度
                        val toastLength = when {
                            task.toastDuration <= 2000 -> Toast.LENGTH_SHORT
                            else -> Toast.LENGTH_LONG
                        }

                        val toast = Toast.makeText(context, task.toastMessage, toastLength)
                        toast.show()

                        // 如果指定了自定义持续时间，使用Handler延时取消
                        if (task.toastDuration != 2000) {
                            Handler(Looper.getMainLooper()).postDelayed({
                                toast.cancel()
                            }, task.toastDuration.toLong())
                        }

                        // 处理震动
                        if (task.enableVibration) {
                            try {
                                val vibrationMode = VibrationManager.VibrationMode.valueOf(task.vibrationMode)
                                val vibrationIntensity = VibrationManager.VibrationIntensity.valueOf(task.vibrationIntensity)

                                val vibrationConfig = VibrationManager.VibrationConfig(
                                    mode = vibrationMode,
                                    intensity = vibrationIntensity,
                                    duration = task.toastDuration.toLong() // 使用Toast的持续时间作为震动时长
                                )

                                VibrationManager.startVibration(context, vibrationConfig)

                                Log.d(TAG, "Toast vibration started: mode=${task.vibrationMode}, intensity=${task.vibrationIntensity}")
                            } catch (e: Exception) {
                                Log.e(TAG, "Error starting toast vibration", e)
                            }
                        }
                    }
                    Log.d(TAG, "Toast message shown: ${task.toastMessage}, vibration=${task.enableVibration}")
                    true
                }

                NotificationOperation.RESTORE_HIDDEN_NOTIFICATIONS -> {
                    // 恢复隐藏的通知
                    val success = when (task.restoreNotificationMode) {
                        NotificationRestoreMode.ALL -> {
                            com.weinuo.quickcommands.utils.NotificationHelper.restoreHiddenNotifications(
                                context = context,
                                packageName = ""
                            )
                        }
                        NotificationRestoreMode.SPECIFIC_APP -> {
                            com.weinuo.quickcommands.utils.NotificationHelper.restoreHiddenNotifications(
                                context = context,
                                packageName = task.restoreNotificationPackage
                            )
                        }
                    }
                    Log.d(TAG, "Restore hidden notifications: success=$success, mode=${task.restoreNotificationMode}")
                    success
                }

                NotificationOperation.SHOW_DIALOG -> {
                    // 显示对话框
                    withContext(Dispatchers.Main) {
                        // 这里需要通过Activity或Fragment来显示对话框
                        // 由于在后台执行，我们使用系统悬浮窗的方式显示
                        showSystemDialog(task)
                    }
                    Log.d(TAG, "Dialog shown: title=${task.dialogTitle}")
                    true
                }

                NotificationOperation.SHOW_BUBBLE -> {
                    // 显示气泡通知
                    val success = showBubbleNotification(task)
                    Log.d(TAG, "Bubble notification shown: success=$success, htmlFormat=${task.bubbleHtmlFormat}")
                    success
                }

                NotificationOperation.SHOW_NOTIFICATION -> {
                    // 显示系统通知
                    val success = com.weinuo.quickcommands.utils.NotificationHelper.showNotification(
                        context = context,
                        title = task.notificationTitle,
                        text = task.notificationText,
                        channelId = task.notificationChannelId,
                        priority = task.notificationPriority,
                        soundType = task.dialogNotificationSound,
                        iconPath = task.notificationIconPath
                    )
                    Log.d(TAG, "System notification shown: success=$success, iconPath=${task.notificationIconPath}")
                    success
                }

                NotificationOperation.CLEAR_NOTIFICATIONS -> {
                    // 清除通知
                    val success = when (task.clearNotificationMode) {
                        NotificationClearMode.ALL -> {
                            com.weinuo.quickcommands.utils.NotificationHelper.clearNotifications(
                                context = context,
                                packageName = "",
                                notificationId = -1
                            )
                        }
                        NotificationClearMode.SPECIFIC_APP -> {
                            com.weinuo.quickcommands.utils.NotificationHelper.clearNotifications(
                                context = context,
                                packageName = task.clearNotificationPackage,
                                notificationId = task.clearNotificationId
                            )
                        }
                        NotificationClearMode.TRIGGER_BASED -> {
                            // 使用通知触发器清除，这里可以根据具体需求实现
                            // 暂时使用清除所有的逻辑
                            com.weinuo.quickcommands.utils.NotificationHelper.clearNotifications(
                                context = context,
                                packageName = "",
                                notificationId = -1
                            )
                        }
                    }
                    Log.d(TAG, "Notifications cleared: success=$success, mode=${task.clearNotificationMode}")
                    success
                }

                NotificationOperation.FLOATING_NOTIFICATION_CONTROL -> {
                    // 浮动通知控制
                    val success = when (task.floatingNotificationOperation) {
                        SwitchOperation.ENABLE -> com.weinuo.quickcommands.utils.NotificationHelper.controlFloatingNotifications(context, true)
                        SwitchOperation.DISABLE -> com.weinuo.quickcommands.utils.NotificationHelper.controlFloatingNotifications(context, false)
                        SwitchOperation.TOGGLE -> com.weinuo.quickcommands.utils.NotificationHelper.toggleFloatingNotifications(context)
                    }
                    Log.d(TAG, "Floating notification control: operation=${task.floatingNotificationOperation}, success=$success")
                    success
                }

                NotificationOperation.RINGTONE_SETTINGS -> {
                    // 检查铃声URI是否有效
                    if (task.selectedRingtoneUri.isEmpty()) {
                        Log.e(TAG, "No ringtone URI provided for notification ringtone task")
                        false
                    } else {
                        // 设置通知铃声
                        val success = RingtoneHelper.setRingtone(
                            context = context,
                            ringtoneUri = task.selectedRingtoneUri,
                            ringtoneType = RingtoneHelper.RingtoneType.NOTIFICATION
                        )
                        if (success) {

                            Log.d(TAG, "Notification ringtone set successfully: ${task.selectedRingtoneName}")
                        } else {
                            Log.e(TAG, "Failed to set notification ringtone: ${task.selectedRingtoneName}")
                        }
                        success
                    }
                }

                NotificationOperation.REPLY_NOTIFICATION -> {
                    // 通知回复
                    val success = com.weinuo.quickcommands.utils.NotificationHelper.replyToNotification(
                        context = context,
                        packageName = task.replyNotificationPackage,
                        replyText = task.replyNotificationText
                    )
                    Log.d(TAG, "Notification reply sent: success=$success")
                    success
                }

                NotificationOperation.INTERACT_NOTIFICATION -> {
                    // 通知交互
                    val success = com.weinuo.quickcommands.utils.NotificationHelper.interactWithNotification(
                        context = context,
                        packageName = task.interactionNotificationPackage,
                        action = task.interactionNotificationAction.name.lowercase()
                    )
                    Log.d(TAG, "Notification interaction: action=${task.interactionNotificationAction}, success=$success")
                    success
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing notification task: ${task.operation}", e)
            false
        }
    }

    /**
     * 显示系统对话框
     */
    private suspend fun showSystemDialog(task: NotificationTask) {
        try {
            // 检查悬浮窗权限
            if (!com.weinuo.quickcommands.utils.OverlayPermissionUtil.hasOverlayPermission(context)) {
                Log.e(TAG, "No overlay permission for showing dialog")
                return
            }

            // 创建悬浮窗对话框
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val layoutParams = WindowManager.LayoutParams().apply {
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                format = PixelFormat.TRANSLUCENT
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                gravity = Gravity.CENTER
            }

            // 创建对话框视图
            val dialogView = LayoutInflater.from(context).inflate(
                android.R.layout.simple_list_item_2, null
            )

            // 设置对话框内容
            val titleView = dialogView.findViewById<TextView>(android.R.id.text1)
            val messageView = dialogView.findViewById<TextView>(android.R.id.text2)

            titleView.text = task.dialogTitle
            if (task.dialogHtmlFormat) {
                messageView.text = Html.fromHtml(task.dialogText, Html.FROM_HTML_MODE_LEGACY)
            } else {
                messageView.text = task.dialogText
            }

            // 播放通知声音
            if (task.dialogNotificationSound != NotificationSoundType.NONE) {
                playNotificationSound(task.dialogNotificationSound)
            }

            // 显示对话框
            windowManager.addView(dialogView, layoutParams)

            // 设置自动关闭（如果不要求完成后才能后续动作）
            if (!task.dialogRequireCompletion) {
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        windowManager.removeView(dialogView)
                    } catch (e: Exception) {
                        // 忽略移除视图时的异常
                    }
                }, 5000) // 5秒后自动关闭
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error showing system dialog", e)
        }
    }

    /**
     * 显示气泡通知
     */
    private suspend fun showBubbleNotification(task: NotificationTask): Boolean {
        return try {
            // 检查通知权限和气泡通知权限
            if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)) {
                Log.e(TAG, "No notification permission for bubble notification")
                return false
            }

            if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.areBubblesAllowed(context)) {
                Log.e(TAG, "Bubble notifications are not allowed")
                return false
            }

            // 处理HTML格式化的文本
            val displayText = if (task.bubbleHtmlFormat) {
                // 如果启用HTML格式化，先转换HTML然后转为字符串
                Html.fromHtml(task.bubbleText, Html.FROM_HTML_MODE_LEGACY).toString()
            } else {
                task.bubbleText
            }

            // 使用NotificationHelper创建真正的气泡通知
            val success = com.weinuo.quickcommands.utils.NotificationHelper.showBubbleNotification(
                context = context,
                title = task.bubbleTitle,
                text = displayText,
                channelId = "bubble_notifications",
                priority = NotificationCompat.PRIORITY_HIGH,
                soundType = NotificationSoundType.NOTIFICATION
            )

            Log.d(TAG, "Bubble notification shown with HTML format: ${task.bubbleHtmlFormat}")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error showing bubble notification", e)
            false
        }
    }

    /**
     * 播放通知声音
     */
    private fun playNotificationSound(soundType: NotificationSoundType) {
        try {
            val soundUri = when (soundType) {
                NotificationSoundType.RINGTONE -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)
                NotificationSoundType.NOTIFICATION -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                NotificationSoundType.ALARM -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                NotificationSoundType.DEFAULT -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                NotificationSoundType.NONE -> return
            }

            val ringtone = RingtoneManager.getRingtone(context, soundUri)
            ringtone?.play()
        } catch (e: Exception) {
            Log.e(TAG, "Error playing notification sound", e)
        }
    }

    /**
     * 执行位置任务
     *
     * @param task 位置任务
     * @return 任务是否成功执行
     */
    private suspend fun executeLocationTask(task: LocationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 执行时权限检查：位置权限检查（针对需要位置权限的操作）
                when (task.operation) {
                    LocationOperation.SHARE_LOCATION,
                    LocationOperation.FORCE_LOCATION_UPDATE -> {
                        if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                            Log.e(TAG, "Location permission not granted for location task execution")

                            return@withContext false
                        }
                    }
                    LocationOperation.TOGGLE_LOCATION_SERVICE,
                    LocationOperation.SET_LOCATION_UPDATE_FREQUENCY -> {
                        // 这些操作不需要位置权限，但可能需要其他权限
                    }
                }



                val success = when (task.operation) {
                    LocationOperation.SHARE_LOCATION -> executeShareLocation(task)
                    LocationOperation.TOGGLE_LOCATION_SERVICE -> executeToggleLocationService(task)
                    LocationOperation.FORCE_LOCATION_UPDATE -> executeForceLocationUpdate(task)
                    LocationOperation.SET_LOCATION_UPDATE_FREQUENCY -> executeSetLocationUpdateFrequency(task)
                }



                Log.d(TAG, "Location task completed: operation=${task.operation}, result=$success")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing location task", e)



                false
            }
        }
    }

    /**
     * 执行分享位置
     */
    private suspend fun executeShareLocation(task: LocationTask): Boolean {
        return try {
            // 检查位置权限
            if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                Log.e(TAG, "Location permissions not granted for share location")

                return false
            }

            // 检查通讯录权限（短信和联系人功能需要）
            if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
                Log.e(TAG, "Communication permissions not granted for share location")

                return false
            }

            // 直接使用任务中已经包含位置信息的分享消息
            val locationText = task.shareMessage

            when (task.shareMethod) {
                LocationShareMethod.SMS -> {
                    // 使用系统默认短信应用发送位置
                    val intent = Intent(Intent.ACTION_SENDTO).apply {
                        data = Uri.parse("smsto:")
                        putExtra("sms_body", locationText)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(intent)
                }
                LocationShareMethod.CONTACT -> {
                    if (task.contactId.isNotEmpty() && task.contactName.isNotEmpty()) {
                        // 获取联系人的电话号码
                        val contactPhoneNumber = try {
                            com.weinuo.quickcommands.utils.ContactsHelper.getContactPhoneNumber(context, task.contactId)
                        } catch (e: Exception) {
                            Log.e(TAG, "Failed to get contact phone number", e)
                            null
                        }

                        if (contactPhoneNumber != null && contactPhoneNumber.isNotEmpty()) {
                            // 向指定联系人发送位置信息
                            val intent = Intent(Intent.ACTION_SENDTO).apply {
                                data = Uri.parse("smsto:$contactPhoneNumber")
                                putExtra("sms_body", locationText)
                                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            }
                            context.startActivity(intent)
                        } else {
                            Log.e(TAG, "Contact phone number not found for contact: ${task.contactName}")
                            return false
                        }
                    } else {
                        Log.e(TAG, "Contact information is missing for share location")
                        return false
                    }
                }
                LocationShareMethod.PHONE_NUMBER -> {
                    if (task.phoneNumber.isNotEmpty()) {
                        // 向指定手机号发送位置信息
                        val intent = Intent(Intent.ACTION_SENDTO).apply {
                            data = Uri.parse("smsto:${task.phoneNumber}")
                            putExtra("sms_body", locationText)
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        context.startActivity(intent)
                    } else {
                        Log.e(TAG, "Phone number is missing for share location")
                        return false
                    }
                }
            }

            Log.d(TAG, "Share location completed: method=${task.shareMethod}, message=$locationText")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error sharing location", e)
            false
        }
    }

    /**
     * 执行切换定位服务
     */
    private suspend fun executeToggleLocationService(task: LocationTask): Boolean {
        return try {
            when (task.locationServiceControlMethod) {
                LocationServiceControlMethod.SYSTEM_SETTINGS -> {
                    // 打开系统位置设置页面
                    val intent = Intent(android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS).apply {
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(intent)

                    Log.d(TAG, "Opened location settings")
                    true
                }
                LocationServiceControlMethod.SHIZUKU -> {
                    // 使用Shizuku命令控制定位服务
                    val command = when (task.locationServiceOperation) {
                        SwitchOperation.ENABLE -> "settings put secure location_mode 3"
                        SwitchOperation.DISABLE -> "settings put secure location_mode 0"
                        SwitchOperation.TOGGLE -> {
                            // 检测当前状态并切换
                            val currentResult = ShizukuManager.executeCommand("settings get secure location_mode")
                            if (currentResult.trim() == "0") {
                                "settings put secure location_mode 3"
                            } else {
                                "settings put secure location_mode 0"
                            }
                        }
                    }

                    val result = ShizukuManager.executeCommand(command)
                    val success = !result.contains("Error:") && !result.contains("错误")

                    Log.d(TAG, "Location service control completed: operation=${task.locationServiceOperation}, command=$command, result=$result")
                    success
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error toggling location service", e)
            false
        }
    }

    /**
     * 执行强制位置更新
     */
    private suspend fun executeForceLocationUpdate(task: LocationTask): Boolean {
        return try {
            // 检查位置权限
            if (!com.weinuo.quickcommands.utils.LocationPermissionUtil.hasLocationPermission(context)) {
                Log.e(TAG, "Location permissions not granted for force location update")
                return false
            }

            // 使用Shizuku命令强制位置更新
            val command = "am broadcast -a android.location.GPS_ENABLED_CHANGE"
            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Force location update completed: command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error forcing location update", e)
            false
        }
    }

    /**
     * 执行设置位置更新频率
     */
    private suspend fun executeSetLocationUpdateFrequency(task: LocationTask): Boolean {
        return try {
            // 将频率转换为毫秒
            val frequencyMs = when (task.updateFrequencyUnit) {
                LocationUpdateFrequencyUnit.SECONDS -> task.updateFrequencyValue * 1000L
                LocationUpdateFrequencyUnit.MINUTES -> task.updateFrequencyValue * 60 * 1000L
            }

            // 使用Shizuku命令设置位置更新频率（这里使用模拟命令，实际需要根据系统API调整）
            val command = "settings put global location_background_throttle_interval_ms $frequencyMs"
            val result = ShizukuManager.executeCommand(command)
            val success = !result.contains("Error:") && !result.contains("错误")

            Log.d(TAG, "Set location update frequency completed: frequency=${task.updateFrequencyValue}${task.updateFrequencyUnit.displayName}, command=$command, result=$result")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error setting location update frequency", e)
            false
        }
    }

    /**
     * 执行文件操作任务
     *
     * @param task 文件操作任务
     * @return 任务是否成功执行
     */
    private suspend fun executeFileOperationTask(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 执行时权限检查：存储权限检查
                if (!com.weinuo.quickcommands.utils.StoragePermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for file operation task execution")

                    return@withContext false
                }

                val success = when (task.operation) {
                    FileOperation.WRITE_FILE -> executeWriteFile(task)
                    FileOperation.OPEN_FILE -> executeOpenFile(task)
                    FileOperation.FILE_OPERATION -> executeFileOperation(task)
                }

                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行写入文件操作
     */
    private suspend fun executeWriteFile(task: FileOperationTask): Boolean {
        return try {
            // 检查存储权限
            if (!StoragePermissionUtil.hasStoragePermission(context)) {
                Log.e(TAG, "Storage permission not granted for write file operation")
                return false
            }

            val folderPath = task.folderPath
            val fileName = task.fileName
            val content = task.fileContent

            if (folderPath.isEmpty() || fileName.isEmpty()) {
                Log.e(TAG, "Folder path or file name is empty")
                return false
            }

            val folder = File(folderPath)
            if (!folder.exists()) {
                folder.mkdirs()
            }

            val file = File(folder, fileName)

            when (task.writeMode) {
                FileWriteMode.APPEND -> {
                    file.appendText(content + "\n")
                }
                FileWriteMode.OVERWRITE -> {
                    file.writeText(content)
                }
                FileWriteMode.PREPARE_COMMIT -> {
                    // 准备提交模式：先写入临时文件，然后重命名
                    val tempFile = File(folder, "$fileName.tmp")
                    tempFile.writeText(content)
                    tempFile.renameTo(file)
                }
            }

            Log.d(TAG, "Write file completed: ${file.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error writing file", e)
            false
        }
    }

    /**
     * 执行打开文件操作
     */
    private suspend fun executeOpenFile(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val folderPath = task.folderPath
                val fileName = task.fileName

                if (folderPath.isEmpty() || fileName.isEmpty()) {
                    Log.e(TAG, "Folder path or file name is empty")
                    return@withContext false
                }

                val file = File(folderPath, fileName)
                if (!file.exists()) {
                    Log.e(TAG, "File does not exist: ${file.absolutePath}")
                    return@withContext false
                }

                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )

                val intent = Intent(Intent.ACTION_VIEW).apply {
                    setDataAndType(uri, getMimeType(file))
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION

                    // 如果指定了应用名称，尝试设置包名
                    if (task.appName.isNotEmpty()) {
                        // 这里需要根据应用名称查找包名，暂时使用应用名称作为包名
                        setPackage(task.appName)
                    }
                }

                context.startActivity(intent)
                Log.d(TAG, "Open file completed: ${file.absolutePath}")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error opening file", e)
                false
            }
        }
    }

    /**
     * 执行文件操作（复制、移动、删除、压缩、新建文件夹）
     */
    private suspend fun executeFileOperation(task: FileOperationTask): Boolean {
        return try {
            // 检查存储权限
            if (!StoragePermissionUtil.hasStoragePermission(context)) {
                Log.e(TAG, "Storage permission not granted for file operation")
                return false
            }

            when (task.fileOperationType) {
                FileOperationType.COPY -> executeCopyFiles(task)
                FileOperationType.MOVE -> executeMoveFiles(task)
                FileOperationType.DELETE -> executeDeleteFiles(task)
                FileOperationType.COMPRESS -> executeCompressFiles(task)
                FileOperationType.CREATE_FOLDER -> executeCreateFolder(task)
                FileOperationType.RENAME -> executeRenameFile(task)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing file operation", e)
            false
        }
    }

    /**
     * 执行复制文件操作
     */
    private suspend fun executeCopyFiles(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting copy files operation")

                // 检查存储权限
                if (!StoragePermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for copy operation")
                    return@withContext false
                }

                // 解析文件模式
                val (sourcePattern, _) = parseFilePatterns(task.specificFilePattern)

                // 根据用户选择的路径获取源文件列表
                val sourceFiles = getFilesByUserPath(task.sourcePath, task.sourceSelectionMode, sourcePattern)
                if (sourceFiles.isEmpty()) {
                    Log.w(TAG, "No source files found for copy operation")
                    return@withContext false
                }

                // 获取目标目录：优先使用用户选择的路径，否则根据目标选择模式确定
                val targetDir = if (task.targetPath.isNotEmpty()) {
                    getUserSelectedDirectory(task.targetPath)
                } else {
                    getTargetDirectory(task.targetSelectionMode)
                }

                if (targetDir == null || !targetDir.exists()) {
                    Log.e(TAG, "Target directory not found or not accessible. User path: ${task.targetPath}, Target mode: ${task.targetSelectionMode}")
                    return@withContext false
                }

                var successCount = 0
                var totalCount = sourceFiles.size

                // 执行复制操作
                for (sourceFile in sourceFiles) {
                    try {
                        val targetFile = File(targetDir, sourceFile.name)

                        // 如果目标文件已存在，添加序号
                        val finalTargetFile = getUniqueFileName(targetFile)

                        if (sourceFile.isDirectory) {
                            // 复制目录
                            copyDirectory(sourceFile, finalTargetFile)
                        } else {
                            // 复制文件
                            sourceFile.copyTo(finalTargetFile, overwrite = false)
                        }

                        successCount++
                        Log.d(TAG, "Successfully copied: ${sourceFile.name} -> ${finalTargetFile.name}")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to copy file: ${sourceFile.name}", e)
                    }
                }

                val success = successCount > 0
                Log.d(TAG, "Copy operation completed: $successCount/$totalCount files copied")



                success
            } catch (e: Exception) {
                Log.e(TAG, "Error in copy files operation", e)
                false
            }
        }
    }

    /**
     * 执行移动文件操作
     */
    private suspend fun executeMoveFiles(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting move files operation")

                // 检查存储权限
                if (!StoragePermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for move operation")
                    return@withContext false
                }

                // 解析文件模式
                val (sourcePattern, _) = parseFilePatterns(task.specificFilePattern)

                // 根据用户选择的路径获取源文件列表
                val sourceFiles = getFilesByUserPath(task.sourcePath, task.sourceSelectionMode, sourcePattern)
                if (sourceFiles.isEmpty()) {
                    Log.w(TAG, "No source files found for move operation")
                    return@withContext false
                }

                // 获取目标目录：优先使用用户选择的路径，否则根据目标选择模式确定
                val targetDir = if (task.targetPath.isNotEmpty()) {
                    getUserSelectedDirectory(task.targetPath)
                } else {
                    getTargetDirectory(task.targetSelectionMode)
                }

                if (targetDir == null || !targetDir.exists()) {
                    Log.e(TAG, "Target directory not found or not accessible. User path: ${task.targetPath}, Target mode: ${task.targetSelectionMode}")
                    return@withContext false
                }

                var successCount = 0
                var totalCount = sourceFiles.size

                // 执行移动操作
                for (sourceFile in sourceFiles) {
                    try {
                        val targetFile = File(targetDir, sourceFile.name)

                        // 如果目标文件已存在，添加序号
                        val finalTargetFile = getUniqueFileName(targetFile)

                        // 移动文件或目录
                        val moveSuccess = sourceFile.renameTo(finalTargetFile)
                        if (moveSuccess) {
                            successCount++
                            Log.d(TAG, "Successfully moved: ${sourceFile.name} -> ${finalTargetFile.name}")
                        } else {
                            // 如果直接移动失败，尝试复制后删除
                            if (sourceFile.isDirectory) {
                                copyDirectory(sourceFile, finalTargetFile)
                                deleteDirectory(sourceFile)
                            } else {
                                sourceFile.copyTo(finalTargetFile, overwrite = false)
                                sourceFile.delete()
                            }
                            successCount++
                            Log.d(TAG, "Successfully moved (copy+delete): ${sourceFile.name} -> ${finalTargetFile.name}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to move file: ${sourceFile.name}", e)
                    }
                }

                val success = successCount > 0
                Log.d(TAG, "Move operation completed: $successCount/$totalCount files moved")



                success
            } catch (e: Exception) {
                Log.e(TAG, "Error in move files operation", e)
                false
            }
        }
    }

    /**
     * 执行删除文件操作
     */
    private suspend fun executeDeleteFiles(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting delete files operation")

                // 检查存储权限
                if (!StoragePermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for delete operation")
                    return@withContext false
                }

                // 解析文件模式
                val (sourcePattern, _) = parseFilePatterns(task.specificFilePattern)

                // 根据用户选择的路径获取要删除的文件列表
                val filesToDelete = getFilesByUserPath(task.sourcePath, task.sourceSelectionMode, sourcePattern)
                if (filesToDelete.isEmpty()) {
                    Log.w(TAG, "No files found for delete operation")
                    return@withContext false
                }

                var successCount = 0
                var totalCount = filesToDelete.size

                // 执行删除操作
                for (file in filesToDelete) {
                    try {
                        val deleteSuccess = if (file.isDirectory) {
                            deleteDirectory(file)
                        } else {
                            file.delete()
                        }

                        if (deleteSuccess) {
                            successCount++
                            Log.d(TAG, "Successfully deleted: ${file.name}")
                        } else {
                            Log.w(TAG, "Failed to delete: ${file.name}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error deleting file: ${file.name}", e)
                    }
                }

                val success = successCount > 0
                Log.d(TAG, "Delete operation completed: $successCount/$totalCount files deleted")



                success
            } catch (e: Exception) {
                Log.e(TAG, "Error in delete files operation", e)
                false
            }
        }
    }

    /**
     * 执行压缩文件操作
     */
    private suspend fun executeCompressFiles(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting compress files operation")

                // 检查存储权限
                if (!StoragePermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for compress operation")
                    return@withContext false
                }

                // 解析文件模式
                val (sourcePattern, _) = parseFilePatterns(task.specificFilePattern)

                // 根据用户选择的路径获取要压缩的文件列表
                val filesToCompress = getFilesByUserPath(task.sourcePath, task.sourceSelectionMode, sourcePattern)
                if (filesToCompress.isEmpty()) {
                    Log.w(TAG, "No files found for compress operation")
                    return@withContext false
                }

                // 确定压缩文件的保存位置
                val zipFile = when (task.compressionLocation) {
                    CompressionLocation.CUSTOM_PATH -> {
                        if (task.customCompressionPath.isNotEmpty()) {
                            File(task.customCompressionPath)
                        } else {
                            Log.e(TAG, "Custom compression path is empty")
                            return@withContext false
                        }
                    }
                    CompressionLocation.CURRENT_LOCATION -> {
                        // 使用第一个文件的父目录作为压缩位置
                        val parentDir = filesToCompress.firstOrNull()?.parentFile
                            ?: context.getExternalFilesDir(null)
                            ?: File(context.filesDir, "compressed")

                        // 生成唯一的压缩文件名
                        val timestamp = System.currentTimeMillis()
                        File(parentDir, "compressed_$timestamp.zip")
                    }
                }

                // 确保压缩文件的父目录存在
                zipFile.parentFile?.mkdirs()

                // 执行压缩操作
                val success = compressFiles(filesToCompress, zipFile)

                if (success) {
                    Log.d(TAG, "Successfully compressed ${filesToCompress.size} files to: ${zipFile.absolutePath}")

                    // 如果设置了删除原文件，则删除原文件
                    if (task.deleteOriginalAfterCompression) {
                        var deletedCount = 0
                        for (file in filesToCompress) {
                            try {
                                val deleteSuccess = if (file.isDirectory) {
                                    deleteDirectory(file)
                                } else {
                                    file.delete()
                                }
                                if (deleteSuccess) {
                                    deletedCount++
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Failed to delete original file: ${file.name}", e)
                            }
                        }
                        Log.d(TAG, "Deleted $deletedCount original files after compression")
                    }
                } else {
                    Log.e(TAG, "Failed to compress files")
                }



                success
            } catch (e: Exception) {
                Log.e(TAG, "Error in compress files operation", e)
                false
            }
        }
    }

    /**
     * 执行新建文件夹操作
     */
    private suspend fun executeCreateFolder(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting create folder operation")

                // 检查存储权限
                if (!StoragePermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for create folder operation")
                    return@withContext false
                }

                // 获取用户选择的目标目录
                val targetDir = getUserSelectedDirectory(task.targetPath)
                if (targetDir == null || !targetDir.exists()) {
                    Log.e(TAG, "Target directory not found or not accessible: ${task.targetPath}")
                    return@withContext false
                }

                // 确定新文件夹名称
                val folderName = if (task.newFolderName.isNotEmpty()) {
                    task.newFolderName
                } else {
                    "新建文件夹_${System.currentTimeMillis()}"
                }

                // 创建新文件夹
                val newFolder = File(targetDir, folderName)

                // 检查文件夹是否已存在
                val finalFolder = if (newFolder.exists()) {
                    if (task.skipIfFolderExists) {
                        Log.d(TAG, "Folder already exists and skip option is enabled: ${newFolder.absolutePath}")
                        return@withContext true // 跳过创建，视为成功
                    } else {
                        // 如果不跳过，添加序号
                        getUniqueFileName(newFolder)
                    }
                } else {
                    newFolder
                }

                val success = finalFolder.mkdirs()

                if (success) {
                    Log.d(TAG, "Successfully created folder: ${finalFolder.absolutePath}")
                } else {
                    Log.e(TAG, "Failed to create folder: ${finalFolder.absolutePath}")
                }



                success
            } catch (e: Exception) {
                Log.e(TAG, "Error in create folder operation", e)
                false
            }
        }
    }

    /**
     * 执行重命名文件操作
     */
    private suspend fun executeRenameFile(task: FileOperationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting rename file operation")

                // 检查存储权限
                if (!StoragePermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for rename operation")
                    return@withContext false
                }

                // 验证必要参数
                if (task.folderPath.isEmpty() || task.fileName.isEmpty() || task.newFileName.isEmpty()) {
                    Log.e(TAG, "Missing required parameters for rename operation")
                    return@withContext false
                }

                // 构建源文件路径
                val sourceFile = File(task.folderPath, task.fileName)
                if (!sourceFile.exists()) {
                    Log.e(TAG, "Source file does not exist: ${sourceFile.absolutePath}")
                    return@withContext false
                }

                // 构建新文件名
                val newFileName = if (task.newFileExtension.isNotEmpty()) {
                    "${task.newFileName}.${task.newFileExtension}"
                } else {
                    task.newFileName
                }

                // 构建目标文件路径
                val targetFile = File(task.folderPath, newFileName)

                // 检查目标文件是否已存在
                if (targetFile.exists()) {
                    if (!task.allowOverwrite) {
                        Log.w(TAG, "Target file already exists and overwrite is not allowed: ${targetFile.absolutePath}")
                        return@withContext false
                    } else {
                        Log.d(TAG, "Target file exists but overwrite is allowed: ${targetFile.absolutePath}")
                    }
                }

                // 执行重命名操作
                val success = sourceFile.renameTo(targetFile)

                if (success) {
                    Log.d(TAG, "Successfully renamed file: ${sourceFile.name} -> ${targetFile.name}")
                } else {
                    Log.e(TAG, "Failed to rename file: ${sourceFile.name} -> ${targetFile.name}")
                }



                success
            } catch (e: Exception) {
                Log.e(TAG, "Error in rename file operation", e)
                false
            }
        }
    }

    /**
     * 获取文件的MIME类型
     */
    private fun getMimeType(file: File): String {
        val extension = file.extension.lowercase()
        return when (extension) {
            "txt" -> "text/plain"
            "pdf" -> "application/pdf"
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "mp4" -> "video/mp4"
            "mp3" -> "audio/mpeg"
            "zip" -> "application/zip"
            "doc" -> "application/msword"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "xls" -> "application/vnd.ms-excel"
            "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            "ppt" -> "application/vnd.ms-powerpoint"
            "pptx" -> "application/vnd.openxmlformats-officedocument.presentationml.presentation"
            else -> "application/octet-stream"
        }
    }

    /**
     * 执行相机任务
     *
     * @param task 相机任务
     * @return 任务是否成功执行
     */
    private suspend fun executeCameraTask(task: CameraTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 执行时权限检查：相机权限检查
                when (task.operation) {
                    CameraOperation.OPEN_LAST_PHOTO -> {
                        if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasStoragePermission(context)) {
                            Log.e(TAG, "Storage permission not granted for camera task execution")

                            return@withContext false
                        }
                    }
                    CameraOperation.TAKE_PHOTO -> {
                        if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasPhotoPermissions(context)) {
                            Log.e(TAG, "Photo permissions not granted for camera task execution")

                            return@withContext false
                        }
                    }
                    CameraOperation.RECORD_VIDEO -> {
                        if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasVideoPermissions(context)) {
                            Log.e(TAG, "Video permissions not granted for camera task execution")

                            return@withContext false
                        }
                    }
                    CameraOperation.SCREENSHOT -> {
                        if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasStoragePermission(context)) {
                            return@withContext false
                        }
                    }
                }

                val success = when (task.operation) {
                    CameraOperation.OPEN_LAST_PHOTO -> executeOpenLastPhoto()
                    CameraOperation.TAKE_PHOTO -> executeTakePhoto(task)
                    CameraOperation.RECORD_VIDEO -> executeRecordVideo(task)
                    CameraOperation.SCREENSHOT -> executeScreenshot()
                }

                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 打开最后一张照片
     */
    private suspend fun executeOpenLastPhoto(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查存储权限
                if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasMediaAccessPermissions(context)) {
                    Log.e(TAG, "Media access permissions not granted")
                    return@withContext false
                }

                val intent = Intent(Intent.ACTION_VIEW).apply {
                    type = "image/*"
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
                Log.d(TAG, "Last photo opened successfully")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error opening last photo", e)
                false
            }
        }
    }

    /**
     * 拍照
     */
    private suspend fun executeTakePhoto(task: CameraTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查相机和存储权限
                if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasPhotoPermissions(context)) {
                    Log.e(TAG, "Photo permissions not granted")
                    return@withContext false
                }

                val intent = Intent(android.provider.MediaStore.ACTION_IMAGE_CAPTURE).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK

                    // 设置前置或后置摄像头（如果支持）
                    when (task.cameraType) {
                        CameraType.FRONT -> {
                            putExtra("android.intent.extras.CAMERA_FACING", 1)
                            putExtra("android.intent.extra.USE_FRONT_CAMERA", true)
                        }
                        CameraType.BACK -> {
                            putExtra("android.intent.extras.CAMERA_FACING", 0)
                            putExtra("android.intent.extra.USE_FRONT_CAMERA", false)
                        }
                    }

                    // 设置保存位置（如果需要）
                    if (task.photoSaveLocation == SaveLocation.CUSTOM && task.customPhotoPath.isNotEmpty()) {
                        try {
                            Log.d(TAG, "Processing custom photo path: ${task.customPhotoPath}")

                            // 生成唯一的文件名，避免文件名冲突
                            val timestamp = System.currentTimeMillis()
                            val fileName = "photo_$timestamp.jpg"

                            // 处理自定义路径，支持多种路径格式
                            val customDir = when {
                                // 如果是完整的文件系统路径
                                task.customPhotoPath.startsWith("/") -> {
                                    java.io.File(task.customPhotoPath)
                                }
                                // 如果是相对于外部存储的路径
                                else -> {
                                    val externalStorageDir = android.os.Environment.getExternalStorageDirectory()
                                    java.io.File(externalStorageDir, task.customPhotoPath)
                                }
                            }

                            Log.d(TAG, "Resolved custom directory: ${customDir.absolutePath}")

                            // 检查目录是否存在或可以创建
                            val photoFile = if (customDir.exists() && customDir.isDirectory) {
                                Log.d(TAG, "Custom directory exists")
                                java.io.File(customDir, fileName)
                            } else if (customDir.mkdirs()) {
                                Log.d(TAG, "Custom directory created successfully")
                                java.io.File(customDir, fileName)
                            } else {
                                Log.w(TAG, "Failed to create custom directory, using DCIM fallback")
                                // 如果自定义路径无效，回退到DCIM目录
                                val dcimDir = java.io.File(android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_DCIM), "Camera")
                                dcimDir.mkdirs()
                                java.io.File(dcimDir, fileName)
                            }

                            val photoUri = androidx.core.content.FileProvider.getUriForFile(
                                context,
                                "${context.packageName}.fileprovider",
                                photoFile
                            )
                            putExtra(android.provider.MediaStore.EXTRA_OUTPUT, photoUri)

                            Log.d(TAG, "Custom photo save path set: ${photoFile.absolutePath}")
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to set custom photo path: ${task.customPhotoPath}", e)
                            // 如果设置自定义路径失败，不设置EXTRA_OUTPUT，让相机应用使用默认位置
                        }
                    }
                }

                context.startActivity(intent)
                Log.d(TAG, "Camera app opened for photo capture: ${task.cameraType.displayName}")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error taking photo", e)
                false
            }
        }
    }

    /**
     * 截屏
     */
    private suspend fun executeScreenshot(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查存储权限
                if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasStoragePermission(context)) {
                    Log.e(TAG, "Storage permission not granted for screenshot")
                    return@withContext false
                }

                // 使用系统截屏功能
                val intent = Intent(Intent.ACTION_MAIN).apply {
                    component = android.content.ComponentName(
                        "com.android.systemui",
                        "com.android.systemui.screenshot.TakeScreenshotService"
                    )
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }

                try {
                    context.startActivity(intent)
                    Log.d(TAG, "Screenshot service started successfully")
                    true
                } catch (e: Exception) {
                    // 如果系统截屏服务不可用，尝试使用其他方法
                    Log.w(TAG, "System screenshot service not available, trying alternative method", e)

                    // 尝试使用广播方式触发截屏
                    val screenshotIntent = Intent("android.intent.action.SCREENSHOT").apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }

                    try {
                        context.sendBroadcast(screenshotIntent)
                        Log.d(TAG, "Screenshot broadcast sent successfully")
                        true
                    } catch (broadcastException: Exception) {
                        Log.e(TAG, "Failed to trigger screenshot via broadcast", broadcastException)

                        // 最后尝试使用Shell命令（需要root权限）
                        try {
                            val result = com.weinuo.quickcommands.shizuku.ShizukuManager.executeCommand("screencap -p /sdcard/screenshot.png")
                            val success = !result.contains("Error:") && !result.contains("错误")
                            Log.d(TAG, "Screenshot via shell command result: $success")
                            success
                        } catch (shellException: Exception) {
                            Log.e(TAG, "Failed to take screenshot via shell command", shellException)
                            false
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error taking screenshot", e)
                false
            }
        }
    }

    /**
     * 录像
     */
    private suspend fun executeRecordVideo(task: CameraTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查相机、录音和存储权限
                if (!com.weinuo.quickcommands.utils.CameraPermissionUtil.hasVideoPermissions(context)) {
                    Log.e(TAG, "Video permissions not granted")
                    return@withContext false
                }

                when (task.videoOperation) {
                    VideoRecordingOperation.START, VideoRecordingOperation.START_WITH_DURATION -> {
                        val intent = Intent(android.provider.MediaStore.ACTION_VIDEO_CAPTURE).apply {
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK

                            // 设置前置或后置摄像头（如果支持）
                            when (task.cameraType) {
                                CameraType.FRONT -> {
                                    putExtra("android.intent.extras.CAMERA_FACING", 1)
                                    putExtra("android.intent.extra.USE_FRONT_CAMERA", true)
                                }
                                CameraType.BACK -> {
                                    putExtra("android.intent.extras.CAMERA_FACING", 0)
                                    putExtra("android.intent.extra.USE_FRONT_CAMERA", false)
                                }
                            }

                            // 设置录像时长（如果是定时录像）
                            if (task.videoOperation == VideoRecordingOperation.START_WITH_DURATION) {
                                val totalSeconds = task.recordingDurationMinutes * 60 + task.recordingDurationSeconds
                                putExtra(android.provider.MediaStore.EXTRA_DURATION_LIMIT, totalSeconds)
                            }

                            // 设置保存位置（如果需要）
                            if (task.videoSaveLocation == SaveLocation.CUSTOM && task.customVideoPath.isNotEmpty()) {
                                try {
                                    Log.d(TAG, "Processing custom video path: ${task.customVideoPath}")

                                    // 生成唯一的文件名，避免文件名冲突
                                    val timestamp = System.currentTimeMillis()
                                    val fileName = "video_$timestamp.mp4"

                                    // 处理自定义路径，支持多种路径格式
                                    val customDir = when {
                                        // 如果是完整的文件系统路径
                                        task.customVideoPath.startsWith("/") -> {
                                            java.io.File(task.customVideoPath)
                                        }
                                        // 如果是相对于外部存储的路径
                                        else -> {
                                            val externalStorageDir = android.os.Environment.getExternalStorageDirectory()
                                            java.io.File(externalStorageDir, task.customVideoPath)
                                        }
                                    }

                                    Log.d(TAG, "Resolved custom directory: ${customDir.absolutePath}")

                                    // 检查目录是否存在或可以创建
                                    val videoFile = if (customDir.exists() && customDir.isDirectory) {
                                        Log.d(TAG, "Custom directory exists")
                                        java.io.File(customDir, fileName)
                                    } else if (customDir.mkdirs()) {
                                        Log.d(TAG, "Custom directory created successfully")
                                        java.io.File(customDir, fileName)
                                    } else {
                                        Log.w(TAG, "Failed to create custom directory, using Movies fallback")
                                        // 如果自定义路径无效，回退到Movies目录
                                        val moviesDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MOVIES)
                                        moviesDir.mkdirs()
                                        java.io.File(moviesDir, fileName)
                                    }

                                    val videoUri = androidx.core.content.FileProvider.getUriForFile(
                                        context,
                                        "${context.packageName}.fileprovider",
                                        videoFile
                                    )
                                    putExtra(android.provider.MediaStore.EXTRA_OUTPUT, videoUri)

                                    Log.d(TAG, "Custom video save path set: ${videoFile.absolutePath}")
                                } catch (e: Exception) {
                                    Log.w(TAG, "Failed to set custom video path: ${task.customVideoPath}", e)
                                    // 如果设置自定义路径失败，不设置EXTRA_OUTPUT，让相机应用使用默认位置
                                }
                            }
                        }

                        context.startActivity(intent)
                        Log.d(TAG, "Camera app opened for video recording: ${task.cameraType.displayName}, operation: ${task.videoOperation.displayName}")
                        true
                    }

                    VideoRecordingOperation.STOP -> {
                        // 停止录像通常需要通过广播或其他方式，这里使用简单的方法
                        // 发送停止录像的广播（某些相机应用可能支持）
                        try {
                            val stopIntent = Intent("android.media.action.VIDEO_CAPTURE_STOP").apply {
                                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            }
                            context.sendBroadcast(stopIntent)
                            Log.d(TAG, "Stop video recording broadcast sent")
                            true
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to send stop recording broadcast, trying alternative method", e)
                            // 备用方法：尝试按返回键停止录像
                            false
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error recording video", e)
                false
            }
        }
    }

    /**
     * 执行信息任务
     *
     * @param task 信息任务
     * @return 任务是否成功执行
     */
    private suspend fun executeInformationTask(task: InformationTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 执行时权限检查：通信权限检查
                when (task.operation) {
                    InformationOperation.SEND_SMS -> {
                        if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasSmsPermission(context)) {
                            Log.e(TAG, "SMS permission not granted for information task execution")

                            return@withContext false
                        }
                    }
                    InformationOperation.SEND_EMAIL -> {
                        // 邮件发送通常不需要特殊权限，使用Intent启动系统邮件应用
                    }
                    InformationOperation.MESSAGE_RINGTONE -> {
                        // 信息铃声设置不需要特殊权限
                    }
                }

                when (task.operation) {
                    InformationOperation.SEND_SMS -> executeSmsTask(task)
                    InformationOperation.SEND_EMAIL -> executeEmailTask(task)
                    InformationOperation.MESSAGE_RINGTONE -> executeMessageRingtoneTask(task)
                }
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行短信任务
     *
     * @param task 信息任务
     * @return 任务是否成功执行
     */
    private suspend fun executeSmsTask(task: InformationTask): Boolean {
        return try {
            if (task.phoneNumber.isEmpty() || task.messageText.isEmpty()) {
                Log.e(TAG, "Phone number or message text is empty")
                return false
            }

            // 检查短信权限
            if (!CommunicationPermissionUtil.hasSmsPermission(context)) {
                Log.e(TAG, "SMS permission not granted")
                return false
            }

            if (task.draftOnly) {
                // 仅预填写短信，不发送
                val smsIntent = createSmsIntent(task.phoneNumber, task.messageText, task.simCardSelection)
                context.startActivity(smsIntent)



                Log.d(TAG, "SMS draft created for: ${task.phoneNumber} with SIM: ${task.simCardSelection}")
                true
            } else {
                // 直接发送短信
                val success = sendSmsWithSimSelection(task)

                if (success) {

                    Log.d(TAG, "SMS sent to: ${task.phoneNumber} with SIM: ${task.simCardSelection}")
                }

                success
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sending SMS", e)
            false
        }
    }

    /**
     * 创建短信Intent，支持SIM卡选择
     */
    private fun createSmsIntent(phoneNumber: String, messageText: String, simCardSelection: SimCardSelection): Intent {
        return when (simCardSelection) {
            SimCardSelection.SIM1 -> {
                Intent(Intent.ACTION_SENDTO).apply {
                    data = Uri.parse("smsto:$phoneNumber")
                    putExtra("sms_body", messageText)
                    putExtra("subscription", 0) // SIM卡1的订阅ID通常为0
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
            }
            SimCardSelection.SIM2 -> {
                Intent(Intent.ACTION_SENDTO).apply {
                    data = Uri.parse("smsto:$phoneNumber")
                    putExtra("sms_body", messageText)
                    putExtra("subscription", 1) // SIM卡2的订阅ID通常为1
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
            }
            SimCardSelection.ASK_EACH_TIME -> {
                Intent(Intent.ACTION_SENDTO).apply {
                    data = Uri.parse("smsto:$phoneNumber")
                    putExtra("sms_body", messageText)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    // 不设置subscription，让系统询问用户选择
                }
            }
        }
    }

    /**
     * 使用指定SIM卡发送短信
     */
    private suspend fun sendSmsWithSimSelection(task: InformationTask): Boolean {
        return try {
            when (task.simCardSelection) {
                SimCardSelection.ASK_EACH_TIME -> {
                    // 使用默认SmsManager，让系统处理SIM卡选择
                    val smsManager = SmsManager.getDefault()
                    val parts = smsManager.divideMessage(task.messageText)

                    if (parts.size == 1) {
                        smsManager.sendTextMessage(task.phoneNumber, null, task.messageText, null, null)
                    } else {
                        smsManager.sendMultipartTextMessage(task.phoneNumber, null, parts, null, null)
                    }
                    true
                }
                SimCardSelection.SIM1, SimCardSelection.SIM2 -> {
                    // 尝试使用指定的SIM卡发送
                    sendSmsWithSpecificSim(task)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sending SMS with SIM selection", e)
            false
        }
    }

    /**
     * 使用指定SIM卡发送短信
     */
    private suspend fun sendSmsWithSpecificSim(task: InformationTask): Boolean {
        return try {
            // Android 8.0+ 支持多SIM卡管理
            val subscriptionManager = context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as? android.telephony.SubscriptionManager
            val subscriptions = subscriptionManager?.activeSubscriptionInfoList

            if (subscriptions != null && subscriptions.isNotEmpty()) {
                val targetSlot = when (task.simCardSelection) {
                    SimCardSelection.SIM1 -> 0
                    SimCardSelection.SIM2 -> 1
                    else -> 0
                }

                // 查找对应卡槽的订阅
                val targetSubscription = subscriptions.find { it.simSlotIndex == targetSlot }

                if (targetSubscription != null) {
                    val smsManager = SmsManager.getSmsManagerForSubscriptionId(targetSubscription.subscriptionId)
                    val parts = smsManager.divideMessage(task.messageText)

                    if (parts.size == 1) {
                        smsManager.sendTextMessage(task.phoneNumber, null, task.messageText, null, null)
                    } else {
                        smsManager.sendMultipartTextMessage(task.phoneNumber, null, parts, null, null)
                    }

                    Log.d(TAG, "SMS sent using SIM slot $targetSlot (subscription ${targetSubscription.subscriptionId})")
                    true
                } else {
                    Log.w(TAG, "No subscription found for SIM slot $targetSlot, falling back to default")
                    // 回退到默认SmsManager
                    val smsManager = SmsManager.getDefault()
                    val parts = smsManager.divideMessage(task.messageText)

                    if (parts.size == 1) {
                        smsManager.sendTextMessage(task.phoneNumber, null, task.messageText, null, null)
                    } else {
                        smsManager.sendMultipartTextMessage(task.phoneNumber, null, parts, null, null)
                    }
                    true
                }
            } else {
                Log.w(TAG, "No active subscriptions found, using default SmsManager")
                // 回退到默认SmsManager
                val smsManager = SmsManager.getDefault()
                val parts = smsManager.divideMessage(task.messageText)

                if (parts.size == 1) {
                    smsManager.sendTextMessage(task.phoneNumber, null, task.messageText, null, null)
                } else {
                    smsManager.sendMultipartTextMessage(task.phoneNumber, null, parts, null, null)
                }
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sending SMS with specific SIM", e)
            false
        }
    }

    /**
     * 执行邮件任务
     *
     * @param task 信息任务
     * @return 任务是否成功执行
     */
    private suspend fun executeEmailTask(task: InformationTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (task.emailRecipient.isEmpty() || task.senderEmail.isEmpty() ||
                    task.smtpServer.isEmpty()) {
                    Log.e(TAG, "Email configuration is incomplete")

                    // 发送失败通知
                    if (task.notifyOnFailure) {
                        sendEmailNotification("邮件发送失败", "邮件配置不完整", false)
                    }

                    return@withContext false
                }

                // 检查身份验证配置
                if (task.useAuthentication && (task.username.isEmpty() || task.senderPassword.isEmpty())) {
                    Log.e(TAG, "Email authentication configuration is incomplete")

                    // 发送失败通知
                    if (task.notifyOnFailure) {
                        sendEmailNotification("邮件发送失败", "身份验证配置不完整", false)
                    }

                    return@withContext false
                }

                // 使用Intent发送邮件（通过系统邮件应用）
                val emailIntent = Intent(Intent.ACTION_SEND).apply {
                    type = if (task.isHtmlEmail) "text/html" else "message/rfc822"
                    putExtra(Intent.EXTRA_EMAIL, arrayOf(task.emailRecipient))
                    putExtra(Intent.EXTRA_SUBJECT, task.emailSubject)
                    putExtra(Intent.EXTRA_TEXT, task.messageText)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }

                // 尝试启动邮件应用
                val chooserIntent = Intent.createChooser(emailIntent, "选择邮件应用")
                chooserIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

                withContext(Dispatchers.Main) {
                    context.startActivity(chooserIntent)
                }

                // 发送成功通知
                if (task.notifyOnCompletion) {
                    sendEmailNotification("邮件发送完成", "邮件已发送至 ${task.emailRecipient}", true)
                }



                Log.d(TAG, "Email intent created for: ${task.emailRecipient}")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error sending email", e)

                // 发送失败通知
                if (task.notifyOnFailure) {
                    sendEmailNotification("邮件发送失败", "发送邮件时出现错误: ${e.message}", false)
                }

                false
            }
        }
    }

    /**
     * 发送邮件相关通知
     *
     * @param title 通知标题
     * @param message 通知内容
     * @param isSuccess 是否为成功通知
     */
    private suspend fun sendEmailNotification(title: String, message: String, isSuccess: Boolean) {
        withContext(Dispatchers.Main) {
            try {
                // 检查通知权限
                if (!com.weinuo.quickcommands.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)) {
                    Log.w(TAG, "No notification permission for email notification")
                    return@withContext
                }

                // 发送通知
                val success = com.weinuo.quickcommands.utils.NotificationHelper.showNotification(
                    context = context,
                    title = title,
                    text = message,
                    priority = if (isSuccess) 0 else 1 // 失败通知使用高优先级
                )

                if (success) {
                    Log.d(TAG, "Email notification sent: $title")
                } else {
                    Log.e(TAG, "Failed to send email notification: $title")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending email notification", e)
            }
        }
    }

    /**
     * 执行信息铃声设置任务
     *
     * @param task 信息任务
     * @return 任务是否成功执行
     */
    private suspend fun executeMessageRingtoneTask(task: InformationTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 检查铃声URI是否有效
                if (task.selectedRingtoneUri.isEmpty()) {
                    Log.e(TAG, "No ringtone URI provided for message ringtone task")
                    return@withContext false
                }

                // 使用RingtoneHelper设置信息铃声
                val success = RingtoneHelper.setRingtone(
                    context = context,
                    ringtoneUri = task.selectedRingtoneUri,
                    ringtoneType = RingtoneHelper.RingtoneType.NOTIFICATION
                )

                if (success) {

                    Log.d(TAG, "Message ringtone set successfully: ${task.selectedRingtoneName}")
                } else {
                    Log.e(TAG, "Failed to set message ringtone: ${task.selectedRingtoneName}")
                }

                success
            } catch (e: Exception) {
                Log.e(TAG, "Error setting message ringtone", e)



                false
            }
        }
    }

    /**
     * 执行应用程序任务
     *
     * @param task 应用程序任务
     * @param eventData 事件数据（可选）
     * @return 任务是否成功执行
     */
    private suspend fun executeApplicationTask(task: ApplicationTask, eventData: Map<String, Any>? = null): Boolean {
        return withContext(Dispatchers.IO) {
            try {

                val success = when (task.operation) {
                    ApplicationOperation.JAVASCRIPT_CODE, ApplicationOperation.EXECUTE_JAVASCRIPT -> executeJavaScriptCode(task)
                    ApplicationOperation.SHELL_SCRIPT, ApplicationOperation.EXECUTE_SHELL_SCRIPT -> executeShellScript(task)
                    ApplicationOperation.TASKER_PLUGIN, ApplicationOperation.TASKER_LOCALE_PLUGIN -> executeTaskerPlugin(task)
                    ApplicationOperation.LAUNCH_APP -> executeLaunchApp(task)
                    ApplicationOperation.LAUNCH_SHORTCUT -> executeLaunchShortcut(task)
                    ApplicationOperation.OPEN_WEBSITE -> executeOpenWebsite(task)
                    ApplicationOperation.FORCE_STOP_APP -> executeForceStopApp(task, eventData) // 传递事件数据以支持触发应用模式
                    ApplicationOperation.FREEZE_APP -> executeFreezeApp(task, eventData) // 传递事件数据以支持触发应用模式
                    ApplicationOperation.UNFREEZE_APP -> executeUnfreezeApp(task, eventData) // 传递事件数据以支持触发应用模式
                }

                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行JavaScript代码
     *
     * @param task 应用程序任务
     * @return 任务是否成功执行
     */
    private suspend fun executeJavaScriptCode(task: ApplicationTask): Boolean {
        return try {
            // 创建WebView来执行JavaScript代码
            withContext(Dispatchers.Main) {
                val webView = android.webkit.WebView(context)
                webView.settings.javaScriptEnabled = true

                // 执行JavaScript代码
                webView.evaluateJavascript(task.javascriptCode) { result ->
                    Log.d(TAG, "JavaScript execution result: $result")
                }
            }

            Log.d(TAG, "JavaScript code executed: ${task.javascriptCode}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error executing JavaScript code", e)
            false
        }
    }

    /**
     * 执行Shell脚本
     *
     * @param task 应用程序任务
     * @return 任务是否成功执行
     */
    private suspend fun executeShellScript(task: ApplicationTask): Boolean {
        return try {
            Log.d(TAG, "开始执行Shell脚本: ${task.shellScript.take(100)}...")

            // 验证脚本内容
            if (task.shellScript.isBlank()) {
                Log.e(TAG, "Shell脚本内容为空")



                return false
            }

            val timeoutMillis = (task.shellTimeoutMinutes * 60 + task.shellTimeoutSeconds) * 1000L



            val result = when (task.shellExecutionMode) {
                ShellExecutionMode.NORMAL -> {
                    // 普通Shell执行
                    executeNormalShellScript(task.shellScript, timeoutMillis)
                }
                ShellExecutionMode.SHIZUKU -> {
                    // 通过Shizuku执行
                    executeShizukuShellScript(task.shellScript, timeoutMillis)
                }
            }

            result
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 执行普通Shell脚本
     */
    private suspend fun executeNormalShellScript(script: String, timeoutMillis: Long): Boolean {
        return withContext(Dispatchers.IO) {
            var process: Process? = null
            try {
                Log.d(TAG, "启动普通Shell进程，超时时间: ${timeoutMillis}ms")

                process = Runtime.getRuntime().exec(arrayOf("sh", "-c", script))

                val startTime = System.currentTimeMillis()
                val completed = if (timeoutMillis > 0) {
                    process.waitFor(timeoutMillis, java.util.concurrent.TimeUnit.MILLISECONDS)
                } else {
                    process.waitFor()
                    true
                }

                val executionTime = System.currentTimeMillis() - startTime

                if (!completed) {
                    Log.w(TAG, "Shell脚本执行超时，强制终止进程")
                    process.destroyForcibly()



                    false
                } else {
                    val exitCode = process.exitValue()

                    // 读取输出和错误信息
                    val output = try {
                        process.inputStream.bufferedReader().readText().take(1000) // 限制长度
                    } catch (e: Exception) {
                        "无法读取输出: ${e.message}"
                    }

                    val errorOutput = try {
                        process.errorStream.bufferedReader().readText().take(1000) // 限制长度
                    } catch (e: Exception) {
                        "无法读取错误输出: ${e.message}"
                    }

                    Log.d(TAG, "Shell脚本执行完成: 退出码=$exitCode, 执行时间=${executionTime}ms")
                    if (output.isNotBlank()) {
                        Log.d(TAG, "Shell脚本输出: $output")
                    }
                    if (errorOutput.isNotBlank()) {
                        Log.w(TAG, "Shell脚本错误输出: $errorOutput")
                    }



                    exitCode == 0
                }
            } catch (e: Exception) {
                Log.e(TAG, "普通Shell脚本执行异常", e)

                // 确保进程被清理
                process?.destroyForcibly()



                false
            }
        }
    }



    /**
     * 通过Shizuku执行Shell脚本
     */
    private suspend fun executeShizukuShellScript(script: String, timeoutMillis: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "通过Shizuku执行Shell脚本，超时时间: ${timeoutMillis}ms")

                val startTime = System.currentTimeMillis()
                val result = ShizukuManager.executeCommand(script)
                val executionTime = System.currentTimeMillis() - startTime

                Log.d(TAG, "Shizuku Shell脚本执行完成，执行时间: ${executionTime}ms")
                Log.d(TAG, "Shizuku Shell脚本结果: ${result.take(500)}...") // 限制日志长度

                // 检查结果是否包含错误信息
                val hasError = result.contains("Error:") ||
                              result.contains("错误") ||
                              result.contains("失败") ||
                              result.contains("Permission denied") ||
                              result.contains("not found") ||
                              result.contains("Exception") ||
                              result.contains("Failed")

                // 检查退出码
                val exitCodeMatch = Regex("Exit code: (\\d+)").find(result)
                val exitCode = exitCodeMatch?.groupValues?.get(1)?.toIntOrNull() ?: -1

                val success = !hasError && (exitCode == 0 || exitCode == -1) // -1表示没有退出码信息



                if (!success) {
                    Log.w(TAG, "Shizuku Shell脚本执行失败: 退出码=$exitCode, 有错误=$hasError")
                }

                success
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行Tasker/Locale插件
     *
     * 根据 Tasker/Locale 插件协议，正确执行插件任务：
     * 1. 发送广播到插件应用
     * 2. 使用标准的 Tasker 插件 Intent 格式
     * 3. 传递配置数据
     *
     * @param task 应用程序任务
     * @return 任务是否成功执行
     */
    private suspend fun executeTaskerPlugin(task: ApplicationTask): Boolean {
        return try {
            if (task.taskerPluginPackage.isEmpty()) {
                return false
            }

            // 根据 Tasker/Locale 插件协议，发送广播执行插件
            val intent = Intent().apply {
                // 设置目标包名
                setPackage(task.taskerPluginPackage)

                // 使用配置的插件执行 action，如果为空则使用默认值
                action = if (task.taskerPluginAction.isNotEmpty()) {
                    task.taskerPluginAction
                } else {
                    "com.twofortyfouram.locale.intent.action.FIRE_SETTING"
                }

                // 添加标准的 Tasker 插件 extras
                putExtra("com.twofortyfouram.locale.intent.extra.BUNDLE", android.os.Bundle().apply {
                    // 如果有配置数据，尝试解析并添加到 Bundle
                    if (task.taskerPluginExtras.isNotEmpty()) {
                        try {
                            // 简单处理：将配置数据作为字符串传递
                            putString("config_data", task.taskerPluginExtras)

                            // 如果是 JSON 格式，可以尝试解析
                            if (task.taskerPluginExtras.trim().startsWith("{")) {
                                putString("json_config", task.taskerPluginExtras)
                            }
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to parse plugin extras: ${task.taskerPluginExtras}", e)
                        }
                    }
                })

                // 添加 Tasker 插件标识
                putExtra("com.twofortyfouram.locale.intent.extra.BLURB", "Background Manager Plugin Execution")
            }

            // 发送广播到插件
            withContext(Dispatchers.Main) {
                context.sendBroadcast(intent)
            }

            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 启动应用
     *
     * @param task 应用程序任务
     * @return 任务是否成功执行
     */
    private suspend fun executeLaunchApp(task: ApplicationTask): Boolean {
        return try {
            Log.d(TAG, "尝试启动应用: ${task.appPackageName} (${task.appName})")

            val packageManager = context.packageManager

            // 检查应用是否已安装
            val isInstalled = try {
                packageManager.getPackageInfo(task.appPackageName, 0)
                true
            } catch (e: Exception) {
                false
            }

            if (!isInstalled) {
                return false
            }

            val launchIntent = packageManager.getLaunchIntentForPackage(task.appPackageName)

            if (launchIntent == null) {
                Log.e(TAG, "无法获取启动Intent: ${task.appPackageName}")



                return false
            }

            // 设置Intent标志，确保应用在新任务中启动
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)

            withContext(Dispatchers.Main) {
                context.startActivity(launchIntent)
            }

            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 启动快捷方式
     *
     * @param task 应用程序任务
     * @return 任务是否成功执行
     */
    private suspend fun executeLaunchShortcut(task: ApplicationTask): Boolean {
        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N_MR1) {
                val shortcutManager = context.getSystemService(Context.SHORTCUT_SERVICE) as android.content.pm.ShortcutManager

                // 尝试启动快捷方式
                val intent = shortcutManager.createShortcutResultIntent(
                    android.content.pm.ShortcutInfo.Builder(context, task.shortcutId).build()
                )

                withContext(Dispatchers.Main) {
                    context.startActivity(intent)
                }

                Log.d(TAG, "Shortcut launched: ${task.shortcutId} (${task.shortcutName})")
                true
            } else {
                Log.w(TAG, "Shortcut launching not supported on this Android version")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error launching shortcut: ${task.shortcutId}", e)
            false
        }
    }

    /**
     * 打开网站
     *
     * @param task 应用程序任务
     * @return 任务是否成功执行
     */
    private suspend fun executeOpenWebsite(task: ApplicationTask): Boolean {
        return try {
            val finalUrl = if (task.urlEncodeParams) {
                // 对URL参数进行编码
                val uri = android.net.Uri.parse(task.websiteUrl)
                val encodedQuery = uri.query?.let { query ->
                    java.net.URLEncoder.encode(query, "UTF-8")
                }
                if (encodedQuery != null) {
                    uri.buildUpon().encodedQuery(encodedQuery).build().toString()
                } else {
                    task.websiteUrl
                }
            } else {
                task.websiteUrl
            }

            val intent = Intent(Intent.ACTION_VIEW, android.net.Uri.parse(finalUrl)).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            withContext(Dispatchers.Main) {
                context.startActivity(intent)
            }

            Log.d(TAG, "Website opened: $finalUrl")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening website: ${task.websiteUrl}", e)
            false
        }
    }

    /**
     * 强制停止应用（增强版）
     *
     * @param task 应用程序任务
     * @param eventData 事件数据（可选，用于触发应用模式）
     * @return 任务是否成功执行
     */
    private suspend fun executeForceStopApp(task: ApplicationTask, eventData: Map<String, Any>? = null): Boolean {
        return try {
            when (task.forceStopScope) {
                ForceStopScope.SELECTED_APP -> {
                    // 停止指定应用（支持多选和所有高级功能）
                    if (task.forceStopSelectedApps.isNotEmpty()) {
                        // 使用多选应用列表，支持所有高级功能
                        forceStopSelectedAppsWithAdvancedOptions(
                            selectedApps = task.forceStopSelectedApps,
                            skipForegroundApp = task.skipForegroundApp,
                            skipMusicPlayingApp = task.skipMusicPlayingApp,
                            skipVpnApp = task.skipVpnApp,
                            selectedVpnApps = task.selectedVpnApps,
                            sortByBackgroundTime = task.sortByBackgroundTime,
                            appSortingStrategy = task.appSortingStrategy,
                            enableMemoryThresholdCheck = task.enableMemoryThresholdCheck,
                            memoryThreshold = task.memoryThreshold,
                            memoryThresholdIsPercentage = task.memoryThresholdIsPercentage,
                            memoryCheckFrequency = task.memoryCheckFrequency,
                            enableCustomCheckFrequency = task.enableCustomCheckFrequency,
                            customCheckFrequencySize = task.customCheckFrequencySize,
                            customForceStopCommand = task.effectiveForceStopCommand,
                            enableRealtimeForegroundDetection = task.enableRealtimeForegroundDetection
                        )
                    } else {
                        false
                    }
                }
                ForceStopScope.TRIGGER_APP -> {
                    // 停止触发条件的应用，从事件数据中获取触发应用信息
                    executeForceStopTriggerApp(task, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error force stopping apps", e)
            false
        }
    }

    /**
     * 强制停止触发应用
     *
     * 专门用于停止触发条件的应用，常与后台时间条件配合使用
     * 从事件数据中获取触发应用信息并停止所有触发的应用
     *
     * @param task 应用程序任务
     * @param eventData 事件数据（包含触发应用信息）
     * @return 任务是否成功执行
     */
    private suspend fun executeForceStopTriggerApp(task: ApplicationTask, eventData: Map<String, Any>? = null): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "执行强制停止触发应用任务")

                // 优先从事件数据中获取所有触发的应用
                val triggeredApps = eventData?.get("triggeredApps") as? List<SimpleAppInfo>

                if (!triggeredApps.isNullOrEmpty()) {
                    Log.d(TAG, "Found ${triggeredApps.size} triggered apps from event data")

                    // 停止所有触发的应用
                    var allSuccess = true
                    for (app in triggeredApps) {
                        val success = executeForceStopSingleTriggerApp(app, task, "事件数据")
                        if (!success) {
                            allSuccess = false
                        }
                    }
                    return@withContext allSuccess
                }

                // 回退：尝试获取单个触发应用
                val triggerApp = eventData?.get("triggerApp") as? SimpleAppInfo
                if (triggerApp != null) {
                    Log.d(TAG, "Found single trigger app from event data: ${triggerApp.appName} (${triggerApp.packageName})")
                    return@withContext executeForceStopSingleTriggerApp(triggerApp, task, "事件数据")
                }

                // 最终回退：获取最近使用的非前台应用作为"触发应用"
                val currentForegroundApp = getForegroundPackage()
                val fallbackTriggerApp = findMostRecentBackgroundApp(currentForegroundApp)

                if (fallbackTriggerApp == null) {
                    Log.d(TAG, "No trigger apps found, task completed successfully")
                    return@withContext true // 无触发应用不算失败
                }

                Log.d(TAG, "Using fallback trigger app: ${fallbackTriggerApp.appName} (${fallbackTriggerApp.packageName})")
                return@withContext executeForceStopSingleTriggerApp(fallbackTriggerApp, task, "回退检测")

            } catch (e: Exception) {
                Log.e(TAG, "Error executing force stop trigger app task", e)
                false
            }
        }
    }

    /**
     * 执行单个触发应用的强制停止
     *
     * @param triggerApp 触发应用信息
     * @param task 应用程序任务
     * @param dataSource 数据来源描述
     * @return 任务是否成功执行
     */
    private suspend fun executeForceStopSingleTriggerApp(
        triggerApp: SimpleAppInfo,
        task: ApplicationTask,
        dataSource: String
    ): Boolean {
        return try {
            // 获取当前前台应用
            val currentForegroundApp = getForegroundPackage()

            // 检查是否需要跳过前台应用
            if (task.skipForegroundApp && triggerApp.packageName == currentForegroundApp) {
                Log.d(TAG, "Skipping foreground app: ${triggerApp.packageName}")



                return true // 跳过但不算失败
            }

            // 获取正在播放音乐的应用包名集合
            val musicPlayingPackages = if (task.skipMusicPlayingApp) {
                getMusicPlayingPackages()
            } else {
                emptySet()
            }

            // 检查是否需要跳过正在播放音乐的应用
            if (task.skipMusicPlayingApp && musicPlayingPackages.contains(triggerApp.packageName)) {
                Log.d(TAG, "Skipping music playing app: ${triggerApp.packageName}")



                return true // 跳过但不算失败
            }

            // 检查是否有VPN应用激活
            val hasVpnActive = if (task.skipVpnApp) {
                // 加载应用列表以检查VPN状态
                appRepository.loadApps()
                val userApps = appRepository.userApps.first()
                val systemApps = appRepository.systemApps.first()
                val allApps = userApps + systemApps
                hasVpnActive(allApps)
            } else {
                false
            }

            // 检查是否需要跳过VPN应用
            if (task.skipVpnApp && hasVpnActive) {
                // 优先使用用户指定的VPN应用列表
                val isVpnApp = if (task.selectedVpnApps.isNotEmpty()) {
                    task.selectedVpnApps.any { it.packageName == triggerApp.packageName }
                } else {
                    // 回退到简单检查：如果包名包含vpn关键字，认为是VPN应用
                    triggerApp.packageName.lowercase().contains("vpn") ||
                            triggerApp.appName.lowercase().contains("vpn")
                }
                if (isVpnApp) {
                    Log.d(TAG, "Skipping VPN app: ${triggerApp.packageName}")



                    return true // 跳过但不算失败
                }
            }

            // 强制停止触发应用（已在上面检查过跳过条件，这里不需要重复检查）
            val success = forceStopSingleApp(triggerApp.packageName, false, task.effectiveForceStopCommand)



            Log.d(TAG, "Force stop trigger app result: ${triggerApp.appName} (${triggerApp.packageName}) - $success (source: $dataSource)")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error executing force stop for trigger app: ${triggerApp.packageName}", e)



            false
        }
    }

    /**
     * 查找最近的后台应用（简化实现）
     * 用于模拟触发应用，直到完整的事件数据传递机制实现
     */
    private suspend fun findMostRecentBackgroundApp(currentForegroundApp: String?): SimpleAppInfo? {
        return try {
            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 24 * 60 * 60 * 1000 // 24小时前

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            // 找到最近使用但不是当前前台的应用
            val recentBackgroundApp = usageStats
                .filter { it.packageName != currentForegroundApp && it.lastTimeUsed > 0 }
                .maxByOrNull { it.lastTimeUsed }

            if (recentBackgroundApp != null) {
                // 获取应用名称
                val packageManager = context.packageManager
                val appName = try {
                    val appInfo = packageManager.getApplicationInfo(recentBackgroundApp.packageName, 0)
                    packageManager.getApplicationLabel(appInfo).toString()
                } catch (e: Exception) {
                    recentBackgroundApp.packageName
                }

                SimpleAppInfo(
                    packageName = recentBackgroundApp.packageName,
                    appName = appName,
                    isSystemApp = false // 简化实现，不区分系统应用
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error finding most recent background app", e)
            null
        }
    }



    /**
     * 强制停止选中的应用列表（增强版，支持所有高级功能）
     */
    private suspend fun forceStopSelectedAppsWithAdvancedOptions(
        selectedApps: List<SimpleAppInfo>,
        skipForegroundApp: Boolean,
        skipMusicPlayingApp: Boolean,
        skipVpnApp: Boolean,
        selectedVpnApps: List<SimpleAppInfo> = emptyList(),
        sortByBackgroundTime: Boolean = false,
        appSortingStrategy: AppSortingStrategy = AppSortingStrategy.BACKGROUND_TIME_ONLY,
        enableMemoryThresholdCheck: Boolean = false,
        memoryThreshold: Int = 3,
        memoryThresholdIsPercentage: Boolean = false,
        memoryCheckFrequency: MemoryCheckFrequency = MemoryCheckFrequency.BALANCED,
        enableCustomCheckFrequency: Boolean = false,
        customCheckFrequencySize: Int = 5,
        customForceStopCommand: String = "am force-stop [package_name]",
        enableRealtimeForegroundDetection: Boolean = false
    ): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 清除前台应用检测缓存，确保任务开始时状态是最新的
                if (enableRealtimeForegroundDetection) {
                    cachedForegroundApp = null
                    cachedForegroundTimestamp = 0
                    Log.d(TAG, "Cleared foreground app cache for realtime detection")
                }

                // 获取当前前台应用
                val foregroundPackage = if (skipForegroundApp) {
                    getForegroundPackage()
                } else {
                    null
                }

                // 获取正在播放音乐的应用包名集合
                val musicPlayingPackages = if (skipMusicPlayingApp) {
                    getMusicPlayingPackages()
                } else {
                    emptySet()
                }

                // 检查是否有VPN应用激活
                val hasVpnActive = if (skipVpnApp) {
                    // 加载应用列表以检查VPN状态
                    appRepository.loadApps()
                    val userApps = appRepository.userApps.first()
                    val systemApps = appRepository.systemApps.first()
                    val allApps = userApps + systemApps
                    hasVpnActive(allApps)
                } else {
                    false
                }

                // 获取用户指定的VPN应用包名集合
                val vpnAppPackages = selectedVpnApps.map { it.packageName }.toSet()

                // 获取应用重要性设置
                val settingsRepository = com.weinuo.quickcommands.data.SettingsRepository(context)
                val appImportanceMap = selectedApps.associate { app ->
                    app.packageName to settingsRepository.getAppImportance(app.packageName)
                }

                // 过滤要停止的应用
                var appsToStop = selectedApps.filter { app ->
                    // 检查是否需要跳过前台应用
                    if (skipForegroundApp && app.packageName == foregroundPackage) {
                        Log.d(TAG, "Skipping foreground app: ${app.packageName}")
                        return@filter false
                    }

                    // 检查是否需要跳过正在播放音乐的应用
                    if (skipMusicPlayingApp && musicPlayingPackages.contains(app.packageName)) {
                        Log.d(TAG, "Skipping music playing app: ${app.packageName}")
                        return@filter false
                    }

                    // 检查是否需要跳过VPN应用
                    if (skipVpnApp && hasVpnActive) {
                        // 优先使用用户指定的VPN应用列表
                        val isVpnApp = if (vpnAppPackages.isNotEmpty()) {
                            vpnAppPackages.contains(app.packageName)
                        } else {
                            // 回退到简单检查：如果包名包含vpn关键字，认为是VPN应用
                            app.packageName.lowercase().contains("vpn") ||
                                    app.appName.lowercase().contains("vpn")
                        }
                        if (isVpnApp) {
                            Log.d(TAG, "Skipping VPN app: ${app.packageName}")
                            return@filter false
                        }
                    }



                    true
                }

                // 应用排序逻辑
                val sortedAppsToStop = sortAppsByStrategy(appsToStop, appSortingStrategy)

                var successCount = 0
                var totalCount = 0

                // 根据是否启用内存阈值检查来处理应用
                if (enableMemoryThresholdCheck) {
                    // 启用内存阈值检查时，按组处理应用
                    val groupSize = if (enableCustomCheckFrequency) {
                        customCheckFrequencySize
                    } else {
                        memoryCheckFrequency.groupSize
                    }

                    val appGroups = sortedAppsToStop.chunked(groupSize)
                    val frequencyType = if (enableCustomCheckFrequency) "自定义($customCheckFrequencySize)" else memoryCheckFrequency.displayName
                    Log.d(TAG, "Processing ${sortedAppsToStop.size} selected apps in ${appGroups.size} groups of size $groupSize (频率: $frequencyType)")

                    for (groupIndex in appGroups.indices) {
                        val appGroup = appGroups[groupIndex]
                        // 处理当前组的应用
                        for (app in appGroup) {
                            totalCount++
                            val success = forceStopSingleAppWithRealtimeDetection(
                                packageName = app.packageName,
                                skipForegroundApp = skipForegroundApp,
                                customCommand = customForceStopCommand,
                                enableRealtimeDetection = enableRealtimeForegroundDetection
                            )
                            if (success) {
                                successCount++
                            }
                        }

                        // 组间检查内存阈值
                        if (groupIndex < appGroups.size - 1) { // 不是最后一组
                            if (isMemoryAboveThreshold(memoryThreshold, memoryThresholdIsPercentage)) {
                                Log.d(TAG, "Memory above threshold after group ${groupIndex + 1}, stopping force stop operation")
                                break
                            }
                        }
                    }
                } else {
                    // 未启用内存阈值检查时，直接处理所有应用
                    for (app in sortedAppsToStop) {
                        totalCount++
                        val success = forceStopSingleAppWithRealtimeDetection(
                            packageName = app.packageName,
                            skipForegroundApp = skipForegroundApp,
                            customCommand = customForceStopCommand,
                            enableRealtimeDetection = enableRealtimeForegroundDetection
                        )
                        if (success) {
                            successCount++
                        }
                    }
                }

                // 更新应用运行状态
                appRepository.updateRunningStatus()

                Log.d(TAG, "Force stopped $successCount/$totalCount selected apps")
                successCount > 0 || totalCount == 0
            } catch (e: Exception) {
                Log.e(TAG, "Error force stopping selected apps with advanced options", e)
                false
            }
        }
    }

    /**
     * 强制停止选中的应用列表（简化版，保持向后兼容）
     */
    private suspend fun forceStopSelectedApps(
        selectedApps: List<SimpleAppInfo>,
        skipForegroundApp: Boolean,
        skipMusicPlayingApp: Boolean,
        skipVpnApp: Boolean,
        selectedVpnApps: List<SimpleAppInfo> = emptyList()
    ): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 获取当前前台应用
                val foregroundPackage = if (skipForegroundApp) {
                    getForegroundPackage()
                } else {
                    null
                }

                // 获取正在播放音乐的应用包名集合
                val musicPlayingPackages = if (skipMusicPlayingApp) {
                    getMusicPlayingPackages()
                } else {
                    emptySet()
                }

                // 检查是否有VPN应用激活
                val hasVpnActive = if (skipVpnApp) {
                    // 加载应用列表以检查VPN状态
                    appRepository.loadApps()
                    val userApps = appRepository.userApps.first()
                    val systemApps = appRepository.systemApps.first()
                    val allApps = userApps + systemApps
                    hasVpnActive(allApps)
                } else {
                    false
                }

                var successCount = 0
                var totalCount = 0

                // 遍历选中的应用，强制停止符合条件的应用
                for (app in selectedApps) {
                    totalCount++

                    // 检查是否需要跳过前台应用
                    if (skipForegroundApp && app.packageName == foregroundPackage) {
                        Log.d(TAG, "Skipping foreground app: ${app.packageName}")
                        continue
                    }

                    // 检查是否需要跳过正在播放音乐的应用
                    if (skipMusicPlayingApp && musicPlayingPackages.contains(app.packageName)) {
                        Log.d(TAG, "Skipping music playing app: ${app.packageName}")
                        continue
                    }

                    // 检查是否需要跳过VPN应用
                    if (skipVpnApp && hasVpnActive) {
                        // 优先使用用户指定的VPN应用列表
                        val isVpnApp = if (selectedVpnApps.isNotEmpty()) {
                            selectedVpnApps.any { it.packageName == app.packageName }
                        } else {
                            // 回退到简单检查：如果包名包含vpn关键字，认为是VPN应用
                            app.packageName.lowercase().contains("vpn") ||
                                    app.appName.lowercase().contains("vpn")
                        }
                        if (isVpnApp) {
                            Log.d(TAG, "Skipping VPN app: ${app.packageName}")
                            continue
                        }
                    }

                    // 强制停止应用
                    val success = forceStopSingleApp(app.packageName, skipForegroundApp)
                    if (success) {
                        successCount++
                    }
                }

                Log.d(TAG, "Force stopped $successCount/$totalCount selected apps")
                successCount > 0 || totalCount == 0
            } catch (e: Exception) {
                Log.e(TAG, "Error force stopping selected apps", e)
                false
            }
        }
    }




    /**
     * 检查内存是否高于阈值
     */
    private fun isMemoryAboveThreshold(threshold: Int, isPercentage: Boolean): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            if (isPercentage) {
                val availablePercentage = (memoryInfo.availMem * 100 / memoryInfo.totalMem).toInt()
                val result = availablePercentage > threshold
                Log.d(TAG, "Memory check (percentage): ${availablePercentage}% > ${threshold}% = $result")
                result
            } else {
                val availableGB = memoryInfo.availMem / (1024 * 1024 * 1024)
                val result = availableGB > threshold
                Log.d(TAG, "Memory check (GB): ${availableGB}GB > ${threshold}GB = $result")
                result
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory threshold", e)
            false
        }
    }

    /**
     * 按排序策略排序应用（SimpleAppInfo版本）
     */
    private suspend fun sortAppsByStrategy(
        apps: List<SimpleAppInfo>,
        strategy: AppSortingStrategy
    ): List<SimpleAppInfo> {
        return try {
            when (strategy) {
                AppSortingStrategy.BACKGROUND_TIME_ONLY -> {
                    // 仅按后台时间排序
                    sortSimpleAppsByBackgroundTime(apps)
                }
                AppSortingStrategy.PRIORITY_ONLY -> {
                    // 仅按应用重要性排序
                    sortSimpleAppsByPriority(apps)
                }
                AppSortingStrategy.PRIORITY_THEN_BACKGROUND_TIME -> {
                    // 先按重要性排序，同重要性内按后台时间排序
                    sortSimpleAppsByPriorityThenBackgroundTime(apps)
                }
                AppSortingStrategy.BACKGROUND_TIME_THEN_PRIORITY -> {
                    // 先按后台时间排序，时间相近时按重要性排序
                    sortSimpleAppsByBackgroundTimeThenPriority(apps)
                }
                AppSortingStrategy.SMART_COMPREHENSIVE -> {
                    // 智能综合排序
                    sortSimpleAppsBySmartScore(apps)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by strategy: $strategy", e)
            apps
        }
    }

    /**
     * 按后台时间排序SimpleAppInfo列表
     */
    private fun sortSimpleAppsByBackgroundTime(apps: List<SimpleAppInfo>): List<SimpleAppInfo> {
        return try {
            val currentTime = System.currentTimeMillis()
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val endTime = currentTime
            val startTime = endTime - 24 * 60 * 60 * 1000 // 24小时前

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            // 创建包名到最后使用时间的映射
            val lastUsedTimeMap = usageStats.associateBy({ it.packageName }, { it.lastTimeUsed })

            // 按最后使用时间排序，最久未使用的排在前面
            apps.sortedBy { app ->
                lastUsedTimeMap[app.packageName] ?: 0L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by background time", e)
            apps
        }
    }

    /**
     * 按应用重要性排序SimpleAppInfo列表
     */
    private suspend fun sortSimpleAppsByPriority(apps: List<SimpleAppInfo>): List<SimpleAppInfo> {
        return try {
            val settingsRepository = com.weinuo.quickcommands.data.SettingsRepository(context)
            val appImportanceMap = settingsRepository.getAppImportanceMap()

            apps.sortedBy { app ->
                val importance = appImportanceMap[app.packageName]
                importance?.priority ?: AppImportance.NORMAL.priority
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by priority", e)
            apps
        }
    }

    /**
     * 先按重要性排序，同重要性内按后台时间排序
     */
    private suspend fun sortSimpleAppsByPriorityThenBackgroundTime(apps: List<SimpleAppInfo>): List<SimpleAppInfo> {
        return try {
            val settingsRepository = com.weinuo.quickcommands.data.SettingsRepository(context)
            val appImportanceMap = settingsRepository.getAppImportanceMap()

            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 24 * 60 * 60 * 1000

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )
            val lastUsedTimeMap = usageStats.associateBy({ it.packageName }, { it.lastTimeUsed })

            apps.sortedWith(compareBy<SimpleAppInfo> { app ->
                val importance = appImportanceMap[app.packageName]
                importance?.priority ?: AppImportance.NORMAL.priority
            }.thenBy { app ->
                lastUsedTimeMap[app.packageName] ?: 0L
            })
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by priority then background time", e)
            apps
        }
    }

    /**
     * 先按后台时间排序，时间相近时按重要性排序
     */
    private suspend fun sortSimpleAppsByBackgroundTimeThenPriority(apps: List<SimpleAppInfo>): List<SimpleAppInfo> {
        return try {
            val settingsRepository = com.weinuo.quickcommands.data.SettingsRepository(context)
            val appImportanceMap = settingsRepository.getAppImportanceMap()

            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 24 * 60 * 60 * 1000

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )
            val lastUsedTimeMap = usageStats.associateBy({ it.packageName }, { it.lastTimeUsed })

            // 按后台时间分组（以1小时为单位），然后在组内按重要性排序
            val timeGroupSize = 60 * 60 * 1000L // 1小时
            apps.sortedWith(compareBy<SimpleAppInfo> { app ->
                val lastUsed = lastUsedTimeMap[app.packageName] ?: 0L
                lastUsed / timeGroupSize // 时间分组
            }.thenBy { app ->
                val importance = appImportanceMap[app.packageName]
                importance?.priority ?: AppImportance.NORMAL.priority
            })
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by background time then priority", e)
            apps
        }
    }

    /**
     * 智能综合排序
     */
    private suspend fun sortSimpleAppsBySmartScore(apps: List<SimpleAppInfo>): List<SimpleAppInfo> {
        return try {
            val settingsRepository = com.weinuo.quickcommands.data.SettingsRepository(context)
            val appImportanceMap = settingsRepository.getAppImportanceMap()

            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 24 * 60 * 60 * 1000

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )
            val lastUsedTimeMap = usageStats.associateBy({ it.packageName }, { it.lastTimeUsed })

            apps.sortedBy { app ->
                val importance = appImportanceMap[app.packageName] ?: AppImportance.NORMAL
                val lastUsed = lastUsedTimeMap[app.packageName] ?: 0L
                val timeSinceLastUsed = currentTime - lastUsed

                // 综合评分：重要性权重60%，时间权重40%
                val priorityScore = importance.priority * 0.6
                val timeScore = (timeSinceLastUsed / (24 * 60 * 60 * 1000.0)) * 0.4 // 天数

                priorityScore + timeScore
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by smart score", e)
            apps
        }
    }

    /**
     * 按后台时间排序应用
     */
    private fun sortAppsByBackgroundTime(apps: List<AppInfo>): List<AppInfo> {
        return try {
            val currentTime = System.currentTimeMillis()
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val endTime = currentTime
            val startTime = endTime - 24 * 60 * 60 * 1000 // 24小时前

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            // 创建包名到最后使用时间的映射
            val lastUsedTimeMap = usageStats.associateBy({ it.packageName }, { it.lastTimeUsed })

            // 按最后使用时间排序，最久未使用的排在前面
            apps.sortedBy { app ->
                lastUsedTimeMap[app.packageName] ?: 0L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by background time", e)
            apps
        }
    }

    /**
     * 按清理策略排序应用
     */
    private fun sortAppsByStrategy(
        apps: List<AppInfo>,
        strategy: CleanupStrategy,
        sortByBackgroundTime: Boolean
    ): List<AppInfo> {
        return try {
            val sortedApps = mutableListOf<AppInfo>()
            val remainingApps = apps.toMutableList()

            // 获取使用统计信息（如果需要按后台时间排序）
            val usageStatsMap = if (sortByBackgroundTime) {
                getUsageStatsMap()
            } else {
                emptyMap()
            }

            // 获取应用重要性映射
            val settingsRepository = com.weinuo.quickcommands.data.SettingsRepository(context)
            val appImportanceMap = settingsRepository.getAppImportanceMap()

            // 按规则顺序处理
            for (rule in strategy.rules.sortedBy { it.order }) {
                if (!rule.enabled) continue

                val appsForRule = filterAppsByRule(remainingApps, rule, usageStatsMap, appImportanceMap)

                // 如果启用了后台时间排序，对当前规则的应用进行排序
                val sortedRuleApps = if (sortByBackgroundTime && appsForRule.isNotEmpty()) {
                    appsForRule.sortedBy { app ->
                        usageStatsMap[app.packageName] ?: 0L
                    }
                } else {
                    appsForRule
                }

                sortedApps.addAll(sortedRuleApps)
                remainingApps.removeAll(appsForRule)
            }

            // 添加剩余的应用
            if (remainingApps.isNotEmpty()) {
                val sortedRemaining = if (sortByBackgroundTime) {
                    remainingApps.sortedBy { app ->
                        usageStatsMap[app.packageName] ?: 0L
                    }
                } else {
                    remainingApps
                }
                sortedApps.addAll(sortedRemaining)
            }

            Log.d(TAG, "Sorted ${apps.size} apps by strategy: ${strategy.name}")
            sortedApps
        } catch (e: Exception) {
            Log.e(TAG, "Error sorting apps by strategy", e)
            apps
        }
    }

    /**
     * 获取使用统计信息映射
     */
    private fun getUsageStatsMap(): Map<String, Long> {
        return try {
            val currentTime = System.currentTimeMillis()
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val endTime = currentTime
            val startTime = endTime - 24 * 60 * 60 * 1000 // 24小时前

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            usageStats.associateBy({ it.packageName }, { it.lastTimeUsed })
        } catch (e: Exception) {
            Log.e(TAG, "Error getting usage stats", e)
            emptyMap()
        }
    }

    /**
     * 根据规则过滤应用
     */
    private fun filterAppsByRule(
        apps: List<AppInfo>,
        rule: CleanupRule,
        usageStatsMap: Map<String, Long>,
        appImportanceMap: Map<String, AppImportance>
    ): List<AppInfo> {
        return try {
            when (rule.type) {
                CleanupRuleType.LOW_PRIORITY_APPS -> {
                    // 过滤出低优先级应用（不重要和非常不重要）
                    apps.filter { app ->
                        val importance = appImportanceMap[app.packageName]
                        importance == AppImportance.UNIMPORTANT ||
                        importance == AppImportance.VERY_UNIMPORTANT
                    }
                }
                CleanupRuleType.HIGH_PRIORITY_APPS -> {
                    // 过滤出高优先级应用（重要和非常重要）
                    apps.filter { app ->
                        val importance = appImportanceMap[app.packageName]
                        importance == AppImportance.IMPORTANT ||
                        importance == AppImportance.VERY_IMPORTANT
                    }
                }
                CleanupRuleType.LONG_UNUSED_APPS -> {
                    val hoursThreshold = rule.parameters["hours"] as? Int ?: 6
                    val timeThreshold = System.currentTimeMillis() - hoursThreshold * 60 * 60 * 1000
                    apps.filter { app ->
                        val lastUsed = usageStatsMap[app.packageName] ?: 0
                        lastUsed < timeThreshold
                    }
                }
                CleanupRuleType.MEMORY_HEAVY_APPS -> {
                    val memoryThresholdMB = rule.parameters["memoryThresholdMB"] as? Int ?: 100
                    filterMemoryHeavyAppsFromAppInfo(apps, memoryThresholdMB)
                }
                CleanupRuleType.SYSTEM_APPS -> {
                    apps.filter { it.isSystemApp }
                }
                CleanupRuleType.USER_APPS -> {
                    apps.filter { !it.isSystemApp }
                }
                CleanupRuleType.NORMAL_APPS -> {
                    // 返回没有设置重要性的应用（普通应用）
                    apps.filter { app ->
                        val importance = appImportanceMap[app.packageName]
                        importance == null || importance == AppImportance.NORMAL
                    }
                }
                CleanupRuleType.SPECIFIC_APPS -> {
                    // 暂时返回空列表，因为还没有实现指定应用功能
                    // TODO: 实现指定应用功能后，这里应该过滤出指定的应用
                    emptyList()
                }
                // 使用频率相关规则
                CleanupRuleType.MOST_USED_APPS,
                CleanupRuleType.LEAST_USED_APPS,
                CleanupRuleType.FREQUENT_APPS,
                CleanupRuleType.RARE_APPS,
                CleanupRuleType.LONG_SESSION_APPS,
                CleanupRuleType.SHORT_SESSION_APPS,
                CleanupRuleType.SMART_USAGE_RANKING -> {
                    // 暂时返回空列表，因为还没有实现使用频率功能
                    // TODO: 实现使用频率功能后，这里应该根据使用统计过滤应用
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error filtering apps by rule: ${rule.type}", e)
            emptyList()
        }
    }

    /**
     * 过滤内存占用大的应用（AppInfo版本）
     */
    private fun filterMemoryHeavyAppsFromAppInfo(apps: List<AppInfo>, memoryThresholdMB: Int): List<AppInfo> {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryHeavyApps = mutableListOf<AppInfo>()

            // 获取运行中的进程信息
            val runningProcesses = activityManager.runningAppProcesses ?: emptyList()
            val processMemoryMap = mutableMapOf<String, Int>()

            // 批量获取进程内存信息
            val pids = runningProcesses.map { it.pid }.toIntArray()
            if (pids.isNotEmpty()) {
                val memoryInfos = activityManager.getProcessMemoryInfo(pids)

                runningProcesses.forEachIndexed { index, processInfo ->
                    if (index < memoryInfos.size) {
                        val memoryInfo = memoryInfos[index]
                        val totalMemoryKB = memoryInfo.totalPss
                        val totalMemoryMB = totalMemoryKB / 1024

                        // 记录每个包名的最大内存占用
                        processInfo.pkgList?.forEach { packageName ->
                            val currentMemory = processMemoryMap[packageName] ?: 0
                            if (totalMemoryMB > currentMemory) {
                                processMemoryMap[packageName] = totalMemoryMB
                            }
                        }
                    }
                }
            }

            // 过滤出内存占用超过阈值的应用
            apps.forEach { app ->
                val memoryUsage = processMemoryMap[app.packageName] ?: 0
                if (memoryUsage >= memoryThresholdMB) {
                    memoryHeavyApps.add(app)
                    Log.d(TAG, "Memory heavy app: ${app.packageName} (${memoryUsage}MB)")
                }
            }

            Log.d(TAG, "Found ${memoryHeavyApps.size} memory heavy apps (threshold: ${memoryThresholdMB}MB)")
            memoryHeavyApps

        } catch (e: Exception) {
            Log.e(TAG, "Error filtering memory heavy apps", e)
            emptyList()
        }
    }

    /**
     * 过滤内存占用大的应用（SimpleAppInfo版本）
     */
    private fun filterMemoryHeavyApps(apps: List<SimpleAppInfo>, memoryThresholdMB: Int): List<SimpleAppInfo> {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryHeavyApps = mutableListOf<SimpleAppInfo>()

            // 获取运行中的进程信息
            val runningProcesses = activityManager.runningAppProcesses ?: emptyList()
            val processMemoryMap = mutableMapOf<String, Int>()

            // 批量获取进程内存信息
            val pids = runningProcesses.map { it.pid }.toIntArray()
            if (pids.isNotEmpty()) {
                val memoryInfos = activityManager.getProcessMemoryInfo(pids)

                runningProcesses.forEachIndexed { index, processInfo ->
                    if (index < memoryInfos.size) {
                        val memoryInfo = memoryInfos[index]
                        val totalMemoryKB = memoryInfo.totalPss
                        val totalMemoryMB = totalMemoryKB / 1024

                        // 记录每个包名的最大内存占用
                        processInfo.pkgList?.forEach { packageName ->
                            val currentMemory = processMemoryMap[packageName] ?: 0
                            if (totalMemoryMB > currentMemory) {
                                processMemoryMap[packageName] = totalMemoryMB
                            }
                        }
                    }
                }
            }

            // 过滤出内存占用超过阈值的应用
            apps.forEach { app ->
                val memoryUsage = processMemoryMap[app.packageName] ?: 0
                if (memoryUsage >= memoryThresholdMB) {
                    memoryHeavyApps.add(app)
                    Log.d(TAG, "Memory heavy app: ${app.packageName} (${memoryUsage}MB)")
                }
            }

            Log.d(TAG, "Found ${memoryHeavyApps.size} memory heavy apps (threshold: ${memoryThresholdMB}MB)")
            memoryHeavyApps

        } catch (e: Exception) {
            Log.e(TAG, "Error filtering memory heavy apps", e)
            emptyList()
        }
    }

    /**
     * 强制停止单个应用（支持自定义命令）
     */
    private suspend fun forceStopSingleApp(
        packageName: String,
        skipForegroundApp: Boolean,
        customCommand: String = "am force-stop [package_name]"
    ): Boolean {
        return forceStopSingleAppWithRealtimeDetection(
            packageName = packageName,
            skipForegroundApp = skipForegroundApp,
            customCommand = customCommand,
            enableRealtimeDetection = false
        )
    }

    /**
     * 强制停止单个应用（支持自定义命令和实时前台检测）
     *
     * @param packageName 要停止的应用包名
     * @param skipForegroundApp 是否跳过前台应用
     * @param customCommand 自定义强制停止命令
     * @param enableRealtimeDetection 是否启用实时前台应用检测
     * @return 是否成功停止应用
     */
    private suspend fun forceStopSingleAppWithRealtimeDetection(
        packageName: String,
        skipForegroundApp: Boolean,
        customCommand: String = "am force-stop [package_name]",
        enableRealtimeDetection: Boolean = false
    ): Boolean {
        return try {
            // 检查是否是前台应用
            if (skipForegroundApp) {
                val isForeground = if (enableRealtimeDetection) {
                    isForegroundAppOptimized(packageName, true)
                } else {
                    isForegroundApp(packageName)
                }

                if (isForeground) {
                    Log.d(TAG, "Skipping foreground app: $packageName (realtime detection: $enableRealtimeDetection)")
                    return true
                }
            }

            // 使用ActivityManager强制停止应用
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

            // 尝试使用反射调用forceStopPackage方法
            try {
                val method = ActivityManager::class.java.getMethod("forceStopPackage", String::class.java)
                method.invoke(activityManager, packageName)
                Log.d(TAG, "Force stopped app: $packageName")
                true
            } catch (e: Exception) {
                // 如果反射失败，尝试使用Shizuku执行自定义命令
                Log.w(TAG, "Failed to force stop app using ActivityManager, trying Shizuku with custom command", e)
                val finalCommand = customCommand.replace("[package_name]", packageName)
                val result = ShizukuManager.executeCommand(finalCommand)
                val success = !result.contains("Error:") && !result.contains("错误")
                Log.d(TAG, "Shizuku custom command result for $packageName: $result")
                success
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error force stopping app: $packageName", e)
            false
        }
    }

    // 前台应用检测缓存
    private var cachedForegroundApp: String? = null
    private var cachedForegroundTimestamp: Long = 0
    private val FOREGROUND_CACHE_VALID_TIME = 200L // 200ms缓存有效期

    /**
     * 使用UsageStatsManager获取当前前台应用
     * 这是唯一可靠的方法，ActivityManager.getRunningTasks()在新版本Android中已被限制
     * 复用现有的getForegroundPackage()方法逻辑
     */
    private fun getCurrentForegroundAppPackage(): String? {
        return getForegroundPackage()
    }

    /**
     * 检查应用是否是前台应用（使用UsageStatsManager）
     */
    private fun isForegroundApp(packageName: String): Boolean {
        return try {
            val currentForegroundApp = getCurrentForegroundAppPackage()
            val result = currentForegroundApp == packageName
            Log.d(TAG, "Foreground app check: current=$currentForegroundApp, target=$packageName, result=$result")
            result
        } catch (e: Exception) {
            Log.w(TAG, "Error checking foreground app", e)
            false
        }
    }

    /**
     * 检查应用是否是前台应用（带缓存优化）
     *
     * @param packageName 要检查的应用包名
     * @param enableRealtimeDetection 是否启用实时检测（使用缓存优化）
     * @return 是否是前台应用
     */
    private fun isForegroundAppOptimized(packageName: String, enableRealtimeDetection: Boolean): Boolean {
        return try {
            if (!enableRealtimeDetection) {
                // 不启用实时检测时，使用原有逻辑
                return isForegroundApp(packageName)
            }

            // 启用实时检测时，使用缓存优化
            val currentTime = System.currentTimeMillis()
            if (cachedForegroundApp != null &&
                currentTime - cachedForegroundTimestamp < FOREGROUND_CACHE_VALID_TIME) {
                // 使用缓存结果
                Log.d(TAG, "Using cached foreground app: $cachedForegroundApp")
                return cachedForegroundApp == packageName
            }

            // 缓存过期，重新检测
            val currentForegroundApp = getCurrentForegroundAppPackage()

            // 更新缓存
            cachedForegroundApp = currentForegroundApp
            cachedForegroundTimestamp = currentTime

            Log.d(TAG, "Updated foreground app cache: $currentForegroundApp")
            return currentForegroundApp == packageName

        } catch (e: Exception) {
            Log.w(TAG, "Error checking foreground app with optimization", e)
            false
        }
    }

    // ==================== 文件操作辅助方法 ====================



    /**
     * 解析合并的文件模式字符串，返回源文件模式和目标文件模式
     * 格式：sourcePattern|targetPattern 或 单一模式
     */
    private fun parseFilePatterns(combinedPattern: String): Pair<String, String> {
        return if (combinedPattern.contains("|")) {
            val parts = combinedPattern.split("|", limit = 2)
            Pair(parts[0], parts.getOrElse(1) { "" })
        } else {
            Pair(combinedPattern, combinedPattern)
        }
    }

    /**
     * 根据用户选择的路径获取文件列表
     */
    private fun getFilesByUserPath(userPath: String, selectionMode: FileSelectionMode, specificPattern: String = ""): List<File> {
        return try {
            if (userPath.isEmpty()) {
                Log.w(TAG, "User path is empty, falling back to default selection mode")
                return getFilesBySelectionMode(selectionMode, specificPattern)
            }

            val sourceDir = File(userPath)
            if (!sourceDir.exists()) {
                Log.e(TAG, "User selected path does not exist: $userPath")
                return emptyList()
            }

            when (selectionMode) {
                FileSelectionMode.ALL_FILES -> {
                    // 返回目录中的所有文件和文件夹
                    sourceDir.listFiles()?.toList() ?: emptyList()
                }
                FileSelectionMode.ALL_MEDIA -> {
                    // 返回目录中的所有媒体文件
                    sourceDir.listFiles()?.filter { file ->
                        val ext = file.extension.lowercase()
                        ext in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "mp3", "wav", "flac", "aac", "ogg", "m4a")
                    } ?: emptyList()
                }
                FileSelectionMode.IMAGES -> {
                    // 返回目录中的图片文件
                    sourceDir.listFiles()?.filter { file ->
                        file.extension.lowercase() in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp")
                    } ?: emptyList()
                }
                FileSelectionMode.VIDEOS -> {
                    // 返回目录中的视频文件
                    sourceDir.listFiles()?.filter { file ->
                        file.extension.lowercase() in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm")
                    } ?: emptyList()
                }
                FileSelectionMode.AUDIO -> {
                    // 返回目录中的音频文件
                    sourceDir.listFiles()?.filter { file ->
                        file.extension.lowercase() in listOf("mp3", "wav", "flac", "aac", "ogg", "m4a")
                    } ?: emptyList()
                }
                FileSelectionMode.SPECIFIC_PATTERN -> {
                    // 根据指定模式过滤文件
                    if (specificPattern.isNotEmpty()) {
                        sourceDir.listFiles()?.filter { file ->
                            file.name.matches(Regex(specificPattern.replace("*", ".*")))
                        } ?: emptyList()
                    } else {
                        sourceDir.listFiles()?.toList() ?: emptyList()
                    }
                }
                FileSelectionMode.FOLDERS -> {
                    // 返回目录中的所有文件夹
                    sourceDir.listFiles()?.filter { it.isDirectory } ?: emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting files by user path: $userPath", e)
            emptyList()
        }
    }

    /**
     * 根据用户选择的路径获取目录
     */
    private fun getUserSelectedDirectory(userPath: String): File? {
        return try {
            if (userPath.isEmpty()) {
                Log.w(TAG, "User path is empty")
                return null
            }

            val dir = File(userPath)
            if (!dir.exists()) {
                Log.e(TAG, "User selected directory does not exist: $userPath")
                return null
            }

            if (!dir.isDirectory) {
                Log.e(TAG, "User selected path is not a directory: $userPath")
                return null
            }

            dir
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user selected directory: $userPath", e)
            null
        }
    }

    /**
     * 根据选择模式获取文件列表
     */
    private fun getFilesBySelectionMode(selectionMode: FileSelectionMode, specificPattern: String = ""): List<File> {
        return try {
            when (selectionMode) {
                FileSelectionMode.ALL_FILES -> {
                    // 所有文件模式，返回外部存储根目录的所有文件
                    val externalDir = Environment.getExternalStorageDirectory()
                    if (externalDir.exists() && externalDir.isDirectory) {
                        externalDir.listFiles()?.toList() ?: emptyList()
                    } else {
                        emptyList()
                    }
                }
                FileSelectionMode.ALL_MEDIA -> {
                    // 所有媒体文件，包括图片、视频、音频
                    val mediaFiles = mutableListOf<File>()

                    // 添加图片文件
                    val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                    if (picturesDir.exists()) {
                        picturesDir.listFiles()?.let { mediaFiles.addAll(it) }
                    }

                    // 添加DCIM文件
                    val dcimDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
                    if (dcimDir.exists()) {
                        dcimDir.listFiles()?.let { mediaFiles.addAll(it) }
                    }

                    // 添加视频文件
                    val moviesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MOVIES)
                    if (moviesDir.exists()) {
                        moviesDir.listFiles()?.let { mediaFiles.addAll(it) }
                    }

                    // 添加音频文件
                    val musicDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC)
                    if (musicDir.exists()) {
                        musicDir.listFiles()?.let { mediaFiles.addAll(it) }
                    }

                    mediaFiles
                }
                FileSelectionMode.IMAGES -> {
                    // 图片文件
                    val imageFiles = mutableListOf<File>()

                    val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                    if (picturesDir.exists()) {
                        picturesDir.listFiles()?.filter { file ->
                            file.extension.lowercase() in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp")
                        }?.let { imageFiles.addAll(it) }
                    }

                    val dcimDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
                    if (dcimDir.exists()) {
                        dcimDir.listFiles()?.filter { file ->
                            file.extension.lowercase() in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp")
                        }?.let { imageFiles.addAll(it) }
                    }

                    imageFiles
                }
                FileSelectionMode.VIDEOS -> {
                    // 视频文件
                    val moviesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MOVIES)
                    if (moviesDir.exists() && moviesDir.isDirectory) {
                        moviesDir.listFiles()?.filter { file ->
                            file.extension.lowercase() in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm")
                        } ?: emptyList()
                    } else {
                        emptyList()
                    }
                }
                FileSelectionMode.AUDIO -> {
                    // 音频文件
                    val musicDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC)
                    if (musicDir.exists() && musicDir.isDirectory) {
                        musicDir.listFiles()?.filter { file ->
                            file.extension.lowercase() in listOf("mp3", "wav", "flac", "aac", "ogg", "m4a")
                        } ?: emptyList()
                    } else {
                        emptyList()
                    }
                }
                FileSelectionMode.SPECIFIC_PATTERN -> {
                    // 指定文件模式
                    if (specificPattern.isNotEmpty()) {
                        val file = File(specificPattern)
                        if (file.exists()) listOf(file) else emptyList()
                    } else {
                        emptyList()
                    }
                }
                FileSelectionMode.FOLDERS -> {
                    // 文件夹模式，返回外部存储根目录的所有文件夹
                    val externalDir = Environment.getExternalStorageDirectory()
                    if (externalDir.exists() && externalDir.isDirectory) {
                        externalDir.listFiles()?.filter { it.isDirectory } ?: emptyList()
                    } else {
                        emptyList()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting files by selection mode: $selectionMode", e)
            emptyList()
        }
    }

    /**
     * 根据目标选择模式获取目标目录
     */
    private fun getTargetDirectory(targetMode: FileSelectionMode): File? {
        return try {
            when (targetMode) {
                FileSelectionMode.ALL_FILES -> {
                    // 所有文件模式，返回外部存储根目录
                    Environment.getExternalStorageDirectory()
                }
                FileSelectionMode.ALL_MEDIA -> {
                    // 所有媒体文件模式，返回图片目录
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                }
                FileSelectionMode.IMAGES -> {
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                }
                FileSelectionMode.VIDEOS -> {
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MOVIES)
                }
                FileSelectionMode.AUDIO -> {
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC)
                }
                FileSelectionMode.SPECIFIC_PATTERN -> {
                    // 指定模式，返回外部存储根目录
                    Environment.getExternalStorageDirectory()
                }
                FileSelectionMode.FOLDERS -> {
                    // 文件夹模式，返回外部存储根目录
                    Environment.getExternalStorageDirectory()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting target directory for mode: $targetMode", e)
            null
        }
    }

    /**
     * 获取唯一的文件名（如果文件已存在，添加序号）
     */
    private fun getUniqueFileName(originalFile: File): File {
        if (!originalFile.exists()) {
            return originalFile
        }

        val parent = originalFile.parentFile ?: return originalFile
        val nameWithoutExtension = originalFile.nameWithoutExtension
        val extension = originalFile.extension

        var counter = 1
        var newFile: File

        do {
            val newName = if (extension.isNotEmpty()) {
                "${nameWithoutExtension}_$counter.$extension"
            } else {
                "${nameWithoutExtension}_$counter"
            }
            newFile = File(parent, newName)
            counter++
        } while (newFile.exists() && counter < 1000) // 防止无限循环

        return newFile
    }

    /**
     * 复制目录
     */
    private fun copyDirectory(sourceDir: File, targetDir: File): Boolean {
        return try {
            if (!sourceDir.exists() || !sourceDir.isDirectory) {
                return false
            }

            // 创建目标目录
            if (!targetDir.exists()) {
                targetDir.mkdirs()
            }

            // 复制目录中的所有文件和子目录
            sourceDir.listFiles()?.forEach { file ->
                val targetFile = File(targetDir, file.name)
                if (file.isDirectory) {
                    copyDirectory(file, targetFile)
                } else {
                    file.copyTo(targetFile, overwrite = false)
                }
            }

            true
        } catch (e: Exception) {
            Log.e(TAG, "Error copying directory: ${sourceDir.absolutePath}", e)
            false
        }
    }

    /**
     * 删除目录
     */
    private fun deleteDirectory(dir: File): Boolean {
        return try {
            if (!dir.exists()) {
                return true
            }

            if (dir.isDirectory) {
                // 递归删除目录中的所有文件和子目录
                dir.listFiles()?.forEach { file ->
                    if (file.isDirectory) {
                        deleteDirectory(file)
                    } else {
                        file.delete()
                    }
                }
            }

            // 删除目录本身
            dir.delete()
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting directory: ${dir.absolutePath}", e)
            false
        }
    }

    /**
     * 压缩文件列表到ZIP文件
     */
    private fun compressFiles(files: List<File>, zipFile: File): Boolean {
        return try {
            zipFile.parentFile?.mkdirs()

            java.util.zip.ZipOutputStream(java.io.FileOutputStream(zipFile)).use { zipOut ->
                for (file in files) {
                    if (file.exists()) {
                        if (file.isDirectory) {
                            compressDirectory(file, file.name, zipOut)
                        } else {
                            compressFile(file, file.name, zipOut)
                        }
                    }
                }
            }

            Log.d(TAG, "Successfully compressed ${files.size} files to: ${zipFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error compressing files to: ${zipFile.absolutePath}", e)
            false
        }
    }

    /**
     * 压缩单个文件到ZIP流
     */
    private fun compressFile(file: File, entryName: String, zipOut: java.util.zip.ZipOutputStream) {
        try {
            val zipEntry = java.util.zip.ZipEntry(entryName)
            zipOut.putNextEntry(zipEntry)

            java.io.FileInputStream(file).use { fileIn ->
                fileIn.copyTo(zipOut)
            }

            zipOut.closeEntry()
        } catch (e: Exception) {
            Log.e(TAG, "Error compressing file: ${file.absolutePath}", e)
        }
    }

    /**
     * 递归压缩目录到ZIP流
     */
    private fun compressDirectory(dir: File, baseName: String, zipOut: java.util.zip.ZipOutputStream) {
        try {
            val files = dir.listFiles() ?: return

            // 如果目录为空，添加一个目录条目
            if (files.isEmpty()) {
                val zipEntry = java.util.zip.ZipEntry("$baseName/")
                zipOut.putNextEntry(zipEntry)
                zipOut.closeEntry()
                return
            }

            // 压缩目录中的所有文件和子目录
            for (file in files) {
                val entryName = "$baseName/${file.name}"
                if (file.isDirectory) {
                    compressDirectory(file, entryName, zipOut)
                } else {
                    compressFile(file, entryName, zipOut)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error compressing directory: ${dir.absolutePath}", e)
        }
    }

    /**
     * 执行系统操作
     *
     * @param task 设备动作任务
     * @return 任务是否成功执行
     */
    private suspend fun executeSystemOperation(task: DeviceActionTask): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                // 获取系统操作无障碍服务实例
                val systemOperationService = com.weinuo.quickcommands.service.SystemOperationAccessibilityService.getInstance()

                if (systemOperationService == null) {
                    return@withContext false
                }

                // 根据操作类型执行相应的系统操作
                val operationType = when (task.operation) {
                    DeviceActionOperation.QUICK_SETTINGS -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_QUICK_SETTINGS
                    DeviceActionOperation.POWER_MENU -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_POWER_MENU
                    DeviceActionOperation.RECENT_TASKS -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_RECENT_TASKS
                    DeviceActionOperation.APP_DRAWER -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_APP_DRAWER
                    DeviceActionOperation.ACCESSIBILITY_TOGGLE -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_ACCESSIBILITY_TOGGLE
                    DeviceActionOperation.BACK_KEY -> com.weinuo.quickcommands.service.SystemOperationAccessibilityService.ACTION_BACK_KEY
                    else -> {
                        return@withContext false
                    }
                }

                // 执行系统操作
                val success = systemOperationService.executeSystemOperation(operationType)

                success

            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 执行检查屏幕文字任务
     */
    private suspend fun executeCheckScreenText(task: ScreenControlTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 获取界面交互无障碍服务实例
                val interfaceInteractionService = com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService.getInstance()

                if (interfaceInteractionService == null) {
                    Log.e(TAG, "界面交互无障碍服务未启用，无法执行检查屏幕文字任务")
                    return@withContext false
                }

                // 检查屏幕文字
                val checkResult = interfaceInteractionService.checkScreenTextAdvanced(
                    targetText = task.checkTextContent,
                    caseSensitive = task.checkTextCaseSensitive,
                    useRegex = task.checkTextUseRegex,
                    matchMode = task.checkTextMatchMode,
                    includeOverlay = task.checkTextIncludeOverlay,
                    ignoreHidden = task.checkTextIgnoreHidden
                )

                // 确定输出文件路径
                val outputFile = if (task.checkTextOutputFile.isNotEmpty()) {
                    task.checkTextOutputFile
                } else {
                    // 使用默认路径
                    "${context.getExternalFilesDir(null)?.absolutePath}/screen_check_result.txt"
                }

                // 保存检查结果到文件
                val saveSuccess = interfaceInteractionService.saveCheckResultToFile(checkResult.found, outputFile)

                // 如果找到文本且指定了视图ID输出文件，保存视图ID
                if (checkResult.found && task.checkTextViewIdOutputFile.isNotEmpty() && checkResult.viewId != null) {
                    interfaceInteractionService.saveViewIdToFile(checkResult.viewId, task.checkTextViewIdOutputFile)
                }

                if (saveSuccess) {
                    Log.d(TAG, "Check screen text completed: text='${task.checkTextContent}', found=${checkResult.found}, saved to: $outputFile")
                } else {
                    Log.e(TAG, "Failed to save check result to file: $outputFile")
                }

                saveSuccess
            } catch (e: Exception) {
                Log.e(TAG, "Error executing check screen text", e)
                false
            }
        }
    }

    /**
     * 执行读取屏幕内容任务
     */
    private suspend fun executeReadScreenContent(task: ScreenControlTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 获取界面交互无障碍服务实例
                val interfaceInteractionService = com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService.getInstance()

                if (interfaceInteractionService == null) {
                    Log.e(TAG, "界面交互无障碍服务未启用，无法执行读取屏幕内容任务")
                    return@withContext false
                }

                // 读取屏幕内容
                val content = interfaceInteractionService.readScreenContent()

                // 确定输出文件路径
                val outputFile = if (task.readContentOutputFile.isNotEmpty()) {
                    task.readContentOutputFile
                } else {
                    // 使用默认路径
                    "${context.getExternalFilesDir(null)?.absolutePath}/screen_content.txt"
                }

                // 保存屏幕内容到文件
                val saveSuccess = interfaceInteractionService.saveContentToFile(content, outputFile)

                if (saveSuccess) {
                    Log.d(TAG, "Read screen content completed: content length=${content.length}, saved to: $outputFile")
                } else {
                    Log.e(TAG, "Failed to save screen content to file: $outputFile")
                }

                saveSuccess
            } catch (e: Exception) {
                Log.e(TAG, "Error executing read screen content", e)
                false
            }
        }
    }

    /**
     * 执行检查像素颜色任务
     */
    private suspend fun executeCheckPixelColor(task: ScreenControlTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 获取界面交互无障碍服务实例
                val interfaceInteractionService = com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService.getInstance()

                if (interfaceInteractionService == null) {
                    Log.e(TAG, "界面交互无障碍服务未启用，无法执行检查像素颜色任务")
                    return@withContext false
                }

                // 计算实际像素坐标
                val (actualX, actualY) = when (task.checkPixelCoordinateType) {
                    CoordinateType.PIXEL -> {
                        Pair(task.checkPixelX.toInt(), task.checkPixelY.toInt())
                    }
                    CoordinateType.PERCENTAGE -> {
                        val displayMetrics = context.resources.displayMetrics
                        Pair(
                            (task.checkPixelX * displayMetrics.widthPixels).toInt(),
                            (task.checkPixelY * displayMetrics.heightPixels).toInt()
                        )
                    }
                }

                // 通过无障碍服务获取指定位置的界面元素颜色
                val colorInfo = interfaceInteractionService.getPixelColorAtPosition(actualX, actualY)

                // 确定输出文件路径
                val outputFile = if (task.checkPixelColorOutputFile.isNotEmpty()) {
                    task.checkPixelColorOutputFile
                } else {
                    // 使用默认路径
                    "${context.getExternalFilesDir(null)?.absolutePath}/pixel_color.txt"
                }

                // 保存颜色信息到文件
                val saveSuccess = interfaceInteractionService.savePixelColorToFile(colorInfo, outputFile)

                if (saveSuccess) {
                    Log.d(TAG, "Check pixel color completed: position=($actualX, $actualY), color=${colorInfo.colorHex}, saved to: $outputFile")
                } else {
                    Log.e(TAG, "Failed to save pixel color to file: $outputFile")
                }

                saveSuccess
            } catch (e: Exception) {
                Log.e(TAG, "Error executing check pixel color", e)
                false
            }
        }
    }

    /**
     * 执行冻结应用任务
     *
     * @param task 应用任务
     * @param eventData 事件数据（可选）
     * @return 任务是否成功执行
     */
    private suspend fun executeFreezeApp(task: ApplicationTask, eventData: Map<String, Any>? = null): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Executing freeze app task: scope=${task.freezeScope}")

                when (task.freezeScope) {
                    ForceStopScope.SELECTED_APP -> {
                        // 冻结选择的应用列表
                        freezeSelectedApps(task.freezeSelectedApps)
                    }
                    ForceStopScope.TRIGGER_APP -> {
                        // 冻结触发条件的应用
                        freezeTriggerApp(eventData)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing freeze app task", e)
                false
            }
        }
    }

    /**
     * 执行解冻应用任务
     *
     * @param task 应用任务
     * @param eventData 事件数据（可选）
     * @return 任务是否成功执行
     */
    private suspend fun executeUnfreezeApp(task: ApplicationTask, eventData: Map<String, Any>? = null): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Executing unfreeze app task: scope=${task.unfreezeScope}")

                val success = when (task.unfreezeScope) {
                    ForceStopScope.SELECTED_APP -> {
                        // 解冻选择的应用列表
                        unfreezeSelectedApps(task.unfreezeSelectedApps, task.openAppAfterUnfreeze)
                    }
                    ForceStopScope.TRIGGER_APP -> {
                        // 解冻触发条件的应用
                        unfreezeTriggerApp(eventData, task.openAppAfterUnfreeze)
                    }
                }

                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing unfreeze app task", e)
                false
            }
        }
    }

    /**
     * 冻结选择的应用列表
     */
    private suspend fun freezeSelectedApps(selectedApps: List<SimpleAppInfo>): Boolean {
        return try {
            var successCount = 0
            for (app in selectedApps) {
                if (freezeSingleApp(app.packageName)) {
                    successCount++
                }
            }

            Log.d(TAG, "Freeze apps completed: $successCount/${selectedApps.size} apps frozen")
            successCount > 0 // 至少冻结一个应用就算成功
        } catch (e: Exception) {
            Log.e(TAG, "Error freezing selected apps", e)
            false
        }
    }

    /**
     * 解冻选择的应用列表
     */
    private suspend fun unfreezeSelectedApps(selectedApps: List<SimpleAppInfo>, openAfterUnfreeze: Boolean): Boolean {
        return try {
            var successCount = 0
            for (app in selectedApps) {
                if (unfreezeSingleApp(app.packageName)) {
                    successCount++

                    // 如果需要解冻后打开应用
                    if (openAfterUnfreeze) {
                        delay(1000) // 等待应用解冻完成
                        launchAppByPackageName(app.packageName)
                    }
                }
            }

            Log.d(TAG, "Unfreeze apps completed: $successCount/${selectedApps.size} apps unfrozen")
            successCount > 0 // 至少解冻一个应用就算成功
        } catch (e: Exception) {
            Log.e(TAG, "Error unfreezing selected apps", e)
            false
        }
    }

    /**
     * 冻结触发条件的应用
     */
    private suspend fun freezeTriggerApp(eventData: Map<String, Any>?): Boolean {
        return try {
            // 优先从事件数据中获取所有触发的应用
            val triggeredApps = eventData?.get("triggeredApps") as? List<SimpleAppInfo>

            if (!triggeredApps.isNullOrEmpty()) {
                Log.d(TAG, "Freezing ${triggeredApps.size} triggered apps from event data")

                // 冻结所有触发的应用
                var allSuccess = true
                for (app in triggeredApps) {
                    Log.d(TAG, "Freezing triggered app: ${app.appName} (${app.packageName})")
                    val success = freezeSingleApp(app.packageName)
                    if (!success) {
                        allSuccess = false
                    }
                }
                return allSuccess
            }

            // 回退：尝试获取单个触发应用
            val triggerApp = eventData?.get("triggerApp") as? SimpleAppInfo
            if (triggerApp != null) {
                Log.d(TAG, "Freezing single trigger app: ${triggerApp.appName} (${triggerApp.packageName})")
                return freezeSingleApp(triggerApp.packageName)
            }

            // 最终回退：尝试获取包名字符串（兼容旧格式）
            val triggerPackageName = eventData?.get("triggerPackageName") as? String
            if (!triggerPackageName.isNullOrEmpty()) {
                Log.d(TAG, "Freezing trigger app by package name: $triggerPackageName")
                return freezeSingleApp(triggerPackageName)
            }

            Log.w(TAG, "No trigger app information found in event data")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "Error freezing trigger app", e)
            false
        }
    }

    /**
     * 解冻触发条件的应用
     */
    private suspend fun unfreezeTriggerApp(eventData: Map<String, Any>?, openAfterUnfreeze: Boolean): Boolean {
        return try {
            // 优先从事件数据中获取所有触发的应用
            val triggeredApps = eventData?.get("triggeredApps") as? List<SimpleAppInfo>

            if (!triggeredApps.isNullOrEmpty()) {
                Log.d(TAG, "Unfreezing ${triggeredApps.size} triggered apps from event data")

                // 解冻所有触发的应用
                var allSuccess = true
                for (app in triggeredApps) {
                    Log.d(TAG, "Unfreezing triggered app: ${app.appName} (${app.packageName})")
                    val success = unfreezeSingleApp(app.packageName)
                    if (!success) {
                        allSuccess = false
                    }

                    // 如果需要解冻后打开应用（只对第一个应用执行，避免同时打开多个应用）
                    if (success && openAfterUnfreeze && app == triggeredApps.first()) {
                        delay(1000) // 等待应用解冻完成
                        launchAppByPackageName(app.packageName)
                    }
                }
                return allSuccess
            }

            // 回退：尝试获取单个触发应用
            val triggerApp = eventData?.get("triggerApp") as? SimpleAppInfo
            if (triggerApp != null) {
                Log.d(TAG, "Unfreezing single trigger app: ${triggerApp.appName} (${triggerApp.packageName})")
                val success = unfreezeSingleApp(triggerApp.packageName)

                // 如果需要解冻后打开应用
                if (success && openAfterUnfreeze) {
                    delay(1000) // 等待应用解冻完成
                    launchAppByPackageName(triggerApp.packageName)
                }

                return success
            }

            // 最终回退：尝试获取包名字符串（兼容旧格式）
            val triggerPackageName = eventData?.get("triggerPackageName") as? String
            if (!triggerPackageName.isNullOrEmpty()) {
                Log.d(TAG, "Unfreezing trigger app by package name: $triggerPackageName")
                val success = unfreezeSingleApp(triggerPackageName)

                // 如果需要解冻后打开应用
                if (success && openAfterUnfreeze) {
                    delay(1000) // 等待应用解冻完成
                    launchAppByPackageName(triggerPackageName)
                }

                return success
            }

            Log.w(TAG, "No trigger app information found in event data")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "Error unfreezing trigger app", e)
            false
        }
    }

    /**
     * 冻结单个应用
     */
    private suspend fun freezeSingleApp(packageName: String): Boolean {
        return try {
            Log.d(TAG, "Freezing app: $packageName")
            val success = ShizukuManager.freezeApp(packageName)
            Log.d(TAG, "Freeze app $packageName: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error freezing app: $packageName", e)
            false
        }
    }

    /**
     * 解冻单个应用
     */
    private suspend fun unfreezeSingleApp(packageName: String): Boolean {
        return try {
            Log.d(TAG, "Unfreezing app: $packageName")
            val success = ShizukuManager.unfreezeApp(packageName)
            Log.d(TAG, "Unfreeze app $packageName: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error unfreezing app: $packageName", e)
            false
        }
    }

    /**
     * 通过包名启动应用
     */
    private suspend fun launchAppByPackageName(packageName: String): Boolean {
        return try {
            val intent = context.packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                Log.d(TAG, "Launched app: $packageName")
                true
            } else {
                Log.w(TAG, "No launch intent found for package: $packageName")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error launching app: $packageName", e)
            false
        }
    }



    /**
     * 执行自动点击器回放任务
     */
    private suspend fun executeAutoClickerPlayback(task: ScreenControlTask): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 获取自动点击器无障碍服务实例
                val autoClickerService = com.weinuo.quickcommands.service.AutoClickerAccessibilityService.getInstance()

                if (autoClickerService == null) {
                    Log.e(TAG, "自动点击器无障碍服务未启用，无法执行回放任务")
                    return@withContext false
                }

                // 检查Android版本兼容性
                if (!com.weinuo.quickcommands.service.AutoClickerAccessibilityService.isGesturePlaybackSupported()) {
                    Log.e(TAG, "当前Android版本不支持手势回放功能")
                    return@withContext false
                }

                // 根据操作来源类型执行不同的回放逻辑
                val success = when (task.autoClickerSourceType) {
                    com.weinuo.quickcommands.model.AutoClickerSourceType.INSTANT_RECORDING -> {
                        // 即时录制回放
                        if (task.recordedGesture.isEmpty()) {
                            Log.e(TAG, "即时录制数据为空，无法执行回放")
                            false
                        } else {
                            autoClickerService.playbackRecordedGesture(
                                recordingId = task.recordedGesture,
                                loopCount = task.playbackLoopCount,
                                playbackSpeed = task.playbackSpeed,
                                delayBetweenLoops = task.delayBetweenLoops
                            )
                        }
                    }
                    com.weinuo.quickcommands.model.AutoClickerSourceType.QUICK_OPERATION -> {
                        // 快速操作回放
                        autoClickerService.executeQuickOperation(
                            operationType = task.quickOperationType,
                            clickX = task.clickX,
                            clickY = task.clickY,
                            clickCount = task.clickCount,
                            clickInterval = task.clickInterval,
                            longPressDuration = task.longPressDuration,
                            swipeStartX = task.swipeStartX,
                            swipeStartY = task.swipeStartY,
                            swipeEndX = task.swipeEndX,
                            swipeEndY = task.swipeEndY,
                            swipeDuration = task.swipeDuration,
                            loopCount = task.playbackLoopCount,
                            playbackSpeed = task.playbackSpeed,
                            delayBetweenLoops = task.delayBetweenLoops
                        )
                    }
                }

                Log.d(TAG, "自动点击器回放任务已启动: sourceType=${task.autoClickerSourceType}, loops=${task.playbackLoopCount}, speed=${task.playbackSpeed}")
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error executing auto clicker playback", e)
                false
            }
        }
    }




}

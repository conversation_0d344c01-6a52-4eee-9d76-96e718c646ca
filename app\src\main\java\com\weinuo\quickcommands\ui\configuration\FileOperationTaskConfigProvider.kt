package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.screens.LocalNavController
import com.weinuo.quickcommands.utils.FilePickerUtil

/**
 * 文件操作任务配置数据提供器
 *
 * 提供文件操作任务的配置项列表，支持文件写入、文件打开、文件管理等操作。
 * 使用通用组件复用架构，每个文件操作类型都有独立的配置内容组件。
 *
 * <AUTHOR>
 * @since 1.0.0
 */
object FileOperationTaskConfigProvider {

    /**
     * 获取文件操作任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 文件操作任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<FileOperation>> {
        return listOf(
            // 写入文件
            ConfigurationCardItem(
                id = "write_file",
                title = context.getString(R.string.file_write_file),
                description = context.getString(R.string.file_write_file_description),
                operationType = FileOperation.WRITE_FILE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    WriteFileConfigContent(operation, onComplete)
                }
            ),

            // 打开文件
            ConfigurationCardItem(
                id = "open_file",
                title = context.getString(R.string.file_open_file),
                description = context.getString(R.string.file_open_file_description),
                operationType = FileOperation.OPEN_FILE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    OpenFileConfigContent(operation, onComplete)
                }
            ),

            // 文件操作 - 实验性功能
            ConfigurationCardItem(
                id = "file_operation",
                title = context.getString(R.string.file_file_operation),
                description = context.getString(R.string.file_file_operation_description),
                operationType = FileOperation.FILE_OPERATION,
                permissionRequired = true,
                isExperimental = true,
                content = { operation, onComplete ->
                    FileManagementConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 写入文件配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun WriteFileConfigContent(
    operation: FileOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    var folderPath by rememberSaveable { mutableStateOf("") }
    var fileName by rememberSaveable { mutableStateOf("") }
    var fileContent by rememberSaveable { mutableStateOf("") }
    var writeMode by rememberSaveable { mutableStateOf(FileWriteMode.APPEND) }

    // 文件选择器启动器
    val directoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            folderPath = realPath ?: it.toString()
        }
    }

    val fileLauncher = FilePickerUtil.rememberFilePickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getRealPath(context, it)
            fileName = realPath ?: it.toString()
        }
    }


    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 文件夹选择按钮
        Button(
            onClick = {
                FilePickerUtil.launchDirectoryPicker(directoryLauncher)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(if (folderPath.isEmpty()) "选择文件夹" else "已选择: $folderPath")
        }

        // 文件名输入
        OutlinedTextField(
            value = fileName,
            onValueChange = { fileName = it },
            label = { Text("文件名") },
            placeholder = { Text("例如: example.txt") },
            modifier = Modifier.fillMaxWidth()
        )

        // 或选择文件按钮
        Button(
            onClick = {
                FilePickerUtil.launchFilePicker(fileLauncher, arrayOf("*/*"))
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("或选择文件")
        }

        // 文件内容输入
        OutlinedTextField(
            value = fileContent,
            onValueChange = { fileContent = it },
            label = { Text("文件内容") },
            placeholder = { Text("要写入的文件内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 6
        )

        // 写入模式选择
        Text(
            text = "写入模式",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Column(modifier = Modifier.selectableGroup()) {
            FileWriteMode.values().forEach { mode ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { writeMode = mode }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = writeMode == mode,
                        onClick = { writeMode = mode }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = mode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = FileOperationTask(
                    operation = operation,
                    folderPath = folderPath,
                    fileName = fileName,
                    fileContent = fileContent,
                    writeMode = writeMode
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = folderPath.isNotBlank() && fileName.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 打开文件配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun OpenFileConfigContent(
    operation: FileOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    var folderPath by rememberSaveable { mutableStateOf("") }
    var fileName by rememberSaveable { mutableStateOf("") }
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedAppPackageName = app.packageName
                selectedAppName = app.appName
                selectedAppIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    // 文件选择器启动器
    val directoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            folderPath = realPath ?: it.toString()
        }
    }

    val fileLauncher = FilePickerUtil.rememberFilePickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getRealPath(context, it)
            fileName = realPath ?: it.toString()
        }
    }


    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 文件夹选择按钮
        Button(
            onClick = {
                FilePickerUtil.launchDirectoryPicker(directoryLauncher)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(if (folderPath.isEmpty()) "选择文件夹" else "已选择: $folderPath")
        }

        // 文件名输入
        OutlinedTextField(
            value = fileName,
            onValueChange = { fileName = it },
            label = { Text("文件名") },
            placeholder = { Text("例如: example.txt") },
            modifier = Modifier.fillMaxWidth()
        )

        // 文件选择按钮
        Button(
            onClick = {
                FilePickerUtil.launchFilePicker(fileLauncher, arrayOf("*/*"))
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("或选择文件")
        }

        // 应用选择（可选）
        Text(
            text = "指定应用（可选）",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedApp != null) {
                    "已选择: ${selectedApp.appName}"
                } else {
                    "点击选择应用（可选）"
                }
            )
        }

        if (selectedApp != null) {
            Text(
                text = "包名: ${selectedApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = FileOperationTask(
                    operation = operation,
                    folderPath = folderPath,
                    fileName = fileName,
                    appName = selectedApp?.packageName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = folderPath.isNotBlank() && fileName.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 文件管理配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun FileManagementConfigContent(
    operation: FileOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    var fileOperationType by rememberSaveable { mutableStateOf(FileOperationType.COPY) }
    var sourceSelectionMode by rememberSaveable { mutableStateOf(FileSelectionMode.ALL_FILES) }
    var targetSelectionMode by rememberSaveable { mutableStateOf(FileSelectionMode.ALL_FILES) }
    var sourceFilePattern by rememberSaveable { mutableStateOf("") }
    var targetFilePattern by rememberSaveable { mutableStateOf("") }
    var compressionLocation by rememberSaveable { mutableStateOf(CompressionLocation.CURRENT_LOCATION) }
    var customCompressionPath by rememberSaveable { mutableStateOf("") }
    var deleteOriginalAfterCompression by rememberSaveable { mutableStateOf(false) }

    // 新建文件夹专用状态
    var newFolderName by rememberSaveable { mutableStateOf("") }
    var skipIfFolderExists by rememberSaveable { mutableStateOf(false) }

    // 压缩配置专用状态
    var compressionFormat by rememberSaveable { mutableStateOf(CompressionFormat.ZIP) }
    var compressionLevel by rememberSaveable { mutableStateOf(CompressionLevel.NORMAL) }

    // 文件选择器相关状态
    var sourcePath by rememberSaveable { mutableStateOf("") }
    var targetPath by rememberSaveable { mutableStateOf("") }

    // 重命名操作专用状态
    var folderPath by rememberSaveable { mutableStateOf("") }
    var fileName by rememberSaveable { mutableStateOf("") }
    var newFileName by rememberSaveable { mutableStateOf("") }
    var newFileExtension by rememberSaveable { mutableStateOf("") }
    var allowOverwrite by rememberSaveable { mutableStateOf(false) }

    // 文件选择器启动器
    val sourceDirectoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            sourcePath = realPath ?: it.toString()
        }
    }

    val targetDirectoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            targetPath = realPath ?: it.toString()
        }
    }

    // 压缩目录选择器启动器
    val compressionDirectoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            customCompressionPath = realPath ?: it.toString()
        }
    }

    // 重命名操作的文件选择器启动器
    val renameDirectoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            folderPath = realPath ?: it.toString()
        }
    }

    val renameFileLauncher = FilePickerUtil.rememberFilePickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getRealPath(context, it)
            val fullPath = realPath ?: it.toString()
            fileName = fullPath
            // 自动解析文件名和扩展名
            val file = java.io.File(fullPath)
            val nameWithoutExtension = file.nameWithoutExtension
            val extension = file.extension
            newFileName = nameWithoutExtension
            newFileExtension = extension
        }
    }



    Column(
        modifier = Modifier
            .fillMaxWidth() // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 文件操作类型选择
        Text(
            text = "操作类型",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Column(modifier = Modifier.selectableGroup()) {
            FileOperationType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { fileOperationType = type }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = fileOperationType == type,
                        onClick = { fileOperationType = type }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = type.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 重命名操作的特殊配置
        if (fileOperationType == FileOperationType.RENAME) {
            // 文件夹选择
            Button(
                onClick = {
                    FilePickerUtil.launchDirectoryPicker(renameDirectoryLauncher)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(if (folderPath.isEmpty()) "选择文件夹" else "已选择: $folderPath")
            }

            // 文件名输入
            OutlinedTextField(
                value = fileName,
                onValueChange = {
                    fileName = it
                    // 当手动输入时，自动解析文件名和扩展名
                    if (it.isNotEmpty()) {
                        val file = java.io.File(it)
                        newFileName = file.nameWithoutExtension
                        newFileExtension = file.extension
                    }
                },
                label = { Text("源文件名") },
                placeholder = { Text("例如: example.txt") },
                modifier = Modifier.fillMaxWidth()
            )

            // 文件选择按钮
            Button(
                onClick = {
                    FilePickerUtil.launchFilePicker(renameFileLauncher, arrayOf("*/*"))
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("或选择文件")
            }

            // 当前文件信息显示
            if (fileName.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "当前文件信息",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "文件名: ${java.io.File(fileName).nameWithoutExtension}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "扩展名: ${java.io.File(fileName).extension}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 新文件名输入
            OutlinedTextField(
                value = newFileName,
                onValueChange = { newFileName = it },
                label = { Text("新文件名") },
                placeholder = { Text("不包含扩展名") },
                modifier = Modifier.fillMaxWidth()
            )

            // 新扩展名输入
            OutlinedTextField(
                value = newFileExtension,
                onValueChange = { newFileExtension = it },
                label = { Text("新扩展名（可选）") },
                placeholder = { Text("例如: txt") },
                modifier = Modifier.fillMaxWidth()
            )

            // 完整新文件名预览
            if (newFileName.isNotEmpty()) {
                val previewName = if (newFileExtension.isNotEmpty()) {
                    "$newFileName.$newFileExtension"
                } else {
                    newFileName
                }
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "预览新文件名",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        Text(
                            text = previewName,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }

            // 覆盖确认选项
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = allowOverwrite,
                    onCheckedChange = { allowOverwrite = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("如果目标文件已存在则覆盖")
            }
        }

        // 源文件选择模式（复制、移动、删除、压缩时显示）
        else if (fileOperationType in listOf(FileOperationType.COPY, FileOperationType.MOVE, FileOperationType.DELETE, FileOperationType.COMPRESS)) {
            Text(
                text = "源文件选择",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Column(modifier = Modifier.selectableGroup()) {
                FileSelectionMode.values().forEach { mode ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null // 移除涟漪效果
                            ) { sourceSelectionMode = mode }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = sourceSelectionMode == mode,
                            onClick = { sourceSelectionMode = mode }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = mode.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 源目录选择按钮
            Button(
                onClick = {
                    FilePickerUtil.launchDirectoryPicker(sourceDirectoryLauncher)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(if (sourcePath.isEmpty()) "选择源目录" else "源目录: $sourcePath")
            }
        }

        // 目标文件选择模式（复制、移动、压缩时显示）
        if (fileOperationType in listOf(FileOperationType.COPY, FileOperationType.MOVE, FileOperationType.COMPRESS)) {
            Text(
                text = when (fileOperationType) {
                    FileOperationType.COMPRESS -> "压缩输出位置"
                    else -> "目标位置选择"
                },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Column(modifier = Modifier.selectableGroup()) {
                FileSelectionMode.values().forEach { mode ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null // 移除涟漪效果
                            ) { targetSelectionMode = mode }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = targetSelectionMode == mode,
                            onClick = { targetSelectionMode = mode }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = when (mode) {
                                FileSelectionMode.ALL_FILES -> "外部存储根目录"
                                FileSelectionMode.ALL_MEDIA -> "图片目录"
                                FileSelectionMode.IMAGES -> "图片目录"
                                FileSelectionMode.VIDEOS -> "视频目录"
                                FileSelectionMode.AUDIO -> "音乐目录"
                                FileSelectionMode.SPECIFIC_PATTERN -> "自定义目录"
                                FileSelectionMode.FOLDERS -> "外部存储根目录"
                            },
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 自定义目标目录选择（当选择SPECIFIC_PATTERN时显示）
            if (targetSelectionMode == FileSelectionMode.SPECIFIC_PATTERN) {
                Button(
                    onClick = {
                        FilePickerUtil.launchDirectoryPicker(targetDirectoryLauncher)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(if (targetPath.isEmpty()) {
                        when (fileOperationType) {
                            FileOperationType.COMPRESS -> "选择自定义压缩目录"
                            else -> "选择自定义目标目录"
                        }
                    } else {
                        when (fileOperationType) {
                            FileOperationType.COMPRESS -> "压缩目录: $targetPath"
                            else -> "目标目录: $targetPath"
                        }
                    })
                }
            }
        }

        // 源文件指定模式输入（当源文件选择SPECIFIC_PATTERN时显示）
        if (sourceSelectionMode == FileSelectionMode.SPECIFIC_PATTERN) {
            OutlinedTextField(
                value = sourceFilePattern,
                onValueChange = { sourceFilePattern = it },
                label = { Text("源文件模式") },
                placeholder = { Text("例如: *.txt, *.jpg") },
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 目标文件指定模式输入（当目标文件选择SPECIFIC_PATTERN时显示）
        // 适用于：复制、移动、压缩操作
        if (targetSelectionMode == FileSelectionMode.SPECIFIC_PATTERN &&
            fileOperationType in listOf(FileOperationType.COPY, FileOperationType.MOVE, FileOperationType.COMPRESS)) {
            OutlinedTextField(
                value = targetFilePattern,
                onValueChange = { targetFilePattern = it },
                label = { Text(
                    when (fileOperationType) {
                        FileOperationType.COPY -> "目标文件模式"
                        FileOperationType.MOVE -> "目标文件模式"
                        FileOperationType.COMPRESS -> "压缩文件命名模式"
                        else -> "目标文件模式"
                    }
                ) },
                placeholder = { Text(
                    when (fileOperationType) {
                        FileOperationType.COMPRESS -> "例如: backup_*.zip"
                        else -> "例如: *.txt, *.jpg"
                    }
                ) },
                modifier = Modifier.fillMaxWidth()
            )
        }



        // 新建文件夹配置（新建文件夹操作时显示）
        if (fileOperationType == FileOperationType.CREATE_FOLDER) {
            Text(
                text = "目标目录",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // 目标目录选择按钮
            Button(
                onClick = {
                    FilePickerUtil.launchDirectoryPicker(targetDirectoryLauncher)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(if (targetPath.isEmpty()) "选择目标目录" else "已选择: $targetPath")
            }

            // 文件夹名称输入
            OutlinedTextField(
                value = newFolderName,
                onValueChange = { newFolderName = it },
                label = { Text("文件夹名称") },
                placeholder = { Text("例如: MyFolder") },
                modifier = Modifier.fillMaxWidth()
            )

            // 文件夹存在处理选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .toggleable(
                        value = skipIfFolderExists,
                        onValueChange = { skipIfFolderExists = it },
                        role = Role.Checkbox
                    )
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = skipIfFolderExists,
                    onCheckedChange = { skipIfFolderExists = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "如果文件夹已存在则跳过",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 压缩配置（压缩操作时显示）
        if (fileOperationType == FileOperationType.COMPRESS) {
            Text(
                text = "压缩位置",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Column(modifier = Modifier.selectableGroup()) {
                CompressionLocation.values().forEach { location ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null // 移除涟漪效果
                            ) { compressionLocation = location }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = compressionLocation == location,
                            onClick = { compressionLocation = location }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = location.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 自定义压缩路径（当选择CUSTOM_PATH时显示）
            if (compressionLocation == CompressionLocation.CUSTOM_PATH) {
                Button(
                    onClick = {
                        FilePickerUtil.launchDirectoryPicker(compressionDirectoryLauncher)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(if (customCompressionPath.isEmpty()) "选择压缩目录" else "已选择: $customCompressionPath")
                }
            }

            // 压缩格式选择
            Text(
                text = "压缩格式",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Column(modifier = Modifier.selectableGroup()) {
                CompressionFormat.values().forEach { format ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null // 移除涟漪效果
                            ) { compressionFormat = format }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = compressionFormat == format,
                            onClick = { compressionFormat = format }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = format.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 压缩级别选择
            Text(
                text = "压缩级别",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Column(modifier = Modifier.selectableGroup()) {
                CompressionLevel.values().forEach { level ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null // 移除涟漪效果
                            ) { compressionLevel = level }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = compressionLevel == level,
                            onClick = { compressionLevel = level }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = level.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 压缩后删除原文件选项
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = deleteOriginalAfterCompression,
                    onCheckedChange = { deleteOriginalAfterCompression = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("压缩后删除原文件")
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = if (fileOperationType == FileOperationType.RENAME) {
                    FileOperationTask(
                        operation = operation,
                        fileOperationType = fileOperationType,
                        folderPath = folderPath,
                        fileName = fileName,
                        newFileName = newFileName,
                        newFileExtension = newFileExtension,
                        allowOverwrite = allowOverwrite
                    )
                } else {
                    // 将源文件模式和目标文件模式合并为一个字符串，使用"|"分隔符
                    val combinedFilePattern = when {
                        sourceFilePattern.isNotEmpty() && targetFilePattern.isNotEmpty() ->
                            "$sourceFilePattern|$targetFilePattern"
                        sourceFilePattern.isNotEmpty() -> sourceFilePattern
                        targetFilePattern.isNotEmpty() -> targetFilePattern
                        else -> ""
                    }

                    FileOperationTask(
                        operation = operation,
                        fileOperationType = fileOperationType,
                        sourceSelectionMode = sourceSelectionMode,
                        targetSelectionMode = targetSelectionMode,
                        specificFilePattern = combinedFilePattern,
                        sourcePath = sourcePath,
                        targetPath = targetPath,
                        compressionLocation = compressionLocation,
                        customCompressionPath = customCompressionPath,
                        deleteOriginalAfterCompression = deleteOriginalAfterCompression,
                        newFolderName = newFolderName,
                        skipIfFolderExists = skipIfFolderExists,
                        compressionFormat = compressionFormat,
                        compressionLevel = compressionLevel
                    )
                }
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (fileOperationType) {
                FileOperationType.RENAME -> folderPath.isNotEmpty() && fileName.isNotEmpty() && newFileName.isNotEmpty()
                FileOperationType.COPY, FileOperationType.MOVE, FileOperationType.COMPRESS -> {
                    // 源目录必须选择，目标可以是预设目录或自定义目录
                    sourcePath.isNotEmpty() && (
                        targetSelectionMode != FileSelectionMode.SPECIFIC_PATTERN || targetPath.isNotEmpty()
                    )
                }
                FileOperationType.DELETE -> sourcePath.isNotEmpty()
                FileOperationType.CREATE_FOLDER -> targetPath.isNotEmpty() && newFolderName.isNotEmpty()
            }
        ) {
            Text("确认配置")
        }
    }
}

package com.weinuo.quickcommands.ui.theme.examples

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.config.*
import com.weinuo.quickcommands.ui.theme.provider.*
import com.weinuo.quickcommands.ui.theme.system.AppTheme

/**
 * 主题系统使用示例
 *
 * 展示如何使用新的主题系统创建主题感知的UI组件
 * 这个示例可以用于测试和验证主题系统的功能
 */
@Composable
fun ThemeSystemExample() {
    AppThemeProvider {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 主题切换器
            ThemeSwitcherExample()
            
            // 注意：TopAppBar组件已删除，各主题直接使用专用组件
            // ThemedTopAppBarExample() // 已删除
            
            // 主题感知的卡片示例
            ThemedCardExample()
            
            // 主题感知的按钮示例
            ThemedButtonExample()
            
            // 主题感知的文本输入框示例
            ThemedTextFieldExample()
            
            // 主题感知的底部导航栏示例
            ThemedBottomNavigationExample()
        }
    }
}

/**
 * 主题切换器示例
 */
@Composable
private fun ThemeSwitcherExample() {
    val currentTheme = currentTheme()
    val themeSwitcher = rememberThemeSwitcher()
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "当前主题: ${currentTheme.displayName}",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                AppTheme.values().forEach { theme ->
                    FilterChip(
                        onClick = { themeSwitcher(theme) },
                        label = { Text(theme.displayName) },
                        selected = currentTheme == theme
                    )
                }
            }
        }
    }
}

/**
 * 主题感知的顶部应用栏示例
 *
 * 注意：TopAppBar组件已删除，各主题直接使用专用组件
 * - 天空蓝主题：IntegratedTopAppBar
 * - 海洋蓝主题：LayeredTopAppBar
 */
// 此示例已删除，因为TopAppBar组件已从主题感知系统中移除

/**
 * 主题感知的卡片示例
 */
@Composable
private fun ThemedCardExample() {
    ThemedComponentCreator.Card(
        config = CardConfig(
            content = {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "主题感知卡片",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "这个卡片会根据当前选择的主题自动调整样式，包括阴影、圆角、颜色等。",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        )
    )
}

/**
 * 主题感知的按钮示例
 */
@Composable
private fun ThemedButtonExample() {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        ThemedComponentCreator.Button(
            config = ButtonConfig(
                onClick = { /* 处理点击 */ },
                text = "主要按钮"
            )
        )
        
        ThemedComponentCreator.Button(
            config = ButtonConfig(
                onClick = { /* 处理点击 */ },
                enabled = false,
                text = "禁用按钮"
            )
        )
    }
}

/**
 * 主题感知的文本输入框示例
 */
@Composable
private fun ThemedTextFieldExample() {
    var textValue by remember { mutableStateOf("") }
    
    ThemedComponentCreator.TextField(
        config = TextFieldConfig(
            value = textValue,
            onValueChange = { textValue = it },
            label = "主题感知输入框",
            placeholder = "请输入文本...",
            leadingIcon = Icons.Default.Search
        )
    )
}

/**
 * 主题感知的底部导航栏示例
 */
@Composable
private fun ThemedBottomNavigationExample() {
    var selectedIndex by remember { mutableStateOf(0) }
    
    val tabs = listOf(
        NavigationTab(
            label = "首页",
            icon = Icons.Default.Home,
            selectedIcon = Icons.Filled.Home
        ),
        NavigationTab(
            label = "搜索",
            icon = Icons.Default.Search,
            selectedIcon = Icons.Filled.Search
        ),
        NavigationTab(
            label = "收藏",
            icon = Icons.Default.Favorite,
            selectedIcon = Icons.Filled.Favorite
        ),
        NavigationTab(
            label = "设置",
            icon = Icons.Default.Settings,
            selectedIcon = Icons.Filled.Settings
        )
    )
    
    ThemedComponentCreator.BottomNavigation(
        config = BottomNavigationConfig(
            tabs = tabs,
            selectedIndex = selectedIndex,
            onTabSelected = { selectedIndex = it }
        )
    )
}

/**
 * 主题特性检查示例
 */
@Composable
private fun ThemeFeatureExample() {
    val supportsBlur = isThemeFeatureSupported(com.weinuo.quickcommands.ui.theme.system.ThemeFeature.BLUR_EFFECTS)
    val supportsShadow = isThemeFeatureSupported(com.weinuo.quickcommands.ui.theme.system.ThemeFeature.SHADOW_EFFECTS)
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "主题特性支持",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text("模糊效果: ${if (supportsBlur) "支持" else "不支持"}")
            Text("阴影效果: ${if (supportsShadow) "支持" else "不支持"}")
        }
    }
}

/**
 * 样式配置获取示例
 */
@Composable
private fun StyleConfigurationExample() {
    val cornerRadius = ThemedStyleProvider.cornerRadius()
    val elevation = ThemedStyleProvider.elevation()
    val spacing = ThemedStyleProvider.spacing()
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "当前主题样式配置",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text("小圆角: ${cornerRadius.small}")
            Text("中等圆角: ${cornerRadius.medium}")
            Text("大圆角: ${cornerRadius.large}")
            Text("低阴影: ${elevation.low}")
            Text("中等阴影: ${elevation.medium}")
            Text("高阴影: ${elevation.high}")
            Text("小间距: ${spacing.small}")
            Text("中等间距: ${spacing.medium}")
            Text("大间距: ${spacing.large}")
        }
    }
}

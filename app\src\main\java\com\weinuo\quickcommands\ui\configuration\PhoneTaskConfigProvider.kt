package com.weinuo.quickcommands.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.ui.activities.ContactSelectionActivity
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.components.RingtoneConfigurationContent
import com.weinuo.quickcommands.utils.ContactsHelper
import com.weinuo.quickcommands.utils.RingtoneHelper


/**
 * 电话任务配置数据提供器
 *
 * 提供电话任务特定的配置项列表，支持6种电话操作类型，
 * 使用新的模块化配置系统架构，复用通用组件。
 */
object PhoneTaskConfigProvider {

    /**
     * 获取电话任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 电话任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<PhoneOperation>> {
        return listOf(
            // 打开通话记录
            ConfigurationCardItem(
                id = "open_call_log",
                title = context.getString(R.string.phone_open_call_log),
                description = context.getString(R.string.phone_open_call_log_description),
                operationType = PhoneOperation.OPEN_CALL_LOG,
                permissionRequired = true,
                content = { operation, onComplete ->
                    OpenCallLogConfigContent(operation, onComplete)
                }
            ),

            // 拒接电话
            ConfigurationCardItem(
                id = "reject_call",
                title = context.getString(R.string.phone_reject_call),
                description = context.getString(R.string.phone_reject_call_description),
                operationType = PhoneOperation.REJECT_CALL,
                permissionRequired = true, // 需要通信权限
                content = { operation, onComplete ->
                    RejectCallConfigContent(operation, onComplete)
                }
            ),

            // 拨打电话
            ConfigurationCardItem(
                id = "make_call",
                title = context.getString(R.string.phone_make_call),
                description = context.getString(R.string.phone_make_call_description),
                operationType = PhoneOperation.MAKE_CALL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    MakeCallConfigContent(operation, onComplete)
                }
            ),

            // 接听电话
            ConfigurationCardItem(
                id = "answer_call",
                title = context.getString(R.string.phone_answer_call),
                description = context.getString(R.string.phone_answer_call_description),
                operationType = PhoneOperation.ANSWER_CALL,
                permissionRequired = true, // 需要通信权限
                content = { operation, onComplete ->
                    AnswerCallConfigContent(operation, onComplete)
                }
            ),

            // 清除通话记录
            ConfigurationCardItem(
                id = "clear_call_log",
                title = context.getString(R.string.phone_clear_call_log),
                description = context.getString(R.string.phone_clear_call_log_description),
                operationType = PhoneOperation.CLEAR_CALL_LOG,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ClearCallLogConfigContent(operation, onComplete)
                }
            ),

            // 电话铃声设置
            ConfigurationCardItem(
                id = "ringtone_settings",
                title = context.getString(R.string.phone_ringtone_settings),
                description = context.getString(R.string.phone_ringtone_settings_description),
                operationType = PhoneOperation.RINGTONE_SETTINGS,
                permissionRequired = false, // 铃声设置不需要特殊权限
                content = { operation, onComplete ->
                    RingtoneSettingsConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 打开通话记录配置内容
 */
@Composable
private fun OpenCallLogConfigContent(
    operation: PhoneOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置打开通话记录",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "将打开系统通话记录页面，无需额外配置。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Button(
            onClick = {
                val task = PhoneTask(
                    operation = PhoneOperation.OPEN_CALL_LOG
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 拒接电话配置内容
 */
@Composable
private fun RejectCallConfigContent(
    operation: PhoneOperation,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置拒接电话",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "将拒接当前来电，无需额外配置。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Button(
            onClick = {
                val task = PhoneTask(
                    operation = PhoneOperation.REJECT_CALL
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 拨打电话配置内容
 */
@Composable
private fun MakeCallConfigContent(
    operation: PhoneOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    val context = LocalContext.current

    // 使用rememberSaveable保持导航过程中的状态
    var makeCallType by rememberSaveable { mutableStateOf(MakeCallType.MANUAL_INPUT) }
    var phoneNumber by rememberSaveable { mutableStateOf("") }
    var contactName by rememberSaveable { mutableStateOf("") }
    var selectedContactIds by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }
    var simCardSelection by rememberSaveable { mutableStateOf(SimCardSelection.ASK_EACH_TIME) }

    // 联系人选择ActivityResultLauncher
    val contactSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val contactData = result.data?.getSerializableExtra(
                ContactSelectionActivity.RESULT_SELECTED_CONTACTS
            ) as? ArrayList<Map<String, String>> ?: arrayListOf()

            if (contactData.isNotEmpty()) {
                // 自动切换到选择联系人模式
                makeCallType = MakeCallType.CONTACT
                // 更新选中的联系人
                selectedContactIds = contactData.map { it["id"] ?: "" }
                contactName = if (contactData.size == 1) {
                    contactData.first()["name"] ?: ""
                } else {
                    "已选择${contactData.size}个联系人"
                }
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置拨打电话",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 拨打方式选择
        Text(
            text = "拨打方式",
            style = MaterialTheme.typography.bodyLarge
        )

        Column(modifier = Modifier.selectableGroup()) {
            MakeCallType.values().forEach { type ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = makeCallType == type,
                            onClick = { makeCallType = type },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp)
                ) {
                    RadioButton(
                        selected = makeCallType == type,
                        onClick = { makeCallType = type }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (type) {
                            MakeCallType.MANUAL_INPUT -> "手动输入号码"
                            MakeCallType.CONTACT -> "选择联系人"
                            MakeCallType.RECENT_CALL -> "最近拨打的号码"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 根据拨打方式显示相应的输入框
        when (makeCallType) {
            MakeCallType.MANUAL_INPUT -> {
                OutlinedTextField(
                    value = phoneNumber,
                    onValueChange = { phoneNumber = it },
                    label = { Text("电话号码") },
                    placeholder = { Text("请输入电话号码") },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            MakeCallType.CONTACT -> {
                // 联系人选择按钮
                OutlinedButton(
                    onClick = {
                        ContactSelectionActivity.startForMultiSelection(context, selectedContactIds)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        if (selectedContactIds.isNotEmpty()) {
                            "已选择 ${selectedContactIds.size} 个联系人"
                        } else {
                            "点击选择联系人"
                        }
                    )
                }

                // 显示选中的联系人名称
                if (contactName.isNotEmpty()) {
                    Text(
                        text = contactName,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
            MakeCallType.RECENT_CALL -> {
                Text(
                    text = "将拨打最近拨打的号码",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // SIM卡选择
        Text(
            text = "SIM卡选择",
            style = MaterialTheme.typography.bodyLarge
        )

        Column(modifier = Modifier.selectableGroup()) {
            SimCardSelection.values().forEach { simCard ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = simCardSelection == simCard,
                            onClick = { simCardSelection = simCard },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = simCardSelection == simCard,
                        onClick = { simCardSelection = simCard }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (simCard) {
                            SimCardSelection.SIM1 -> "SIM卡1"
                            SimCardSelection.SIM2 -> "SIM卡2"
                            SimCardSelection.ASK_EACH_TIME -> "每次询问"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        Button(
            onClick = {
                val task = PhoneTask(
                    operation = PhoneOperation.MAKE_CALL,
                    makeCallType = makeCallType,
                    phoneNumber = phoneNumber,
                    contactName = contactName,
                    contactIds = selectedContactIds,
                    simCardSelection = simCardSelection
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (makeCallType) {
                MakeCallType.MANUAL_INPUT -> phoneNumber.isNotBlank()
                MakeCallType.CONTACT -> selectedContactIds.isNotEmpty()
                MakeCallType.RECENT_CALL -> true
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 接听电话配置内容
 */
@Composable
private fun AnswerCallConfigContent(
    operation: PhoneOperation,
    onComplete: (Any) -> Unit
) {
    var answerDelayType by rememberSaveable { mutableStateOf(AnswerCallDelayType.NO_DELAY) }
    var answerDelaySeconds by rememberSaveable { mutableStateOf(0) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置接听电话",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 延迟类型选择
        Text(
            text = "接听延迟类型",
            style = MaterialTheme.typography.bodyLarge
        )

        AnswerCallDelayType.values().forEach { type ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = answerDelayType == type,
                        onClick = { answerDelayType = type }
                    )
            ) {
                RadioButton(
                    selected = answerDelayType == type,
                    onClick = { answerDelayType = type }
                )
                Text(
                    text = when (type) {
                        AnswerCallDelayType.NO_DELAY -> "无延迟"
                        AnswerCallDelayType.CUSTOM_DELAY -> "自定义延迟"
                    },
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 自定义延迟输入
        if (answerDelayType == AnswerCallDelayType.CUSTOM_DELAY) {
            OutlinedTextField(
                value = answerDelaySeconds.toString(),
                onValueChange = { value ->
                    answerDelaySeconds = value.toIntOrNull()?.coerceIn(0, 60) ?: 0
                },
                label = { Text("延迟秒数") },
                placeholder = { Text("请输入延迟秒数 (0-60)") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = { Text("建议范围：0-60秒") }
            )
        }

        Button(
            onClick = {
                val task = PhoneTask(
                    operation = PhoneOperation.ANSWER_CALL,
                    answerDelayType = answerDelayType,
                    answerDelaySeconds = answerDelaySeconds
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 清除通话记录配置内容
 */
@Composable
private fun ClearCallLogConfigContent(
    operation: PhoneOperation,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    var clearLogType by rememberSaveable { mutableStateOf(ClearCallLogType.ALL) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置清除通话记录",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 清除类型选择
        Text(
            text = "清除类型",
            style = MaterialTheme.typography.bodyLarge
        )

        ClearCallLogType.values().forEach { type ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = clearLogType == type,
                        onClick = { clearLogType = type }
                    )
            ) {
                RadioButton(
                    selected = clearLogType == type,
                    onClick = { clearLogType = type }
                )
                Text(
                    text = when (type) {
                        ClearCallLogType.ALL -> "全部记录"
                        ClearCallLogType.INCOMING -> "呼入记录"
                        ClearCallLogType.OUTGOING -> "呼出记录"
                        ClearCallLogType.MISSED -> "未接记录"
                        ClearCallLogType.VOICEMAIL -> "语音信箱记录"
                        ClearCallLogType.REJECTED -> "已拒接记录"
                        ClearCallLogType.BLOCKED -> "已屏蔽记录"
                    },
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Button(
            onClick = {
                val task = PhoneTask(
                    operation = PhoneOperation.CLEAR_CALL_LOG,
                    clearLogType = clearLogType
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 电话铃声配置内容
 * 专门用于电话任务的铃声配置，固定为电话铃声类型
 */
@Composable
private fun RingtoneSettingsConfigContent(
    operation: PhoneOperation,
    onComplete: (Any) -> Unit
) {
    // 使用通用铃声配置组件，固定为电话铃声类型
    RingtoneConfigurationContent(
        title = "配置电话铃声",
        description = "选择电话来电时的铃声",
        ringtoneType = RingtoneHelper.RingtoneType.RINGTONE,
        showTypeSelection = false, // 不显示类型选择，固定为电话铃声
        onComplete = { selectedRingtoneUri, selectedRingtoneName ->
            val task = PhoneTask(
                operation = PhoneOperation.RINGTONE_SETTINGS,
                selectedRingtoneUri = selectedRingtoneUri,
                selectedRingtoneName = selectedRingtoneName
            )
            onComplete(task)
        }
    )
}

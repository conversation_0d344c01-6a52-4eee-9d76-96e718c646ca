package com.weinuo.quickcommands.storage

import android.content.Context
import android.content.SharedPreferences
import com.weinuo.quickcommands.model.SmartReminderConfig
import com.weinuo.quickcommands.model.SmartReminderType
import java.util.UUID

/**
 * 智慧提醒配置存储适配器
 *
 * 负责智慧提醒配置的持久化存储和读取，使用原生数据类型存储，
 * 避免JSON序列化的复杂性和潜在问题。
 *
 * 存储策略：
 * - 每个配置字段单独存储，便于查询和更新
 * - 使用类型安全的键名规范
 * - 支持批量操作和单个配置操作
 * - 提供配置变更监听机制
 *
 * 设计原则：
 * - 原生存储：使用SharedPreferences的原生数据类型
 * - 高性能：支持增量更新，避免全量读写
 * - 可扩展：便于添加新的配置字段
 * - 线程安全：所有操作都是线程安全的
 */
class SmartReminderConfigAdapter(context: Context) {

    companion object {
        private const val PREFS_NAME = "smart_reminder_configs"
        private const val KEY_PREFIX = "reminder_"

        // 配置字段键名后缀
        private const val SUFFIX_ENABLED = "_enabled"
        private const val SUFFIX_CONFIGURED = "_configured"
        private const val SUFFIX_CREATED_TIME = "_created_time"
        private const val SUFFIX_MODIFIED_TIME = "_modified_time"

        // 屏幕旋转提醒特定配置字段
        private const val SUFFIX_SENSITIVITY = "_sensitivity"
        private const val SUFFIX_COOLDOWN_TIME = "_cooldown_time"
        private const val SUFFIX_DELAY_TIME = "_delay_time"
        private const val SUFFIX_AUTO_DISMISS_ENABLED = "_auto_dismiss_enabled"
        private const val SUFFIX_AUTO_DISMISS_SECONDS = "_auto_dismiss_seconds"

        // 圆形按钮显示配置字段
        private const val SUFFIX_BUTTON_POSITION = "_button_position"
        private const val SUFFIX_BUTTON_SIZE = "_button_size"
        private const val SUFFIX_BUTTON_MARGIN_X = "_button_margin_x"
        private const val SUFFIX_BUTTON_MARGIN_Y = "_button_margin_y"
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * 保存智慧提醒配置
     */
    fun saveConfig(config: SmartReminderConfig) {
        val editor = sharedPreferences.edit()
        val keyPrefix = KEY_PREFIX + config.reminderTypeId

        editor.putBoolean(keyPrefix + SUFFIX_ENABLED, config.isEnabled)
        editor.putBoolean(keyPrefix + SUFFIX_CONFIGURED, config.isConfigured)
        editor.putLong(keyPrefix + SUFFIX_CREATED_TIME, config.createdTime)
        editor.putLong(keyPrefix + SUFFIX_MODIFIED_TIME, config.lastModifiedTime)

        editor.apply()
    }

    /**
     * 读取智慧提醒配置
     */
    fun loadConfig(reminderTypeId: String): SmartReminderConfig? {
        val keyPrefix = KEY_PREFIX + reminderTypeId

        // 检查配置是否存在
        if (!sharedPreferences.contains(keyPrefix + SUFFIX_ENABLED)) {
            return null
        }

        return SmartReminderConfig(
            reminderTypeId = reminderTypeId,
            isEnabled = sharedPreferences.getBoolean(keyPrefix + SUFFIX_ENABLED, false),
            isConfigured = sharedPreferences.getBoolean(keyPrefix + SUFFIX_CONFIGURED, false),
            createdTime = sharedPreferences.getLong(keyPrefix + SUFFIX_CREATED_TIME, System.currentTimeMillis()),
            lastModifiedTime = sharedPreferences.getLong(keyPrefix + SUFFIX_MODIFIED_TIME, System.currentTimeMillis())
        )
    }

    /**
     * 读取所有智慧提醒配置
     */
    fun loadAllConfigs(): Map<String, SmartReminderConfig> {
        val configs = mutableMapOf<String, SmartReminderConfig>()

        // 遍历所有智慧提醒类型
        SmartReminderType.getAllTypes().forEach { reminderType ->
            val config = loadConfig(reminderType.id)
            if (config != null) {
                configs[reminderType.id] = config
            } else {
                // 如果配置不存在，创建默认配置
                val defaultConfig = SmartReminderConfig.createDefault(reminderType)
                configs[reminderType.id] = defaultConfig
                saveConfig(defaultConfig) // 保存默认配置
            }
        }

        return configs
    }

    /**
     * 删除智慧提醒配置
     */
    fun deleteConfig(reminderTypeId: String) {
        val editor = sharedPreferences.edit()
        val keyPrefix = KEY_PREFIX + reminderTypeId

        editor.remove(keyPrefix + SUFFIX_ENABLED)
        editor.remove(keyPrefix + SUFFIX_CONFIGURED)
        editor.remove(keyPrefix + SUFFIX_CREATED_TIME)
        editor.remove(keyPrefix + SUFFIX_MODIFIED_TIME)

        // 删除屏幕旋转提醒特定配置
        editor.remove(keyPrefix + SUFFIX_SENSITIVITY)
        editor.remove(keyPrefix + SUFFIX_COOLDOWN_TIME)
        editor.remove(keyPrefix + SUFFIX_DELAY_TIME)
        editor.remove(keyPrefix + SUFFIX_AUTO_DISMISS_ENABLED)
        editor.remove(keyPrefix + SUFFIX_AUTO_DISMISS_SECONDS)

        // 删除圆形按钮显示配置
        editor.remove(keyPrefix + SUFFIX_BUTTON_POSITION)
        editor.remove(keyPrefix + SUFFIX_BUTTON_SIZE)
        editor.remove(keyPrefix + SUFFIX_BUTTON_MARGIN_X)
        editor.remove(keyPrefix + SUFFIX_BUTTON_MARGIN_Y)

        editor.apply()
    }

    /**
     * 更新启用状态
     */
    fun updateEnabledState(reminderTypeId: String, isEnabled: Boolean) {
        val config = loadConfig(reminderTypeId)
        if (config != null) {
            val updatedConfig = config.copyWithUpdate(isEnabled = isEnabled)
            saveConfig(updatedConfig)
        }
    }

    /**
     * 更新配置状态
     */
    fun updateConfiguredState(reminderTypeId: String, isConfigured: Boolean) {
        val config = loadConfig(reminderTypeId)
        if (config != null) {
            val updatedConfig = config.copyWithUpdate(isConfigured = isConfigured)
            saveConfig(updatedConfig)
        }
    }



    /**
     * 清除所有配置
     */
    fun clearAllConfigs() {
        sharedPreferences.edit().clear().apply()
    }

    // ========== 屏幕旋转提醒专门配置方法 ==========

    /**
     * 保存屏幕旋转提醒配置
     */
    fun saveScreenRotationConfig(
        reminderTypeId: String,
        sensitivity: Int,
        cooldownTime: Int,
        delayTime: Int,
        autoDismissEnabled: Boolean,
        autoDismissSeconds: Int,
        buttonPosition: String,
        buttonSize: Int,
        buttonMarginX: Int,
        buttonMarginY: Int
    ) {
        val editor = sharedPreferences.edit()
        val keyPrefix = KEY_PREFIX + reminderTypeId

        // 保存屏幕旋转特定配置
        editor.putInt(keyPrefix + SUFFIX_SENSITIVITY, sensitivity)
        editor.putInt(keyPrefix + SUFFIX_COOLDOWN_TIME, cooldownTime)
        editor.putInt(keyPrefix + SUFFIX_DELAY_TIME, delayTime)
        editor.putBoolean(keyPrefix + SUFFIX_AUTO_DISMISS_ENABLED, autoDismissEnabled)
        editor.putInt(keyPrefix + SUFFIX_AUTO_DISMISS_SECONDS, autoDismissSeconds)

        // 保存圆形按钮显示配置
        editor.putString(keyPrefix + SUFFIX_BUTTON_POSITION, buttonPosition)
        editor.putInt(keyPrefix + SUFFIX_BUTTON_SIZE, buttonSize)
        editor.putInt(keyPrefix + SUFFIX_BUTTON_MARGIN_X, buttonMarginX)
        editor.putInt(keyPrefix + SUFFIX_BUTTON_MARGIN_Y, buttonMarginY)

        // 标记为已配置
        editor.putBoolean(keyPrefix + SUFFIX_CONFIGURED, true)
        editor.putLong(keyPrefix + SUFFIX_MODIFIED_TIME, System.currentTimeMillis())

        editor.apply()
    }

    /**
     * 读取屏幕旋转提醒配置
     */
    fun loadScreenRotationConfig(reminderTypeId: String): ScreenRotationReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId

        return ScreenRotationReminderConfig(
            sensitivity = sharedPreferences.getInt(keyPrefix + SUFFIX_SENSITIVITY, 45),
            cooldownTime = sharedPreferences.getInt(keyPrefix + SUFFIX_COOLDOWN_TIME, 30),
            delayTime = sharedPreferences.getInt(keyPrefix + SUFFIX_DELAY_TIME, 2),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + SUFFIX_AUTO_DISMISS_ENABLED, false),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + SUFFIX_AUTO_DISMISS_SECONDS, 5),
            buttonPosition = sharedPreferences.getString(keyPrefix + SUFFIX_BUTTON_POSITION, "bottom_left") ?: "bottom_left",
            buttonSize = sharedPreferences.getInt(keyPrefix + SUFFIX_BUTTON_SIZE, 56),
            buttonMarginX = sharedPreferences.getInt(keyPrefix + SUFFIX_BUTTON_MARGIN_X, 24),
            buttonMarginY = sharedPreferences.getInt(keyPrefix + SUFFIX_BUTTON_MARGIN_Y, 24)
        )
    }

    /**
     * 屏幕旋转提醒配置数据类
     */
    data class ScreenRotationReminderConfig(
        val sensitivity: Int = 45,
        val cooldownTime: Int = 30,
        val delayTime: Int = 2,
        val autoDismissEnabled: Boolean = false,
        val autoDismissSeconds: Int = 5,
        val buttonPosition: String = "bottom_left", // bottom_left, bottom_right, top_left, top_right
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24 // 垂直边距(dp)
    )

    /**
     * 手电筒提醒配置数据类
     */
    data class FlashlightReminderConfig(
        val delayTime: Int = 10, // 延迟提醒时间（秒）
        val cooldownTime: Int = 60, // 冷却时间（秒）
        val autoDismissEnabled: Boolean = false, // 是否自动消失
        val autoDismissSeconds: Int = 5, // 自动消失时间（秒）
        val buttonPosition: String = "bottom_left", // 按钮位置
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24 // 垂直边距(dp)
    )

    /**
     * 新应用提醒配置数据类
     */
    data class NewAppReminderConfig(
        val delayTime: Int = 3, // 延迟提醒时间（秒）
        val cooldownTime: Int = 60, // 冷却时间（秒）
        val autoDismissEnabled: Boolean = true, // 是否自动消失
        val autoDismissSeconds: Int = 10, // 自动消失时间（秒）
        val buttonPosition: String = "bottom_right", // 按钮位置
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24, // 垂直边距(dp)
        val includeSystemApps: Boolean = false // 是否包含系统应用
    )

    /**
     * 音乐应用提醒配置数据类
     */
    data class MusicAppReminderConfig(
        val selectedMusicApps: List<SelectedMusicApp> = emptyList(), // 选中的音乐应用列表
        val delayTime: Int = 2, // 延迟提醒时间（秒）
        val cooldownTime: Int = 60, // 冷却时间（秒）
        val autoDismissEnabled: Boolean = true, // 是否自动消失
        val autoDismissSeconds: Int = 10, // 自动消失时间（秒）
        val buttonConfigs: List<MusicAppButtonConfig> = emptyList() // 按钮配置列表
    )

    /**
     * 选中的音乐应用数据类
     */
    data class SelectedMusicApp(
        val packageName: String,
        val appName: String,
        val isEnabled: Boolean = true
    )

    /**
     * 音乐应用按钮配置数据类
     */
    data class MusicAppButtonConfig(
        val id: String = UUID.randomUUID().toString(),
        val appPackageName: String, // 关联的应用包名
        val buttonPosition: String = "bottom_right", // 按钮位置
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24, // 垂直边距(dp)
        val isEnabled: Boolean = true // 是否启用此按钮
    )

    /**
     * 地址提醒配置数据类
     */
    data class AddressReminderConfig(
        val selectedMapApps: List<SelectedMapApp> = emptyList(), // 选中的地图应用列表
        val delayTime: Int = 2, // 延迟提醒时间（秒）
        val cooldownTime: Int = 60, // 冷却时间（秒）
        val autoDismissEnabled: Boolean = true, // 是否自动消失
        val autoDismissSeconds: Int = 10, // 自动消失时间（秒）
        val buttonConfigs: List<AddressReminderButtonConfig> = emptyList() // 按钮配置列表
    )

    /**
     * 选中的地图应用数据类
     */
    data class SelectedMapApp(
        val packageName: String,
        val appName: String,
        val isEnabled: Boolean = true
    )

    /**
     * 地址提醒按钮配置数据类
     */
    data class AddressReminderButtonConfig(
        val id: String = UUID.randomUUID().toString(),
        val appPackageName: String, // 关联的应用包名
        val buttonPosition: String = "bottom_right", // 按钮位置
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24, // 垂直边距(dp)
        val isEnabled: Boolean = true // 是否启用此按钮
    )

    /**
     * 购物应用提醒配置数据类
     */
    data class ShoppingAppReminderConfig(
        val enabledBuiltInPlatforms: Set<String> = setOf("淘宝", "京东", "天猫", "拼多多"), // 启用的内置平台
        val customPlatforms: List<CustomShoppingPlatform> = emptyList(), // 自定义购物平台
        val detectionDelay: Int = 500, // 检测延迟时间（毫秒）
        val cooldownTime: Int = 30, // 冷却时间（秒）
        val autoDismissEnabled: Boolean = true, // 是否自动消失
        val autoDismissSeconds: Int = 10, // 自动消失时间（秒）
        val buttonPosition: String = "bottom_right", // 按钮位置
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24 // 垂直边距(dp)
    )

    /**
     * 应用链接提醒配置数据类
     */
    data class AppLinkReminderConfig(
        val enabledBuiltInPlatforms: Set<String> = setOf("抖音", "快手", "小红书", "百度网盘", "夸克网盘"), // 启用的内置平台
        val customPlatforms: List<CustomAppPlatform> = emptyList(), // 自定义应用平台
        val detectionDelay: Int = 500, // 检测延迟时间（毫秒）
        val cooldownTime: Int = 30, // 冷却时间（秒）
        val autoDismissEnabled: Boolean = true, // 是否自动消失
        val autoDismissSeconds: Int = 10, // 自动消失时间（秒）
        val buttonPosition: String = "bottom_right", // 按钮位置
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24, // 垂直边距(dp)
        val intelligentRecognitionEnabled: Boolean = true // 智能链接识别开关
    )

    /**
     * 自定义应用平台数据类
     */
    data class CustomAppPlatform(
        val id: String = UUID.randomUUID().toString(),
        val name: String,
        val packageName: String,
        val urlPatterns: List<String>
    )

    /**
     * 分享网址提醒配置数据类
     */
    data class ShareUrlReminderConfig(
        val customUrlPatterns: List<String> = emptyList(), // 自定义URL模式
        val detectionDelay: Int = 500, // 检测延迟时间（毫秒）
        val cooldownTime: Int = 30, // 冷却时间（秒）
        val autoDismissEnabled: Boolean = true, // 是否自动消失
        val autoDismissSeconds: Int = 10, // 自动消失时间（秒）
        val buttonPosition: String = "bottom_right", // 按钮位置
        val buttonSize: Int = 56, // 按钮尺寸(dp)
        val buttonMarginX: Int = 24, // 水平边距(dp)
        val buttonMarginY: Int = 24 // 垂直边距(dp)
    )

    /**
     * 自定义购物平台数据类
     */
    data class CustomShoppingPlatform(
        val id: String = "", // 平台唯一ID
        val name: String = "", // 平台名称
        val packageName: String = "", // 应用包名
        val urlPatterns: List<String> = emptyList(), // URL匹配规则
        val useRegex: Boolean = false, // 是否使用正则表达式
        val caseSensitive: Boolean = false, // 是否大小写敏感
        val enabled: Boolean = true // 是否启用
    )

    /**
     * 保存手电筒提醒配置
     */
    fun saveFlashlightReminderConfig(reminderTypeId: String, config: FlashlightReminderConfig) {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        sharedPreferences.edit().apply {
            putInt(keyPrefix + "_delay_time", config.delayTime)
            putInt(keyPrefix + "_cooldown_time", config.cooldownTime)
            putBoolean(keyPrefix + "_auto_dismiss_enabled", config.autoDismissEnabled)
            putInt(keyPrefix + "_auto_dismiss_seconds", config.autoDismissSeconds)
            putString(keyPrefix + "_button_position", config.buttonPosition)
            putInt(keyPrefix + "_button_size", config.buttonSize)
            putInt(keyPrefix + "_button_margin_x", config.buttonMarginX)
            putInt(keyPrefix + "_button_margin_y", config.buttonMarginY)
            apply()
        }
    }

    /**
     * 加载手电筒提醒配置
     */
    fun loadFlashlightReminderConfig(reminderTypeId: String): FlashlightReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        return FlashlightReminderConfig(
            delayTime = sharedPreferences.getInt(keyPrefix + "_delay_time", 10),
            cooldownTime = sharedPreferences.getInt(keyPrefix + "_cooldown_time", 60),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + "_auto_dismiss_enabled", false),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + "_auto_dismiss_seconds", 5),
            buttonPosition = sharedPreferences.getString(keyPrefix + "_button_position", "bottom_left") ?: "bottom_left",
            buttonSize = sharedPreferences.getInt(keyPrefix + "_button_size", 56),
            buttonMarginX = sharedPreferences.getInt(keyPrefix + "_button_margin_x", 24),
            buttonMarginY = sharedPreferences.getInt(keyPrefix + "_button_margin_y", 24)
        )
    }

    /**
     * 保存新应用提醒配置
     */
    fun saveNewAppReminderConfig(reminderTypeId: String, config: NewAppReminderConfig) {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        sharedPreferences.edit().apply {
            putInt(keyPrefix + "_delay_time", config.delayTime)
            putInt(keyPrefix + "_cooldown_time", config.cooldownTime)
            putBoolean(keyPrefix + "_auto_dismiss_enabled", config.autoDismissEnabled)
            putInt(keyPrefix + "_auto_dismiss_seconds", config.autoDismissSeconds)
            putString(keyPrefix + "_button_position", config.buttonPosition)
            putInt(keyPrefix + "_button_size", config.buttonSize)
            putInt(keyPrefix + "_button_margin_x", config.buttonMarginX)
            putInt(keyPrefix + "_button_margin_y", config.buttonMarginY)
            putBoolean(keyPrefix + "_include_system_apps", config.includeSystemApps)
            apply()
        }
    }

    /**
     * 加载新应用提醒配置
     */
    fun loadNewAppReminderConfig(reminderTypeId: String): NewAppReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        return NewAppReminderConfig(
            delayTime = sharedPreferences.getInt(keyPrefix + "_delay_time", 3),
            cooldownTime = sharedPreferences.getInt(keyPrefix + "_cooldown_time", 60),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + "_auto_dismiss_enabled", true),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + "_auto_dismiss_seconds", 10),
            buttonPosition = sharedPreferences.getString(keyPrefix + "_button_position", "bottom_right") ?: "bottom_right",
            buttonSize = sharedPreferences.getInt(keyPrefix + "_button_size", 56),
            buttonMarginX = sharedPreferences.getInt(keyPrefix + "_button_margin_x", 24),
            buttonMarginY = sharedPreferences.getInt(keyPrefix + "_button_margin_y", 24),
            includeSystemApps = sharedPreferences.getBoolean(keyPrefix + "_include_system_apps", false)
        )
    }

    /**
     * 保存音乐应用提醒配置
     */
    fun saveMusicAppReminderConfig(reminderTypeId: String, config: MusicAppReminderConfig) {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        sharedPreferences.edit().apply {
            // 保存基本配置
            putInt(keyPrefix + "_delay_time", config.delayTime)
            putInt(keyPrefix + "_cooldown_time", config.cooldownTime)
            putBoolean(keyPrefix + "_auto_dismiss_enabled", config.autoDismissEnabled)
            putInt(keyPrefix + "_auto_dismiss_seconds", config.autoDismissSeconds)

            // 保存选中的音乐应用数量
            putInt(keyPrefix + "_selected_music_apps_count", config.selectedMusicApps.size)

            // 保存每个选中的音乐应用
            config.selectedMusicApps.forEachIndexed { index, app ->
                val appPrefix = keyPrefix + "_selected_music_app_${index}_"
                putString(appPrefix + "package_name", app.packageName)
                putString(appPrefix + "app_name", app.appName)
                putBoolean(appPrefix + "is_enabled", app.isEnabled)
            }

            // 保存按钮配置数量
            putInt(keyPrefix + "_button_configs_count", config.buttonConfigs.size)

            // 保存每个按钮配置
            config.buttonConfigs.forEachIndexed { index, buttonConfig ->
                val buttonPrefix = keyPrefix + "_button_config_${index}_"
                putString(buttonPrefix + "id", buttonConfig.id)
                putString(buttonPrefix + "app_package_name", buttonConfig.appPackageName)
                putString(buttonPrefix + "button_position", buttonConfig.buttonPosition)
                putInt(buttonPrefix + "button_size", buttonConfig.buttonSize)
                putInt(buttonPrefix + "button_margin_x", buttonConfig.buttonMarginX)
                putInt(buttonPrefix + "button_margin_y", buttonConfig.buttonMarginY)
                putBoolean(buttonPrefix + "is_enabled", buttonConfig.isEnabled)
            }

            // 如果选择了音乐应用，标记为已配置
            if (config.selectedMusicApps.isNotEmpty()) {
                putBoolean(keyPrefix + SUFFIX_CONFIGURED, true)
                putLong(keyPrefix + SUFFIX_MODIFIED_TIME, System.currentTimeMillis())
            }

            apply()
        }
    }

    /**
     * 加载音乐应用提醒配置
     */
    fun loadMusicAppReminderConfig(reminderTypeId: String): MusicAppReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId

        // 加载选中的音乐应用
        val selectedMusicAppsCount = sharedPreferences.getInt(keyPrefix + "_selected_music_apps_count", 0)
        val selectedMusicApps = mutableListOf<SelectedMusicApp>()

        for (i in 0 until selectedMusicAppsCount) {
            val appPrefix = keyPrefix + "_selected_music_app_${i}_"
            val packageName = sharedPreferences.getString(appPrefix + "package_name", "") ?: ""
            val appName = sharedPreferences.getString(appPrefix + "app_name", "") ?: ""
            val isEnabled = sharedPreferences.getBoolean(appPrefix + "is_enabled", true)

            if (packageName.isNotEmpty()) {
                selectedMusicApps.add(SelectedMusicApp(packageName, appName, isEnabled))
            }
        }

        // 加载按钮配置
        val buttonConfigsCount = sharedPreferences.getInt(keyPrefix + "_button_configs_count", 0)
        val buttonConfigs = mutableListOf<MusicAppButtonConfig>()

        for (i in 0 until buttonConfigsCount) {
            val buttonPrefix = keyPrefix + "_button_config_${i}_"
            val id = sharedPreferences.getString(buttonPrefix + "id", "") ?: UUID.randomUUID().toString()
            val appPackageName = sharedPreferences.getString(buttonPrefix + "app_package_name", "") ?: ""
            val buttonPosition = sharedPreferences.getString(buttonPrefix + "button_position", "bottom_right") ?: "bottom_right"
            val buttonSize = sharedPreferences.getInt(buttonPrefix + "button_size", 56)
            val buttonMarginX = sharedPreferences.getInt(buttonPrefix + "button_margin_x", 24)
            val buttonMarginY = sharedPreferences.getInt(buttonPrefix + "button_margin_y", 24)
            val isEnabled = sharedPreferences.getBoolean(buttonPrefix + "is_enabled", true)

            if (appPackageName.isNotEmpty()) {
                buttonConfigs.add(MusicAppButtonConfig(id, appPackageName, buttonPosition, buttonSize, buttonMarginX, buttonMarginY, isEnabled))
            }
        }

        return MusicAppReminderConfig(
            selectedMusicApps = selectedMusicApps,
            buttonConfigs = buttonConfigs,
            delayTime = sharedPreferences.getInt(keyPrefix + "_delay_time", 2),
            cooldownTime = sharedPreferences.getInt(keyPrefix + "_cooldown_time", 60),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + "_auto_dismiss_enabled", true),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + "_auto_dismiss_seconds", 10)
        )
    }

    /**
     * 保存购物应用提醒配置
     */
    fun saveShoppingAppReminderConfig(reminderTypeId: String, config: ShoppingAppReminderConfig) {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        sharedPreferences.edit().apply {
            // 保存启用的内置平台
            putStringSet(keyPrefix + "_enabled_builtin_platforms", config.enabledBuiltInPlatforms)

            // 保存自定义平台数量
            putInt(keyPrefix + "_custom_platforms_count", config.customPlatforms.size)

            // 保存每个自定义平台的详细信息
            config.customPlatforms.forEachIndexed { index, platform ->
                val platformPrefix = keyPrefix + "_custom_platform_${index}_"
                putString(platformPrefix + "id", platform.id)
                putString(platformPrefix + "name", platform.name)
                putString(platformPrefix + "package_name", platform.packageName)
                putStringSet(platformPrefix + "url_patterns", platform.urlPatterns.toSet())
                putBoolean(platformPrefix + "use_regex", platform.useRegex)
                putBoolean(platformPrefix + "case_sensitive", platform.caseSensitive)
                putBoolean(platformPrefix + "enabled", platform.enabled)
            }

            // 保存检测配置
            putInt(keyPrefix + "_detection_delay", config.detectionDelay)
            putInt(keyPrefix + "_cooldown_time", config.cooldownTime)
            putBoolean(keyPrefix + "_auto_dismiss_enabled", config.autoDismissEnabled)
            putInt(keyPrefix + "_auto_dismiss_seconds", config.autoDismissSeconds)

            // 保存悬浮窗配置
            putString(keyPrefix + "_button_position", config.buttonPosition)
            putInt(keyPrefix + "_button_size", config.buttonSize)
            putInt(keyPrefix + "_button_margin_x", config.buttonMarginX)
            putInt(keyPrefix + "_button_margin_y", config.buttonMarginY)

            apply()
        }
    }

    /**
     * 加载购物应用提醒配置
     */
    fun loadShoppingAppReminderConfig(reminderTypeId: String): ShoppingAppReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId

        // 加载启用的内置平台
        val enabledBuiltInPlatforms = sharedPreferences.getStringSet(
            keyPrefix + "_enabled_builtin_platforms",
            setOf("淘宝", "京东", "天猫", "拼多多")
        ) ?: setOf("淘宝", "京东", "天猫", "拼多多")

        // 加载自定义平台
        val customPlatformsCount = sharedPreferences.getInt(keyPrefix + "_custom_platforms_count", 0)
        val customPlatforms = mutableListOf<CustomShoppingPlatform>()

        for (index in 0 until customPlatformsCount) {
            val platformPrefix = keyPrefix + "_custom_platform_${index}_"
            val platform = CustomShoppingPlatform(
                id = sharedPreferences.getString(platformPrefix + "id", "") ?: "",
                name = sharedPreferences.getString(platformPrefix + "name", "") ?: "",
                packageName = sharedPreferences.getString(platformPrefix + "package_name", "") ?: "",
                urlPatterns = (sharedPreferences.getStringSet(platformPrefix + "url_patterns", emptySet()) ?: emptySet()).toList(),
                useRegex = sharedPreferences.getBoolean(platformPrefix + "use_regex", false),
                caseSensitive = sharedPreferences.getBoolean(platformPrefix + "case_sensitive", false),
                enabled = sharedPreferences.getBoolean(platformPrefix + "enabled", true)
            )
            customPlatforms.add(platform)
        }

        return ShoppingAppReminderConfig(
            enabledBuiltInPlatforms = enabledBuiltInPlatforms,
            customPlatforms = customPlatforms,
            detectionDelay = sharedPreferences.getInt(keyPrefix + "_detection_delay", 500),
            cooldownTime = sharedPreferences.getInt(keyPrefix + "_cooldown_time", 30),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + "_auto_dismiss_enabled", true),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + "_auto_dismiss_seconds", 10),
            buttonPosition = sharedPreferences.getString(keyPrefix + "_button_position", "bottom_right") ?: "bottom_right",
            buttonSize = sharedPreferences.getInt(keyPrefix + "_button_size", 56),
            buttonMarginX = sharedPreferences.getInt(keyPrefix + "_button_margin_x", 24),
            buttonMarginY = sharedPreferences.getInt(keyPrefix + "_button_margin_y", 24)
        )
    }

    /**
     * 保存应用链接提醒配置
     */
    fun saveAppLinkReminderConfig(reminderTypeId: String, config: AppLinkReminderConfig) {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        sharedPreferences.edit().apply {
            // 保存启用的内置平台
            putStringSet(keyPrefix + "_enabled_builtin_platforms", config.enabledBuiltInPlatforms)

            // 保存自定义平台数量
            putInt(keyPrefix + "_custom_platforms_count", config.customPlatforms.size)

            // 保存每个自定义平台的详细信息
            config.customPlatforms.forEachIndexed { index, platform ->
                val platformPrefix = keyPrefix + "_custom_platform_${index}_"
                putString(platformPrefix + "id", platform.id)
                putString(platformPrefix + "name", platform.name)
                putString(platformPrefix + "package_name", platform.packageName)
                putStringSet(platformPrefix + "url_patterns", platform.urlPatterns.toSet())
            }

            // 保存其他配置
            putInt(keyPrefix + "_detection_delay", config.detectionDelay)
            putInt(keyPrefix + "_cooldown_time", config.cooldownTime)
            putBoolean(keyPrefix + "_auto_dismiss_enabled", config.autoDismissEnabled)
            putInt(keyPrefix + "_auto_dismiss_seconds", config.autoDismissSeconds)
            putString(keyPrefix + "_button_position", config.buttonPosition)
            putInt(keyPrefix + "_button_size", config.buttonSize)
            putInt(keyPrefix + "_button_margin_x", config.buttonMarginX)
            putInt(keyPrefix + "_button_margin_y", config.buttonMarginY)
            putBoolean(keyPrefix + "_intelligent_recognition_enabled", config.intelligentRecognitionEnabled)

            apply()
        }
    }

    /**
     * 加载应用链接提醒配置
     */
    fun loadAppLinkReminderConfig(reminderTypeId: String): AppLinkReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId

        // 加载启用的内置平台
        val enabledBuiltInPlatforms = sharedPreferences.getStringSet(
            keyPrefix + "_enabled_builtin_platforms",
            setOf("抖音", "快手", "小红书", "百度网盘", "夸克网盘")
        ) ?: setOf("抖音", "快手", "小红书", "百度网盘", "夸克网盘")

        // 加载自定义平台
        val customPlatformsCount = sharedPreferences.getInt(keyPrefix + "_custom_platforms_count", 0)
        val customPlatforms = mutableListOf<CustomAppPlatform>()
        for (index in 0 until customPlatformsCount) {
            val platformPrefix = keyPrefix + "_custom_platform_${index}_"
            val platform = CustomAppPlatform(
                id = sharedPreferences.getString(platformPrefix + "id", "") ?: "",
                name = sharedPreferences.getString(platformPrefix + "name", "") ?: "",
                packageName = sharedPreferences.getString(platformPrefix + "package_name", "") ?: "",
                urlPatterns = (sharedPreferences.getStringSet(platformPrefix + "url_patterns", emptySet()) ?: emptySet()).toList()
            )
            customPlatforms.add(platform)
        }

        return AppLinkReminderConfig(
            enabledBuiltInPlatforms = enabledBuiltInPlatforms,
            customPlatforms = customPlatforms,
            detectionDelay = sharedPreferences.getInt(keyPrefix + "_detection_delay", 500),
            cooldownTime = sharedPreferences.getInt(keyPrefix + "_cooldown_time", 30),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + "_auto_dismiss_enabled", true),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + "_auto_dismiss_seconds", 10),
            buttonPosition = sharedPreferences.getString(keyPrefix + "_button_position", "bottom_right") ?: "bottom_right",
            buttonSize = sharedPreferences.getInt(keyPrefix + "_button_size", 56),
            buttonMarginX = sharedPreferences.getInt(keyPrefix + "_button_margin_x", 24),
            buttonMarginY = sharedPreferences.getInt(keyPrefix + "_button_margin_y", 24),
            intelligentRecognitionEnabled = sharedPreferences.getBoolean(keyPrefix + "_intelligent_recognition_enabled", true)
        )
    }

    /**
     * 保存分享网址提醒配置
     */
    fun saveShareUrlReminderConfig(reminderTypeId: String, config: ShareUrlReminderConfig) {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        sharedPreferences.edit().apply {
            // 保存自定义URL模式
            putStringSet(keyPrefix + "_custom_url_patterns", config.customUrlPatterns.toSet())

            // 保存其他配置
            putInt(keyPrefix + "_detection_delay", config.detectionDelay)
            putInt(keyPrefix + "_cooldown_time", config.cooldownTime)
            putBoolean(keyPrefix + "_auto_dismiss_enabled", config.autoDismissEnabled)
            putInt(keyPrefix + "_auto_dismiss_seconds", config.autoDismissSeconds)
            putString(keyPrefix + "_button_position", config.buttonPosition)
            putInt(keyPrefix + "_button_size", config.buttonSize)
            putInt(keyPrefix + "_button_margin_x", config.buttonMarginX)
            putInt(keyPrefix + "_button_margin_y", config.buttonMarginY)

            apply()
        }
    }

    /**
     * 加载分享网址提醒配置
     */
    fun loadShareUrlReminderConfig(reminderTypeId: String): ShareUrlReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId

        // 加载自定义URL模式
        val customUrlPatterns = (sharedPreferences.getStringSet(
            keyPrefix + "_custom_url_patterns",
            emptySet()
        ) ?: emptySet()).toList()

        return ShareUrlReminderConfig(
            customUrlPatterns = customUrlPatterns,
            detectionDelay = sharedPreferences.getInt(keyPrefix + "_detection_delay", 500),
            cooldownTime = sharedPreferences.getInt(keyPrefix + "_cooldown_time", 30),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + "_auto_dismiss_enabled", true),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + "_auto_dismiss_seconds", 10),
            buttonPosition = sharedPreferences.getString(keyPrefix + "_button_position", "bottom_right") ?: "bottom_right",
            buttonSize = sharedPreferences.getInt(keyPrefix + "_button_size", 56),
            buttonMarginX = sharedPreferences.getInt(keyPrefix + "_button_margin_x", 24),
            buttonMarginY = sharedPreferences.getInt(keyPrefix + "_button_margin_y", 24)
        )
    }

    /**
     * 保存地址提醒配置
     */
    fun saveAddressReminderConfig(reminderTypeId: String, config: AddressReminderConfig) {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        sharedPreferences.edit().apply {
            // 保存基本配置
            putInt(keyPrefix + "_delay_time", config.delayTime)
            putInt(keyPrefix + "_cooldown_time", config.cooldownTime)
            putBoolean(keyPrefix + "_auto_dismiss_enabled", config.autoDismissEnabled)
            putInt(keyPrefix + "_auto_dismiss_seconds", config.autoDismissSeconds)

            // 保存选中的地图应用数量
            putInt(keyPrefix + "_selected_map_apps_count", config.selectedMapApps.size)

            // 保存每个选中的地图应用
            config.selectedMapApps.forEachIndexed { index, app ->
                val appPrefix = keyPrefix + "_selected_map_app_${index}_"
                putString(appPrefix + "package_name", app.packageName)
                putString(appPrefix + "app_name", app.appName)
                putBoolean(appPrefix + "is_enabled", app.isEnabled)
            }

            // 保存按钮配置数量
            putInt(keyPrefix + "_button_configs_count", config.buttonConfigs.size)

            // 保存每个按钮配置
            config.buttonConfigs.forEachIndexed { index, buttonConfig ->
                val buttonPrefix = keyPrefix + "_button_config_${index}_"
                putString(buttonPrefix + "id", buttonConfig.id)
                putString(buttonPrefix + "app_package_name", buttonConfig.appPackageName)
                putString(buttonPrefix + "button_position", buttonConfig.buttonPosition)
                putInt(buttonPrefix + "button_size", buttonConfig.buttonSize)
                putInt(buttonPrefix + "button_margin_x", buttonConfig.buttonMarginX)
                putInt(buttonPrefix + "button_margin_y", buttonConfig.buttonMarginY)
                putBoolean(buttonPrefix + "is_enabled", buttonConfig.isEnabled)
            }

            apply()
        }
    }

    /**
     * 加载地址提醒配置
     */
    fun loadAddressReminderConfig(reminderTypeId: String): AddressReminderConfig {
        val keyPrefix = KEY_PREFIX + reminderTypeId

        // 加载选中的地图应用
        val selectedMapAppsCount = sharedPreferences.getInt(keyPrefix + "_selected_map_apps_count", 0)
        val selectedMapApps = mutableListOf<SelectedMapApp>()

        for (i in 0 until selectedMapAppsCount) {
            val appPrefix = keyPrefix + "_selected_map_app_${i}_"
            val packageName = sharedPreferences.getString(appPrefix + "package_name", "") ?: ""
            val appName = sharedPreferences.getString(appPrefix + "app_name", "") ?: ""
            val isEnabled = sharedPreferences.getBoolean(appPrefix + "is_enabled", true)

            if (packageName.isNotEmpty()) {
                selectedMapApps.add(SelectedMapApp(packageName, appName, isEnabled))
            }
        }

        // 加载按钮配置
        val buttonConfigsCount = sharedPreferences.getInt(keyPrefix + "_button_configs_count", 0)
        val buttonConfigs = mutableListOf<AddressReminderButtonConfig>()

        for (i in 0 until buttonConfigsCount) {
            val buttonPrefix = keyPrefix + "_button_config_${i}_"
            val id = sharedPreferences.getString(buttonPrefix + "id", "") ?: UUID.randomUUID().toString()
            val appPackageName = sharedPreferences.getString(buttonPrefix + "app_package_name", "") ?: ""
            val buttonPosition = sharedPreferences.getString(buttonPrefix + "button_position", "bottom_right") ?: "bottom_right"
            val buttonSize = sharedPreferences.getInt(buttonPrefix + "button_size", 56)
            val buttonMarginX = sharedPreferences.getInt(buttonPrefix + "button_margin_x", 24)
            val buttonMarginY = sharedPreferences.getInt(buttonPrefix + "button_margin_y", 24)
            val isEnabled = sharedPreferences.getBoolean(buttonPrefix + "is_enabled", true)

            if (appPackageName.isNotEmpty()) {
                buttonConfigs.add(AddressReminderButtonConfig(id, appPackageName, buttonPosition, buttonSize, buttonMarginX, buttonMarginY, isEnabled))
            }
        }



        return AddressReminderConfig(
            selectedMapApps = selectedMapApps,
            buttonConfigs = buttonConfigs,
            delayTime = sharedPreferences.getInt(keyPrefix + "_delay_time", 2),
            cooldownTime = sharedPreferences.getInt(keyPrefix + "_cooldown_time", 60),
            autoDismissEnabled = sharedPreferences.getBoolean(keyPrefix + "_auto_dismiss_enabled", true),
            autoDismissSeconds = sharedPreferences.getInt(keyPrefix + "_auto_dismiss_seconds", 10)
        )
    }

    /**
     * 检查配置是否存在
     */
    fun hasConfig(reminderTypeId: String): Boolean {
        val keyPrefix = KEY_PREFIX + reminderTypeId
        return sharedPreferences.contains(keyPrefix + SUFFIX_ENABLED)
    }
}

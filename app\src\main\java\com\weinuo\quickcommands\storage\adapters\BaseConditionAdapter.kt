package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.SharedTriggerCondition
import com.weinuo.quickcommands.storage.AppListStorageEngine
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageKeyGenerator
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 条件存储适配器接口
 *
 * 定义了所有条件存储适配器必须实现的基本操作。
 * 每种SharedTriggerCondition子类都需要对应的适配器实现。
 *
 * @param T 具体的条件类型
 */
interface ConditionStorageAdapter<T : SharedTriggerCondition> {

    /**
     * 保存条件到存储
     *
     * @param condition 要保存的条件
     * @return 操作是否成功
     */
    fun save(condition: T): Boolean

    /**
     * 从存储加载条件
     *
     * @param conditionId 条件ID
     * @return 加载的条件，失败时返回null
     */
    fun load(conditionId: String): T?

    /**
     * 从存储删除条件
     *
     * @param conditionId 条件ID
     * @return 操作是否成功
     */
    fun delete(conditionId: String): Boolean

    /**
     * 获取条件类型标识
     *
     * @return 条件类型字符串
     */
    fun getConditionType(): String

    /**
     * 检查条件是否存在
     *
     * @param conditionId 条件ID
     * @return 条件是否存在
     */
    fun exists(conditionId: String): Boolean
}

/**
 * 条件存储适配器基类
 *
 * 提供所有条件适配器的通用功能实现，包括：
 * - 基础字段的存储和加载
 * - 键名生成规则
 * - 通用的删除和检查操作
 * - 错误处理和日志记录
 *
 * @param T 具体的条件类型
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
abstract class BaseConditionAdapter<T : SharedTriggerCondition>(
    protected val storageManager: NativeTypeStorageManager,
    protected val appListEngine: AppListStorageEngine
) : ConditionStorageAdapter<T> {

    companion object {
        private const val TAG = "BaseConditionAdapter"

        // 基础字段名常量
        protected const val FIELD_TYPE = "type"
        protected const val FIELD_ID = "id"
    }

    /**
     * 获取条件存储键前缀
     *
     * @param conditionId 条件ID
     * @return 键前缀
     */
    protected fun getPrefix(conditionId: String): String {
        return StorageKeyGenerator.getConditionPrefix(conditionId)
    }

    /**
     * 生成条件字段键名
     *
     * @param conditionId 条件ID
     * @param fieldName 字段名
     * @return 完整键名
     */
    protected fun generateKey(conditionId: String, fieldName: String): String {
        return StorageKeyGenerator.generateConditionKey(conditionId, fieldName)
    }

    /**
     * 保存基础字段
     * 所有条件都有的通用字段
     *
     * @param conditionId 条件ID
     * @param condition 条件实例
     * @return 存储操作列表
     */
    protected fun saveBaseFields(conditionId: String, condition: SharedTriggerCondition): List<StorageOperation> {
        return listOf(
            StorageOperation.createStringOperation(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_TYPE),
                getConditionType()
            ),
            StorageOperation.createStringOperation(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_ID),
                condition.id
            )
        )
    }

    /**
     * 保存字符串列表
     * 将字符串列表拆分为独立的键值对存储
     *
     * @param baseKey 基础键名
     * @param list 字符串列表
     * @return 存储操作列表
     */
    protected fun saveStringList(baseKey: String, list: List<String>): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        // 保存列表大小
        operations.add(StorageOperation.createIntOperation(
            StorageDomain.CONDITIONS,
            StorageKeyGenerator.generateCollectionKey(baseKey, "count"),
            list.size
        ))

        // 保存每个元素
        list.forEachIndexed { index, item ->
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.CONDITIONS,
                StorageKeyGenerator.generateCollectionKey(baseKey, index.toString()),
                item
            ))
        }

        return operations
    }

    /**
     * 加载字符串列表
     * 从拆分的键值对重建字符串列表
     *
     * @param baseKey 基础键名
     * @return 字符串列表
     */
    protected fun loadStringList(baseKey: String): List<String> {
        val countKey = StorageKeyGenerator.generateCollectionKey(baseKey, "count")
        val count = storageManager.loadInt(StorageDomain.CONDITIONS, countKey, 0)

        if (count == 0) {
            return emptyList()
        }

        val list = mutableListOf<String>()
        for (index in 0 until count) {
            val itemKey = StorageKeyGenerator.generateCollectionKey(baseKey, index.toString())
            val item = storageManager.loadString(StorageDomain.CONDITIONS, itemKey)
            if (item.isNotEmpty()) {
                list.add(item)
            }
        }

        return list
    }

    /**
     * 保存枚举值
     * 将枚举转换为字符串存储
     *
     * @param key 键名
     * @param enumValue 枚举值
     * @return 存储操作
     */
    protected fun <E : Enum<E>> saveEnum(key: String, enumValue: E): StorageOperation {
        return StorageOperation.createStringOperation(
            StorageDomain.CONDITIONS,
            key,
            enumValue.name
        )
    }

    /**
     * 加载枚举值
     * 从字符串重建枚举值
     *
     * @param key 键名
     * @param enumClass 枚举类
     * @param defaultValue 默认值
     * @return 枚举值
     */
    protected fun <E : Enum<E>> loadEnum(
        key: String,
        enumClass: Class<E>,
        defaultValue: E
    ): E {
        val enumName = storageManager.loadString(StorageDomain.CONDITIONS, key)
        return if (enumName.isNotEmpty()) {
            try {
                java.lang.Enum.valueOf(enumClass, enumName)
            } catch (e: IllegalArgumentException) {
                Log.w(TAG, "Invalid enum value: $enumName for ${enumClass.simpleName}, using default")
                defaultValue
            }
        } else {
            defaultValue
        }
    }

    /**
     * 删除条件的所有相关数据
     *
     * @param conditionId 条件ID
     * @return 操作是否成功
     */
    override fun delete(conditionId: String): Boolean {
        return try {
            val prefix = getPrefix(conditionId)
            val success = storageManager.deleteByPrefix(StorageDomain.CONDITIONS, prefix)
            if (success) {
                Log.d(TAG, "Successfully deleted condition: $conditionId")
            } else {
                Log.e(TAG, "Failed to delete condition: $conditionId")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Exception while deleting condition: $conditionId", e)
            false
        }
    }

    /**
     * 检查条件是否存在
     *
     * @param conditionId 条件ID
     * @return 条件是否存在
     */
    override fun exists(conditionId: String): Boolean {
        val typeKey = generateKey(conditionId, FIELD_TYPE)
        return storageManager.containsKey(StorageDomain.CONDITIONS, typeKey)
    }

    /**
     * 验证条件ID是否有效
     *
     * @param conditionId 条件ID
     * @return 是否有效
     */
    protected fun isValidConditionId(conditionId: String): Boolean {
        return conditionId.isNotBlank()
    }

    /**
     * 记录保存成功日志
     *
     * @param conditionId 条件ID
     */
    protected fun logSaveSuccess(conditionId: String) {
        Log.d(TAG, "Successfully saved ${getConditionType()} condition: $conditionId")
    }

    /**
     * 记录保存失败日志
     *
     * @param conditionId 条件ID
     * @param error 错误信息
     */
    protected fun logSaveError(conditionId: String, error: String) {
        Log.e(TAG, "Failed to save ${getConditionType()} condition: $conditionId, error: $error")
    }

    /**
     * 记录加载失败日志
     *
     * @param conditionId 条件ID
     * @param error 错误信息
     */
    protected fun logLoadError(conditionId: String, error: String) {
        Log.e(TAG, "Failed to load ${getConditionType()} condition: $conditionId, error: $error")
    }
}

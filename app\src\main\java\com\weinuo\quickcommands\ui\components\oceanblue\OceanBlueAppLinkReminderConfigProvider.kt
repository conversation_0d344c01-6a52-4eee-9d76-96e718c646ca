package com.weinuo.quickcommands.ui.components.oceanblue

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.service.QuickCommandsService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import kotlinx.coroutines.launch
import java.util.*

/**
 * 应用链接提醒配置提供者
 *
 * 提供应用链接提醒功能的完整配置界面，包括：
 * - 内置应用平台管理
 * - 自定义应用平台管理
 * - 检测行为配置
 * - 悬浮窗显示配置
 *
 * 设计特点：
 * - 丰富的自定义选项
 * - 用户友好的界面
 * - 实时配置验证
 * - 支持配置导入导出
 */
object OceanBlueAppLinkReminderConfigProvider {

    /**
     * 获取应用链接提醒配置项
     */
    fun getConfigurationItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ConfigurationCardItem(
            id = "app_link_reminder",
            title = context.getString(R.string.app_link_reminder_title),
            description = context.getString(R.string.app_link_reminder_description),
            operationType = SmartReminderType.APP_LINK_REMINDER,
            permissionRequired = true, // 应用链接提醒需要悬浮窗权限
            content = { reminderType, onComplete ->
                AppLinkReminderConfigContent(reminderType, onComplete)
            },
            editableContent = { reminderType, initialConfig, onComplete ->
                AppLinkReminderConfigContent(reminderType, onComplete, initialConfig)
            }
        )
    }

    /**
     * 获取配置内容组件（支持外部保存请求）
     *
     * 提供应用链接提醒的配置内容组件，用于在智慧提醒详细配置界面中显示。
     * 支持外部保存请求机制，用于右上角保存按钮。
     *
     * @param reminderType 智慧提醒类型
     * @param onComplete 配置完成回调
     * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
     */
    @Composable
    fun getConfigContent(
        reminderType: SmartReminderType,
        onComplete: (Any) -> Unit,
        onSaveRequested: ((suspend () -> Unit) -> Unit)? = null,
        navController: androidx.navigation.NavController? = null
    ) {
        AppLinkReminderConfigContent(
            reminderType = reminderType,
            onComplete = onComplete,
            onSaveRequested = onSaveRequested,
            navController = navController
        )
    }
}

/**
 * 应用链接提醒配置内容组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AppLinkReminderConfigContent(
    reminderType: SmartReminderType,
    onComplete: (Any) -> Unit,
    initialConfig: Any? = null,
    onSaveRequested: ((suspend () -> Unit) -> Unit)? = null,
    navController: androidx.navigation.NavController? = null
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val configAdapter = remember { SmartReminderConfigAdapter(context) }

    // 内置应用平台列表
    val builtInPlatforms = listOf("抖音", "快手", "小红书", "百度网盘", "夸克网盘")

    // 配置状态
    var enabledBuiltInPlatforms by remember { mutableStateOf(setOf("抖音", "快手", "小红书", "百度网盘", "夸克网盘")) }
    var customPlatforms by remember { mutableStateOf(listOf<SmartReminderConfigAdapter.CustomAppPlatform>()) }
    var detectionDelay by remember { mutableStateOf("500") }
    var cooldownTime by remember { mutableStateOf("30") }
    var autoDismissEnabled by remember { mutableStateOf(true) }
    var autoDismissSeconds by remember { mutableStateOf("10") }
    var buttonPosition by remember { mutableStateOf("bottom_right") }
    var buttonSize by remember { mutableStateOf("56") }
    var buttonMarginX by remember { mutableStateOf("24") }
    var buttonMarginY by remember { mutableStateOf("24") }
    var intelligentRecognitionEnabled by remember { mutableStateOf(true) }

    // 加载现有配置
    LaunchedEffect(reminderType) {
        try {
            val config = configAdapter.loadAppLinkReminderConfig(reminderType.id)
            enabledBuiltInPlatforms = config.enabledBuiltInPlatforms
            customPlatforms = config.customPlatforms
            detectionDelay = config.detectionDelay.toString()
            cooldownTime = config.cooldownTime.toString()
            autoDismissEnabled = config.autoDismissEnabled
            autoDismissSeconds = config.autoDismissSeconds.toString()
            buttonPosition = config.buttonPosition
            buttonSize = config.buttonSize.toString()
            buttonMarginX = config.buttonMarginX.toString()
            buttonMarginY = config.buttonMarginY.toString()
            intelligentRecognitionEnabled = config.intelligentRecognitionEnabled
        } catch (e: Exception) {
            // 使用默认配置
        }
    }

    // 保存配置函数
    val saveConfig: suspend () -> Unit = {
        try {
            val config = SmartReminderConfigAdapter.AppLinkReminderConfig(
                enabledBuiltInPlatforms = enabledBuiltInPlatforms,
                customPlatforms = customPlatforms,
                detectionDelay = detectionDelay.toIntOrNull()?.coerceIn(100, 2000) ?: 500,
                cooldownTime = cooldownTime.toIntOrNull()?.coerceIn(10, 300) ?: 30,
                autoDismissEnabled = autoDismissEnabled,
                autoDismissSeconds = autoDismissSeconds.toIntOrNull()?.coerceIn(1, 60) ?: 10,
                buttonPosition = buttonPosition,
                buttonSize = buttonSize.toIntOrNull()?.coerceIn(40, 80) ?: 56,
                buttonMarginX = buttonMarginX.toIntOrNull()?.coerceIn(8, 100) ?: 24,
                buttonMarginY = buttonMarginY.toIntOrNull()?.coerceIn(8, 100) ?: 24,
                intelligentRecognitionEnabled = intelligentRecognitionEnabled
            )
            configAdapter.saveAppLinkReminderConfig(reminderType.id, config)

            // 通知服务重新加载配置
            QuickCommandsService.recheckSmartReminderMonitoring(context)

            // 注意：这里不调用onComplete，避免自动跳转
            // onComplete(config)

        } catch (e: Exception) {
            // 处理保存错误
        }
    }

    // 注册保存函数到外部保存请求
    LaunchedEffect(onSaveRequested) {
        onSaveRequested?.invoke {
            scope.launch {
                saveConfig()
            }
        }
    }

    // 监听自定义应用平台保存结果
    LaunchedEffect(navController) {
        navController?.currentBackStackEntry?.savedStateHandle?.getLiveData<SmartReminderConfigAdapter.CustomAppPlatform>("saved_custom_app_platform")?.observeForever { savedPlatform ->
            if (savedPlatform != null) {
                // 添加或更新自定义平台
                val existingIndex = customPlatforms.indexOfFirst { it.id == savedPlatform.id }
                if (existingIndex >= 0) {
                    // 更新现有平台
                    customPlatforms = customPlatforms.toMutableList().apply {
                        set(existingIndex, savedPlatform)
                    }
                } else {
                    // 添加新平台
                    customPlatforms = customPlatforms + savedPlatform
                }
                // 清除结果，避免重复处理
                navController?.currentBackStackEntry?.savedStateHandle?.remove<SmartReminderConfigAdapter.CustomAppPlatform>("saved_custom_app_platform")
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // 应用平台管理
        ConfigSection(
            title = "应用平台管理",
            description = "选择要监控的应用平台，支持内置平台和自定义平台"
        ) {
            AppPlatformManagementContent(
                builtInPlatforms = builtInPlatforms,
                enabledBuiltInPlatforms = enabledBuiltInPlatforms,
                onBuiltInPlatformToggle = { platform, enabled ->
                    enabledBuiltInPlatforms = if (enabled) {
                        enabledBuiltInPlatforms + platform
                    } else {
                        enabledBuiltInPlatforms - platform
                    }
                },
                customPlatforms = customPlatforms,
                onAddCustomPlatform = {
                    navController?.navigate(
                        com.weinuo.quickcommands.navigation.Screen.CustomAppPlatformConfig.createNewRoute()
                    )
                },
                onEditCustomPlatform = { platform ->
                    // TODO: 实现平台数据序列化
                    navController?.navigate(
                        com.weinuo.quickcommands.navigation.Screen.CustomAppPlatformConfig.createNewRoute()
                    )
                },
                onDeleteCustomPlatform = { platform ->
                    customPlatforms = customPlatforms.filter { it.id != platform.id }
                }
            )
        }

        // 智能链接识别
        ConfigSection(
            title = context.getString(R.string.intelligent_link_recognition_title),
            description = context.getString(R.string.intelligent_link_recognition_description)
        ) {
            IntelligentRecognitionSettingsContent(
                intelligentRecognitionEnabled = intelligentRecognitionEnabled,
                onIntelligentRecognitionToggle = { intelligentRecognitionEnabled = it }
            )
        }

        // 检测设置
        ConfigSection(
            title = "检测设置",
            description = "调整应用检测的延迟时间和提醒间隔"
        ) {
            DetectionSettingsContent(
                detectionDelay = detectionDelay,
                onDetectionDelayChange = { detectionDelay = it },
                cooldownTime = cooldownTime,
                onCooldownTimeChange = { cooldownTime = it }
            )
        }

        // 悬浮窗设置
        ConfigSection(
            title = "悬浮窗设置",
            description = "设置提醒按钮的显示样式和行为"
        ) {
            OverlaySettingsContent(
                autoDismissEnabled = autoDismissEnabled,
                onAutoDismissEnabledChange = { autoDismissEnabled = it },
                autoDismissSeconds = autoDismissSeconds,
                onAutoDismissSecondsChange = { autoDismissSeconds = it },
                buttonPosition = buttonPosition,
                onButtonPositionChange = { buttonPosition = it },
                buttonSize = buttonSize,
                onButtonSizeChange = { buttonSize = it },
                buttonMarginX = buttonMarginX,
                onButtonMarginXChange = { buttonMarginX = it },
                buttonMarginY = buttonMarginY,
                onButtonMarginYChange = { buttonMarginY = it }
            )
        }
    }
}

/**
 * 应用平台管理内容组件
 */
@Composable
private fun AppPlatformManagementContent(
    builtInPlatforms: List<String>,
    enabledBuiltInPlatforms: Set<String>,
    onBuiltInPlatformToggle: (String, Boolean) -> Unit,
    customPlatforms: List<SmartReminderConfigAdapter.CustomAppPlatform>,
    onAddCustomPlatform: () -> Unit,
    onEditCustomPlatform: (SmartReminderConfigAdapter.CustomAppPlatform) -> Unit,
    onDeleteCustomPlatform: (SmartReminderConfigAdapter.CustomAppPlatform) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 内置平台部分
        Text(
            text = "内置应用平台",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )

        // 内置平台开关列表
        builtInPlatforms.forEach { platform ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Switch(
                    checked = platform in enabledBuiltInPlatforms,
                    onCheckedChange = { enabled ->
                        onBuiltInPlatformToggle(platform, enabled)
                    }
                )

                Spacer(modifier = Modifier.width(12.dp))

                Text(
                    text = platform,
                    style = MaterialTheme.typography.bodyLarge
                )
            }
        }

        // 自定义平台部分
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "自定义应用平台",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface
            )

            IconButton(onClick = onAddCustomPlatform) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加自定义平台"
                )
            }
        }

        // 自定义平台列表
        if (customPlatforms.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无自定义平台\n点击右上角 + 号添加",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            customPlatforms.forEach { platform ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = platform.name,
                                style = MaterialTheme.typography.titleSmall,
                                fontWeight = FontWeight.Medium
                            )

                            Text(
                                text = "包名: ${platform.packageName}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            Text(
                                text = "规则: ${platform.urlPatterns.size} 条",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                        Row {
                            IconButton(onClick = { onEditCustomPlatform(platform) }) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = "编辑平台"
                                )
                            }

                            IconButton(onClick = { onDeleteCustomPlatform(platform) }) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "删除平台"
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 检测设置内容组件
 */
@Composable
private fun DetectionSettingsContent(
    detectionDelay: String,
    onDetectionDelayChange: (String) -> Unit,
    cooldownTime: String,
    onCooldownTimeChange: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 检测延迟设置
        OutlinedTextField(
            value = detectionDelay,
            onValueChange = { newValue ->
                // 只允许输入数字
                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                    onDetectionDelayChange(newValue)
                }
            },
            label = { Text("检测延迟（毫秒）") },
            placeholder = { Text("500") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            supportingText = {
                val value = detectionDelay.toIntOrNull()
                when {
                    value == null && detectionDelay.isNotEmpty() -> {
                        Text(
                            text = "请输入有效数字",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null && (value < 100 || value > 2000) -> {
                        Text(
                            text = "建议范围：100-2000毫秒",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null -> {
                        Text(
                            text = when {
                                value <= 200 -> "快速响应"
                                value <= 800 -> "平衡设置"
                                else -> "稳定检测"
                            },
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    else -> {
                        Text(
                            text = "建议范围：100-2000毫秒",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        )

        // 冷却时间设置
        OutlinedTextField(
            value = cooldownTime,
            onValueChange = { newValue ->
                // 只允许输入数字
                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                    onCooldownTimeChange(newValue)
                }
            },
            label = { Text("冷却时间（秒）") },
            placeholder = { Text("30") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            supportingText = {
                val value = cooldownTime.toIntOrNull()
                when {
                    value == null && cooldownTime.isNotEmpty() -> {
                        Text(
                            text = "请输入有效数字",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null && (value < 10 || value > 300) -> {
                        Text(
                            text = "建议范围：10-300秒",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null -> {
                        Text(
                            text = when {
                                value <= 20 -> "频繁提醒"
                                value <= 60 -> "适中频率"
                                else -> "较少打扰"
                            },
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    else -> {
                        Text(
                            text = "建议范围：10-300秒",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        )
    }
}

/**
 * 悬浮窗设置内容组件
 */
@Composable
private fun OverlaySettingsContent(
    autoDismissEnabled: Boolean,
    onAutoDismissEnabledChange: (Boolean) -> Unit,
    autoDismissSeconds: String,
    onAutoDismissSecondsChange: (String) -> Unit,
    buttonPosition: String,
    onButtonPositionChange: (String) -> Unit,
    buttonSize: String,
    onButtonSizeChange: (String) -> Unit,
    buttonMarginX: String,
    onButtonMarginXChange: (String) -> Unit,
    buttonMarginY: String,
    onButtonMarginYChange: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 自动消失设置
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "启用自动消失",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = "提醒按钮将在指定时间后自动关闭",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            Switch(
                checked = autoDismissEnabled,
                onCheckedChange = onAutoDismissEnabledChange
            )
        }

        // 自动消失时间设置
        if (autoDismissEnabled) {
            OutlinedTextField(
                value = autoDismissSeconds,
                onValueChange = { newValue ->
                    // 只允许输入数字
                    if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                        onAutoDismissSecondsChange(newValue)
                    }
                },
                label = { Text("自动消失时间（秒）") },
                placeholder = { Text("10") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                supportingText = {
                    val value = autoDismissSeconds.toIntOrNull()
                    when {
                        value == null && autoDismissSeconds.isNotEmpty() -> {
                            Text(
                                text = "请输入有效数字",
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                        value != null && (value < 1 || value > 60) -> {
                            Text(
                                text = "建议范围：1-60秒",
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                        value != null -> {
                            Text(
                                text = when {
                                    value <= 5 -> "快速消失"
                                    value <= 15 -> "适中时间"
                                    else -> "较长显示"
                                },
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                        else -> {
                            Text(
                                text = "建议范围：1-60秒",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            )
        }

        // 按钮位置设置
        val positionOptions = listOf(
            "bottom_left" to "左下角",
            "bottom_right" to "右下角",
            "top_left" to "左上角",
            "top_right" to "右上角"
        )

        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            positionOptions.forEach { (value, label) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = buttonPosition == value,
                        onClick = { onButtonPositionChange(value) }
                    )
                    Text(
                        text = label,
                        modifier = Modifier.padding(start = 8.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 按钮尺寸设置
        OutlinedTextField(
            value = buttonSize,
            onValueChange = { newValue ->
                // 只允许输入数字
                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                    onButtonSizeChange(newValue)
                }
            },
            label = { Text("按钮尺寸（dp）") },
            placeholder = { Text("56") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            supportingText = {
                val value = buttonSize.toIntOrNull()
                when {
                    value == null && buttonSize.isNotEmpty() -> {
                        Text(
                            text = "请输入有效数字",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null && (value < 40 || value > 80) -> {
                        Text(
                            text = "建议范围：40-80dp",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null -> {
                        Text(
                            text = when {
                                value <= 48 -> "小尺寸"
                                value <= 64 -> "标准尺寸"
                                else -> "大尺寸"
                            },
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    else -> {
                        Text(
                            text = "建议范围：40-80dp",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        )

        // 水平边距设置
        OutlinedTextField(
            value = buttonMarginX,
            onValueChange = { newValue ->
                // 只允许输入数字
                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                    onButtonMarginXChange(newValue)
                }
            },
            label = { Text("水平边距（dp）") },
            placeholder = { Text("24") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            supportingText = {
                val value = buttonMarginX.toIntOrNull()
                when {
                    value == null && buttonMarginX.isNotEmpty() -> {
                        Text(
                            text = "请输入有效数字",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null && (value < 8 || value > 100) -> {
                        Text(
                            text = "建议范围：8-100dp",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null -> {
                        Text(
                            text = when {
                                value <= 16 -> "贴边显示"
                                value <= 40 -> "适中距离"
                                else -> "远离边缘"
                            },
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    else -> {
                        Text(
                            text = "建议范围：8-100dp",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        )

        // 垂直边距设置
        OutlinedTextField(
            value = buttonMarginY,
            onValueChange = { newValue ->
                // 只允许输入数字
                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                    onButtonMarginYChange(newValue)
                }
            },
            label = { Text("垂直边距（dp）") },
            placeholder = { Text("24") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            supportingText = {
                val value = buttonMarginY.toIntOrNull()
                when {
                    value == null && buttonMarginY.isNotEmpty() -> {
                        Text(
                            text = "请输入有效数字",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null && (value < 8 || value > 100) -> {
                        Text(
                            text = "建议范围：8-100dp",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    value != null -> {
                        Text(
                            text = when {
                                value <= 16 -> "贴边显示"
                                value <= 40 -> "适中距离"
                                else -> "远离边缘"
                            },
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    else -> {
                        Text(
                            text = "建议范围：8-100dp",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        )
    }
}

/**
 * 配置区块组件
 */
@Composable
private fun ConfigSection(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            content()
        }
    }
}

/**
 * 智能链接识别设置内容组件
 */
@Composable
private fun IntelligentRecognitionSettingsContent(
    intelligentRecognitionEnabled: Boolean,
    onIntelligentRecognitionToggle: (Boolean) -> Unit
) {
    val context = LocalContext.current

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 智能链接识别开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = context.getString(R.string.intelligent_link_recognition_enabled),
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = context.getString(R.string.intelligent_link_recognition_help),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            Switch(
                checked = intelligentRecognitionEnabled,
                onCheckedChange = onIntelligentRecognitionToggle
            )
        }

        // 功能说明
        if (intelligentRecognitionEnabled) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "智能识别功能",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = "• 标准化协议格式和域名分隔符\n" +
                                "• 移除文本中的干扰词汇和提示内容\n" +
                                "• 清理表情符号和非标准字符\n" +
                                "• 过滤括号内的无关信息\n" +
                                "• 优化链接格式以提高识别准确性",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}
package com.weinuo.quickcommands.shizuku

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import rikka.shizuku.Shizuku
import rikka.shizuku.ShizukuProvider
import java.io.BufferedReader
import java.io.InputStreamReader
import java.lang.reflect.Method

/**
 * Shizuku 管理器，处理 Shizuku 权限和命令执行
 */
object ShizukuManager {
    private const val TAG = "ShizukuManager"

    /**
     * 检查 Shizuku 是否已安装
     */
    fun isShizukuInstalled(context: Context): Boolean {
        return try {
            context.packageManager.getPackageInfo(ShizukuProvider.MANAGER_APPLICATION_ID, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * 检查 Shizuku 是否正在运行
     */
    fun isShizukuRunning(): Boolean {
        return Shizuku.pingBinder()
    }

    /**
     * 检查是否有 Shizuku 权限
     */
    fun checkShizukuPermission(): Boolean {
        return try {
            Shizuku.checkSelfPermission() == PackageManager.PERMISSION_GRANTED
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Shizuku permission", e)
            false
        }
    }

    /**
     * 请求 Shizuku 权限
     */
    fun requestShizukuPermission(requestCode: Int) {
        if (Shizuku.shouldShowRequestPermissionRationale()) {
            // 用户之前拒绝过权限，可以在这里显示解释
        }
        Shizuku.requestPermission(requestCode)
    }

    /**
     * 使用反射获取Shizuku的newProcess方法
     * 缓存方法引用以提高性能
     */
    private var newProcessMethod: Method? = null

    private fun getNewProcessMethod(): Method {
        if (newProcessMethod == null) {
            try {
                newProcessMethod = Shizuku::class.java.getDeclaredMethod(
                    "newProcess",
                    Array<String>::class.java,
                    Array<String>::class.java,
                    String::class.java
                )
                newProcessMethod?.isAccessible = true
            } catch (e: Exception) {
                throw e
            }
        }
        return newProcessMethod!!
    }

    /**
     * 执行 Shell 命令
     * 使用Shizuku的进程代理能力执行命令
     */
    fun executeCommand(command: String): String {
        return try {
            if (!isShizukuRunning()) {
                return "Shizuku 未运行"
            }

            if (Shizuku.checkSelfPermission() != PackageManager.PERMISSION_GRANTED) {
                return "没有 Shizuku 权限"
            }

            // 使用Shizuku的进程代理能力创建进程
            val process = try {
                val method = getNewProcessMethod()
                method.invoke(null, arrayOf("sh", "-c", command), null, null) as Process
            } catch (e: Exception) {
                return "创建Shizuku进程失败: ${e.message}"
            }

            // 读取进程输出
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val errorReader = BufferedReader(InputStreamReader(process.errorStream))

            val output = StringBuilder()
            var line: String?

            while (reader.readLine().also { line = it } != null) {
                output.append(line).append("\n")
            }

            while (errorReader.readLine().also { line = it } != null) {
                output.append("Error: ").append(line).append("\n")
            }

            // 等待进程完成并获取退出码
            val exitCode = process.waitFor()
            output.append("Exit code: ").append(exitCode)

            output.toString()
        } catch (e: Exception) {
            "执行命令出错: ${e.message}"
        }
    }

    /**
     * 执行流式命令（用于需要持续输出的命令，如getevent）
     *
     * @param command 要执行的命令
     * @param onOutput 输出回调函数
     * @param maxDuration 最大执行时间（毫秒），0表示无限制
     * @return 创建的进程对象，可用于停止命令
     */
    fun executeStreamingCommand(
        command: String,
        onOutput: (String) -> Unit,
        maxDuration: Long = 0
    ): Process? {
        return try {
            if (!isShizukuRunning()) {
                onOutput("Shizuku 未运行")
                return null
            }

            if (Shizuku.checkSelfPermission() != PackageManager.PERMISSION_GRANTED) {
                onOutput("没有 Shizuku 权限")
                return null
            }

            // 使用Shizuku的进程代理能力创建进程
            val process = try {
                val method = getNewProcessMethod()
                method.invoke(null, arrayOf("sh", "-c", command), null, null) as Process
            } catch (e: Exception) {
                onOutput("创建Shizuku进程失败: ${e.message}")
                return null
            }

            // 在后台线程中读取输出
            Thread {
                try {
                    val reader = BufferedReader(InputStreamReader(process.inputStream))
                    val startTime = System.currentTimeMillis()

                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        onOutput(line!!)

                        // 检查是否超时
                        if (maxDuration > 0 && System.currentTimeMillis() - startTime > maxDuration) {
                            break
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error reading streaming output", e)
                    onOutput("读取输出出错: ${e.message}")
                }
            }.start()

            process
        } catch (e: Exception) {
            Log.e(TAG, "Error executing streaming command", e)
            onOutput("执行流式命令出错: ${e.message}")
            null
        }
    }

    /**
     * 强制停止应用
     * 使用Shizuku的进程代理能力执行强制停止命令
     *
     * @param packageName 要停止的应用包名
     * @return 是否成功执行
     */
    fun forceStopApp(packageName: String): Boolean {
        // 使用固定的强制停止命令
        val command = "am force-stop $packageName"

        // 执行命令
        val result = executeCommand(command)

        // 检查是否执行成功
        val isSuccess = !result.contains("Error") &&
                        !result.contains("出错") &&
                        !result.contains("permission") &&
                        !result.contains("权限")



        return isSuccess
    }

    /**
     * 冻结应用
     * 使用Shizuku的进程代理能力执行应用禁用命令
     *
     * @param packageName 要冻结的应用包名
     * @return 是否成功执行
     */
    fun freezeApp(packageName: String): Boolean {
        // 使用pm disable命令冻结应用
        val command = "pm disable $packageName"

        // 执行命令
        val result = executeCommand(command)

        // 检查是否执行成功
        val isSuccess = !result.contains("Error") &&
                        !result.contains("出错") &&
                        !result.contains("permission") &&
                        !result.contains("权限") &&
                        !result.contains("Failed")

        Log.d(TAG, "Freeze app $packageName: result=$result, success=$isSuccess")
        return isSuccess
    }

    /**
     * 解冻应用
     * 使用Shizuku的进程代理能力执行应用启用命令
     *
     * @param packageName 要解冻的应用包名
     * @return 是否成功执行
     */
    fun unfreezeApp(packageName: String): Boolean {
        // 使用pm enable命令解冻应用
        val command = "pm enable $packageName"

        // 执行命令
        val result = executeCommand(command)

        // 检查是否执行成功
        val isSuccess = !result.contains("Error") &&
                        !result.contains("出错") &&
                        !result.contains("permission") &&
                        !result.contains("权限") &&
                        !result.contains("Failed")

        Log.d(TAG, "Unfreeze app $packageName: result=$result, success=$isSuccess")
        return isSuccess
    }

    /**
     * 检查应用是否被冻结
     * 使用Shizuku的进程代理能力查询应用状态
     *
     * @param packageName 要检查的应用包名
     * @return 应用是否被冻结
     */
    fun isAppFrozen(packageName: String): Boolean {
        // 使用pm list packages命令查询应用状态
        val command = "pm list packages -d $packageName"

        // 执行命令
        val result = executeCommand(command)

        // 如果结果包含包名，说明应用被禁用（冻结）
        val isFrozen = result.contains(packageName)

        Log.d(TAG, "Check app frozen $packageName: result=$result, frozen=$isFrozen")
        return isFrozen
    }

    // 移除了getRunningApps和isAppRunning方法，改为使用AppRepository中的UsageStatsManager实现
}

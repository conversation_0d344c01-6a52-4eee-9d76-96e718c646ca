package com.weinuo.quickcommands.model

/**
 * 自适应策略枚举
 */
enum class AdaptiveStrategy(val displayName: String) {
    CONSERVATIVE("保守策略"),
    BALANCED("平衡策略"),
    AGGRESSIVE("激进策略"),
    CUSTOM("自定义策略")
}

/**
 * 事件驱动配置
 */
data class EventDrivenConfig(
    val triggerOnAppForeground: Boolean = true,
    val triggerOnAppLaunch: <PERSON>olean = true,
    val triggerOnMemoryPressure: Boolean = true,
    val appLaunchDelaySeconds: Int = 10,
    val foregroundDelaySeconds: Int = 5,
    val enableSmartDelay: Boolean = true,
    val monitorDurationSeconds: Int = 30,
    val monitorIntervalSeconds: Int = 3,
    val cooldownSeconds: Int = 60
)

/**
 * 自适应配置
 */
data class AdaptiveConfig(
    val adaptiveStrategy: AdaptiveStrategy = AdaptiveStrategy.BALANCED,
    val enableMemoryPressureAdaptation: Boolean = true,
    val enableAppActivityAdaptation: Boolean = true,
    val memoryAbundantFrequency: Int = 120,
    val memoryTightFrequency: Int = 5
)

/**
 * 智能学习配置
 */
data class IntelligentConfig(
    val enableLearning: Boolean = true,
    val minSamplesForPrediction: Int = 5,
    val confidenceThreshold: Float = 0.7f,
    val stabilityCheckInterval: Int = 2,
    val stabilityThresholdMB: Int = 50,
    val requiredStableChecks: Int = 3,
    val maxHistoryRecords: Int = 30,
    val dataRetentionDays: Int = 30
)

/**
 * 混合模式配置
 */
data class HybridConfig(
    val enableEventDriven: Boolean = true,
    val enableAdaptive: Boolean = true,
    val enableIntelligent: Boolean = false,
    val eventDrivenWeight: Float = 0.4f,
    val adaptiveWeight: Float = 0.4f,
    val intelligentWeight: Float = 0.2f
)

/**
 * 高级配置基类
 */
sealed class AdvancedMemoryConfig {
    data class EventDriven(val config: EventDrivenConfig) : AdvancedMemoryConfig()
    data class Adaptive(val config: AdaptiveConfig) : AdvancedMemoryConfig()
    data class Intelligent(val config: IntelligentConfig) : AdvancedMemoryConfig()
    data class Hybrid(val config: HybridConfig) : AdvancedMemoryConfig()
}

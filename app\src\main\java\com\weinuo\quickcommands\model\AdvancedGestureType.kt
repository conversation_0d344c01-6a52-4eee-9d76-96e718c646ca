package com.weinuo.quickcommands.model

/**
 * 高级手势类型枚举
 *
 * 用于高级悬浮窗录制模式中的手势类型选择。
 * 每种手势类型对应不同的触摸操作模式。
 */
enum class AdvancedGestureType(
    val displayName: String,
    val description: String,
    val requiresEndPoint: Boolean = false
) {
    /**
     * 单点点击
     * 在指定位置执行一次快速点击
     */
    TAP(
        displayName = "点击",
        description = "在指定位置执行一次快速点击",
        requiresEndPoint = false
    ),

    /**
     * 长按
     * 在指定位置按住一段时间
     */
    LONG_PRESS(
        displayName = "长按",
        description = "在指定位置按住一段时间",
        requiresEndPoint = false
    ),

    /**
     * 滑动
     * 从起始点滑动到结束点
     */
    SWIPE(
        displayName = "滑动",
        description = "从起始点滑动到结束点",
        requiresEndPoint = true
    ),

    /**
     * 多点触控（预留）
     * 同时在多个位置进行触摸操作
     */
    MULTI_TOUCH(
        displayName = "多点触控",
        description = "同时在多个位置进行触摸操作",
        requiresEndPoint = false
    );

    /**
     * 获取对应的TouchEventType
     */
    fun toTouchEventType(): TouchEventType {
        return when (this) {
            TAP -> TouchEventType.TAP
            LONG_PRESS -> TouchEventType.LONG_PRESS
            SWIPE -> TouchEventType.SWIPE
            MULTI_TOUCH -> TouchEventType.MULTI_TAP
        }
    }

    companion object {
        /**
         * 从TouchEventType转换为AdvancedGestureType
         */
        fun fromTouchEventType(touchEventType: TouchEventType): AdvancedGestureType {
            return when (touchEventType) {
                TouchEventType.TAP -> TAP
                TouchEventType.LONG_PRESS -> LONG_PRESS
                TouchEventType.SWIPE -> SWIPE
                TouchEventType.MULTI_TAP -> MULTI_TOUCH
            }
        }

        /**
         * 获取所有可用的手势类型
         */
        fun getAvailableTypes(): List<AdvancedGestureType> {
            return listOf(TAP, LONG_PRESS, SWIPE) // 暂时不包含多点触控
        }
    }
}

/**
 * 坐标标记数据类
 *
 * 用于在高级悬浮窗录制模式中表示可移动的坐标标记。
 *
 * @property id 标记唯一标识符
 * @property gestureType 手势类型
 * @property position 标记位置（相对坐标）
 * @property duration 手势持续时间（毫秒）
 * @property delayAfter 完成后延迟时间（毫秒）
 * @property label 标记显示标签
 */
data class CoordinateMarker(
    val id: String = java.util.UUID.randomUUID().toString(),
    val gestureType: AdvancedGestureType,
    val position: TouchPosition,
    val duration: Long = 0L,
    val delayAfter: Long = 500L,
    val label: String = ""
) {
    /**
     * 获取显示标签
     */
    fun getDisplayLabel(): String {
        return if (label.isNotEmpty()) {
            label
        } else {
            gestureType.displayName
        }
    }

    /**
     * 转换为TouchEvent
     */
    fun toTouchEvent(): TouchEvent {
        return TouchEvent(
            id = id,
            type = gestureType.toTouchEventType(),
            position = position,
            duration = duration,
            delayAfter = delayAfter,
            description = getDisplayLabel()
        )
    }

    /**
     * 检查是否为滑动手势
     */
    fun isSwipeGesture(): Boolean {
        return gestureType == AdvancedGestureType.SWIPE
    }

    /**
     * 检查是否需要结束点
     */
    fun requiresEndPoint(): Boolean {
        return gestureType.requiresEndPoint
    }

    /**
     * 获取绝对坐标X
     */
    fun getAbsoluteX(screenWidth: Int): Float {
        return position.startX * screenWidth
    }

    /**
     * 获取绝对坐标Y
     */
    fun getAbsoluteY(screenHeight: Int): Float {
        return position.startY * screenHeight
    }

    /**
     * 获取绝对结束坐标X（滑动手势用）
     */
    fun getAbsoluteEndX(screenWidth: Int): Float {
        return position.endX * screenWidth
    }

    /**
     * 获取绝对结束坐标Y（滑动手势用）
     */
    fun getAbsoluteEndY(screenHeight: Int): Float {
        return position.endY * screenHeight
    }
}

/**
 * 高级手势录制配置
 *
 * 用于配置高级悬浮窗录制模式的参数。
 *
 * @property recordingName 录制名称
 * @property showCoordinates 是否显示坐标信息
 * @property snapToGrid 是否启用网格对齐
 * @property gridSize 网格大小（像素）
 * @property markerSize 标记大小（dp）
 * @property autoSave 是否自动保存
 */
data class AdvancedRecordingConfig(
    val recordingName: String = "高级录制_${System.currentTimeMillis()}",
    val showCoordinates: Boolean = true,
    val snapToGrid: Boolean = false,
    val gridSize: Int = 50,
    val markerSize: Int = 40,
    val autoSave: Boolean = true
) {
    /**
     * 验证配置是否有效
     */
    fun isValid(): Boolean {
        return recordingName.isNotBlank() &&
                gridSize > 0 &&
                markerSize > 0
    }
}

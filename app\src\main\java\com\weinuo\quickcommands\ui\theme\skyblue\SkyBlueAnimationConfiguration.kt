package com.weinuo.quickcommands.ui.theme.skyblue

import androidx.compose.animation.core.*
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import com.weinuo.quickcommands.ui.theme.system.AnimationConfiguration
import com.weinuo.quickcommands.ui.theme.config.AnimationSpec
import com.weinuo.quickcommands.ui.theme.config.SpringAnimationSpec
import com.weinuo.quickcommands.ui.theme.config.EasingAnimationSpec

/**
 * 天空蓝主题动画配置
 *
 * 定义整合设计风格的动画效果
 * 特点：流畅的动画过渡，统一的动画语言
 */
class SkyBlueAnimationConfiguration : AnimationConfiguration {

    /**
     * 快速动画持续时间（毫秒）
     */
    override val fastDuration: Int = 150

    /**
     * 中等动画持续时间（毫秒）
     */
    override val mediumDuration: Int = 300

    /**
     * 慢速动画持续时间（毫秒）
     */
    override val slowDuration: Int = 500

    /**
     * 进入动画配置
     */
    override val enter: AnimationSpec = AnimationSpec(
        duration = mediumDuration,
        delay = 0,
        easing = "ease_in_out"
    )

    /**
     * 退出动画配置
     */
    override val exit: AnimationSpec = AnimationSpec(
        duration = fastDuration,
        delay = 0,
        easing = "ease_in"
    )

    /**
     * 过渡动画配置
     */
    override val transition: AnimationSpec = AnimationSpec(
        duration = mediumDuration,
        delay = 0,
        easing = "ease_in_out"
    )

    /**
     * 弹性动画配置
     */
    override val spring: SpringAnimationSpec = SpringAnimationSpec(
        damping = 0.8f,
        stiffness = 400f,
        visibilityThreshold = 0.01f
    )

    /**
     * 缓动动画配置
     */
    override val easing: EasingAnimationSpec = EasingAnimationSpec(
        duration = mediumDuration,
        easing = "ease_in_out"
    )

    /**
     * 是否启用动画
     */
    override val animationsEnabled: Boolean = true

    // 以下是扩展方法，用于创建具体的动画效果
    // 这些方法不是接口要求的，但可以被主题系统的其他部分使用

    /**
     * 淡入淡出动画
     */
    fun getFadeInAnimation(durationMillis: Int = mediumDuration): EnterTransition {
        return fadeIn(
            animationSpec = tween(
                durationMillis = durationMillis,
                easing = FastOutSlowInEasing
            )
        )
    }

    fun getFadeOutAnimation(durationMillis: Int = fastDuration): ExitTransition {
        return fadeOut(
            animationSpec = tween(
                durationMillis = durationMillis,
                easing = FastOutSlowInEasing
            )
        )
    }

    /**
     * 滑动动画
     */
    fun getSlideInFromBottomAnimation(durationMillis: Int = mediumDuration): EnterTransition {
        return slideInVertically(
            animationSpec = tween(
                durationMillis = durationMillis,
                easing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f)
            ),
            initialOffsetY = { it }
        )
    }

    fun getSlideOutToBottomAnimation(durationMillis: Int = fastDuration): ExitTransition {
        return slideOutVertically(
            animationSpec = tween(
                durationMillis = durationMillis,
                easing = LinearOutSlowInEasing
            ),
            targetOffsetY = { it }
        )
    }

    /**
     * 缩放动画
     */
    fun getScaleInAnimation(durationMillis: Int = mediumDuration): EnterTransition {
        return scaleIn(
            animationSpec = tween(
                durationMillis = durationMillis,
                easing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f)
            ),
            initialScale = 0.8f
        )
    }

    fun getScaleOutAnimation(durationMillis: Int = fastDuration): ExitTransition {
        return scaleOut(
            animationSpec = tween(
                durationMillis = durationMillis,
                easing = LinearOutSlowInEasing
            ),
            targetScale = 0.8f
        )
    }

    /**
     * 弹簧动画规格
     */
    fun getSpringAnimationSpec(): SpringSpec<Float> {
        return spring(
            dampingRatio = spring.damping,
            stiffness = spring.stiffness,
            visibilityThreshold = spring.visibilityThreshold
        )
    }

    /**
     * 补间动画规格
     */
    fun getTweenAnimationSpec(
        durationMillis: Int,
        easing: Easing = FastOutSlowInEasing
    ): TweenSpec<Float> {
        return tween(
            durationMillis = durationMillis,
            easing = easing
        )
    }

    /**
     * 获取特定组件的动画配置
     */
    fun getComponentAnimation(component: ComponentAnimationType): ComponentAnimationConfig {
        return when (component) {
            ComponentAnimationType.BUTTON -> ComponentAnimationConfig(
                pressedScale = 0.98f,
                pressedDuration = fastDuration,
                pressedEasing = FastOutSlowInEasing,
                rippleDuration = mediumDuration,
                rippleEasing = FastOutLinearInEasing
            )
            ComponentAnimationType.CARD -> ComponentAnimationConfig(
                pressedScale = 0.99f,
                pressedDuration = fastDuration,
                pressedEasing = FastOutSlowInEasing,
                rippleDuration = mediumDuration,
                rippleEasing = FastOutLinearInEasing
            )
            ComponentAnimationType.DIALOG -> ComponentAnimationConfig(
                pressedScale = 1.0f, // 对话框不使用缩放
                pressedDuration = mediumDuration,
                pressedEasing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f),
                rippleDuration = slowDuration,
                rippleEasing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f)
            )
            ComponentAnimationType.BOTTOM_SHEET -> ComponentAnimationConfig(
                pressedScale = 1.0f, // 底部弹窗不使用缩放
                pressedDuration = mediumDuration,
                pressedEasing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f),
                rippleDuration = slowDuration,
                rippleEasing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f)
            )
            ComponentAnimationType.FAB -> ComponentAnimationConfig(
                pressedScale = 0.95f, // FAB使用明显的缩放
                pressedDuration = fastDuration,
                pressedEasing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f),
                rippleDuration = mediumDuration,
                rippleEasing = FastOutSlowInEasing
            )
            ComponentAnimationType.CHIP -> ComponentAnimationConfig(
                pressedScale = 0.95f,
                pressedDuration = fastDuration,
                pressedEasing = FastOutSlowInEasing,
                rippleDuration = mediumDuration,
                rippleEasing = FastOutLinearInEasing
            )
            ComponentAnimationType.SWITCH -> ComponentAnimationConfig(
                pressedScale = 1.0f, // 开关不使用缩放
                pressedDuration = mediumDuration,
                pressedEasing = CubicBezierEasing(0.2f, 0.0f, 0.0f, 1.0f),
                rippleDuration = mediumDuration,
                rippleEasing = FastOutSlowInEasing
            )
            ComponentAnimationType.SLIDER -> ComponentAnimationConfig(
                pressedScale = 1.1f, // 滑块手柄使用放大
                pressedDuration = fastDuration,
                pressedEasing = FastOutSlowInEasing,
                rippleDuration = fastDuration,
                rippleEasing = LinearEasing
            )
            ComponentAnimationType.TAB -> ComponentAnimationConfig(
                pressedScale = 1.0f, // 标签页不使用缩放
                pressedDuration = mediumDuration,
                pressedEasing = FastOutSlowInEasing,
                rippleDuration = mediumDuration,
                rippleEasing = FastOutLinearInEasing
            )
            ComponentAnimationType.LIST_ITEM -> ComponentAnimationConfig(
                pressedScale = 1.0f, // 列表项不使用缩放
                pressedDuration = fastDuration,
                pressedEasing = FastOutSlowInEasing,
                rippleDuration = mediumDuration,
                rippleEasing = FastOutLinearInEasing
            )
        }
    }

    /**
     * 页面过渡动画
     */
    fun getPageTransition(transition: PageTransitionType): Pair<EnterTransition, ExitTransition> {
        return when (transition) {
            PageTransitionType.FADE -> Pair(
                getFadeInAnimation(mediumDuration),
                getFadeOutAnimation(mediumDuration)
            )
            PageTransitionType.SLIDE_UP -> Pair(
                getSlideInFromBottomAnimation(slowDuration),
                getSlideOutToBottomAnimation(mediumDuration)
            )
            PageTransitionType.SCALE -> Pair(
                getScaleInAnimation(mediumDuration),
                getScaleOutAnimation(mediumDuration)
            )
            PageTransitionType.COMBINED -> Pair(
                getFadeInAnimation(mediumDuration) + getScaleInAnimation(mediumDuration),
                getFadeOutAnimation(mediumDuration) + getScaleOutAnimation(mediumDuration)
            )
        }
    }
}

/**
 * 组件动画类型枚举
 */
enum class ComponentAnimationType {
    BUTTON,
    CARD,
    DIALOG,
    BOTTOM_SHEET,
    FAB,
    CHIP,
    SWITCH,
    SLIDER,
    TAB,
    LIST_ITEM
}

/**
 * 页面过渡类型枚举
 */
enum class PageTransitionType {
    FADE,
    SLIDE_UP,
    SCALE,
    COMBINED
}

/**
 * 组件动画配置数据类
 */
data class ComponentAnimationConfig(
    val pressedScale: Float,
    val pressedDuration: Int,
    val pressedEasing: Easing,
    val rippleDuration: Int,
    val rippleEasing: Easing
)

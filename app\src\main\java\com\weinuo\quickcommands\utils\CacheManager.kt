package com.weinuo.quickcommands.utils

import android.util.Log
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.flow.first

/**
 * 通用缓存管理器
 *
 * 为各种数据类型提供轻量级的内存缓存功能，避免重复加载
 * 使用按需加载策略，只在真正需要时才加载数据
 *
 * 特性：
 * - 泛型支持，可缓存任意类型的数据
 * - 可配置的缓存有效期
 * - 线程安全的缓存操作
 * - 自动过期清理
 * - 内存友好，避免内存泄漏
 */
class CacheManager<T> private constructor(
    private val cacheValidTime: Long,
    private val cacheKey: String
) {

    private var cachedData: T? = null
    private var cacheTimestamp: Long = 0
    private val mutex = Mutex()

    companion object {
        private const val TAG = "CacheManager"

        // 预定义的缓存有效期
        const val CACHE_5_MINUTES = 5 * 60 * 1000L
        const val CACHE_10_MINUTES = 10 * 60 * 1000L
        const val CACHE_30_MINUTES = 30 * 60 * 1000L

        /**
         * 创建缓存管理器实例
         *
         * @param cacheValidTime 缓存有效期（毫秒）
         * @param cacheKey 缓存键，用于日志标识
         */
        fun <T> create(cacheValidTime: Long, cacheKey: String): CacheManager<T> {
            return CacheManager(cacheValidTime, cacheKey)
        }
    }

    /**
     * 获取缓存数据，如果缓存无效则执行加载函数
     *
     * @param loader 数据加载函数
     * @return 缓存的或新加载的数据
     */
    suspend fun getOrLoad(loader: suspend () -> T): T {
        return mutex.withLock {
            val currentTime = System.currentTimeMillis()

            // 检查缓存是否有效
            if (cachedData != null && currentTime - cacheTimestamp < cacheValidTime) {
                Log.d(TAG, "Cache hit for $cacheKey")
                cachedData!!
            } else {
                Log.d(TAG, "Cache miss for $cacheKey, loading data...")
                try {
                    val newData = loader()
                    cachedData = newData
                    cacheTimestamp = currentTime
                    Log.d(TAG, "Data loaded and cached for $cacheKey")
                    newData
                } catch (e: Exception) {
                    Log.e(TAG, "Error loading data for $cacheKey", e)
                    // 如果加载失败且有旧缓存，返回旧缓存
                    cachedData?.let {
                        Log.w(TAG, "Returning stale cache for $cacheKey due to load error")
                        it
                    } ?: throw e
                }
            }
        }
    }

    /**
     * 强制刷新缓存
     *
     * @param loader 数据加载函数
     * @return 新加载的数据
     */
    suspend fun refresh(loader: suspend () -> T): T {
        return mutex.withLock {
            Log.d(TAG, "Force refreshing cache for $cacheKey")
            try {
                val newData = loader()
                cachedData = newData
                cacheTimestamp = System.currentTimeMillis()
                Log.d(TAG, "Cache refreshed for $cacheKey")
                newData
            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing cache for $cacheKey", e)
                throw e
            }
        }
    }

    /**
     * 清除缓存
     */
    fun clear() {
        cachedData = null
        cacheTimestamp = 0
        Log.d(TAG, "Cache cleared for $cacheKey")
    }

    /**
     * 检查缓存是否有效
     */
    fun isValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        return cachedData != null && currentTime - cacheTimestamp < cacheValidTime
    }

    /**
     * 获取缓存的数据（不触发加载）
     */
    fun getCachedData(): T? = cachedData
}

/**
 * 联系人缓存管理器
 */
object ContactsCacheManager {
    private val cacheManager = CacheManager.create<List<com.weinuo.quickcommands.utils.ContactsHelper.ContactInfo>>(
        CacheManager.CACHE_10_MINUTES,
        "contacts"
    )

    suspend fun getContacts(context: android.content.Context): List<com.weinuo.quickcommands.utils.ContactsHelper.ContactInfo> {
        return cacheManager.getOrLoad {
            com.weinuo.quickcommands.utils.ContactsHelper.getAllContacts(context)
        }
    }

    suspend fun refreshContacts(context: android.content.Context): List<com.weinuo.quickcommands.utils.ContactsHelper.ContactInfo> {
        return cacheManager.refresh {
            com.weinuo.quickcommands.utils.ContactsHelper.getAllContacts(context)
        }
    }

    fun clearCache() = cacheManager.clear()
}

/**
 * 铃声缓存管理器
 */
object RingtonesCacheManager {
    private val cacheManagers = mutableMapOf<String, CacheManager<List<com.weinuo.quickcommands.utils.RingtoneHelper.RingtoneInfo>>>()

    private fun getCacheManager(ringtoneType: String): CacheManager<List<com.weinuo.quickcommands.utils.RingtoneHelper.RingtoneInfo>> {
        return cacheManagers.getOrPut(ringtoneType) {
            CacheManager.create(CacheManager.CACHE_30_MINUTES, "ringtones_$ringtoneType")
        }
    }

    suspend fun getRingtones(
        context: android.content.Context,
        ringtoneType: com.weinuo.quickcommands.utils.RingtoneHelper.RingtoneType
    ): List<com.weinuo.quickcommands.utils.RingtoneHelper.RingtoneInfo> {
        val cacheManager = getCacheManager(ringtoneType.name)
        return cacheManager.getOrLoad {
            com.weinuo.quickcommands.utils.RingtoneHelper.getRingtones(context, ringtoneType)
        }
    }

    fun clearCache() {
        cacheManagers.values.forEach { it.clear() }
        cacheManagers.clear()
    }
}

/**
 * 账号缓存管理器
 */
object AccountsCacheManager {
    private val cacheManager = CacheManager.create<List<com.weinuo.quickcommands.ui.screens.AccountInfo>>(
        CacheManager.CACHE_5_MINUTES,
        "accounts"
    )

    suspend fun getAccounts(context: android.content.Context): List<com.weinuo.quickcommands.ui.screens.AccountInfo> {
        return cacheManager.getOrLoad {
            getSystemAccountsInternal(context)
        }
    }

    private suspend fun getSystemAccountsInternal(context: android.content.Context): List<com.weinuo.quickcommands.ui.screens.AccountInfo> {
        return kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
            try {
                val accountManager = android.accounts.AccountManager.get(context)
                val accounts = accountManager.accounts

                accounts.map { account ->
                    com.weinuo.quickcommands.ui.screens.AccountInfo(
                        name = account.name,
                        type = account.type
                    )
                }.distinctBy { it.name } // 去重，避免重复账号
                    .sortedWith(compareBy<com.weinuo.quickcommands.ui.screens.AccountInfo> { it.type }.thenBy { it.name })
            } catch (e: Exception) {
                emptyList()
            }
        }
    }

    fun clearCache() = cacheManager.clear()
}

/**
 * 分享目标缓存管理器
 */
object ShareTargetsCacheManager {
    private val cacheManager = CacheManager.create<List<com.weinuo.quickcommands.model.ShareTarget>>(
        CacheManager.CACHE_10_MINUTES,
        "share_targets"
    )

    suspend fun getShareTargets(context: android.content.Context): List<com.weinuo.quickcommands.model.ShareTarget> {
        return cacheManager.getOrLoad {
            com.weinuo.quickcommands.model.ShareTarget.queryShareTextTargets(context)
        }
    }

    fun clearCache() = cacheManager.clear()
}

/**
 * 秒表缓存管理器
 */
object StopwatchesCacheManager {
    private val cacheManager = CacheManager.create<List<com.weinuo.quickcommands.model.Stopwatch>>(
        CacheManager.CACHE_5_MINUTES,
        "stopwatches"
    )

    suspend fun getStopwatches(context: android.content.Context): List<com.weinuo.quickcommands.model.Stopwatch> {
        return cacheManager.getOrLoad {
            com.weinuo.quickcommands.model.StopwatchManager.getAllStopwatches(context)
        }
    }

    fun clearCache() = cacheManager.clear()
}

/**
 * 应用缓存管理器
 *
 * 为应用列表提供统一的缓存管理，避免重复创建AppRepository
 * 使用较短的缓存时间，因为应用安装/卸载会影响列表
 * 内部重用AppRepository实例，减少资源消耗
 */
object AppsCacheManager {
    private val userAppsCacheManager = CacheManager.create<List<com.weinuo.quickcommands.model.AppInfo>>(
        CacheManager.CACHE_10_MINUTES,
        "user_apps"
    )

    private val systemAppsCacheManager = CacheManager.create<List<com.weinuo.quickcommands.model.AppInfo>>(
        CacheManager.CACHE_10_MINUTES,
        "system_apps"
    )

    // 重用AppRepository实例，避免重复创建
    private var appRepository: com.weinuo.quickcommands.data.AppRepository? = null
    private var lastContextHashCode: Int = 0

    /**
     * 获取或创建AppRepository实例
     */
    private fun getAppRepository(context: android.content.Context): com.weinuo.quickcommands.data.AppRepository {
        val contextHashCode = context.hashCode()
        if (appRepository == null || lastContextHashCode != contextHashCode) {
            appRepository = com.weinuo.quickcommands.data.AppRepository(context)
            lastContextHashCode = contextHashCode
        }
        return appRepository!!
    }

    /**
     * 获取用户应用列表
     */
    suspend fun getUserApps(context: android.content.Context): List<com.weinuo.quickcommands.model.AppInfo> {
        return userAppsCacheManager.getOrLoad {
            val repository = getAppRepository(context)
            repository.loadApps()
            repository.userApps.first()
        }
    }

    /**
     * 获取系统应用列表
     */
    suspend fun getSystemApps(context: android.content.Context): List<com.weinuo.quickcommands.model.AppInfo> {
        return systemAppsCacheManager.getOrLoad {
            val repository = getAppRepository(context)
            repository.loadApps()
            repository.systemApps.first()
        }
    }

    /**
     * 获取所有应用列表（用户应用 + 系统应用）
     */
    suspend fun getAllApps(context: android.content.Context): Pair<List<com.weinuo.quickcommands.model.AppInfo>, List<com.weinuo.quickcommands.model.AppInfo>> {
        return Pair(getUserApps(context), getSystemApps(context))
    }

    /**
     * 强制刷新应用缓存
     */
    suspend fun refreshApps(context: android.content.Context): Pair<List<com.weinuo.quickcommands.model.AppInfo>, List<com.weinuo.quickcommands.model.AppInfo>> {
        val repository = getAppRepository(context)
        return Pair(
            userAppsCacheManager.refresh {
                repository.loadApps()
                repository.userApps.first()
            },
            systemAppsCacheManager.refresh {
                repository.loadApps()
                repository.systemApps.first()
            }
        )
    }

    /**
     * 清除所有应用缓存
     */
    fun clearCache() {
        userAppsCacheManager.clear()
        systemAppsCacheManager.clear()
    }
}

# 联系人选择功能实现指南

## 问题背景

在Android应用开发中，经常需要实现联系人选择功能。本文档记录了在BackgroundManagerMD3项目中实现可复用联系人选择组件的完整解决方案，特别是如何避免嵌套布局问题和实现良好的用户体验。

## 遇到的问题

1. **按钮点击无响应**: 配置界面中的"点击选择联系人"按钮onClick事件为空
2. **嵌套布局问题**: 原有实现使用对话框嵌套，导致UI问题和崩溃
3. **代码重复**: 多个Provider需要联系人选择功能，但各自实现
4. **导航传参困难**: 在Compose中传递NavController到深层组件困难

## 解决方案架构

### 1. 可复用的联系人选择界面

**文件**: `app/src/main/java/com/my/backgroundmanager/ui/screens/ContactSelectionScreen.kt`

**核心特性**:
- 支持单选(`SINGLE`)和多选(`MULTI`)模式
- 全屏界面替代对话框，避免嵌套布局问题
- 内置搜索功能
- 完整的状态管理（加载、错误、空状态）
- Material Design 3风格

**关键代码结构**:
```kotlin
enum class ContactSelectionMode {
    SINGLE,  // 单选模式
    MULTI    // 多选模式
}

@Composable
fun ContactSelectionScreen(
    selectionMode: ContactSelectionMode,
    initialSelectedContactIds: List<String> = emptyList(),
    onContactsSelected: (List<ContactsHelper.ContactInfo>) -> Unit,
    onDismiss: () -> Unit
)
```

### 2. 导航路由配置

**文件**: `app/src/main/java/com/my/backgroundmanager/navigation/Navigation.kt`

**添加的路由**:
```kotlin
object ContactSelection : Screen(
    route = "contact_selection/{selectionMode}?selectedContactIds={selectedContactIds}",
    titleResId = R.string.contact_selection,
    selectedIcon = Icons.Filled.Settings,
    unselectedIcon = Icons.Outlined.Settings
) {
    fun createSingleSelectionRoute(): String {
        return "contact_selection/SINGLE"
    }

    fun createMultiSelectionRoute(selectedContactIds: List<String> = emptyList()): String {
        return if (selectedContactIds.isNotEmpty()) {
            val encodedIds = java.net.URLEncoder.encode(selectedContactIds.joinToString(","), "UTF-8")
            "contact_selection/MULTI?selectedContactIds=$encodedIds"
        } else {
            "contact_selection/MULTI"
        }
    }
}
```

### 3. 自定义CompositionLocal解决NavController传递问题

**文件**: `app/src/main/java/com/my/backgroundmanager/ui/screens/DetailConfigurationScreen.kt`

**核心解决方案**:
```kotlin
// 创建自定义CompositionLocal
val LocalNavController = staticCompositionLocalOf<NavController?> { null }

@Composable
fun <T> DetailConfigurationScreen(
    // ... 其他参数
    navController: NavController? = null
) {
    // 通过CompositionLocalProvider提供NavController
    if (navController != null) {
        CompositionLocalProvider(LocalNavController provides navController) {
            content()
        }
    } else {
        content()
    }
}
```

### 4. 在配置组件中使用NavController

**在组件中获取NavController**:
```kotlin
@Composable
private fun ContactSelector(
    selectedContactIds: List<String>,
    onContactsChanged: (List<String>) -> Unit
) {
    val navController = LocalNavController.current

    OutlinedButton(
        onClick = {
            if (navController != null) {
                val route = Screen.ContactSelection.createMultiSelectionRoute(selectedContactIds)
                navController.navigate(route)
            }
        }
    ) {
        Text("点击选择联系人")
    }
}
```

### 5. 结果回传机制

**通过savedStateHandle传递结果**:

**MainActivity中的导航处理**:
```kotlin
composable(route = Screen.ContactSelection.route) { backStackEntry ->
    ContactSelectionScreen(
        // ... 参数
        onContactsSelected = { selectedContacts ->
            val previousEntry = navController.previousBackStackEntry
            previousEntry?.savedStateHandle?.apply {
                set("selected_contacts", selectedContacts)
            }
            navController.popBackStack()
        }
    )
}
```

**配置组件中监听结果**:
```kotlin
@Composable
private fun ContactSelector(/* ... */) {
    val navController = LocalNavController.current
    val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle

    LaunchedEffect(savedStateHandle) {
        savedStateHandle?.getLiveData<List<ContactsHelper.ContactInfo>>("selected_contacts")?.observeForever { selectedContacts ->
            if (selectedContacts != null) {
                onContactsChanged(selectedContacts.map { it.id })
                savedStateHandle.remove<List<ContactsHelper.ContactInfo>>("selected_contacts")
            }
        }
    }
}
```

### 6. 自动切换逻辑（重要）

**关键最佳实践：选择后自动切换选项**

在用户完成选择后，应该自动切换到相应的选项，避免用户需要手动再次点击。这是提升用户体验的关键细节。

**实现示例**:
```kotlin
@Composable
private fun ConfigContent(/* ... */) {
    var selectedMode by rememberSaveable { mutableStateOf(SelectionMode.ANY) }
    var selectedItems by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }

    // 监听选择结果
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val selectedContacts = savedStateHandle?.get<List<ContactsHelper.ContactInfo>>("selected_contacts")
        if (selectedContacts != null && selectedContacts.isNotEmpty()) {
            // 🔑 关键：自动切换到指定选项模式
            selectedMode = SelectionMode.SPECIFIC
            // 更新选中的项目
            selectedItems = selectedContacts.map { it.id }
            // 清除结果，避免重复处理
            savedStateHandle.remove<List<ContactsHelper.ContactInfo>>("selected_contacts")
        }
    }

    // UI渲染...
}
```

**适用场景**:
- 联系人选择 → 自动切换到"指定联系人"模式
- 应用选择 → 自动切换到"指定应用"模式
- 文件选择 → 自动切换到"指定文件"模式
- 动态壁纸应用选择 → 自动切换到"动态壁纸"选项

## 实现步骤

### 步骤1: 创建可复用的联系人选择界面
1. 创建`ContactSelectionScreen.kt`
2. 实现单选/多选模式支持
3. 添加搜索功能和状态管理

### 步骤2: 配置导航路由
1. 在`Navigation.kt`中添加联系人选择路由
2. 支持参数传递（选择模式、初始选中项）

### 步骤3: 修改架构支持NavController传递
1. 创建自定义`LocalNavController`
2. 修改`DetailConfigurationScreen`接收NavController参数
3. 通过CompositionLocalProvider提供给子组件

### 步骤4: 修改配置组件
1. 在配置组件中获取`LocalNavController`
2. 实现导航到联系人选择界面
3. 添加结果监听逻辑

### 步骤5: 更新MainActivity
1. 添加联系人选择界面的导航处理
2. 传递NavController到DetailConfigurationScreen
3. 实现结果回传机制

## 关键经验总结

### ✅ 最佳实践

1. **使用全屏界面而非对话框**: 避免嵌套布局问题，提供更好的用户体验
2. **创建可复用组件**: 一次实现，多处使用，减少代码重复
3. **自定义CompositionLocal**: 解决深层组件NavController传递问题
4. **通过savedStateHandle传递结果**: 类型安全的结果回传机制
5. **完整的状态管理**: 处理加载、错误、空状态，提升用户体验
6. **🔑 实现自动切换逻辑**: 选择完成后自动切换到相应选项，避免用户手动操作
7. **学习现有实现**: 遇到问题时先查看类似功能的实现方式

### ❌ 避免的陷阱

1. **不要在onClick回调中直接调用LocalNavController.current**: 会导致编译错误
2. **不要使用嵌套对话框**: 容易导致布局问题和崩溃
3. **不要忘记清除savedStateHandle中的结果**: 避免重复处理
4. **不要在非@Composable函数中调用Composition Local**: 确保在正确的上下文中使用
5. **🚫 不要忘记自动切换逻辑**: 选择后忘记自动切换选项，导致用户需要手动操作
6. **不要重复实现相同逻辑**: 先查看现有类似功能的实现方式

### 🔧 故障排除

**问题**: `@Composable invocations can only happen from the context of a @Composable function`
**解决**: 在@Composable函数级别获取LocalNavController，而不是在onClick回调中

**问题**: `Unresolved reference 'LocalNavController'`
**解决**: 确保正确导入自定义的LocalNavController，而不是尝试使用不存在的系统LocalNavController

**问题**: 联系人选择结果没有正确回传
**解决**: 检查savedStateHandle的key是否一致，确保在正确的时机清除结果

**问题**: 选择后需要再次点击才能看到结果
**解决**: 添加自动切换逻辑，在监听到选择结果时自动切换到相应的选项模式

**问题**: 如何实现自动切换逻辑
**解决**: 参考AppStateConfigProvider等现有实现，在LaunchedEffect中添加状态切换逻辑

## 适用场景

这个解决方案适用于以下场景：
- 需要在多个界面复用联系人选择功能
- 使用Jetpack Compose + Navigation Compose架构
- 需要避免对话框嵌套布局问题
- 需要在深层组件中访问NavController
- 需要类型安全的结果回传机制

## 扩展性考虑

1. **支持更多选择模式**: 可以扩展ContactSelectionMode枚举
2. **自定义过滤条件**: 可以添加更多联系人过滤选项
3. **支持其他数据源**: 可以扩展为选择其他类型的数据（如应用、文件等）
4. **主题定制**: 可以通过参数支持不同的UI主题

这个实现方案提供了一个完整、可复用、用户体验良好的联系人选择解决方案，可以作为类似功能实现的参考模板。

package com.weinuo.quickcommands.ui.components.themed

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.weinuo.quickcommands.ui.theme.config.RadioButtonConfig
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的单选按钮组件
 *
 * 根据当前主题自动选择合适的RadioButton实现：
 * - 海洋蓝主题：使用分层设计风格（LayeredRadioButton）- 标准Material 3样式
 * - 天空蓝主题：使用整合设计风格（SkyBlueRadioButton）- 圆形实心蓝色勾选样式
 * - 未来主题：可以使用各自独有的实现
 *
 * 支持所有标准RadioButton功能：
 * - 选中/未选中状态
 * - 启用/禁用状态
 * - 点击事件处理
 * - 自定义颜色配置
 * - 主题感知的样式和动画
 */
@Composable
fun ThemedRadioButton(
    config: RadioButtonConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    // 使用当前主题的组件工厂创建单选按钮
    // 如果传入了modifier参数，则使用它；否则使用config中的modifier
    val finalModifier = if (modifier != Modifier) modifier else config.modifier
    themeContext.componentFactory.createRadioButton()(
        config.copy(modifier = finalModifier)
    )
}

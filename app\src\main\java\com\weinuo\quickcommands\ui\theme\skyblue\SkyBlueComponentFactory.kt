package com.weinuo.quickcommands.ui.theme.skyblue

import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.weinuo.quickcommands.ui.components.integrated.*
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueFAB
import com.weinuo.quickcommands.ui.theme.system.ComponentFactory
import com.weinuo.quickcommands.ui.theme.system.HazeAwareComponentFactory
import com.weinuo.quickcommands.ui.theme.config.*
import dev.chrisbanes.haze.HazeState

/**
 * 天空蓝主题组件工厂
 *
 * 创建整合设计风格的UI组件
 * 特点：统一的视觉体验，支持模糊效果
 */
class SkyBlueComponentFactory : HazeAwareComponentFactory {

    /**
     * 创建底部导航组件
     *
     * 整合设计风格的底部导航，支持模糊效果
     */
    override fun createBottomNavigation(): @Composable (BottomNavigationConfig) -> Unit = { config ->
        // 注意：这个方法不支持模糊效果，建议使用createBottomNavigationWithHaze
        IntegratedBottomNavigation(config, HazeState())
    }

    /**
     * 创建支持模糊效果的底部导航组件
     */
    override fun createBottomNavigationWithHaze(): @Composable (BottomNavigationConfig, HazeState) -> Unit = { config, hazeState ->
        IntegratedBottomNavigation(config, hazeState)
    }



    /**
     * 创建卡片组件
     * 
     * 整合设计风格的卡片，无阴影，大圆角
     */
    override fun createCard(): @Composable (CardConfig) -> Unit = { config ->
        IntegratedCard(config)
    }

    /**
     * 创建按钮组件
     * 
     * 整合设计风格的按钮，无阴影，大圆角
     */
    override fun createButton(): @Composable (ButtonConfig) -> Unit = { config ->
        IntegratedButton(config)
    }

    /**
     * 创建文本输入框组件
     *
     * 整合设计风格的文本输入框
     */
    override fun createTextField(): @Composable (TextFieldConfig) -> Unit = { config ->
        IntegratedTextField(config)
    }

    /**
     * 创建搜索框组件
     *
     * 整合设计风格的搜索框
     */
    override fun createSearchTextField(): @Composable (SearchTextFieldConfig) -> Unit = { config ->
        IntegratedSearchTextField(config)
    }

    /**
     * 创建浮动操作按钮组件
     *
     * 天空蓝主题专用FAB - 使用SkyBlueFAB组件
     * 特点：56dp完美圆形、天空蓝背景、白色图标、无阴影设计、流畅交互动画
     */
    override fun createFloatingActionButton(): @Composable (FloatingActionButtonConfig) -> Unit = { config ->
        SkyBlueFAB(config)
    }

    /**
     * 创建对话框组件
     *
     * 整合设计风格的对话框 - 使用Material 3默认组件
     */
    override fun createDialog(): @Composable (DialogConfig) -> Unit = { config ->
        androidx.compose.ui.window.Dialog(
            onDismissRequest = config.onDismissRequest,
            properties = config.properties as? androidx.compose.ui.window.DialogProperties ?: androidx.compose.ui.window.DialogProperties(),
            content = config.content
        )
    }

    /**
     * 创建底部弹窗组件
     *
     * 整合设计风格的底部弹窗 - 简化实现
     */
    override fun createBottomSheet(): @Composable (BottomSheetConfig) -> Unit = { config ->
        // 简化实现：使用基本的Surface组件
        androidx.compose.material3.Surface(
            modifier = config.modifier,
            shape = androidx.compose.foundation.shape.RoundedCornerShape(28.dp),
            color = config.containerColor ?: androidx.compose.material3.MaterialTheme.colorScheme.surface,
            contentColor = config.contentColor ?: androidx.compose.material3.MaterialTheme.colorScheme.onSurface,
            tonalElevation = config.tonalElevation ?: 0.dp
        ) {
            config.content()
        }
    }

    /**
     * 创建开关组件
     *
     * 天空蓝主题专用开关 - 使用iOS风格的SkyBlueSwitch
     */
    override fun createSwitch(): @Composable (SwitchConfig) -> Unit = { config ->
        com.weinuo.quickcommands.ui.components.skyblue.SkyBlueSwitch(
            checked = config.checked,
            onCheckedChange = config.onCheckedChange,
            modifier = config.modifier,
            enabled = config.enabled
        )
    }

    /**
     * 创建滑块组件
     *
     * 整合设计风格的滑块 - 使用Material 3默认组件
     */
    override fun createSlider(): @Composable (SliderConfig) -> Unit = { config ->
        androidx.compose.material3.Slider(
            value = config.value,
            onValueChange = config.onValueChange,
            modifier = config.modifier,
            enabled = config.enabled,
            valueRange = config.valueRange,
            steps = config.steps,
            onValueChangeFinished = config.onValueChangeFinished,
            colors = config.colors as? androidx.compose.material3.SliderColors ?: androidx.compose.material3.SliderDefaults.colors()
        )
    }

    /**
     * 创建复选框组件
     *
     * 整合设计风格的复选框 - 使用Material 3默认组件
     */
    override fun createCheckbox(): @Composable (CheckboxConfig) -> Unit = { config ->
        androidx.compose.material3.Checkbox(
            checked = config.checked,
            onCheckedChange = config.onCheckedChange,
            modifier = config.modifier,
            enabled = config.enabled,
            colors = config.colors as? androidx.compose.material3.CheckboxColors ?: androidx.compose.material3.CheckboxDefaults.colors()
        )
    }

    /**
     * 创建单选按钮组件
     *
     * 天空蓝主题专用单选按钮 - 使用SkyBlueRadioButton组件
     * 特点：圆形实心蓝色勾选样式，选中时显示白色勾选标记
     */
    override fun createRadioButton(): @Composable (RadioButtonConfig) -> Unit = { config ->
        com.weinuo.quickcommands.ui.components.skyblue.SkyBlueRadioButton(
            config = config,
            modifier = config.modifier
        )
    }

    /**
     * 创建进度指示器组件
     *
     * 整合设计风格的进度指示器 - 使用Material 3默认组件
     */
    override fun createProgressIndicator(): @Composable (ProgressIndicatorConfig) -> Unit = { config ->
        if (config.progress != null) {
            androidx.compose.material3.LinearProgressIndicator(
                progress = { config.progress },
                modifier = config.modifier,
                color = config.color ?: androidx.compose.material3.MaterialTheme.colorScheme.primary,
                trackColor = config.trackColor ?: androidx.compose.material3.MaterialTheme.colorScheme.surfaceVariant,
                strokeCap = config.strokeCap as? androidx.compose.ui.graphics.StrokeCap ?: androidx.compose.ui.graphics.StrokeCap.Round
            )
        } else {
            androidx.compose.material3.LinearProgressIndicator(
                modifier = config.modifier,
                color = config.color ?: androidx.compose.material3.MaterialTheme.colorScheme.primary,
                trackColor = config.trackColor ?: androidx.compose.material3.MaterialTheme.colorScheme.surfaceVariant,
                strokeCap = config.strokeCap as? androidx.compose.ui.graphics.StrokeCap ?: androidx.compose.ui.graphics.StrokeCap.Round
            )
        }
    }

    /**
     * 创建分割线组件
     *
     * 整合设计风格的分割线 - 使用更细的分割线
     */
    override fun createDivider(): @Composable (DividerConfig) -> Unit = { config ->
        androidx.compose.material3.HorizontalDivider(
            modifier = config.modifier,
            thickness = config.thickness ?: 0.5.dp, // 使用更细的分割线，默认0.5dp
            color = config.color ?: androidx.compose.material3.MaterialTheme.colorScheme.outlineVariant
        )
    }

    /**
     * 创建芯片组件
     *
     * 整合设计风格的芯片 - 使用Material 3默认组件
     */
    override fun createChip(): @Composable (ChipConfig) -> Unit = { config ->
        androidx.compose.material3.AssistChip(
            onClick = config.onClick,
            label = config.label,
            modifier = config.modifier,
            enabled = config.enabled,
            leadingIcon = config.leadingIcon,
            trailingIcon = config.trailingIcon,
            colors = config.colors as? androidx.compose.material3.ChipColors ?: androidx.compose.material3.AssistChipDefaults.assistChipColors(),
            elevation = config.elevation as? androidx.compose.material3.ChipElevation ?: androidx.compose.material3.AssistChipDefaults.assistChipElevation(),
            border = null, // 简化实现，不使用边框
            shape = config.shape as? androidx.compose.ui.graphics.Shape ?: androidx.compose.material3.AssistChipDefaults.shape
        )
    }

    /**
     * 创建标签页组件
     *
     * 整合设计风格的标签页 - 使用Material 3默认组件
     */
    override fun createTabs(): @Composable (TabsConfig) -> Unit = { config ->
        androidx.compose.material3.TabRow(
            selectedTabIndex = config.selectedTabIndex,
            modifier = config.modifier,
            containerColor = config.containerColor ?: androidx.compose.material3.MaterialTheme.colorScheme.surface,
            contentColor = config.contentColor ?: androidx.compose.material3.MaterialTheme.colorScheme.onSurface,
            indicator = { tabPositions ->
                if (config.selectedTabIndex < tabPositions.size) {
                    androidx.compose.material3.TabRowDefaults.SecondaryIndicator()
                }
            },
            divider = config.divider ?: {}
        ) {
            // 渲染标签页
            config.tabs.forEachIndexed { index, tab ->
                androidx.compose.material3.Tab(
                    selected = config.selectedTabIndex == index,
                    onClick = tab.onClick,
                    modifier = tab.modifier,
                    enabled = tab.enabled,
                    text = tab.text,
                    icon = tab.icon,
                    selectedContentColor = tab.selectedContentColor ?: androidx.compose.material3.MaterialTheme.colorScheme.primary,
                    unselectedContentColor = tab.unselectedContentColor ?: androidx.compose.material3.MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }

    /**
     * 创建列表项组件
     *
     * 整合设计风格的列表项 - 使用Material 3默认组件
     */
    override fun createListItem(): @Composable (ListItemConfig) -> Unit = { config ->
        androidx.compose.material3.ListItem(
            headlineContent = config.headlineContent,
            modifier = config.modifier,
            overlineContent = config.overlineContent,
            supportingContent = config.supportingContent,
            leadingContent = config.leadingContent,
            trailingContent = config.trailingContent,
            colors = config.colors as? androidx.compose.material3.ListItemColors ?: androidx.compose.material3.ListItemDefaults.colors(),
            tonalElevation = config.tonalElevation ?: 0.dp,
            shadowElevation = config.shadowElevation ?: 0.dp
        )
    }

    /**
     * 创建图标按钮组件
     *
     * 整合设计风格的图标按钮 - 使用Material 3默认组件
     */
    override fun createIconButton(): @Composable (IconButtonConfig) -> Unit = { config ->
        androidx.compose.material3.IconButton(
            onClick = config.onClick,
            modifier = config.modifier,
            enabled = config.enabled,
            colors = config.colors as? androidx.compose.material3.IconButtonColors ?: androidx.compose.material3.IconButtonDefaults.iconButtonColors(),
            content = config.content
        )
    }

    /**
     * 创建徽章组件
     *
     * 整合设计风格的徽章 - 使用Material 3默认组件
     */
    override fun createBadge(): @Composable (BadgeConfig) -> Unit = { config ->
        androidx.compose.material3.Badge(
            modifier = config.modifier,
            containerColor = config.containerColor ?: androidx.compose.material3.MaterialTheme.colorScheme.error,
            contentColor = config.contentColor ?: androidx.compose.material3.MaterialTheme.colorScheme.onError
        ) {
            config.content?.invoke()
        }
    }

    /**
     * 创建工具提示组件
     *
     * 整合设计风格的工具提示 - 使用Material 3默认组件
     */
    override fun createTooltip(): @Composable (TooltipConfig) -> Unit = { config ->
        // 简化实现：直接显示内容，不使用复杂的Tooltip API
        androidx.compose.foundation.layout.Box(
            modifier = config.modifier
        ) {
            config.content()
        }
    }
}

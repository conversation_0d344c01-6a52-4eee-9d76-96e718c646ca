package com.weinuo.quickcommands.ui.theme.skyblue

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.config.BlurConfiguration

/**
 * 天空蓝主题对话框配置管理器
 *
 * 负责管理对话框的样式配置，包括：
 * - 圆角大小（从全局设置读取）
 * - 模糊效果开关（从全局设置读取）
 * - 模糊强度（从全局设置读取）
 *
 * 特点：
 * - 动态读取全局设置，支持实时更新
 * - 与现有的HazeManager集成
 * - 遵循天空蓝主题的设计原则
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
class SkyBlueDialogConfigManager(
    private val settingsRepository: SettingsRepository
) {

    /**
     * 对话框样式配置数据类
     *
     * @property cornerRadius 圆角大小
     * @property shape 形状（基于圆角大小生成）
     * @property blurEnabled 模糊效果开关
     * @property blurIntensity 模糊强度（0.0-1.0）
     * @property blurRadius 实际模糊半径
     */
    data class DialogStyleConfig(
        val cornerRadius: Dp,
        val shape: Shape,
        val blurEnabled: Boolean,
        val blurIntensity: Float,
        val blurRadius: Dp
    ) {
        companion object {
            /**
             * 创建默认配置
             */
            fun default(): DialogStyleConfig {
                val defaultCornerRadius = 28.dp
                return DialogStyleConfig(
                    cornerRadius = defaultCornerRadius,
                    shape = RoundedCornerShape(defaultCornerRadius),
                    blurEnabled = true,
                    blurIntensity = 0.6f,
                    blurRadius = 25.dp * 0.6f
                )
            }
        }
    }

    /**
     * 获取动态对话框样式配置
     *
     * 从全局设置中读取用户自定义的对话框样式配置
     * 支持实时更新，当设置变化时会自动反映到UI中
     *
     * @return 对话框样式配置
     */
    @Composable
    fun getDynamicDialogStyleConfig(): DialogStyleConfig {
        val globalSettings by settingsRepository.globalSettings.collectAsState()

        val cornerRadius = globalSettings.dialogCornerRadius.dp
        val blurEnabled = globalSettings.dialogBlurEnabled
        val blurIntensity = globalSettings.dialogBlurIntensity.coerceIn(0f, 1f)
        
        // 基于BlurConfiguration的默认最大半径计算实际模糊半径
        val maxBlurRadius = 25.dp // 使用固定的最大模糊半径
        val actualBlurRadius = maxBlurRadius * blurIntensity

        return DialogStyleConfig(
            cornerRadius = cornerRadius,
            shape = RoundedCornerShape(cornerRadius),
            blurEnabled = blurEnabled,
            blurIntensity = blurIntensity,
            blurRadius = actualBlurRadius
        )
    }

    /**
     * 更新对话框圆角大小
     *
     * @param cornerRadius 新的圆角大小（dp）
     */
    fun updateCornerRadius(cornerRadius: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            dialogCornerRadius = cornerRadius.coerceIn(0, 50)
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新对话框模糊效果开关
     *
     * @param enabled 是否启用模糊效果
     */
    fun updateBlurEnabled(enabled: Boolean) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            dialogBlurEnabled = enabled
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新对话框模糊强度
     *
     * @param intensity 模糊强度（0.0-1.0）
     */
    fun updateBlurIntensity(intensity: Float) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            dialogBlurIntensity = intensity.coerceIn(0f, 1f)
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            dialogCornerRadius = 28,
            dialogBlurEnabled = true,
            dialogBlurIntensity = 0.6f
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 获取当前配置的摘要信息
     *
     * @return 配置摘要字符串
     */
    @Composable
    fun getConfigSummary(): String {
        val config = getDynamicDialogStyleConfig()
        return buildString {
            append("圆角: ${config.cornerRadius.value.toInt()}dp")
            append(" | ")
            append("模糊: ${if (config.blurEnabled) "开启" else "关闭"}")
            if (config.blurEnabled) {
                append(" (${(config.blurIntensity * 100).toInt()}%)")
            }
        }
    }

    companion object {
        /**
         * 圆角大小的有效范围
         */
        val CORNER_RADIUS_RANGE = 0..50

        /**
         * 模糊强度的有效范围
         */
        val BLUR_INTENSITY_RANGE = 0f..1f

        /**
         * 预设的圆角大小
         */
        object CornerRadiusPresets {
            const val SMALL = 12
            const val MEDIUM = 20
            const val LARGE = 28
            const val EXTRA_LARGE = 36
        }

        /**
         * 预设的模糊强度
         */
        object BlurIntensityPresets {
            const val LIGHT = 0.3f
            const val MEDIUM = 0.6f
            const val STRONG = 0.8f
        }
    }
}

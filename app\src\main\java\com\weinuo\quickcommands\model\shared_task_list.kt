package com.weinuo.quickcommands.model

import android.content.Context
import android.media.AudioManager

import java.util.UUID
import android.preference.PreferenceManager


/**
 * 共享任务列表
 *
 * 此文件定义了应用中可用的任务类型，以及它们的配置界面。
 * 新的任务类型应该在此文件中定义，并注册到 SharedTaskRegistry 中。
 */

/**
 * 常用Shizuku命令
 *
 * @property description 命令描述
 * @property command 命令内容
 * @property category 命令分类
 */
data class CommonShizukuCommand(
    val description: String,
    val command: String,
    val category: String = ""
)

/**
 * 常用Shizuku命令分类
 */
object ShizukuCommandCategories {
    const val APP_MANAGEMENT = "shizuku_category_app_management"
    const val NETWORK = "shizuku_category_network"
    const val SYSTEM = "shizuku_category_system"
    const val KEY_SIMULATION = "shizuku_category_key_simulation"
    const val SCREEN_OPERATION = "shizuku_category_screen_operation"
}

/**
 * 常用Shizuku命令列表
 */
val commonShizukuCommands = listOf(
    // 应用程序命令
    CommonShizukuCommand("强制停止应用", "am force-stop [package_name]", ShizukuCommandCategories.APP_MANAGEMENT),
    CommonShizukuCommand("清除应用数据", "pm clear [package_name]", ShizukuCommandCategories.APP_MANAGEMENT),
    CommonShizukuCommand("禁用应用", "pm disable [package_name]", ShizukuCommandCategories.APP_MANAGEMENT),
    CommonShizukuCommand("启用应用", "pm enable [package_name]", ShizukuCommandCategories.APP_MANAGEMENT),
    CommonShizukuCommand("卸载应用", "pm uninstall [package_name]", ShizukuCommandCategories.APP_MANAGEMENT),
    CommonShizukuCommand("打开应用", "am start -n [package_name]/[activity_name]", ShizukuCommandCategories.APP_MANAGEMENT),
    CommonShizukuCommand("杀死所有后台进程", "am kill-all", ShizukuCommandCategories.APP_MANAGEMENT),

    // 网络相关命令
    CommonShizukuCommand("关闭移动数据", "svc data disable", ShizukuCommandCategories.NETWORK),
    CommonShizukuCommand("开启移动数据", "svc data enable", ShizukuCommandCategories.NETWORK),
    CommonShizukuCommand("关闭蓝牙", "settings put global bluetooth_on 0", ShizukuCommandCategories.NETWORK),
    CommonShizukuCommand("开启蓝牙", "settings put global bluetooth_on 1", ShizukuCommandCategories.NETWORK),
    CommonShizukuCommand("开启飞行模式", "settings put global airplane_mode_on 1; am broadcast -a android.intent.action.AIRPLANE_MODE --ez state true", ShizukuCommandCategories.NETWORK),
    CommonShizukuCommand("关闭飞行模式", "settings put global airplane_mode_on 0; am broadcast -a android.intent.action.AIRPLANE_MODE --ez state false", ShizukuCommandCategories.NETWORK),

    // 系统控制命令
    CommonShizukuCommand("重启设备", "reboot", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("关机", "reboot -p", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("进入Recovery模式", "reboot recovery", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("进入Bootloader模式", "reboot bootloader", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("清除系统缓存", "sync; echo 3 > /proc/sys/vm/drop_caches", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("设置亮度为最大", "settings put system screen_brightness 255", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("设置亮度为一半", "settings put system screen_brightness 128", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("设置亮度为最低", "settings put system screen_brightness 10", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("开启自动亮度", "settings put system screen_brightness_mode 1", ShizukuCommandCategories.SYSTEM),
    CommonShizukuCommand("关闭自动亮度", "settings put system screen_brightness_mode 0", ShizukuCommandCategories.SYSTEM),

    // 按键模拟命令（适合快捷指令应用）
    CommonShizukuCommand("电源键（开关屏幕）", "input keyevent 26", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("返回键", "input keyevent 4", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("主页键", "input keyevent 3", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("音量增加键", "input keyevent 24", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("音量减小键", "input keyevent 25", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("静音键", "input keyevent 164", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("相机键", "input keyevent 27", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("菜单键", "input keyevent 82", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("多任务键", "input keyevent 187", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("锁屏键", "input keyevent 26", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("播放/暂停", "input keyevent 85", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("下一曲", "input keyevent 87", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("上一曲", "input keyevent 88", ShizukuCommandCategories.KEY_SIMULATION),
    CommonShizukuCommand("截屏", "input keyevent 120", ShizukuCommandCategories.KEY_SIMULATION),

    // 屏幕操作命令
    CommonShizukuCommand("点击屏幕", "input tap [x] [y]", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("滑动屏幕", "input swipe [x1] [y1] [x2] [y2] [duration_ms]", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("输入文本", "input text [text]", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("向上滑动（上滑解锁）", "input swipe 500 1000 500 200 100", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("向下滑动（下拉通知栏）", "input swipe 500 200 500 1000 100", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("向左滑动", "input swipe 800 500 200 500 100", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("向右滑动", "input swipe 200 500 800 500 100", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("长按屏幕", "input swipe 500 500 500 500 1000", ShizukuCommandCategories.SCREEN_OPERATION),
    CommonShizukuCommand("双击屏幕", "input tap 500 500 && sleep 0.1 && input tap 500 500", ShizukuCommandCategories.SCREEN_OPERATION)
)

/**
 * 任务接口
 * 所有任务类型都应该实现此接口
 */
interface SharedTask {
    val id: String
    val type: String
    val displayName: String

    /**
     * 返回任务的简短描述，用于在任务列表中显示
     */
    fun getDescription(): String
}





/**
 * 音量任务操作类型
 */
enum class VolumeOperation(val displayName: String) {
    SPEAKERPHONE_CONTROL("免提通话开/关"),
    VIBRATION_MODE_CONTROL("振动模式启用/禁用"),
    SHOW_VOLUME_POPUP("显示音量弹出窗口"),
    DO_NOT_DISTURB_MODE("勿扰模式"),
    VOLUME_CHANGE("音量变化"),
    VOLUME_ADJUST("音量加/减")
}

/**
 * 免提通话操作类型
 */
enum class SpeakerphoneOperation(val displayName: String) {
    TURN_ON("开启"),
    TURN_OFF("关闭"),
    TOGGLE("切换")
}

/**
 * 振动模式类型
 */
enum class VibrationModeType(val displayName: String) {
    SILENT_WITH_VIBRATION("静音（振动开）"),
    NORMAL_WITHOUT_VIBRATION("标准（振动关闭）"),
    RING_WITH_VIBRATION("响铃时振动"),
    RING_WITHOUT_VIBRATION("响铃时振动关闭"),
    RING_VIBRATION_TOGGLE("响铃时振动切换")
}

/**
 * 音量弹出窗口类型
 */
enum class VolumePopupType(val displayName: String, val streamType: Int) {
    ALARM("闹钟", AudioManager.STREAM_ALARM),
    MEDIA_MUSIC("媒体/音乐", AudioManager.STREAM_MUSIC),
    NOTIFICATION("通知", AudioManager.STREAM_NOTIFICATION),
    RING("铃声", AudioManager.STREAM_RING),
    SYSTEM("系统声音", AudioManager.STREAM_SYSTEM),
    VOICE_CALL("语音通话", AudioManager.STREAM_VOICE_CALL),
    BLUETOOTH("蓝牙声音", 6) // AudioManager.STREAM_BLUETOOTH_SCO
}



/**
 * 勿扰模式类型
 */
enum class DoNotDisturbMode(val displayName: String) {
    NORMAL("正常"),
    CALLS_ONLY("仅限来电响铃"),
    COMPLETE_SILENCE("完全静音"),
    ALARMS_ONLY("仅限闹钟")
}

/**
 * 音量变化音频流类型（扩展版）
 */
enum class VolumeStreamType(val displayName: String, val streamType: Int) {
    ALARM("闹钟", AudioManager.STREAM_ALARM),
    MEDIA_MUSIC("媒体/音乐", AudioManager.STREAM_MUSIC),
    NOTIFICATION("通知", AudioManager.STREAM_NOTIFICATION),
    RING("铃声", AudioManager.STREAM_RING),
    SYSTEM("系统声音", AudioManager.STREAM_SYSTEM),
    VOICE_CALL("语音通话", AudioManager.STREAM_VOICE_CALL),
    BLUETOOTH("蓝牙声音", 6), // AudioManager.STREAM_BLUETOOTH_SCO
    ACCESSIBILITY("无障碍", AudioManager.STREAM_ACCESSIBILITY)
}

/**
 * 音量设置模式
 */
enum class VolumeMode(val displayName: String) {
    PERCENTAGE("百分比"),
    ABSOLUTE("绝对值")
}

/**
 * 音量调节操作类型
 */
enum class VolumeAdjustOperation(val displayName: String) {
    VOLUME_UP("音量加"),
    VOLUME_DOWN("音量减")
}

/**
 * 音量任务
 *
 * @property id 任务唯一标识
 * @property operation 音量操作类型
 * @property speakerphoneOperation 免提通话操作
 * @property vibrationModeType 振动模式类型
 * @property volumePopupType 音量弹出窗口类型
 * @property doNotDisturbMode 勿扰模式类型
 * @property disableVibrationWhenSilent 完全静音时是否同时禁用振动
 * @property volumeStreamType 音量变化音频流类型
 * @property volumeMode 音量设置模式
 * @property volumeValue 音量值
 * @property volumeAdjustOperation 音量调节操作类型
 * @property volumeAdjustStreamType 音量调节音频流类型
 */
data class VolumeTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: VolumeOperation = VolumeOperation.VOLUME_CHANGE,

    // 免提通话参数
    val speakerphoneOperation: SpeakerphoneOperation = SpeakerphoneOperation.TOGGLE,

    // 振动模式参数
    val vibrationModeType: VibrationModeType = VibrationModeType.SILENT_WITH_VIBRATION,

    // 音量弹出窗口参数
    val volumePopupType: VolumePopupType = VolumePopupType.MEDIA_MUSIC,

    // 勿扰模式参数
    val doNotDisturbMode: DoNotDisturbMode = DoNotDisturbMode.NORMAL,
    val disableVibrationWhenSilent: Boolean = false,

    // 音量变化参数
    val volumeStreamType: VolumeStreamType = VolumeStreamType.MEDIA_MUSIC,
    val volumeMode: VolumeMode = VolumeMode.PERCENTAGE,
    val volumeValue: Int = 50,

    // 音量调节参数
    val volumeAdjustOperation: VolumeAdjustOperation = VolumeAdjustOperation.VOLUME_UP,
    val volumeAdjustStreamType: VolumeStreamType = VolumeStreamType.MEDIA_MUSIC
) : SharedTask {
    override val type: String = "volume"
    override val displayName: String = "音量"

    override fun getDescription(): String {
        return when (operation) {
            VolumeOperation.SPEAKERPHONE_CONTROL -> "免提通话: ${speakerphoneOperation.displayName}"
            VolumeOperation.VIBRATION_MODE_CONTROL -> "振动模式: ${vibrationModeType.displayName}"
            VolumeOperation.SHOW_VOLUME_POPUP -> "显示音量弹出窗口: ${volumePopupType.displayName}"
            VolumeOperation.DO_NOT_DISTURB_MODE -> {
                val vibrationText = if (doNotDisturbMode == DoNotDisturbMode.COMPLETE_SILENCE && disableVibrationWhenSilent) {
                    " (禁用振动)"
                } else ""
                "勿扰模式: ${doNotDisturbMode.displayName}${vibrationText}"
            }
            VolumeOperation.VOLUME_CHANGE -> {
                val modeText = if (volumeMode == VolumeMode.PERCENTAGE) "%" else ""
                "音量变化: ${volumeStreamType.displayName} ${volumeValue}${modeText}"
            }
            VolumeOperation.VOLUME_ADJUST -> "音量调节: ${volumeAdjustStreamType.displayName} ${volumeAdjustOperation.displayName}"
        }
    }

    /**
     * 验证音量任务数据的完整性
     *
     * @return 验证是否通过
     */
    fun validateData(): Boolean {
        // 验证ID不为空
        if (id.isBlank()) {
            return false
        }

        // 验证音量值在合理范围内
        if (volumeValue < 0 || volumeValue > 100) {
            return false
        }

        // 根据操作类型验证相关字段
        return when (operation) {
            VolumeOperation.VOLUME_CHANGE -> {
                // 音量变化操作需要有效的音量值
                volumeValue in 0..100
            }
            VolumeOperation.VOLUME_ADJUST -> {
                // 音量调节操作需要有效的调节类型
                true // 枚举值已经保证有效性
            }
            else -> true // 其他操作类型的枚举值已经保证有效性
        }
    }
}





/**
 * 日期与时间任务子类型
 */
enum class DateTimeTaskType(val displayName: String) {
    STOPWATCH("秒表"),
    ALARM("闹钟"),
    SYSTEM_ALARM_RINGTONE("系统闹钟铃声"),
    VOICE_TIME_ANNOUNCEMENT("语音报时")
}

/**
 * 秒表操作类型
 */
enum class StopwatchOperation(val displayName: String) {
    START("开始"),
    PAUSE("暂停"),
    STOP("停止"),
    LAP("计圈"),
    RESET("重置"),
    RESET_AND_START("重置并重启")
}

/**
 * 闹钟操作类型
 */
enum class AlarmOperation(val displayName: String) {
    SET_ALARM("设置闹钟"),
    CANCEL_ALARM("取消闹钟"),
    SNOOZE_ALARM("延迟闹钟"),
    DISMISS_ALARM("关闭闹钟")
}

/**
 * 闹钟时间类型
 */
enum class AlarmTimeType(val displayName: String) {
    ABSOLUTE("固定时间"),
    RELATIVE("相对时间")
}

/**
 * 秒表数据类
 */
data class Stopwatch(
    val id: String = UUID.randomUUID().toString(),
    val name: String = "",
    val startTime: Long = 0L,
    val elapsedTime: Long = 0L,
    val isRunning: Boolean = false
)

/**
 * 秒表管理器
 * 负责管理所有秒表的状态和操作
 *
 * 注意：已重构为使用原生数据类型存储，避免JSON序列化问题
 */
object StopwatchManager {
    private const val PREFS_KEY_STOPWATCH_COUNT = "stopwatch_count"
    private const val PREFS_KEY_STOPWATCH_PREFIX = "stopwatch_"

    /**
     * 获取所有秒表（使用原生数据类型存储）
     */
    fun getAllStopwatches(context: Context): List<Stopwatch> {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        val count = prefs.getInt(PREFS_KEY_STOPWATCH_COUNT, 0)

        val stopwatches = mutableListOf<Stopwatch>()
        for (index in 0 until count) {
            val prefix = "${PREFS_KEY_STOPWATCH_PREFIX}${index}_"

            val id = prefs.getString("${prefix}id", "") ?: ""
            val name = prefs.getString("${prefix}name", "") ?: ""
            val isRunning = prefs.getBoolean("${prefix}is_running", false)
            val startTime = prefs.getLong("${prefix}start_time", 0L)
            val elapsedTime = prefs.getLong("${prefix}elapsed_time", 0L)

            if (id.isNotEmpty()) {
                stopwatches.add(Stopwatch(id, name, startTime, elapsedTime, isRunning))
            }
        }

        return stopwatches
    }

    /**
     * 保存所有秒表（使用原生数据类型存储）
     */
    private fun saveStopwatches(context: Context, stopwatches: List<Stopwatch>) {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        val editor = prefs.edit()

        // 清除旧数据
        val oldCount = prefs.getInt(PREFS_KEY_STOPWATCH_COUNT, 0)
        for (index in 0 until oldCount) {
            val prefix = "${PREFS_KEY_STOPWATCH_PREFIX}${index}_"
            editor.remove("${prefix}id")
            editor.remove("${prefix}name")
            editor.remove("${prefix}is_running")
            editor.remove("${prefix}start_time")
            editor.remove("${prefix}elapsed_time")
        }

        // 保存新数据
        editor.putInt(PREFS_KEY_STOPWATCH_COUNT, stopwatches.size)
        stopwatches.forEachIndexed { index, stopwatch ->
            val prefix = "${PREFS_KEY_STOPWATCH_PREFIX}${index}_"
            editor.putString("${prefix}id", stopwatch.id)
            editor.putString("${prefix}name", stopwatch.name)
            editor.putBoolean("${prefix}is_running", stopwatch.isRunning)
            editor.putLong("${prefix}start_time", stopwatch.startTime)
            editor.putLong("${prefix}elapsed_time", stopwatch.elapsedTime)
        }

        editor.apply()
    }

    /**
     * 创建新秒表
     */
    fun createStopwatch(context: Context, name: String): Stopwatch {
        val stopwatches = getAllStopwatches(context).toMutableList()
        val newStopwatch = Stopwatch(name = name)
        stopwatches.add(newStopwatch)
        saveStopwatches(context, stopwatches)
        return newStopwatch
    }

    /**
     * 根据ID获取秒表
     */
    fun getStopwatchById(context: Context, id: String): Stopwatch? {
        return getAllStopwatches(context).find { it.id == id }
    }

    /**
     * 开始秒表
     */
    fun startStopwatch(context: Context, id: String): Boolean {
        val stopwatches = getAllStopwatches(context).toMutableList()
        val index = stopwatches.indexOfFirst { it.id == id }
        if (index == -1) return false

        val stopwatch = stopwatches[index]
        val currentTime = System.currentTimeMillis()

        stopwatches[index] = stopwatch.copy(
            startTime = currentTime,
            isRunning = true
        )
        saveStopwatches(context, stopwatches)
        return true
    }

    /**
     * 暂停秒表
     */
    fun pauseStopwatch(context: Context, id: String): Boolean {
        val stopwatches = getAllStopwatches(context).toMutableList()
        val index = stopwatches.indexOfFirst { it.id == id }
        if (index == -1) return false

        val stopwatch = stopwatches[index]
        if (!stopwatch.isRunning) return false

        val currentTime = System.currentTimeMillis()
        val additionalElapsed = currentTime - stopwatch.startTime

        stopwatches[index] = stopwatch.copy(
            elapsedTime = stopwatch.elapsedTime + additionalElapsed,
            isRunning = false
        )
        saveStopwatches(context, stopwatches)
        return true
    }

    /**
     * 停止秒表
     */
    fun stopStopwatch(context: Context, id: String): Boolean {
        val stopwatches = getAllStopwatches(context).toMutableList()
        val index = stopwatches.indexOfFirst { it.id == id }
        if (index == -1) return false

        val stopwatch = stopwatches[index]
        if (!stopwatch.isRunning) return true // 已经停止

        val currentTime = System.currentTimeMillis()
        val additionalElapsed = currentTime - stopwatch.startTime

        stopwatches[index] = stopwatch.copy(
            elapsedTime = stopwatch.elapsedTime + additionalElapsed,
            isRunning = false
        )
        saveStopwatches(context, stopwatches)
        return true
    }

    /**
     * 记录圈数（计圈）
     */
    fun lapStopwatch(context: Context, id: String): Boolean {
        val stopwatch = getStopwatchById(context, id) ?: return false
        if (!stopwatch.isRunning) return false

        // 获取当前经过时间
        val currentElapsedTime = getStopwatchElapsedTime(context, id)

        // 这里可以扩展为记录圈数到单独的存储中
        // 目前只是简单返回成功，表示圈数已记录
        android.util.Log.d("StopwatchManager", "Lap recorded for stopwatch $id: ${currentElapsedTime}ms")

        return true
    }

    /**
     * 重置秒表
     */
    fun resetStopwatch(context: Context, id: String): Boolean {
        val stopwatches = getAllStopwatches(context).toMutableList()
        val index = stopwatches.indexOfFirst { it.id == id }
        if (index == -1) return false

        stopwatches[index] = stopwatches[index].copy(
            startTime = 0L,
            elapsedTime = 0L,
            isRunning = false
        )
        saveStopwatches(context, stopwatches)
        return true
    }

    /**
     * 重置并重启秒表
     */
    fun resetAndStartStopwatch(context: Context, id: String): Boolean {
        val stopwatches = getAllStopwatches(context).toMutableList()
        val index = stopwatches.indexOfFirst { it.id == id }
        if (index == -1) return false

        val currentTime = System.currentTimeMillis()
        stopwatches[index] = stopwatches[index].copy(
            startTime = currentTime,
            elapsedTime = 0L,
            isRunning = true
        )
        saveStopwatches(context, stopwatches)
        return true
    }

    /**
     * 删除秒表
     */
    fun deleteStopwatch(context: Context, id: String): Boolean {
        val stopwatches = getAllStopwatches(context).toMutableList()
        val removed = stopwatches.removeIf { it.id == id }
        if (removed) {
            saveStopwatches(context, stopwatches)
        }
        return removed
    }

    /**
     * 获取秒表当前经过的时间（毫秒）
     */
    fun getStopwatchElapsedTime(context: Context, id: String): Long {
        val stopwatch = getStopwatchById(context, id) ?: return 0L
        return if (stopwatch.isRunning) {
            val currentTime = System.currentTimeMillis()
            stopwatch.elapsedTime + (currentTime - stopwatch.startTime)
        } else {
            stopwatch.elapsedTime
        }
    }
}

/**
 * 日期与时间任务
 *
 * @property id 任务唯一标识
 * @property taskType 任务子类型（秒表或闹钟）
 * @property stopwatchId 秒表ID（秒表类型使用）
 * @property stopwatchName 秒表名称（创建新秒表时使用）
 * @property stopwatchOperation 秒表操作（秒表类型使用）
 * @property requireTask 是否需要完成任务才能关闭闹钟（点击指定次数黑色小点）
 * @property clicksRequired 需要点击的次数（默认5次，范围1-50次）
 * @property enableSound 是否启用铃声
 * @property reminderMessage 提醒内容
 * @property enableVibration 是否启用震动
 * @property vibrationMode 震动模式
 * @property vibrationIntensity 震动强度
 */
data class DateTimeTask(
    override val id: String = UUID.randomUUID().toString(),
    val taskType: DateTimeTaskType = DateTimeTaskType.STOPWATCH,

    // 秒表相关属性
    val stopwatchId: String = "",
    val stopwatchName: String = "",
    val stopwatchOperation: StopwatchOperation = StopwatchOperation.START,
    val selectedStopwatchId: String = "",  // 添加缺失的属性
    val newStopwatchName: String = "",     // 添加缺失的属性

    // 闹钟相关属性
    val alarmOperation: AlarmOperation = AlarmOperation.SET_ALARM,  // 添加缺失的属性
    val alarmTimeType: AlarmTimeType = AlarmTimeType.ABSOLUTE,     // 闹钟时间类型：绝对时间或相对时间
    val alarmHour: Int = 8,                // 绝对时间：小时 (0-23)
    val alarmMinute: Int = 0,              // 绝对时间：分钟 (0-59)
    val alarmRelativeHours: Int = 0,       // 相对时间：小时数 (0-23)
    val alarmRelativeMinutes: Int = 5,     // 相对时间：分钟数 (1-59)
    val alarmMessage: String = "",         // 添加缺失的属性
    val alarmDays: Set<Int> = emptySet(),  // 添加缺失的属性
    val alarmVibrate: Boolean = true,      // 添加缺失的属性
    val alarmSkipUI: Boolean = false,      // 添加缺失的属性
    val clicksCount: Int = 1,              // 添加缺失的属性
    val requireTask: Boolean = false,
    val clicksRequired: Int = 5, // 需要点击的次数，默认5次，范围1-50次
    val enableSound: Boolean = true,
    val reminderMessage: String = "",
    val enableVibration: Boolean = false,
    val vibrationMode: String = "CONTINUOUS", // VibrationMode枚举的字符串表示，闹钟默认持续震动
    val vibrationIntensity: String = "MEDIUM", // VibrationIntensity枚举的字符串表示

    // 铃声配置相关属性
    val selectedRingtoneUri: String = "",
    val selectedRingtoneName: String = "",

    // 语音报时相关属性
    val voiceTimeFormat: String = "24", // 时间格式：24小时制或12小时制
    val voiceLanguage: String = "zh-CN", // 语音语言
    val voicePitch: Float = 1.0f, // 语音音调，范围0.5-2.0
    val voiceSpeed: Float = 1.0f, // 语音速度，范围0.5-2.0
    val voiceAudioStream: String = "MUSIC" // 音频流类型
) : SharedTask {
    override val type: String = "date_time"
    override val displayName: String = "日期与时间"

    override fun getDescription(): String {
        return when (taskType) {
            DateTimeTaskType.STOPWATCH -> {
                val stopwatchDisplayName = if (stopwatchName.isNotEmpty()) stopwatchName else "未命名秒表"
                "秒表: $stopwatchDisplayName - ${stopwatchOperation.displayName}"
            }
            DateTimeTaskType.ALARM -> {
                val timeText = when (alarmTimeType) {
                    AlarmTimeType.ABSOLUTE -> "${alarmHour}:${alarmMinute.toString().padStart(2, '0')}"
                    AlarmTimeType.RELATIVE -> {
                        when {
                            alarmRelativeHours > 0 && alarmRelativeMinutes > 0 -> "${alarmRelativeHours}小时${alarmRelativeMinutes}分钟后"
                            alarmRelativeHours > 0 -> "${alarmRelativeHours}小时后"
                            alarmRelativeMinutes > 0 -> "${alarmRelativeMinutes}分钟后"
                            else -> "立即"
                        }
                    }
                }
                val taskText = if (requireTask) "需要点击${clicksRequired}次" else "无需任务"
                val soundText = if (enableSound) {
                    if (selectedRingtoneName.isNotEmpty()) "铃声: $selectedRingtoneName" else "有铃声"
                } else "无铃声"
                val vibrationText = if (enableVibration) "有震动" else "无震动"
                val messageText = if (reminderMessage.isNotEmpty()) {
                    if (reminderMessage.length > 15) reminderMessage.take(15) + "..." else reminderMessage
                } else {
                    "无提醒内容"
                }
                "闹钟: $timeText, $taskText, $soundText, $vibrationText, $messageText"
            }
            DateTimeTaskType.SYSTEM_ALARM_RINGTONE -> {
                if (selectedRingtoneName.isNotEmpty()) {
                    "系统闹钟铃声: $selectedRingtoneName"
                } else {
                    "设置系统闹钟铃声"
                }
            }
            DateTimeTaskType.VOICE_TIME_ANNOUNCEMENT -> {
                val formatText = if (voiceTimeFormat == "24") "24小时制" else "12小时制"
                val languageText = when (voiceLanguage) {
                    "zh-CN" -> "中文"
                    "en-US" -> "英文"
                    else -> voiceLanguage
                }
                "语音报时: $formatText, $languageText"
            }
        }
    }
}

/**
 * 设备动作操作类型
 */
enum class DeviceActionOperation(val displayName: String) {
    SHARE_TEXT("分享文本"),
    CLIPBOARD_REFRESH("剪贴板刷新"),
    LAUNCH_HOME("启动主屏幕"),
    SET_CLIPBOARD("填写剪贴板"),
    TOGGLE_STATUS_BAR("展开/折叠状态栏"),
    VOICE_SEARCH("语音搜索"),
    FLASHLIGHT_CONTROL("手电筒控制"),
    VIBRATION("震动"),
    TEXT_TO_SPEECH("朗读文字"),
    SHIZUKU_COMMAND("Shizuku命令"),
    WAIT_DELAY("等待延迟"),
    // 系统操作功能
    QUICK_SETTINGS("打开快速设置面板"),
    POWER_MENU("显示电源菜单"),
    RECENT_TASKS("打开最近任务"),
    APP_DRAWER("打开应用抽屉"),
    ACCESSIBILITY_TOGGLE("切换无障碍对讲功能"),
    BACK_KEY("按返回键")
}

/**
 * 手电筒控制类型
 */
enum class FlashlightControlType(val displayName: String) {
    TURN_ON("开启"),
    TURN_OFF("关闭"),
    TOGGLE("切换")
}

/**
 * 状态栏操作类型
 */
enum class StatusBarOperation(val displayName: String) {
    EXPAND("展开"),
    COLLAPSE("折叠"),
    TOGGLE("切换")
}

/**
 * TTS音频通道类型
 */
enum class TTSAudioStream(val displayName: String, val streamType: Int) {
    RING("铃声", AudioManager.STREAM_RING),
    NOTIFICATION("通知", AudioManager.STREAM_NOTIFICATION),
    ALARM("闹钟", AudioManager.STREAM_ALARM),
    MUSIC("媒体/音乐", AudioManager.STREAM_MUSIC),
    SYSTEM("系统", AudioManager.STREAM_SYSTEM),
    VOICE_CALL("语音通话", AudioManager.STREAM_VOICE_CALL)
}

/**
 * 设备动作震动模式（本地化版本）
 */
enum class DeviceVibrationPattern(val displayName: String) {
    WAVE("波动"),
    SHORT_BUZZ("短嗡嗡声"),
    LONG_BUZZ("长嗡嗡声"),
    DOUBLE_BUZZ("双震动"),
    TRIPLE_BUZZ("三震动"),
    HEARTBEAT("心跳震动"),
    NOTIFICATION("通知震动"),
    CUSTOM("自定义震动"),
    QUICK("快速"),
    SLOW("慢速"),
    INCREASING("递增"),
    CONSTANT("恒定"),
    DECREASING("递减"),
    MINIMAL("极轻微"),
    LIGHT("轻微"),
    FANTASY("幻想"),
    GAME_OVER("游戏结束")
}

/**
 * 系统操作类型
 */
enum class SystemOperationType(val displayName: String) {
    QUICK_SETTINGS("打开快速设置面板"),
    POWER_MENU("显示电源菜单"),
    RECENT_TASKS("打开最近任务"),
    APP_DRAWER("打开应用抽屉"),
    ACCESSIBILITY_TOGGLE("切换无障碍对讲功能")
}

/**
 * 通知任务
 *
 * @property id 任务唯一标识
 * @property operation 通知操作类型
 * @property toastMessage 弹出消息内容
 * @property toastDuration 弹出消息持续时间（毫秒）
 * @property dialogTitle 对话框标题
 * @property dialogText 对话框文本内容
 * @property dialogHtmlFormat 是否启用HTML格式化
 * @property dialogPreventBackClose 是否防止按后退关闭
 * @property dialogRequireCompletion 是否完成后才能后续动作
 * @property dialogNotificationSound 对话框通知声音类型
 * @property bubbleTitle 气泡通知标题
 * @property bubbleText 气泡通知内容
 * @property bubbleHtmlFormat 气泡通知是否启用HTML格式化
 * @property notificationTitle 系统通知标题
 * @property notificationText 系统通知内容
 * @property notificationIconPath 通知图标文件路径
 * @property notificationChannelId 通知渠道ID
 * @property notificationPriority 通知优先级
 * @property clearNotificationMode 清除通知模式
 * @property clearNotificationPackage 要清除通知的应用包名
 * @property clearNotificationId 要清除的通知ID（-1表示清除所有）
 * @property restoreNotificationMode 恢复通知模式
 * @property restoreNotificationPackage 要恢复通知的应用包名
 * @property floatingNotificationOperation 浮动通知操作类型
 * @property replyNotificationPackage 要回复通知的应用包名
 * @property replyNotificationText 自动回复内容
 * @property interactionNotificationPackage 要交互通知的应用包名
 * @property interactionNotificationAction 通知交互动作类型
 */
data class NotificationTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: NotificationOperation = NotificationOperation.SHOW_TOAST,
    val toastMessage: String = "",
    val toastDuration: Int = 2000,
    val dialogTitle: String = "",
    val dialogText: String = "",
    val dialogHtmlFormat: Boolean = false,
    val dialogPreventBackClose: Boolean = false,
    val dialogRequireCompletion: Boolean = false,
    val dialogNotificationSound: NotificationSoundType = NotificationSoundType.DEFAULT,
    val bubbleTitle: String = "",
    val bubbleText: String = "",
    val bubbleHtmlFormat: Boolean = false,  // 气泡通知HTML格式化
    val notificationTitle: String = "",
    val notificationText: String = "",
    val notificationIcon: String = "",  // 保留原有字段兼容性
    val notificationIconPath: String = "",  // 新增图标文件路径字段
    val notificationChannel: String = "default",  // 添加缺失的属性
    val notificationChannelId: String = "default",
    val notificationPriority: Int = 0, // -2到2，0为默认
    val notificationId: Int = 1,  // 添加缺失的属性
    val autoCancel: Boolean = true,  // 添加缺失的属性
    val ongoing: Boolean = false,  // 添加缺失的属性
    val clearNotificationMode: NotificationClearMode = NotificationClearMode.ALL,  // 清除通知模式
    val clearNotificationPackage: String = "",
    val clearNotificationId: Int = -1,
    val restoreNotificationMode: NotificationRestoreMode = NotificationRestoreMode.ALL,  // 恢复通知模式
    val restoreNotificationPackage: String = "",
    val floatingNotificationOperation: SwitchOperation = SwitchOperation.TOGGLE,
    val replyNotificationPackage: String = "",
    val replyNotificationText: String = "",
    val interactionNotificationPackage: String = "",
    val interactionNotificationAction: NotificationActionType = NotificationActionType.CLICK,

    // Toast震动相关属性（整合自ToastMessageTask）
    val enableVibration: Boolean = false,
    val vibrationMode: String = "SINGLE", // VibrationMode枚举的字符串表示
    val vibrationIntensity: String = "MEDIUM", // VibrationIntensity枚举的字符串表示

    // 铃声配置相关属性
    val selectedRingtoneUri: String = "",
    val selectedRingtoneName: String = ""
) : SharedTask {
    override val type: String = "notification"
    override val displayName: String = "通知"

    override fun getDescription(): String {
        return when (operation) {
            NotificationOperation.SHOW_TOAST -> {
                val vibrationText = if (enableVibration) "有震动" else "无震动"
                "弹出消息: ${toastMessage.take(20)}${if (toastMessage.length > 20) "..." else ""} ($vibrationText)"
            }
            NotificationOperation.RESTORE_HIDDEN_NOTIFICATIONS -> {
                when (restoreNotificationMode) {
                    NotificationRestoreMode.ALL -> "恢复所有隐藏通知"
                    NotificationRestoreMode.SPECIFIC_APP -> "恢复指定应用通知: ${restoreNotificationPackage.takeIf { it.isNotEmpty() } ?: "未选择"}"
                }
            }
            NotificationOperation.SHOW_DIALOG -> "显示对话框: ${dialogTitle.take(15)}${if (dialogTitle.length > 15) "..." else ""}"
            NotificationOperation.SHOW_BUBBLE -> {
                val htmlText = if (bubbleHtmlFormat) " (HTML)" else ""
                "显示气泡通知: ${bubbleTitle.take(15)}${if (bubbleTitle.length > 15) "..." else ""}$htmlText"
            }
            NotificationOperation.SHOW_NOTIFICATION -> "显示通知: ${notificationTitle.take(15)}${if (notificationTitle.length > 15) "..." else ""}"
            NotificationOperation.CLEAR_NOTIFICATIONS -> {
                when (clearNotificationMode) {
                    NotificationClearMode.ALL -> "清除所有通知"
                    NotificationClearMode.SPECIFIC_APP -> "清除指定应用通知: ${clearNotificationPackage.takeIf { it.isNotEmpty() } ?: "未选择"}"
                    NotificationClearMode.TRIGGER_BASED -> "使用通知触发器清除"
                }
            }
            NotificationOperation.FLOATING_NOTIFICATION_CONTROL -> "浮动通知: ${floatingNotificationOperation.name}"
            NotificationOperation.RINGTONE_SETTINGS -> {
                if (selectedRingtoneName.isNotEmpty()) {
                    "通知铃声: $selectedRingtoneName"
                } else {
                    "设置通知铃声"
                }
            }
            NotificationOperation.REPLY_NOTIFICATION -> "通知回复: ${replyNotificationText.take(15)}${if (replyNotificationText.length > 15) "..." else ""}"
            NotificationOperation.INTERACT_NOTIFICATION -> "通知交互: ${interactionNotificationAction.displayName}"
        }
    }
}

/**
 * 通知操作类型
 */
enum class NotificationOperation(val displayName: String) {
    SHOW_TOAST("弹出消息提示"),
    RESTORE_HIDDEN_NOTIFICATIONS("恢复隐藏的通知"),
    SHOW_DIALOG("显示对话框"),
    SHOW_BUBBLE("显示气泡通知"),
    SHOW_NOTIFICATION("显示通知"),
    CLEAR_NOTIFICATIONS("清除通知"),
    FLOATING_NOTIFICATION_CONTROL("浮动通知启用/禁用"),
    RINGTONE_SETTINGS("设置通知铃声"),
    REPLY_NOTIFICATION("通知回复"),
    INTERACT_NOTIFICATION("通知交互")
}

/**
 * 通知声音类型
 */
enum class NotificationSoundType(val displayName: String) {
    DEFAULT("默认"),
    RINGTONE("铃声"),
    NOTIFICATION("通知"),
    ALARM("闹钟"),
    NONE("无声音")
}

/**
 * 通知交互动作类型
 */
enum class NotificationActionType(val displayName: String) {
    CLICK("点击"),
    DISMISS("关闭"),
    EXPAND("展开"),
    COLLAPSE("收起")
}

/**
 * 通知优先级类型
 */
enum class NotificationPriority(val displayName: String, val value: Int) {
    MIN("最低", -2),
    LOW("低", -1),
    DEFAULT("默认", 0),
    HIGH("高", 1),
    MAX("最高", 2)
}

/**
 * Toast持续时间类型
 */
enum class ToastDuration(val displayName: String, val value: Int) {
    SHORT("短时间", 0),
    LONG("长时间", 1)
}

/**
 * 通知清除模式
 */
enum class NotificationClearMode(val displayName: String) {
    ALL("清除所有通知"),
    SPECIFIC_APP("清除指定应用通知"),
    TRIGGER_BASED("使用通知触发器")
}

/**
 * 通知恢复模式
 */
enum class NotificationRestoreMode(val displayName: String) {
    ALL("恢复所有隐藏通知"),
    SPECIFIC_APP("恢复指定应用通知")
}

/**
 * 设备动作任务
 *
 * @property id 任务唯一标识
 * @property operation 设备动作操作类型
 * @property shareText 分享的文本内容
 * @property shareAppPackage 指定分享的应用包名（可选）
 * @property shareAppName 指定分享的应用名称（用于显示）
 * @property shareTargetActivityName 分享目标Activity类名（用于精确分享）
 * @property shareTargetLabel 分享目标标签（如"发送给好友"、"分享到朋友圈"）
 * @property clipboardText 要设置到剪贴板的文本
 * @property statusBarOperation 状态栏操作类型
 * @property flashlightOperation 手电筒操作类型
 * @property vibrationPattern 震动模式
 * @property ttsText 要朗读的文字
 * @property ttsPitch TTS音调（0.5-2.0）
 * @property ttsSpeed TTS速度（0.5-2.0）
 * @property ttsAudioStream TTS音频通道
 * @property ttsLanguage TTS语言代码
 * @property ttsQueueMode 是否排队朗读（true=排队，false=立即打断）
 * @property ttsWaitForCompletion 是否等待完成后才能执行后续动作
 * @property ttsSpellOutNumbers 是否逐个说出数字
 */
data class DeviceActionTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: DeviceActionOperation = DeviceActionOperation.SHARE_TEXT,

    // 分享文本相关
    val shareText: String = "",
    val shareAppPackage: String = "", // 空表示显示应用选择器
    val shareAppName: String = "",
    val shareTargetActivityName: String = "", // 分享目标Activity类名
    val shareTargetLabel: String = "", // 分享目标标签

    // 剪贴板相关
    val clipboardText: String = "",

    // 状态栏相关
    val statusBarOperation: StatusBarOperation = StatusBarOperation.TOGGLE,

    // 手电筒相关
    val flashlightOperation: FlashlightControlType = FlashlightControlType.TOGGLE,

    // 震动相关
    val vibrationPattern: DeviceVibrationPattern = DeviceVibrationPattern.SHORT_BUZZ,

    // TTS相关
    val ttsText: String = "",
    val ttsPitch: Float = 1.0f, // 音调，范围0.5-2.0
    val ttsSpeed: Float = 1.0f, // 速度，范围0.5-2.0
    val ttsAudioStream: TTSAudioStream = TTSAudioStream.MUSIC,
    val ttsLanguage: String = "zh-CN", // 语言代码
    val ttsQueueMode: Boolean = true, // 是否排队朗读
    val ttsWaitForCompletion: Boolean = false, // 是否等待完成后才能执行后续动作
    val ttsSpellOutNumbers: Boolean = false, // 是否逐个说出数字

    // Shizuku命令相关
    val shizukuCommands: String = "", // 要执行的命令列表，以分号分隔

    // 等待延迟相关
    val waitMinutes: Int = 0, // 等待的分钟数
    val waitSeconds: Int = 0, // 等待的秒数

    // 系统操作相关
    val systemOperationType: SystemOperationType = SystemOperationType.QUICK_SETTINGS // 系统操作类型
) : SharedTask {
    override val type: String = "device_action"
    override val displayName: String = "设备动作"

    override fun getDescription(): String {
        return when (operation) {
            DeviceActionOperation.SHARE_TEXT -> {
                val text = if (shareText.length > 20) shareText.take(20) + "..." else shareText
                val target = when {
                    shareTargetLabel.isNotEmpty() && shareTargetLabel != shareAppName ->
                        " -> $shareAppName - $shareTargetLabel"
                    shareAppName.isNotEmpty() ->
                        " -> $shareAppName"
                    else -> ""
                }
                "分享文本: $text$target"
            }
            DeviceActionOperation.CLIPBOARD_REFRESH -> "剪贴板刷新"
            DeviceActionOperation.LAUNCH_HOME -> "启动主屏幕"
            DeviceActionOperation.SET_CLIPBOARD -> {
                val text = if (clipboardText.length > 20) clipboardText.take(20) + "..." else clipboardText
                "设置剪贴板: $text"
            }
            DeviceActionOperation.TOGGLE_STATUS_BAR -> "状态栏: ${statusBarOperation.displayName}"
            DeviceActionOperation.VOICE_SEARCH -> "语音搜索"
            DeviceActionOperation.FLASHLIGHT_CONTROL -> "手电筒: ${flashlightOperation.displayName}"
            DeviceActionOperation.VIBRATION -> "震动: ${vibrationPattern.displayName}"
            DeviceActionOperation.TEXT_TO_SPEECH -> {
                val text = if (ttsText.length > 15) ttsText.take(15) + "..." else ttsText
                "朗读: $text (${ttsAudioStream.displayName})"
            }
            DeviceActionOperation.SHIZUKU_COMMAND -> {
                val commands = if (shizukuCommands.length > 30) shizukuCommands.take(30) + "..." else shizukuCommands
                "Shizuku命令: $commands"
            }
            DeviceActionOperation.WAIT_DELAY -> {
                val totalSeconds = waitMinutes * 60 + waitSeconds
                when {
                    waitMinutes > 0 && waitSeconds > 0 -> "等待: ${waitMinutes}分${waitSeconds}秒"
                    waitMinutes > 0 -> "等待: ${waitMinutes}分钟"
                    waitSeconds > 0 -> "等待: ${waitSeconds}秒"
                    else -> "等待: 0秒"
                }
            }
            // 系统操作功能
            DeviceActionOperation.QUICK_SETTINGS -> "打开快速设置面板"
            DeviceActionOperation.POWER_MENU -> "显示电源菜单"
            DeviceActionOperation.RECENT_TASKS -> "打开最近任务"
            DeviceActionOperation.APP_DRAWER -> "打开应用抽屉"
            DeviceActionOperation.ACCESSIBILITY_TOGGLE -> "切换无障碍对讲功能"
            DeviceActionOperation.BACK_KEY -> "按返回键"
        }
    }
}





/**
 * 连接任务操作类型
 */
enum class ConnectivityOperation {
    SEND_INTENT,       // 发送Intent
    SYNC_ACCOUNT,      // 同步账号
    CHECK_CONNECTION,  // 检查连接
    HOTSPOT_CONTROL,   // 热点开关控制
    MOBILE_DATA_CONTROL, // 移动数据开关控制
    AUTO_SYNC_CONTROL,   // 自动同步开关控制
    AIRPLANE_MODE_CONTROL, // 飞行模式开关控制
    WIFI_CONTROL,      // WiFi控制（整合原有WiFi控制任务）
    NETWORK_STATE_SAVE,    // 保存网络状态
    NETWORK_STATE_RESTORE  // 恢复网络状态
}

/**
 * Intent目标类型
 */
enum class IntentTargetType {
    ACTIVITY,   // Activity
    BROADCAST,  // Broadcast
    SERVICE     // Service
}

/**
 * Intent参数数据类型
 */
enum class IntentParamType {
    STRING,     // 字符串
    BOOLEAN,    // 布尔
    INTEGER,    // 整数
    LONG,       // 长整型
    FLOAT,      // 浮点型
    DOUBLE      // 双精度浮点型
}

/**
 * Intent参数数据类
 */
data class IntentParam(
    val name: String = "",
    val value: String = "",
    val type: IntentParamType = IntentParamType.STRING
)

/**
 * 开关控制操作类型
 */
enum class SwitchOperation(val displayName: String) {
    ENABLE("启用"),   // 启用/开启
    DISABLE("禁用"),  // 禁用/关闭
    TOGGLE("切换")    // 切换
}

/**
 * 电话操作类型
 */
enum class PhoneOperation {
    OPEN_CALL_LOG,      // 打开通话记录
    REJECT_CALL,        // 拒接电话
    MAKE_CALL,          // 拨打电话
    ANSWER_CALL,        // 接听电话
    CLEAR_CALL_LOG,     // 清除通话记录
    RINGTONE_SETTINGS   // 铃声配置
}

/**
 * 拨打电话类型
 */
enum class MakeCallType {
    CONTACT,        // 选择联系人
    MANUAL_INPUT,   // 手动输入号码
    RECENT_CALL     // 最近拨打的号码
}

/**
 * 接听电话延迟类型
 */
enum class AnswerCallDelayType(val displayName: String) {
    NO_DELAY("无延迟"),
    CUSTOM_DELAY("自定义延迟")
}

/**
 * 清除通话记录类型
 */
enum class ClearCallLogType {
    ALL,            // 全部
    INCOMING,       // 呼入
    OUTGOING,       // 呼出
    MISSED,         // 未接
    VOICEMAIL,      // 语音信箱
    REJECTED,       // 已拒接
    BLOCKED         // 已屏蔽
}

/**
 * 媒体操作类型
 */
enum class MediaOperation {
    MULTIMEDIA_CONTROL,     // 多媒体控制
    PLAY_STOP_SOUND,       // 播放/停止声音
    MICROPHONE_RECORDING   // 麦克风录音
}

/**
 * 多媒体控制类型
 */
enum class MultimediaControlType {
    SIMULATE_MEDIA_BUTTON,  // 模拟媒体按钮
    DEFAULT_PLAYER_CONTROL, // 默认播放器控制
    SIMULATE_AUDIO_BUTTON   // 模拟音频按钮
}

/**
 * 媒体按钮类型
 */
enum class MediaButtonType {
    PLAY_PAUSE,     // 播放/暂停
    PLAY,           // 播放
    PAUSE,          // 暂停
    NEXT_TRACK,     // 下一曲
    PREVIOUS_TRACK, // 上一曲
    STOP,           // 停止
    FAST_FORWARD,   // 快进
    REWIND          // 快退
}

/**
 * 音频按钮类型
 */
enum class AudioButtonType {
    VOLUME_UP,      // 音量增加
    VOLUME_DOWN,    // 音量减小
    VOLUME_MUTE     // 静音
}

/**
 * 播放器控制类型
 */
enum class PlayerControlType {
    PLAY,           // 播放
    PAUSE,          // 暂停
    STOP,           // 停止
    NEXT,           // 下一曲
    PREVIOUS        // 上一曲
}

/**
 * 声音播放类型
 */
enum class SoundPlayType {
    SELECT_FILE,        // 选择文件
    SELECT_RINGTONE,    // 选择铃声
    STOP_EXISTING_SOUND // 停止现有声音
}

/**
 * 音频通道类型
 */
enum class AudioStreamType {
    RINGTONE,       // 铃声
    ALARM,          // 闹钟
    NOTIFICATION,   // 通知
    MEDIA_MUSIC,    // 媒体/音乐
    SYSTEM,         // 系统
    VOICE_CALL      // 语音通话
}

/**
 * 录音音频源类型
 */
enum class RecordingSource {
    STANDARD_MIC,   // 标准麦克风
    CAMERA_MIC,     // 摄像机麦克风
    UNPROCESSED     // 未处理
}

/**
 * 录音格式类型
 */
enum class RecordingFormat {
    THREE_GPP,      // 3GPP
    MPEG4,          // MPEG4
    AAC             // AAC
}

/**
 * 录音时长类型
 */
enum class RecordingDurationType {
    CUSTOM_TIME,        // 自定义时间
    UNTIL_CANCELLED,    // 直到被取消
    CANCEL_RECORDING    // 取消录音
}

/**
 * 屏幕控制操作类型
 */
enum class ScreenControlOperation {
    BRIGHTNESS_CONTROL,         // 亮度控制
    KEEP_DEVICE_AWAKE,         // 保持设备唤醒
    SCREEN_ON_OFF,             // 屏幕开关
    SCREEN_DIMNESS,            // 屏幕暗度
    BLOCK_SCREEN_TOUCH,        // 屏蔽屏幕触摸
    FORCE_SCREEN_ROTATION,     // 强制屏幕旋转
    SET_SCREEN_TIMEOUT,        // 设置屏幕超时
    CHECK_SCREEN_TEXT,         // 检查屏幕上的文字
    READ_SCREEN_CONTENT,       // 读取屏幕内容
    CHECK_PIXEL_COLOR,         // 检查像素颜色
    AUTO_CLICKER_PLAYBACK      // 自动点击回放
}

/**
 * 亮度控制类型
 */
enum class BrightnessControlType {
    PERCENTAGE,     // 百分比
    ABSOLUTE_VALUE  // 绝对值
}

/**
 * 屏幕开关操作
 */
enum class ScreenOnOffOperation {
    TURN_ON,    // 开启屏幕
    TURN_OFF    // 关闭屏幕
}

/**
 * 屏幕开关实现方式
 */
enum class ScreenOnOffImplementation(val displayName: String) {
    SHIZUKU("Shizuku方式"),
    ACCESSIBILITY_SERVICE("无障碍服务方式")
}

/**
 * 屏幕暗度传感器模式
 */
enum class ScreenDimnessSensorMode {
    SENSOR_ON,  // 光传感器开
    SENSOR_OFF  // 光传感器关
}

/**
 * 保持设备唤醒操作
 */
enum class KeepAwakeOperation {
    ENABLE,     // 启用保持唤醒
    DISABLE     // 禁用保持唤醒
}

/**
 * 屏幕触摸屏蔽操作
 */
enum class TouchBlockOperation {
    ENABLE,     // 启用
    DISABLE,    // 禁用
    TOGGLE      // 切换
}

/**
 * 强制屏幕旋转模式
 */
enum class ForceRotationMode {
    FORCE_PORTRAIT,             // 强制竖屏
    FORCE_LANDSCAPE,            // 强制横屏
    STOP_FORCE_ROTATION,        // 停止强制旋转
    FORCE_KEEP_CURRENT,         // 强制保持当前方向
    TOGGLE_KEEP_CURRENT,        // 切换是否强制保持当前方向
    FORCE_REVERSE_PORTRAIT,     // 强制反向竖屏
    FORCE_REVERSE_LANDSCAPE,    // 强制反向横屏
    FORCE_SENSOR_LANDSCAPE,     // 强制传感器横屏
    FORCE_SENSOR_PORTRAIT,      // 强制传感器竖屏
    FORCE_SENSOR_ROTATION       // 强制按传感器方向旋转
}

/**
 * 屏幕超时时间单位
 */
enum class ScreenTimeoutUnit {
    SECONDS,    // 秒
    MINUTES     // 分钟
}

/**
 * 坐标类型
 */
enum class CoordinateType {
    PIXEL,      // 像素坐标
    PERCENTAGE  // 百分比坐标
}

/**
 * 文本匹配模式
 */
enum class TextMatchMode {
    CONTAINS,   // 包含
    EXACT_MATCH // 精确匹配
}

/**
 * 自动点击器操作来源类型
 */
enum class AutoClickerSourceType(val displayName: String) {
    INSTANT_RECORDING("即时录制"),     // 即时录制操作
    QUICK_OPERATION("快速操作")       // 预设的快速操作
}

/**
 * 快速操作类型
 */
enum class QuickOperationType(val displayName: String) {
    SINGLE_CLICK("单点点击"),         // 单点点击
    CONTINUOUS_CLICK("连续点击"),     // 连续点击
    SWIPE_OPERATION("滑动操作"),      // 滑动操作
    LONG_PRESS("长按操作")           // 长按操作
}

/**
 * 连接任务
 * 统一的连接控制功能，整合多种连接相关操作
 *
 * @property id 任务唯一标识
 * @property operation 连接操作类型
 * @property intentTargetType Intent目标类型（仅在operation为SEND_INTENT时有效）
 * @property intentAction Intent动作（仅在operation为SEND_INTENT时有效）
 * @property intentPackageName Intent包名（仅在operation为SEND_INTENT时有效）
 * @property intentClassName Intent类名（仅在operation为SEND_INTENT时有效）
 * @property intentDataUrl Intent数据URL（仅在operation为SEND_INTENT时有效）
 * @property intentMimeType Intent MIME类型（仅在operation为SEND_INTENT时有效）
 * @property intentFlags Intent旗帜（仅在operation为SEND_INTENT时有效）
 * @property intentParams Intent附加参数列表（仅在operation为SEND_INTENT时有效，最多六组）
 * @property accountType 账号类型（仅在operation为SYNC_ACCOUNT时有效）
 * @property checkUrl 检查的网址（仅在operation为CHECK_CONNECTION时有效）
 * @property checkTimeout 检查超时时间（毫秒）（仅在operation为CHECK_CONNECTION时有效）
 * @property waitForCompletion 是否等待完成后才能后续动作（仅在operation为CHECK_CONNECTION和SYNC_ACCOUNT时有效）
 * @property hotspotOperation 热点操作类型（仅在operation为HOTSPOT_CONTROL时有效）
 * @property mobileDataOperation 移动数据操作类型（仅在operation为MOBILE_DATA_CONTROL时有效）
 * @property autoSyncOperation 自动同步操作类型（仅在operation为AUTO_SYNC_CONTROL时有效）
 * @property airplaneModeOperation 飞行模式操作类型（仅在operation为AIRPLANE_MODE_CONTROL时有效）
 * @property wifiOperation WiFi操作类型（仅在operation为WIFI_CONTROL时有效）
 */
data class ConnectivityTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: ConnectivityOperation = ConnectivityOperation.WIFI_CONTROL,

    // Intent发送参数
    val intentTargetType: IntentTargetType = IntentTargetType.ACTIVITY,
    val intentAction: String = "",
    val intentPackageName: String = "",
    val intentClassName: String = "",
    val intentDataUrl: String = "",
    val intentMimeType: String = "",
    val intentFlags: Int = 0,
    val intentParams: List<IntentParam> = emptyList(),

    // 同步账号参数
    val accountType: String = "",

    // 检查连接参数
    val checkUrl: String = "",
    val checkTimeout: Int = 5000,
    val waitForCompletion: Boolean = false,

    // 各种开关控制参数
    val hotspotOperation: SwitchOperation = SwitchOperation.TOGGLE,
    val mobileDataOperation: SwitchOperation = SwitchOperation.TOGGLE,
    val autoSyncOperation: SwitchOperation = SwitchOperation.TOGGLE,
    val airplaneModeOperation: SwitchOperation = SwitchOperation.TOGGLE,
    val wifiOperation: SwitchOperation = SwitchOperation.TOGGLE,

    // 自定义网络状态恢复命令参数
    val useCustomRestoreCommands: Boolean = false, // 是否使用自定义恢复命令
    val customWifiEnableCommand: String = "", // 自定义WiFi开启命令
    val customWifiDisableCommand: String = "", // 自定义WiFi关闭命令
    val customMobileDataEnableCommand: String = "", // 自定义移动数据开启命令
    val customMobileDataDisableCommand: String = "", // 自定义移动数据关闭命令
    val effectiveWifiEnableCommand: String = "svc wifi enable", // 最终生效的WiFi开启命令（预处理后）
    val effectiveWifiDisableCommand: String = "svc wifi disable", // 最终生效的WiFi关闭命令（预处理后）
    val effectiveMobileDataEnableCommand: String = "svc data enable", // 最终生效的移动数据开启命令（预处理后）
    val effectiveMobileDataDisableCommand: String = "svc data disable" // 最终生效的移动数据关闭命令（预处理后）
) : SharedTask {
    override val type: String = "connectivity"
    override val displayName: String = "连接"

    override fun getDescription(): String {
        return when (operation) {
            ConnectivityOperation.SEND_INTENT -> {
                val targetText = when (intentTargetType) {
                    IntentTargetType.ACTIVITY -> "Activity"
                    IntentTargetType.BROADCAST -> "广播"
                    IntentTargetType.SERVICE -> "服务"
                }
                "发送Intent到$targetText: ${intentAction.ifEmpty { "无动作" }}"
            }
            ConnectivityOperation.SYNC_ACCOUNT -> "同步账号: ${accountType.ifEmpty { "所有账号" }}"
            ConnectivityOperation.CHECK_CONNECTION -> "检查连接: ${checkUrl.ifEmpty { "未指定网址" }}"
            ConnectivityOperation.HOTSPOT_CONTROL -> {
                when (hotspotOperation) {
                    SwitchOperation.ENABLE -> "启用热点"
                    SwitchOperation.DISABLE -> "禁用热点"
                    SwitchOperation.TOGGLE -> "切换热点状态"
                }
            }
            ConnectivityOperation.MOBILE_DATA_CONTROL -> {
                when (mobileDataOperation) {
                    SwitchOperation.ENABLE -> "开启移动数据"
                    SwitchOperation.DISABLE -> "关闭移动数据"
                    SwitchOperation.TOGGLE -> "切换移动数据状态"
                }
            }
            ConnectivityOperation.AUTO_SYNC_CONTROL -> {
                when (autoSyncOperation) {
                    SwitchOperation.ENABLE -> "开启自动同步"
                    SwitchOperation.DISABLE -> "关闭自动同步"
                    SwitchOperation.TOGGLE -> "切换自动同步状态"
                }
            }
            ConnectivityOperation.AIRPLANE_MODE_CONTROL -> {
                when (airplaneModeOperation) {
                    SwitchOperation.ENABLE -> "进入飞行模式"
                    SwitchOperation.DISABLE -> "退出飞行模式"
                    SwitchOperation.TOGGLE -> "切换飞行模式状态"
                }
            }
            ConnectivityOperation.WIFI_CONTROL -> {
                when (wifiOperation) {
                    SwitchOperation.ENABLE -> "开启WiFi"
                    SwitchOperation.DISABLE -> "关闭WiFi"
                    SwitchOperation.TOGGLE -> "切换WiFi状态"
                }
            }
            ConnectivityOperation.NETWORK_STATE_SAVE -> "保存网络状态"
            ConnectivityOperation.NETWORK_STATE_RESTORE -> {
                val customText = if (useCustomRestoreCommands) " (自定义命令)" else ""
                "恢复网络状态$customText"
            }
        }
    }
}

/**
 * 电话任务
 * 统一的电话功能，支持多种电话操作
 *
 * @property id 任务唯一标识
 * @property operation 电话操作类型
 * @property makeCallType 拨打电话类型（仅在operation为MAKE_CALL时有效）
 * @property phoneNumber 电话号码（手动输入时使用）
 * @property contactName 联系人姓名（选择联系人时使用）
 * @property contactIds 选中的联系人ID列表（选择联系人时使用）
 * @property simCardSelection SIM卡选择（拨打电话时使用）
 * @property answerDelayType 接听电话延迟类型（仅在operation为ANSWER_CALL时有效）
 * @property answerDelaySeconds 接听电话延迟秒数（仅在answerDelayType为CUSTOM_DELAY时有效）
 * @property clearLogType 清除通话记录类型（仅在operation为CLEAR_CALL_LOG时有效）
 * @property selectedRingtoneUri 选中的铃声URI（铃声配置时使用）
 * @property selectedRingtoneName 选中的铃声名称（铃声配置时使用）
 */
data class PhoneTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: PhoneOperation = PhoneOperation.OPEN_CALL_LOG,
    val makeCallType: MakeCallType = MakeCallType.MANUAL_INPUT,
    val phoneNumber: String = "",
    val contactName: String = "",
    val contactIds: List<String> = emptyList(),
    val simCardSelection: SimCardSelection = SimCardSelection.ASK_EACH_TIME,
    val answerDelayType: AnswerCallDelayType = AnswerCallDelayType.NO_DELAY,
    val answerDelaySeconds: Int = 0,
    val clearLogType: ClearCallLogType = ClearCallLogType.ALL,
    val selectedRingtoneUri: String = "",
    val selectedRingtoneName: String = ""
) : SharedTask {
    override val type: String = "phone"
    override val displayName: String = "电话"

    override fun getDescription(): String {
        return when (operation) {
            PhoneOperation.OPEN_CALL_LOG -> "打开通话记录"
            PhoneOperation.REJECT_CALL -> "拒接电话"
            PhoneOperation.MAKE_CALL -> {
                val simText = when (simCardSelection) {
                    SimCardSelection.SIM1 -> " (SIM1)"
                    SimCardSelection.SIM2 -> " (SIM2)"
                    SimCardSelection.ASK_EACH_TIME -> ""
                }
                when (makeCallType) {
                    MakeCallType.CONTACT -> {
                        val contactText = if (contactIds.isNotEmpty()) {
                            "已选择${contactIds.size}个联系人"
                        } else if (contactName.isNotEmpty()) {
                            contactName
                        } else {
                            "未选择联系人"
                        }
                        "拨打电话: $contactText$simText"
                    }
                    MakeCallType.MANUAL_INPUT -> "拨打电话: $phoneNumber$simText"
                    MakeCallType.RECENT_CALL -> "拨打最近通话$simText"
                }
            }
            PhoneOperation.ANSWER_CALL -> {
                when (answerDelayType) {
                    AnswerCallDelayType.NO_DELAY -> "接听电话"
                    AnswerCallDelayType.CUSTOM_DELAY -> "接听电话(延迟${answerDelaySeconds}秒)"
                }
            }
            PhoneOperation.CLEAR_CALL_LOG -> {
                when (clearLogType) {
                    ClearCallLogType.ALL -> "清除全部通话记录"
                    ClearCallLogType.INCOMING -> "清除呼入记录"
                    ClearCallLogType.OUTGOING -> "清除呼出记录"
                    ClearCallLogType.MISSED -> "清除未接记录"
                    ClearCallLogType.VOICEMAIL -> "清除语音信箱记录"
                    ClearCallLogType.REJECTED -> "清除已拒接记录"
                    ClearCallLogType.BLOCKED -> "清除已屏蔽记录"
                }
            }
            PhoneOperation.RINGTONE_SETTINGS -> {
                if (selectedRingtoneName.isNotEmpty()) {
                    "铃声配置: $selectedRingtoneName"
                } else {
                    "铃声配置"
                }
            }
        }
    }
}

/**
 * 媒体任务
 * 统一的媒体控制功能，支持多媒体控制、播放声音、录音等操作
 *
 * @property id 任务唯一标识
 * @property operation 媒体操作类型
 * @property multimediaControlType 多媒体控制类型（仅在operation为MULTIMEDIA_CONTROL时有效）
 * @property mediaButtonType 媒体按钮类型（仅在multimediaControlType为SIMULATE_MEDIA_BUTTON时有效）
 * @property audioButtonType 音频按钮类型（仅在multimediaControlType为SIMULATE_AUDIO_BUTTON时有效）
 * @property playerControlType 播放器控制类型（仅在multimediaControlType为DEFAULT_PLAYER_CONTROL时有效）
 * @property soundPlayType 声音播放类型（仅在operation为PLAY_STOP_SOUND时有效）
 * @property selectedFilePath 选择的文件路径（仅在soundPlayType为SELECT_FILE时有效）
 * @property selectedFileName 选择的文件名称（用于显示）
 * @property selectedRingtoneUri 选择的铃声URI（仅在soundPlayType为SELECT_RINGTONE时有效）
 * @property selectedRingtoneName 选择的铃声名称（用于显示）
 * @property audioStreamType 音频通道类型（仅在operation为PLAY_STOP_SOUND时有效）
 * @property waitForCompletion 是否等待完成后才能后续动作（仅在operation为PLAY_STOP_SOUND时有效）
 * @property recordingDurationType 录音时长类型（仅在operation为MICROPHONE_RECORDING时有效）
 * @property recordingMinutes 录音分钟数（仅在recordingDurationType为CUSTOM_TIME时有效）
 * @property recordingSeconds 录音秒数（仅在recordingDurationType为CUSTOM_TIME时有效）
 * @property recordingSource 录音音频源（仅在operation为MICROPHONE_RECORDING时有效）
 * @property recordingFormat 录音格式（仅在operation为MICROPHONE_RECORDING时有效）
 * @property recordingWaitForCompletion 录音是否等待完成后才能后续动作（仅在operation为MICROPHONE_RECORDING时有效）
 */
data class MediaTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: MediaOperation = MediaOperation.MULTIMEDIA_CONTROL,

    // 多媒体控制参数
    val multimediaControlType: MultimediaControlType = MultimediaControlType.SIMULATE_MEDIA_BUTTON,
    val mediaButtonType: MediaButtonType = MediaButtonType.PLAY_PAUSE,
    val audioButtonType: AudioButtonType = AudioButtonType.VOLUME_UP,
    val playerControlType: PlayerControlType = PlayerControlType.PLAY,

    // 播放/停止声音参数
    val soundPlayType: SoundPlayType = SoundPlayType.SELECT_RINGTONE,
    val selectedFilePath: String = "",
    val selectedFileName: String = "",
    val selectedRingtoneUri: String = "",
    val selectedRingtoneName: String = "",
    val audioStreamType: AudioStreamType = AudioStreamType.MEDIA_MUSIC,
    val waitForCompletion: Boolean = false,

    // 麦克风录音参数
    val recordingDurationType: RecordingDurationType = RecordingDurationType.CUSTOM_TIME,
    val recordingMinutes: Int = 0,
    val recordingSeconds: Int = 30,
    val recordingSource: RecordingSource = RecordingSource.STANDARD_MIC,
    val recordingFormat: RecordingFormat = RecordingFormat.THREE_GPP,
    val recordingWaitForCompletion: Boolean = false,

    // 录音文件路径参数
    val recordingFolderPath: String = "",
    val recordingFileName: String = ""
) : SharedTask {
    override val type: String = "media"
    override val displayName: String = "媒体"

    override fun getDescription(): String {
        return when (operation) {
            MediaOperation.MULTIMEDIA_CONTROL -> {
                when (multimediaControlType) {
                    MultimediaControlType.SIMULATE_MEDIA_BUTTON -> {
                        when (mediaButtonType) {
                            MediaButtonType.PLAY_PAUSE -> "模拟媒体按钮: 播放/暂停"
                            MediaButtonType.PLAY -> "模拟媒体按钮: 播放"
                            MediaButtonType.PAUSE -> "模拟媒体按钮: 暂停"
                            MediaButtonType.NEXT_TRACK -> "模拟媒体按钮: 下一曲"
                            MediaButtonType.PREVIOUS_TRACK -> "模拟媒体按钮: 上一曲"
                            MediaButtonType.STOP -> "模拟媒体按钮: 停止"
                            MediaButtonType.FAST_FORWARD -> "模拟媒体按钮: 快进"
                            MediaButtonType.REWIND -> "模拟媒体按钮: 快退"
                        }
                    }
                    MultimediaControlType.DEFAULT_PLAYER_CONTROL -> {
                        when (playerControlType) {
                            PlayerControlType.PLAY -> "默认播放器: 播放"
                            PlayerControlType.PAUSE -> "默认播放器: 暂停"
                            PlayerControlType.STOP -> "默认播放器: 停止"
                            PlayerControlType.NEXT -> "默认播放器: 下一曲"
                            PlayerControlType.PREVIOUS -> "默认播放器: 上一曲"
                        }
                    }
                    MultimediaControlType.SIMULATE_AUDIO_BUTTON -> {
                        when (audioButtonType) {
                            AudioButtonType.VOLUME_UP -> "模拟音频按钮: 音量增加"
                            AudioButtonType.VOLUME_DOWN -> "模拟音频按钮: 音量减小"
                            AudioButtonType.VOLUME_MUTE -> "模拟音频按钮: 静音"
                        }
                    }
                }
            }
            MediaOperation.PLAY_STOP_SOUND -> {
                when (soundPlayType) {
                    SoundPlayType.SELECT_FILE -> {
                        val fileName = selectedFileName.ifEmpty { "未选择文件" }
                        val streamName = when (audioStreamType) {
                            AudioStreamType.RINGTONE -> "铃声"
                            AudioStreamType.ALARM -> "闹钟"
                            AudioStreamType.NOTIFICATION -> "通知"
                            AudioStreamType.MEDIA_MUSIC -> "媒体"
                            AudioStreamType.SYSTEM -> "系统"
                            AudioStreamType.VOICE_CALL -> "通话"
                        }
                        "播放文件: $fileName (${streamName}通道)"
                    }
                    SoundPlayType.SELECT_RINGTONE -> {
                        val ringtoneName = selectedRingtoneName.ifEmpty { "默认铃声" }
                        val streamName = when (audioStreamType) {
                            AudioStreamType.RINGTONE -> "铃声"
                            AudioStreamType.ALARM -> "闹钟"
                            AudioStreamType.NOTIFICATION -> "通知"
                            AudioStreamType.MEDIA_MUSIC -> "媒体"
                            AudioStreamType.SYSTEM -> "系统"
                            AudioStreamType.VOICE_CALL -> "通话"
                        }
                        "播放铃声: $ringtoneName (${streamName}通道)"
                    }
                    SoundPlayType.STOP_EXISTING_SOUND -> "停止现有声音"
                }
            }
            MediaOperation.MICROPHONE_RECORDING -> {
                when (recordingDurationType) {
                    RecordingDurationType.CUSTOM_TIME -> {
                        val sourceName = when (recordingSource) {
                            RecordingSource.STANDARD_MIC -> "标准麦克风"
                            RecordingSource.CAMERA_MIC -> "摄像机麦克风"
                            RecordingSource.UNPROCESSED -> "未处理"
                        }
                        val formatName = when (recordingFormat) {
                            RecordingFormat.THREE_GPP -> "3GPP"
                            RecordingFormat.MPEG4 -> "MPEG4"
                            RecordingFormat.AAC -> "AAC"
                        }
                        "录音: ${recordingMinutes}分${recordingSeconds}秒 ($sourceName, $formatName)"
                    }
                    RecordingDurationType.UNTIL_CANCELLED -> "录音: 直到被取消"
                    RecordingDurationType.CANCEL_RECORDING -> "取消录音"
                }
            }
        }
    }
}

/**
 * 屏幕控制任务
 * 统一的屏幕控制功能，支持亮度控制、屏幕开关、旋转控制等操作
 *
 * @property id 任务唯一标识
 * @property operation 屏幕控制操作类型
 * @property brightnessControlType 亮度控制类型（仅在operation为BRIGHTNESS_CONTROL时有效）
 * @property brightnessValue 亮度值（百分比或绝对值）
 * @property enableAutoBrightness 是否启用自动亮度（仅在operation为BRIGHTNESS_CONTROL时有效）
 * @property keepAwakeEnabled 保持设备唤醒状态（仅在operation为KEEP_DEVICE_AWAKE时有效）
 * @property screenOnOffOperation 屏幕开关操作（仅在operation为SCREEN_ON_OFF时有效）
 * @property screenDimnessSensorMode 屏幕暗度传感器模式（仅在operation为SCREEN_DIMNESS时有效）
 * @property screenDimnessPercentage 屏幕暗度百分比（仅在screenDimnessSensorMode为SENSOR_ON时有效）
 * @property touchBlockOperation 屏幕触摸屏蔽操作（仅在operation为BLOCK_SCREEN_TOUCH时有效）
 * @property forceRotationMode 强制屏幕旋转模式（仅在operation为FORCE_SCREEN_ROTATION时有效）
 * @property screenTimeoutValue 屏幕超时时间值（仅在operation为SET_SCREEN_TIMEOUT时有效）
 * @property screenTimeoutUnit 屏幕超时时间单位（仅在operation为SET_SCREEN_TIMEOUT时有效）
 */
data class ScreenControlTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: ScreenControlOperation = ScreenControlOperation.BRIGHTNESS_CONTROL,

    // 亮度控制参数
    val brightnessControlType: BrightnessControlType = BrightnessControlType.PERCENTAGE,
    val brightnessValue: Int = 50,
    val enableAutoBrightness: Boolean = false,

    // 保持设备唤醒参数
    val keepAwakeOperation: KeepAwakeOperation = KeepAwakeOperation.ENABLE,

    // 屏幕开关参数
    val screenOnOffOperation: ScreenOnOffOperation = ScreenOnOffOperation.TURN_ON,
    val screenOnOffImplementation: ScreenOnOffImplementation? = null,

    // 屏幕暗度参数
    val screenDimnessSensorMode: ScreenDimnessSensorMode = ScreenDimnessSensorMode.SENSOR_OFF,
    val screenDimnessPercentage: Int = 50,

    // 屏幕触摸屏蔽参数
    val touchBlockOperation: TouchBlockOperation = TouchBlockOperation.ENABLE,
    val emergencyCloseEnabled: Boolean = true,
    val emergencyClickCount: Int = 5,
    val emergencyTimeWindowSeconds: Int = 3,

    // 强制屏幕旋转参数
    val forceRotationMode: ForceRotationMode = ForceRotationMode.FORCE_PORTRAIT,

    // 屏幕超时参数
    val screenTimeoutValue: Int = 30,
    val screenTimeoutUnit: ScreenTimeoutUnit = ScreenTimeoutUnit.SECONDS,

    // 检查屏幕文字参数
    val checkTextContent: String = "", // 要检查的文本内容
    val checkTextCaseSensitive: Boolean = false, // 是否区分大小写
    val checkTextUseRegex: Boolean = false, // 是否使用正则表达式
    val checkTextMatchMode: TextMatchMode = TextMatchMode.CONTAINS, // 文本匹配模式
    val checkTextIncludeOverlay: Boolean = false, // 是否包括叠加层
    val checkTextIgnoreHidden: Boolean = true, // 是否忽略隐藏文本
    val checkTextOutputFile: String = "", // 检查结果输出文件路径
    val checkTextViewIdOutputFile: String = "", // 视图ID输出文件路径

    // 检查像素颜色参数
    val checkPixelCoordinateType: CoordinateType = CoordinateType.PIXEL, // 坐标类型
    val checkPixelX: Float = 0.0f, // X坐标（像素或百分比）
    val checkPixelY: Float = 0.0f, // Y坐标（像素或百分比）
    val checkPixelColorOutputFile: String = "", // 像素颜色输出文件路径

    // 读取屏幕内容参数
    val readContentOutputFile: String = "", // 屏幕内容输出文件路径

    // 自动点击器参数
    val autoClickerSourceType: AutoClickerSourceType = AutoClickerSourceType.INSTANT_RECORDING, // 操作来源类型
    val quickOperationType: QuickOperationType = QuickOperationType.SINGLE_CLICK, // 快速操作类型
    val recordedGesture: String = "", // 录制的手势数据ID（原生存储）
    val playbackLoopCount: Int = 1, // 回放循环次数（0表示无限循环）
    val playbackSpeed: Float = 1.0f, // 回放速度倍率
    val delayBetweenLoops: Long = 1000L, // 循环间延时（毫秒）
    val adaptToScreenSize: Boolean = true, // 是否适配屏幕尺寸
    val stopOnError: Boolean = true, // 遇到错误时是否停止

    // 快速操作参数
    val clickX: Float = 0.5f, // 点击X坐标（相对坐标，0.0-1.0）
    val clickY: Float = 0.5f, // 点击Y坐标（相对坐标，0.0-1.0）
    val clickCount: Int = 1, // 连续点击次数
    val clickInterval: Long = 500L, // 点击间隔（毫秒）
    val longPressDuration: Long = 1000L, // 长按持续时间（毫秒）
    val swipeStartX: Float = 0.5f, // 滑动起始X坐标
    val swipeStartY: Float = 0.8f, // 滑动起始Y坐标
    val swipeEndX: Float = 0.5f, // 滑动结束X坐标
    val swipeEndY: Float = 0.2f, // 滑动结束Y坐标
    val swipeDuration: Long = 300L // 滑动持续时间（毫秒）
) : SharedTask {
    override val type: String = "screen_control"
    override val displayName: String = "屏幕控制"

    override fun getDescription(): String {
        return when (operation) {
            ScreenControlOperation.BRIGHTNESS_CONTROL -> {
                val typeText = when (brightnessControlType) {
                    BrightnessControlType.PERCENTAGE -> "百分比"
                    BrightnessControlType.ABSOLUTE_VALUE -> "绝对值"
                }
                val autoText = if (enableAutoBrightness) "，自动亮度开启" else ""
                "亮度控制: ${brightnessValue}${if (brightnessControlType == BrightnessControlType.PERCENTAGE) "%" else ""} ($typeText$autoText)"
            }
            ScreenControlOperation.KEEP_DEVICE_AWAKE -> {
                when (keepAwakeOperation) {
                    KeepAwakeOperation.ENABLE -> "保持设备唤醒: 启用"
                    KeepAwakeOperation.DISABLE -> "保持设备唤醒: 禁用"
                }
            }
            ScreenControlOperation.SCREEN_ON_OFF -> {
                val operationText = when (screenOnOffOperation) {
                    ScreenOnOffOperation.TURN_ON -> "开启屏幕"
                    ScreenOnOffOperation.TURN_OFF -> "关闭屏幕"
                }
                val implementationText = screenOnOffImplementation?.displayName ?: "未选择实现方式"
                "屏幕开关: $operationText ($implementationText)"
            }
            ScreenControlOperation.SCREEN_DIMNESS -> {
                when (screenDimnessSensorMode) {
                    ScreenDimnessSensorMode.SENSOR_ON -> "屏幕暗度: 光传感器开启，暗度${screenDimnessPercentage}%"
                    ScreenDimnessSensorMode.SENSOR_OFF -> "屏幕暗度: 光传感器关闭"
                }
            }
            ScreenControlOperation.BLOCK_SCREEN_TOUCH -> {
                val operationText = when (touchBlockOperation) {
                    TouchBlockOperation.ENABLE -> "启用"
                    TouchBlockOperation.DISABLE -> "禁用"
                    TouchBlockOperation.TOGGLE -> "切换"
                }
                val emergencyText = if (emergencyCloseEnabled) {
                    "应急关闭: ${emergencyClickCount}次点击/${emergencyTimeWindowSeconds}秒"
                } else {
                    "应急关闭: 禁用"
                }
                "屏蔽屏幕触摸: $operationText ($emergencyText)"
            }
            ScreenControlOperation.FORCE_SCREEN_ROTATION -> {
                when (forceRotationMode) {
                    ForceRotationMode.FORCE_PORTRAIT -> "强制屏幕旋转: 强制竖屏"
                    ForceRotationMode.FORCE_LANDSCAPE -> "强制屏幕旋转: 强制横屏"
                    ForceRotationMode.STOP_FORCE_ROTATION -> "强制屏幕旋转: 停止强制旋转"
                    ForceRotationMode.FORCE_KEEP_CURRENT -> "强制屏幕旋转: 强制保持当前方向"
                    ForceRotationMode.TOGGLE_KEEP_CURRENT -> "强制屏幕旋转: 切换是否强制保持当前方向"
                    ForceRotationMode.FORCE_REVERSE_PORTRAIT -> "强制屏幕旋转: 强制反向竖屏"
                    ForceRotationMode.FORCE_REVERSE_LANDSCAPE -> "强制屏幕旋转: 强制反向横屏"
                    ForceRotationMode.FORCE_SENSOR_LANDSCAPE -> "强制屏幕旋转: 强制传感器横屏"
                    ForceRotationMode.FORCE_SENSOR_PORTRAIT -> "强制屏幕旋转: 强制传感器竖屏"
                    ForceRotationMode.FORCE_SENSOR_ROTATION -> "强制屏幕旋转: 强制按传感器方向旋转"
                }
            }
            ScreenControlOperation.SET_SCREEN_TIMEOUT -> {
                val unitText = when (screenTimeoutUnit) {
                    ScreenTimeoutUnit.SECONDS -> "秒"
                    ScreenTimeoutUnit.MINUTES -> "分钟"
                }
                "设置屏幕超时: ${screenTimeoutValue}${unitText}"
            }
            ScreenControlOperation.CHECK_SCREEN_TEXT -> {
                val textDesc = if (checkTextContent.isNotEmpty()) "「$checkTextContent」" else "指定文本"
                val outputDesc = if (checkTextOutputFile.isNotEmpty()) checkTextOutputFile else "默认文件"
                "检查屏幕文字: $textDesc → $outputDesc"
            }
            ScreenControlOperation.READ_SCREEN_CONTENT -> {
                val outputDesc = if (readContentOutputFile.isNotEmpty()) readContentOutputFile else "默认文件"
                "读取屏幕内容 → $outputDesc"
            }
            ScreenControlOperation.CHECK_PIXEL_COLOR -> {
                val coordDesc = when (checkPixelCoordinateType) {
                    CoordinateType.PIXEL -> "像素坐标"
                    CoordinateType.PERCENTAGE -> "百分比坐标"
                }
                val outputDesc = if (checkPixelColorOutputFile.isNotEmpty()) checkPixelColorOutputFile else "默认文件"
                "检查界面元素颜色: (${checkPixelX}, ${checkPixelY}) $coordDesc → $outputDesc"
            }
            ScreenControlOperation.AUTO_CLICKER_PLAYBACK -> {
                val sourceDesc = when (autoClickerSourceType) {
                    AutoClickerSourceType.INSTANT_RECORDING -> {
                        if (recordedGesture.isNotEmpty()) "即时录制" else "待录制"
                    }
                    AutoClickerSourceType.QUICK_OPERATION -> {
                        when (quickOperationType) {
                            QuickOperationType.SINGLE_CLICK -> "单点点击"
                            QuickOperationType.CONTINUOUS_CLICK -> "连续点击${clickCount}次"
                            QuickOperationType.SWIPE_OPERATION -> "滑动操作"
                            QuickOperationType.LONG_PRESS -> "长按${longPressDuration}ms"
                        }
                    }
                }
                val loopDesc = when (playbackLoopCount) {
                    0 -> "无限循环"
                    1 -> "执行1次"
                    else -> "循环${playbackLoopCount}次"
                }
                val speedDesc = if (playbackSpeed != 1.0f) "，${playbackSpeed}x速度" else ""
                "自动点击回放: $sourceDesc ($loopDesc$speedDesc)"
            }
        }
    }
}

/**
 * 设备设置操作类型
 */
enum class DeviceSettingsOperation(val displayName: String) {
    INVERT_COLORS("反色"),
    FONT_SIZE("字体大小"),
    ENTER_SCREENSAVER("进入屏保模式"),
    AUTO_ROTATE("屏幕自动旋转"),
    ACCESSIBILITY_SERVICE("无障碍服务"),
    DISPLAY_DENSITY("显示密度"),
    IMMERSIVE_MODE("沉浸模式"),
    DARK_THEME("深色主题"),
    DEMO_MODE("演示模式"),
    AMBIENT_DISPLAY("环境显示"),
    POWER_SAVE_MODE("省电模式"),
    SYSTEM_SETTINGS("系统设置"),
    SET_WALLPAPER("设置壁纸"),
    SCREEN_LOCK("设置屏幕锁定"),
    DIGITAL_ASSISTANT("设置数字助理"),
    DEFAULT_KEYBOARD("键盘-设置默认值"),
    KEYBOARD_HINT("键盘提示"),
    DRIVING_MODE("驾驶模式")
}

/**
 * 三态开关操作类型
 */
enum class TriStateOperation(val displayName: String) {
    ENABLE("开启"),
    DISABLE("关闭"),
    TOGGLE("切换")
}

/**
 * 系统设置表类型
 */
enum class SystemSettingsTable(val displayName: String, val tableName: String) {
    SYSTEM("系统设置", "system"),
    SECURE("安全设置", "secure"),
    GLOBAL("全局设置", "global")
}

/**
 * 系统设置值类型
 */
enum class SystemSettingsValueType(val displayName: String) {
    INTEGER("整数"),
    FLOAT("浮点数"),
    LONG("长整数"),
    STRING("字符串")
}

/**
 * 壁纸类型
 */
enum class WallpaperType(val displayName: String) {
    IMAGE("图片"),
    LIVE_WALLPAPER("动态壁纸")
}

/**
 * 壁纸应用位置
 */
enum class WallpaperLocation(val displayName: String) {
    HOME_SCREEN("主屏幕"),
    LOCK_SCREEN("锁定屏幕"),
    BOTH("主屏幕+锁定屏幕")
}

/**
 * 沉浸模式类型
 */
enum class ImmersiveModeType(val displayName: String) {
    DISABLE("关闭"),
    HIDE_NAVIGATION("隐藏导航栏"),
    HIDE_STATUS_BAR("隐藏状态栏"),
    FULL_IMMERSIVE("完全沉浸式")
}

/**
 * 环境显示模式
 */
enum class AmbientDisplayMode(val displayName: String) {
    NOTIFICATION_WAKE("通知唤醒"),
    ALWAYS_ON("始终开启")
}

/**
 * 设备设置任务
 * 统一的设备设置功能，支持各种系统设置的修改
 *
 * @property id 任务唯一标识
 * @property operation 设备设置操作类型
 * @property invertColorsOperation 反色操作（仅在operation为INVERT_COLORS时有效）
 * @property fontSizePercentage 字体大小百分比（仅在operation为FONT_SIZE时有效）
 * @property autoRotateOperation 屏幕自动旋转操作（仅在operation为AUTO_ROTATE时有效）
 * @property accessibilityServiceOperation 无障碍服务操作（仅在operation为ACCESSIBILITY_SERVICE时有效）
 * @property accessibilityServicePackage 无障碍服务包名（仅在accessibilityServiceOperation不为TOGGLE时有效）
 * @property displayDensityPercentage 显示密度百分比（仅在operation为DISPLAY_DENSITY时有效）
 * @property immersiveModeType 沉浸模式类型（仅在operation为IMMERSIVE_MODE时有效）
 * @property darkThemeOperation 深色主题操作（仅在operation为DARK_THEME时有效）
 * @property demoModeOperation 演示模式操作（仅在operation为DEMO_MODE时有效）
 * @property ambientDisplayMode 环境显示模式（仅在operation为AMBIENT_DISPLAY时有效）
 * @property powerSaveModeOperation 省电模式操作（仅在operation为POWER_SAVE_MODE时有效）
 * @property systemSettingsTable 系统设置表（仅在operation为SYSTEM_SETTINGS时有效）
 * @property systemSettingsKey 系统设置键（仅在operation为SYSTEM_SETTINGS时有效）
 * @property systemSettingsValue 系统设置值（仅在operation为SYSTEM_SETTINGS时有效）
 * @property systemSettingsValueType 系统设置值类型（仅在operation为SYSTEM_SETTINGS时有效）
 * @property wallpaperType 壁纸类型（仅在operation为SET_WALLPAPER时有效）
 * @property wallpaperImagePath 壁纸图片路径（仅在wallpaperType为IMAGE时有效）
 * @property wallpaperLocation 壁纸应用位置（仅在operation为SET_WALLPAPER时有效）
 * @property screenLockOperation 屏幕锁定操作（仅在operation为SCREEN_LOCK时有效）
 * @property digitalAssistantPackage 数字助理包名（仅在operation为DIGITAL_ASSISTANT时有效）
 * @property keyboardPackage 键盘包名（仅在operation为DEFAULT_KEYBOARD时有效）
 * @property drivingModeOperation 驾驶模式操作（仅在operation为DRIVING_MODE时有效）
 */
data class DeviceSettingsTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: DeviceSettingsOperation = DeviceSettingsOperation.INVERT_COLORS,

    // 反色参数
    val invertColorsOperation: TriStateOperation = TriStateOperation.TOGGLE,

    // 字体大小参数
    val fontSizePercentage: Int = 100,

    // 屏幕自动旋转参数
    val autoRotateOperation: TriStateOperation = TriStateOperation.TOGGLE,

    // 无障碍服务参数
    val accessibilityServiceOperation: TriStateOperation = TriStateOperation.TOGGLE,
    val accessibilityServicePackage: String = "",

    // 显示密度参数
    val displayDensityPercentage: Int = 100,

    // 沉浸模式参数
    val immersiveModeType: ImmersiveModeType = ImmersiveModeType.FULL_IMMERSIVE,

    // 深色主题参数
    val darkThemeOperation: TriStateOperation = TriStateOperation.TOGGLE,

    // 演示模式参数
    val demoModeOperation: TriStateOperation = TriStateOperation.TOGGLE,

    // 环境显示参数
    val ambientDisplayMode: AmbientDisplayMode = AmbientDisplayMode.NOTIFICATION_WAKE,

    // 省电模式参数
    val powerSaveModeOperation: TriStateOperation = TriStateOperation.TOGGLE,

    // 系统设置参数
    val systemSettingsTable: SystemSettingsTable = SystemSettingsTable.SYSTEM,
    val systemSettingsKey: String = "",
    val systemSettingsValue: String = "",
    val systemSettingsValueType: SystemSettingsValueType = SystemSettingsValueType.STRING,

    // 壁纸参数
    val wallpaperType: WallpaperType = WallpaperType.IMAGE,
    val wallpaperImagePath: String = "",
    val wallpaperLocation: WallpaperLocation = WallpaperLocation.HOME_SCREEN,
    val liveWallpaperPackage: String = "",

    // 屏幕锁定参数
    val screenLockOperation: TriStateOperation = TriStateOperation.ENABLE,

    // 数字助理参数
    val digitalAssistantPackage: String = "",

    // 键盘参数
    val keyboardPackage: String = "",

    // 驾驶模式参数
    val drivingModeOperation: TriStateOperation = TriStateOperation.TOGGLE
) : SharedTask {
    override val type: String = "device_settings"
    override val displayName: String = "设备设置"

    override fun getDescription(): String {
        return when (operation) {
            DeviceSettingsOperation.INVERT_COLORS -> "反色: ${invertColorsOperation.displayName}"
            DeviceSettingsOperation.FONT_SIZE -> "字体大小: ${fontSizePercentage}%"
            DeviceSettingsOperation.ENTER_SCREENSAVER -> "进入屏保模式"
            DeviceSettingsOperation.AUTO_ROTATE -> "屏幕自动旋转: ${autoRotateOperation.displayName}"
            DeviceSettingsOperation.ACCESSIBILITY_SERVICE -> {
                val serviceText = if (accessibilityServicePackage.isNotEmpty()) " ($accessibilityServicePackage)" else ""
                "无障碍服务: ${accessibilityServiceOperation.displayName}$serviceText"
            }
            DeviceSettingsOperation.DISPLAY_DENSITY -> "显示密度: ${displayDensityPercentage}%"
            DeviceSettingsOperation.IMMERSIVE_MODE -> "沉浸模式: ${immersiveModeType.displayName}"
            DeviceSettingsOperation.DARK_THEME -> "深色主题: ${darkThemeOperation.displayName}"
            DeviceSettingsOperation.DEMO_MODE -> "演示模式: ${demoModeOperation.displayName}"
            DeviceSettingsOperation.AMBIENT_DISPLAY -> "环境显示: ${ambientDisplayMode.displayName}"
            DeviceSettingsOperation.POWER_SAVE_MODE -> "省电模式: ${powerSaveModeOperation.displayName}"
            DeviceSettingsOperation.SYSTEM_SETTINGS -> {
                val keyText = if (systemSettingsKey.isNotEmpty()) systemSettingsKey else "未设置"
                val valueText = if (systemSettingsValue.isNotEmpty()) systemSettingsValue else "未设置"
                "系统设置: ${systemSettingsTable.displayName} - $keyText = $valueText"
            }
            DeviceSettingsOperation.SET_WALLPAPER -> {
                val typeText = wallpaperType.displayName
                val locationText = wallpaperLocation.displayName
                "设置壁纸: $typeText ($locationText)"
            }
            DeviceSettingsOperation.SCREEN_LOCK -> "屏幕锁定: ${screenLockOperation.displayName}"
            DeviceSettingsOperation.DIGITAL_ASSISTANT -> {
                val assistantText = if (digitalAssistantPackage.isNotEmpty()) digitalAssistantPackage else "未选择"
                "数字助理: $assistantText"
            }
            DeviceSettingsOperation.DEFAULT_KEYBOARD -> {
                val keyboardText = if (keyboardPackage.isNotEmpty()) keyboardPackage else "未选择"
                "默认键盘: $keyboardText"
            }
            DeviceSettingsOperation.KEYBOARD_HINT -> "键盘提示"
            DeviceSettingsOperation.DRIVING_MODE -> "驾驶模式: ${drivingModeOperation.displayName}"
        }
    }
}

/**
 * 位置操作类型
 */
enum class LocationOperation(val displayName: String) {
    SHARE_LOCATION("分享位置"),
    TOGGLE_LOCATION_SERVICE("切换定位服务"),
    FORCE_LOCATION_UPDATE("强制位置更新"),
    SET_LOCATION_UPDATE_FREQUENCY("设置位置更新频率")
}

/**
 * 位置分享方式
 */
enum class LocationShareMethod(val displayName: String) {
    SMS("短信"),
    CONTACT("联系人"),
    PHONE_NUMBER("手机号码")
}

/**
 * 定位服务控制方式
 */
enum class LocationServiceControlMethod(val displayName: String) {
    SYSTEM_SETTINGS("系统设置"),
    SHIZUKU("Shizuku命令")
}

/**
 * 位置更新频率单位
 */
enum class LocationUpdateFrequencyUnit(val displayName: String) {
    SECONDS("秒"),
    MINUTES("分钟")
}

/**
 * 位置任务
 *
 * @property id 任务唯一标识
 * @property operation 位置操作类型
 * @property shareMethod 位置分享方式
 * @property contactId 联系人ID（当分享方式为联系人时使用）
 * @property contactName 联系人姓名（用于显示）
 * @property phoneNumber 手机号码（当分享方式为手机号码时使用）
 * @property shareMessage 分享消息内容
 * @property locationServiceOperation 定位服务操作（开启/关闭/切换）
 * @property locationServiceControlMethod 定位服务控制方式
 * @property updateFrequencyValue 位置更新频率数值
 * @property updateFrequencyUnit 位置更新频率单位
 */
data class LocationTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: LocationOperation = LocationOperation.SHARE_LOCATION,
    val shareMethod: LocationShareMethod = LocationShareMethod.SMS,
    val contactId: String = "",
    val contactName: String = "",
    val phoneNumber: String = "",
    val shareMessage: String = "我的位置：",
    val locationServiceOperation: SwitchOperation = SwitchOperation.TOGGLE,
    val locationServiceControlMethod: LocationServiceControlMethod = LocationServiceControlMethod.SYSTEM_SETTINGS,
    val updateFrequencyValue: Int = 30,
    val updateFrequencyUnit: LocationUpdateFrequencyUnit = LocationUpdateFrequencyUnit.SECONDS
) : SharedTask {
    override val type: String = "location"
    override val displayName: String = "位置"

    override fun getDescription(): String {
        return when (operation) {
            LocationOperation.SHARE_LOCATION -> {
                val methodText = when (shareMethod) {
                    LocationShareMethod.SMS -> "短信"
                    LocationShareMethod.CONTACT -> if (contactName.isNotEmpty()) "联系人: $contactName" else "联系人: 未选择"
                    LocationShareMethod.PHONE_NUMBER -> if (phoneNumber.isNotEmpty()) "号码: $phoneNumber" else "号码: 未设置"
                }
                "分享位置: $methodText"
            }
            LocationOperation.TOGGLE_LOCATION_SERVICE -> {
                val controlText = when (locationServiceControlMethod) {
                    LocationServiceControlMethod.SYSTEM_SETTINGS -> "系统设置"
                    LocationServiceControlMethod.SHIZUKU -> "Shizuku"
                }
                "定位服务: ${locationServiceOperation.displayName} ($controlText)"
            }
            LocationOperation.FORCE_LOCATION_UPDATE -> "强制位置更新"
            LocationOperation.SET_LOCATION_UPDATE_FREQUENCY -> {
                "位置更新频率: ${updateFrequencyValue}${updateFrequencyUnit.displayName}"
            }
        }
    }
}

/**
 * 应用程序任务操作类型
 */
enum class ApplicationOperation(val displayName: String) {
    JAVASCRIPT_CODE("JavaScript代码"),
    EXECUTE_JAVASCRIPT("执行JavaScript代码"),
    SHELL_SCRIPT("Shell脚本"),
    EXECUTE_SHELL_SCRIPT("执行Shell脚本"),
    TASKER_PLUGIN("Tasker/Locale插件"),
    TASKER_LOCALE_PLUGIN("Tasker/Locale插件"),
    LAUNCH_APP("启动应用"),
    LAUNCH_SHORTCUT("启动快捷方式"),
    OPEN_WEBSITE("打开网站"),
    FORCE_STOP_APP("强制停止应用"),
    FREEZE_APP("冻结应用"),
    UNFREEZE_APP("解冻应用")
}

/**
 * Shell脚本执行方式
 */
enum class ShellExecutionMode(val displayName: String) {
    NORMAL("普通运行"),
    SHIZUKU("通过Shizuku运行")
}

/**
 * 强制停止应用范围
 */
enum class ForceStopScope(val displayName: String) {
    SELECTED_APP("指定应用列表"),
    TRIGGER_APP("触发条件的应用")
}

/**
 * 分组检查模式
 */
enum class GroupCheckMode(val displayName: String, val defaultGroupSize: Int) {
    FAST("快速模式", 10),      // 每10个应用检查一次
    BALANCED("平衡模式", 5),   // 每5个应用检查一次
    PRECISE("精确模式", 2),    // 每2个应用检查一次
    CUSTOM("自定义", 0)        // 自定义数量
}



/**
 * 清理规则类型
 */
enum class CleanupRuleType(val displayName: String, val description: String) {
    LOW_PRIORITY_APPS("低优先级应用", "用户标记为不重要的应用"),
    HIGH_PRIORITY_APPS("高优先级应用", "用户标记为重要的应用"),
    LONG_UNUSED_APPS("长时间未使用应用", "超过指定时间未使用的应用"),
    MEMORY_HEAVY_APPS("内存占用大的应用", "占用内存较多的应用"),
    SYSTEM_APPS("系统应用", "Android系统应用"),
    USER_APPS("用户应用", "用户安装的应用"),
    NORMAL_APPS("普通应用", "未特别标记的应用"),
    SPECIFIC_APPS("指定应用", "用户指定的特定应用"),

    // 使用频率相关规则
    MOST_USED_APPS("最常使用应用", "按使用频率排序，最常用的应用"),
    LEAST_USED_APPS("最少使用应用", "按使用频率排序，最少用的应用"),
    FREQUENT_APPS("高频使用应用", "启动次数超过阈值的应用"),
    RARE_APPS("低频使用应用", "启动次数低于阈值的应用"),
    LONG_SESSION_APPS("长时间使用应用", "单次使用时长较长的应用"),
    SHORT_SESSION_APPS("短时间使用应用", "单次使用时长较短的应用"),
    SMART_USAGE_RANKING("智能使用排序", "综合考虑频率、时长、最近使用时间的智能排序")
}

/**
 * 清理规则
 */
data class CleanupRule(
    val id: String = UUID.randomUUID().toString(),
    val type: CleanupRuleType,
    val order: Int, // 执行顺序，数字越小越先执行
    val enabled: Boolean = true,
    val parameters: Map<String, Any> = emptyMap() // 规则参数，如时间阈值等
)

/**
 * 清理策略
 */
data class CleanupStrategy(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String,
    val rules: List<CleanupRule>,
    val isPreset: Boolean = false // 是否为预设策略
)

/**
 * 应用重要性标记
 */
enum class AppImportance(val displayName: String, val priority: Int) {
    VERY_IMPORTANT("非常重要", 1),
    IMPORTANT("重要", 2),
    NORMAL("普通", 3),
    UNIMPORTANT("不重要", 4),
    VERY_UNIMPORTANT("非常不重要", 5)
}

/**
 * 应用排序策略
 */
enum class AppSortingStrategy(val displayName: String, val description: String) {
    BACKGROUND_TIME_ONLY("按使用时间排序", "最久没用的应用优先停止"),
    PRIORITY_ONLY("按重要性排序", "不重要的应用优先停止"),
    PRIORITY_THEN_BACKGROUND_TIME("重要性 + 使用时间", "不重要的应用优先停止，相同重要性时最久没用的优先"),
    BACKGROUND_TIME_THEN_PRIORITY("使用时间 + 重要性", "最久没用的应用优先停止，使用时间相近时不重要的优先"),
    SMART_COMPREHENSIVE("智能综合排序", "综合考虑重要性和使用时间，智能决定停止顺序")
}

/**
 * 内存检查频率
 */
enum class MemoryCheckFrequency(val displayName: String, val description: String, val groupSize: Int) {
    FREQUENT("频繁检查", "每清理2个应用检查一次内存 - 更精确", 2),
    BALANCED("平衡检查", "每清理5个应用检查一次内存 - 推荐", 5),
    FAST("快速检查", "每清理10个应用检查一次内存 - 更快", 10)
}

/**
 * 条件检测频率
 * 用于设备事件条件和应用状态条件的检测频率设置
 */
enum class ConditionCheckFrequency(val displayName: String, val description: String, val intervalMs: Long) {
    VERY_FREQUENT("非常频繁", "每1秒检查一次 - 最灵敏，耗电较多", 1000L),
    FREQUENT("频繁检查", "每2秒检查一次 - 较灵敏，耗电适中", 2000L),
    BALANCED("平衡检查", "每5秒检查一次 - 推荐设置", 5000L),
    MODERATE("适中检查", "每10秒检查一次 - 较省电", 10000L),
    SLOW("缓慢检查", "每30秒检查一次 - 最省电，响应较慢", 30000L)
}

/**
 * 内存检测模式
 */
enum class MemoryCheckMode(val displayName: String, val description: String) {
    TRADITIONAL("传统定时检测", "按固定间隔持续检测内存状态"),
    EVENT_DRIVEN("事件驱动检测", "基于应用启动等事件触发检测"),
    ADAPTIVE("自适应检测", "根据系统状态动态调整检测频率"),
    INTELLIGENT("智能学习模式", "基于应用行为学习的智能检测"),
    HYBRID("混合模式", "结合多种检测策略的优点")
}



/**
 * 使用统计时间范围
 */
enum class UsageTimeRange(val displayName: String, val days: Int) {
    ONE_DAY("最近1天", 1),
    THREE_DAYS("最近3天", 3),
    ONE_WEEK("最近7天", 7),
    ONE_MONTH("最近30天", 30)
}

/**
 * 使用频率排序模式
 */
enum class UsageFrequencyMode(val displayName: String, val description: String) {
    BY_LAUNCH_COUNT("按启动次数", "根据应用启动次数排序"),
    BY_USAGE_TIME("按使用时长", "根据应用总使用时长排序"),
    BY_AVERAGE_SESSION("按平均使用时长", "根据单次平均使用时长排序"),
    BY_SMART_SCORE("智能综合评分", "综合考虑多个维度的智能评分")
}

/**
 * 应用使用统计数据
 */
data class AppUsageStats(
    val packageName: String,
    val appName: String,
    val totalTimeInForeground: Long, // 总前台时间（毫秒）
    val launchCount: Int, // 启动次数
    val lastTimeUsed: Long, // 最后使用时间
    val averageSessionTime: Long, // 平均单次使用时长
    val usageScore: Float, // 综合使用评分
    val timeRange: UsageTimeRange // 统计时间范围
)

/**
 * 简单策略类型
 */
enum class SimpleStrategyType(val displayName: String, val description: String) {
    PROTECT_IMPORTANT("保护重要应用", "优先清理不重要的应用，保护通讯、工作类重要应用"),
    UNUSED_FIRST("优先清理久未使用", "按后台时间排序，最久未使用的应用优先清理"),
    MEMORY_FIRST("内存优先清理", "优先清理占用内存较大的应用"),
    BALANCED("平衡策略", "综合考虑重要性和使用时间"),
    SMART_USAGE_FREQUENCY("智能使用频率排序", "按应用使用频率智能排序，保护常用应用，优先清理少用应用")
}

/**
 * 应用程序任务
 * 统一的应用程序操作功能，整合多种应用相关操作
 *
 * @property id 任务唯一标识
 * @property operation 应用程序操作类型
 * @property javascriptCode JavaScript代码内容
 * @property shellScript Shell脚本内容
 * @property shellExecutionMode Shell脚本执行方式
 * @property shellTimeoutMinutes Shell脚本超时时间（分钟）
 * @property shellTimeoutSeconds Shell脚本超时时间（秒）
 * @property shellWaitForCompletion 是否等待Shell脚本完成后才能后续动作
 * @property taskerPluginPackage Tasker/Locale插件包名
 * @property taskerPluginAction Tasker/Locale插件动作
 * @property taskerPluginExtras Tasker/Locale插件额外参数
 * @property appPackageName 应用包名
 * @property appName 应用名称（用于显示）
 * @property shortcutId 快捷方式ID
 * @property shortcutName 快捷方式名称（用于显示）
 * @property websiteUrl 网站URL
 * @property forceStopScope 强制停止应用范围
 * @property forceStopPackageName 要强制停止的应用包名（当范围为选择的应用时使用）
 * @property forceStopAppName 要强制停止的应用名称（用于显示）
 * @property skipForegroundApp 检测到是前台应用时跳过
 * @property sortByBackgroundTime 是否按后台时间排序停止（优先停止最久未使用的应用）
 */
data class ApplicationTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: ApplicationOperation = ApplicationOperation.LAUNCH_APP,

    // JavaScript代码参数
    val javascriptCode: String = "",

    // Shell脚本参数
    val shellScript: String = "",
    val shellExecutionMode: ShellExecutionMode = ShellExecutionMode.NORMAL,
    val shellTimeoutMinutes: Int = 0,
    val shellTimeoutSeconds: Int = 30,
    val shellWaitForCompletion: Boolean = false,

    // Tasker/Locale插件参数
    val taskerPluginPackage: String = "",
    val taskerPluginAction: String = "",
    val taskerPluginExtras: String = "",

    // 启动应用参数
    val appPackageName: String = "",
    val appName: String = "",

    // 启动快捷方式参数
    val shortcutId: String = "",
    val shortcutName: String = "",

    // 打开网站参数
    val websiteUrl: String = "",

    // 强制停止应用参数
    val forceStopScope: ForceStopScope = ForceStopScope.SELECTED_APP,
    val forceStopSelectedApps: List<SimpleAppInfo> = emptyList(), // 应用列表
    val skipForegroundApp: Boolean = true,
    val skipMusicPlayingApp: Boolean = true, // 跳过正在播放音乐的应用
    val skipVpnApp: Boolean = false, // 跳过VPN应用
    val selectedVpnApps: List<SimpleAppInfo> = emptyList(), // 用户指定的VPN应用列表
    val autoIncludeNewApps: Boolean = false, // 自动包含新安装应用（仅用于强制停止应用任务）
    val sortByBackgroundTime: Boolean = false, // 按后台时间排序停止
    val appSortingStrategy: AppSortingStrategy = AppSortingStrategy.BACKGROUND_TIME_ONLY, // 应用排序策略
    val enableRealtimeForegroundDetection: Boolean = false, // 启用实时前台应用检测

    // 自定义强制停止命令参数
    val useCustomForceStopCommand: Boolean = false, // 是否使用自定义命令
    val customForceStopCommand: String = "", // 自定义命令模板
    val effectiveForceStopCommand: String = "am force-stop [package_name]", // 最终生效的命令（预处理后）

    // 智能内存管理配置
    val enableMemoryThresholdCheck: Boolean = false, // 启用内存阈值检查
    val memoryThreshold: Int = 3, // 内存阈值
    val memoryThresholdIsPercentage: Boolean = false, // 内存阈值是否为百分比
    val groupCheckMode: GroupCheckMode = GroupCheckMode.BALANCED, // 分组检查模式
    val customGroupSize: Int = 5, // 自定义分组大小
    val memoryCheckFrequency: MemoryCheckFrequency = MemoryCheckFrequency.BALANCED, // 内存检查频率
    val enableCustomCheckFrequency: Boolean = false, // 启用自定义检查频率
    val customCheckFrequencySize: Int = 5, // 自定义检查频率大小

    // 自定义清理策略配置
    val enableCustomCleanupStrategy: Boolean = false, // 启用自定义清理策略
    val cleanupStrategyId: String = "", // 清理策略ID
    val useSimpleMode: Boolean = true, // 使用简单模式
    val simpleStrategyType: SimpleStrategyType = SimpleStrategyType.PROTECT_IMPORTANT, // 简单策略类型

    // 网站参数
    val urlEncodeParams: Boolean = false, // URL参数编码

    // 应用重要性筛选配置
    val enableAppImportanceFilter: Boolean = false, // 启用应用重要性筛选
    val selectedImportanceLevels: Set<AppImportance> = emptySet(), // 选择的重要性级别

    // 冻结应用参数
    val freezeScope: ForceStopScope = ForceStopScope.SELECTED_APP, // 冻结范围（复用ForceStopScope）
    val freezeSelectedApps: List<SimpleAppInfo> = emptyList(), // 要冻结的应用列表

    // 解冻应用参数
    val unfreezeScope: ForceStopScope = ForceStopScope.SELECTED_APP, // 解冻范围（复用ForceStopScope）
    val unfreezeSelectedApps: List<SimpleAppInfo> = emptyList(), // 要解冻的应用列表
    val openAppAfterUnfreeze: Boolean = false // 解冻后是否打开应用
) : SharedTask {
    override val type: String = "application"
    override val displayName: String = "应用程序"

    override fun getDescription(): String {
        return when (operation) {
            ApplicationOperation.JAVASCRIPT_CODE, ApplicationOperation.EXECUTE_JAVASCRIPT -> {
                val codePreview = if (javascriptCode.length > 30) javascriptCode.take(30) + "..." else javascriptCode
                "JavaScript: $codePreview"
            }
            ApplicationOperation.SHELL_SCRIPT, ApplicationOperation.EXECUTE_SHELL_SCRIPT -> {
                val scriptPreview = if (shellScript.length > 30) shellScript.take(30) + "..." else shellScript
                val modeText = shellExecutionMode.displayName
                "Shell脚本: $scriptPreview ($modeText)"
            }
            ApplicationOperation.TASKER_PLUGIN, ApplicationOperation.TASKER_LOCALE_PLUGIN -> {
                val pluginText = if (taskerPluginPackage.isNotEmpty()) taskerPluginPackage else "未选择插件"
                "Tasker插件: $pluginText"
            }
            ApplicationOperation.LAUNCH_APP -> {
                val appText = if (appName.isNotEmpty()) appName else if (appPackageName.isNotEmpty()) appPackageName else "未选择应用"
                "启动应用: $appText"
            }
            ApplicationOperation.LAUNCH_SHORTCUT -> {
                val shortcutText = if (shortcutName.isNotEmpty()) shortcutName else "未选择快捷方式"
                "启动快捷方式: $shortcutText"
            }
            ApplicationOperation.OPEN_WEBSITE -> {
                val urlText = if (websiteUrl.isNotEmpty()) websiteUrl else "未设置网址"
                "打开网站: $urlText"
            }
            ApplicationOperation.FORCE_STOP_APP -> {
                val scopeText = when (forceStopScope) {
                    ForceStopScope.SELECTED_APP -> {
                        if (forceStopSelectedApps.isNotEmpty()) {
                            if (forceStopSelectedApps.size == 1) {
                                forceStopSelectedApps.first().appName
                            } else {
                                "${forceStopSelectedApps.size}个应用"
                            }
                        } else {
                            "未选择应用"
                        }
                    }
                    ForceStopScope.TRIGGER_APP -> "触发条件的应用"
                }
                val skipOptions = mutableListOf<String>()
                if (skipForegroundApp) skipOptions.add("跳过前台")
                if (skipMusicPlayingApp) skipOptions.add("跳过音乐")
                if (skipVpnApp) skipOptions.add("跳过VPN")
                if (sortByBackgroundTime) skipOptions.add("智能排序")
                if (enableMemoryThresholdCheck) {
                    val unit = if (memoryThresholdIsPercentage) "%" else "GB"
                    val frequencyText = if (enableCustomCheckFrequency) {
                        "自定义${customCheckFrequencySize}个"
                    } else {
                        memoryCheckFrequency.displayName
                    }
                    skipOptions.add("内存>${memoryThreshold}${unit}($frequencyText)")
                }
                if (enableCustomCleanupStrategy) {
                    val strategyText = if (useSimpleMode) {
                        simpleStrategyType.displayName
                    } else {
                        "自定义策略"
                    }
                    skipOptions.add(strategyText)
                }
                if (enableAppImportanceFilter && selectedImportanceLevels.isNotEmpty()) {
                    val importanceText = selectedImportanceLevels.joinToString(",") { it.displayName }
                    skipOptions.add("重要性:$importanceText")
                }
                if (selectedVpnApps.isNotEmpty()) {
                    skipOptions.add("VPN应用:${selectedVpnApps.size}个")
                }
                if (useCustomForceStopCommand && customForceStopCommand.isNotEmpty()) {
                    skipOptions.add("自定义命令")
                }
                val skipText = if (skipOptions.isNotEmpty()) "(${skipOptions.joinToString(", ")})" else ""
                "强制停止: $scopeText $skipText"
            }
            ApplicationOperation.FREEZE_APP -> {
                val scopeText = when (freezeScope) {
                    ForceStopScope.SELECTED_APP -> {
                        if (freezeSelectedApps.isNotEmpty()) {
                            if (freezeSelectedApps.size == 1) {
                                freezeSelectedApps.first().appName
                            } else {
                                "${freezeSelectedApps.size}个应用"
                            }
                        } else {
                            "未选择应用"
                        }
                    }
                    ForceStopScope.TRIGGER_APP -> "触发条件的应用"
                }
                "冻结应用: $scopeText"
            }
            ApplicationOperation.UNFREEZE_APP -> {
                val scopeText = when (unfreezeScope) {
                    ForceStopScope.SELECTED_APP -> {
                        if (unfreezeSelectedApps.isNotEmpty()) {
                            if (unfreezeSelectedApps.size == 1) {
                                unfreezeSelectedApps.first().appName
                            } else {
                                "${unfreezeSelectedApps.size}个应用"
                            }
                        } else {
                            "未选择应用"
                        }
                    }
                    ForceStopScope.TRIGGER_APP -> "触发条件的应用"
                }
                val openText = if (openAppAfterUnfreeze) "(解冻后打开)" else ""
                "解冻应用: $scopeText $openText"
            }

        }
    }
}

/**
 * 信息任务操作类型
 */
enum class InformationOperation {
    SEND_SMS,           // 发送短信
    SEND_EMAIL,         // 发送电子邮件
    MESSAGE_RINGTONE    // 信息铃声设置
}

/**
 * SIM卡选择类型
 */
enum class SimCardSelection {
    SIM1,           // SIM卡1
    SIM2,           // SIM卡2
    ASK_EACH_TIME   // 每次询问
}

/**
 * 信息任务
 *
 * @property id 任务唯一标识
 * @property operation 信息操作类型
 * @property phoneNumber 电话号码（短信用）
 * @property messageText 消息文本
 * @property simCardSelection SIM卡选择
 * @property draftOnly 仅预填写不发送
 * @property emailRecipient 收件人邮箱地址
 * @property emailSubject 邮件主题
 * @property smtpServer SMTP服务器地址
 * @property smtpPort SMTP端口
 * @property senderEmail 发件人邮箱
 * @property senderPassword 发件人邮箱密码
 * @property useSSL 是否使用SSL
 * @property useAuthentication 是否使用身份验证
 * @property supportStartTLS 是否支持StartTLS
 * @property notifyOnCompletion 是否在完成后通知
 * @property notifyOnFailure 是否在失败时通知
 * @property isHtmlEmail 是否为HTML邮件
 * @property username 用户名（用于身份验证）
 */
data class InformationTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: InformationOperation = InformationOperation.SEND_SMS,
    val phoneNumber: String = "",
    val messageText: String = "",
    val simCardSelection: SimCardSelection = SimCardSelection.ASK_EACH_TIME,
    val draftOnly: Boolean = false,
    val emailRecipient: String = "",
    val emailSubject: String = "",
    val smtpServer: String = "",
    val smtpPort: Int = 587,
    val senderEmail: String = "",
    val senderPassword: String = "",
    val useSSL: Boolean = true,

    // 新增的SMTP配置字段
    val useAuthentication: Boolean = true,
    val supportStartTLS: Boolean = true,
    val notifyOnCompletion: Boolean = false,
    val notifyOnFailure: Boolean = false,
    val isHtmlEmail: Boolean = false,
    val username: String = "",

    // 铃声配置相关属性
    val selectedRingtoneUri: String = "",
    val selectedRingtoneName: String = ""
) : SharedTask {
    override val type: String = "information"
    override val displayName: String = "信息"

    override fun getDescription(): String {
        return when (operation) {
            InformationOperation.SEND_SMS -> {
                val action = if (draftOnly) "预填写短信" else "发送短信"
                if (phoneNumber.isNotEmpty()) {
                    "$action 至 $phoneNumber"
                } else {
                    action
                }
            }
            InformationOperation.SEND_EMAIL -> {
                if (emailRecipient.isNotEmpty()) {
                    "发送邮件至 $emailRecipient"
                } else {
                    "发送邮件"
                }
            }
            InformationOperation.MESSAGE_RINGTONE -> {
                if (selectedRingtoneName.isNotEmpty()) {
                    "信息铃声: $selectedRingtoneName"
                } else {
                    "设置信息铃声"
                }
            }
        }
    }
}

/**
 * 任务注册表
 * 用于注册和获取可用的任务类型
 */
object SharedTaskRegistry {
    private val taskTypes = mutableListOf<SharedTaskType>()

    init {
        // 注册默认任务类型




        // 注册音量任务类型 - 已迁移到新的模块化配置系统
        // 现在使用VolumeTaskConfigProvider和DetailConfigurationScreen
        register(
            type = "volume",
            displayName = "音量",
            createDefaultTask = { VolumeTask() }
        )





        // 注册日期与时间任务类型 - 已迁移到新的模块化配置系统
        // 现在使用DateTimeTaskConfigProvider和DetailConfigurationScreen
        register(
            type = "date_time",
            displayName = "日期与时间",
            createDefaultTask = { DateTimeTask() }
        )





        // 注册连接任务类型 - 已迁移到新的模块化配置系统
        // 现在使用ConnectivityTaskConfigProvider和DetailConfigurationScreen
        register(
            type = "connectivity",
            displayName = "连接",
            createDefaultTask = { ConnectivityTask() }
        )

        // 注册电话任务类型
        register(
            type = "phone",
            displayName = "电话",
            createDefaultTask = { PhoneTask() }
        )

        // 注册媒体任务类型 - 已迁移到新的模块化配置系统
        // 注意：媒体任务现在使用MediaTaskConfigProvider和DetailConfigurationScreen
        // 在MainActivity中直接处理，不再通过SharedTaskRegistry的configDialogContent
        register(
            type = "media",
            displayName = "媒体",
            createDefaultTask = { MediaTask() }
        )

        // 注册屏幕控制任务类型 - 已迁移到新的模块化配置系统
        // 现在使用ScreenControlTaskConfigProvider和DetailConfigurationScreen
        register(
            type = "screen_control",
            displayName = "屏幕控制",
            createDefaultTask = { ScreenControlTask() }
        )

        // 注册设备动作任务类型 - 已迁移到新的模块化配置系统
        // 现在使用DeviceActionTaskConfigProvider和DetailConfigurationScreen
        register(
            type = "device_action",
            displayName = "设备动作",
            createDefaultTask = { DeviceActionTask() }
        )

        // 注册通知任务类型 - 已迁移到新的模块化配置系统
        // 现在使用NotificationTaskConfigProvider和DetailConfigurationScreen
        register(
            type = "notification",
            displayName = "通知",
            createDefaultTask = { NotificationTask() }
        )

        // 设备设置任务类型现在使用新的模块化配置系统
        // 通过DeviceSettingsTaskConfigProvider和DetailConfigurationScreen处理
        // 保留注册以支持数据序列化/反序列化，但配置界面通过MainActivity路由处理
        register(
            type = "device_settings",
            displayName = "设备设置",
            createDefaultTask = { DeviceSettingsTask() }
        )

        // 注册位置任务类型
        // 注意：位置任务已迁移到新的模块化配置系统，使用LocationTaskConfigProvider
        // 这里保留注册以支持数据序列化/反序列化，但配置界面通过MainActivity路由处理
        register(
            type = "location",
            displayName = "位置",
            createDefaultTask = { LocationTask() }
        )

        // 注册文件操作任务类型 - 已迁移到新的模块化配置系统
        // 注意：文件操作任务现在使用FileOperationTaskConfigProvider和DetailConfigurationScreen
        // 在MainActivity中直接处理，不再通过SharedTaskRegistry的configDialogContent
        register(
            type = "file_operation",
            displayName = "文件操作",
            createDefaultTask = { FileOperationTask() }
        )

        // 注册相机任务类型 - 已迁移到新的模块化配置系统
        register(
            type = "camera",
            displayName = "相机/照片",
            createDefaultTask = { CameraTask() }
        )

        // 注册信息任务类型 - 已迁移到新的模块化配置系统
        // 现在使用 InformationTaskConfigProvider 和 DetailConfigurationScreen
        register(
            type = "information",
            displayName = "信息",
            createDefaultTask = { InformationTask() }
        )

        // 注册应用程序任务类型 - 已迁移到新的模块化配置系统
        // 现在使用ApplicationTaskConfigProvider和DetailConfigurationScreen
        register(
            type = "application",
            displayName = "应用程序",
            createDefaultTask = { ApplicationTask() }
        )
    }

    /**
     * 注册新的任务类型
     */
    fun register(
        type: String,
        displayName: String,
        createDefaultTask: () -> SharedTask
    ) {
        taskTypes.add(
            SharedTaskType(
                type = type,
                displayName = displayName,
                createDefaultTask = createDefaultTask
            )
        )
    }

    /**
     * 获取所有注册的任务类型
     */
    fun getTaskTypes(): List<SharedTaskType> = taskTypes

    /**
     * 获取所有任务类型的类型标识列表
     */
    fun getTaskTypesList(): List<String> = taskTypes.map { it.type }

    /**
     * 根据类型获取任务类型
     */
    fun getTaskTypeByType(type: String): SharedTaskType? {
        return taskTypes.find { it.type == type }
    }

    /**
     * 根据类型获取显示名称
     *
     * @param taskType 任务类型标识
     * @return 显示名称，如果类型不存在则返回null
     */
    fun getDisplayName(taskType: String): String? {
        return getTaskTypeByType(taskType)?.displayName
    }

    /**
     * 创建默认任务实例
     *
     * @param taskType 任务类型标识
     * @return 创建的默认任务实例，如果类型不存在则返回null
     */
    fun createDefaultTask(taskType: String): SharedTask? {
        val type = getTaskTypeByType(taskType) ?: return null
        return type.createDefaultTask()
    }

    /**
     * 验证任务类型是否有效
     *
     * @param taskType 任务类型标识
     * @return 是否为有效的任务类型
     */
    fun isValidTaskType(taskType: String): Boolean {
        return getTaskTypeByType(taskType) != null
    }
}

/**
 * 任务类型
 *
 * @property type 任务类型标识
 * @property displayName 任务类型显示名称
 * @property createDefaultTask 创建默认任务实例的函数
 */
data class SharedTaskType(
    val type: String,
    val displayName: String,
    val createDefaultTask: () -> SharedTask
)



















/**
 * 文件操作类型枚举
 */
enum class FileOperation(val displayName: String) {
    WRITE_FILE("写入文件"),
    OPEN_FILE("打开文件"),
    FILE_OPERATION("文件操作")
}

/**
 * 文件写入模式枚举
 */
enum class FileWriteMode(val displayName: String) {
    APPEND("添加到文件"),
    PREPARE_COMMIT("准备提交"),
    OVERWRITE("覆盖文件")
}



/**
 * 文件操作类型枚举
 */
enum class FileOperationType(val displayName: String) {
    COPY("复制"),
    MOVE("移动"),
    DELETE("删除"),
    COMPRESS("压缩"),
    CREATE_FOLDER("新建文件夹"),
    RENAME("重命名")
}

/**
 * 文件选择模式枚举
 */
enum class FileSelectionMode(val displayName: String) {
    ALL_FILES("全部文件"),
    ALL_MEDIA("所有媒体文件"),
    IMAGES("图像"),
    VIDEOS("视频"),
    AUDIO("音频"),
    SPECIFIC_PATTERN("指定文件模式"),
    FOLDERS("文件夹")
}

/**
 * 压缩位置枚举
 */
enum class CompressionLocation(val displayName: String) {
    CUSTOM_PATH("指定压缩路径"),
    CURRENT_LOCATION("当前位置")
}

/**
 * 压缩格式枚举
 */
enum class CompressionFormat(val displayName: String, val extension: String) {
    ZIP("ZIP格式", "zip"),
    TAR("TAR格式", "tar"),
    TAR_GZ("TAR.GZ格式", "tar.gz"),
    SEVEN_ZIP("7Z格式", "7z")
}

/**
 * 压缩级别枚举
 */
enum class CompressionLevel(val displayName: String, val level: Int) {
    STORE("存储（无压缩）", 0),
    FASTEST("最快", 1),
    FAST("较快", 3),
    NORMAL("标准", 6),
    GOOD("较好", 7),
    BEST("最好", 9)
}

/**
 * 文件操作任务
 *
 * @property id 任务唯一标识
 * @property operation 文件操作类型
 * @property folderPath 文件夹路径（写入文件、打开文件时使用）
 * @property fileName 文件名（写入文件、打开文件时使用）
 * @property fileContent 文件内容（写入文件时使用）
 * @property writeMode 写入模式（写入文件时使用）

 * @property appName 应用名称（打开文件时使用）
 * @property fileOperationType 文件操作类型（文件操作时使用）
 * @property sourceSelectionMode 源文件选择模式（文件操作时使用）
 * @property targetSelectionMode 目标文件选择模式（文件操作时使用）
 * @property specificFilePattern 指定文件模式（文件操作时使用）
 * @property compressionLocation 压缩位置（压缩操作时使用）
 * @property customCompressionPath 自定义压缩路径（压缩操作时使用）
 * @property deleteOriginalAfterCompression 压缩后是否删除原文件（压缩操作时使用）
 * @property sourcePath 源路径（文件操作时使用，通过系统文件选择器选择）
 * @property targetPath 目标路径（文件操作时使用，通过系统文件选择器选择）
 * @property newFolderName 新建文件夹名称（新建文件夹操作时使用）
 * @property skipIfFolderExists 如果文件夹已存在是否跳过（新建文件夹操作时使用）
 * @property compressionFormat 压缩格式（压缩操作时使用）
 * @property compressionLevel 压缩级别（压缩操作时使用）
 */
data class FileOperationTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: FileOperation = FileOperation.WRITE_FILE,

    // 写入文件和打开文件共用参数
    val folderPath: String = "",
    val fileName: String = "",

    // 写入文件专用参数
    val fileContent: String = "",
    val writeMode: FileWriteMode = FileWriteMode.APPEND,

    // 打开文件专用参数
    val appName: String = "",

    // 文件操作专用参数
    val fileOperationType: FileOperationType = FileOperationType.COPY,
    val sourceSelectionMode: FileSelectionMode = FileSelectionMode.ALL_FILES,
    val targetSelectionMode: FileSelectionMode = FileSelectionMode.ALL_FILES,
    val specificFilePattern: String = "",

    // 文件操作路径参数（通过系统文件选择器选择）
    val sourcePath: String = "",
    val targetPath: String = "",

    // 压缩操作专用参数
    val compressionLocation: CompressionLocation = CompressionLocation.CURRENT_LOCATION,
    val customCompressionPath: String = "",
    val deleteOriginalAfterCompression: Boolean = false,

    // 重命名操作专用参数
    val newFileName: String = "",
    val newFileExtension: String = "",
    val allowOverwrite: Boolean = false,

    // 新建文件夹专用参数
    val newFolderName: String = "",
    val skipIfFolderExists: Boolean = false,

    // 压缩操作专用参数
    val compressionFormat: CompressionFormat = CompressionFormat.ZIP,
    val compressionLevel: CompressionLevel = CompressionLevel.NORMAL
) : SharedTask {
    override val type: String = "file_operation"
    override val displayName: String = "文件读写"

    override fun getDescription(): String {
        return when (operation) {
            FileOperation.WRITE_FILE -> {
                val modeText = when (writeMode) {
                    FileWriteMode.APPEND -> "添加"
                    FileWriteMode.PREPARE_COMMIT -> "准备提交"
                    FileWriteMode.OVERWRITE -> "覆盖"
                }
                val pathText = if (folderPath.isNotEmpty()) folderPath else "未指定路径"
                val nameText = if (fileName.isNotEmpty()) fileName else "未指定文件名"
                "写入文件: $pathText/$nameText ($modeText)"
            }
            FileOperation.OPEN_FILE -> {
                val pathText = if (folderPath.isNotEmpty()) folderPath else "未指定路径"
                val nameText = if (fileName.isNotEmpty()) fileName else "未指定文件名"
                val appText = if (appName.isNotEmpty()) " 使用$appName" else ""
                "打开文件: $pathText/$nameText$appText"
            }
            FileOperation.FILE_OPERATION -> {
                when (fileOperationType) {
                    FileOperationType.RENAME -> {
                        val pathText = if (folderPath.isNotEmpty()) folderPath else "未指定路径"
                        val oldNameText = if (fileName.isNotEmpty()) fileName else "未指定文件"
                        val newNameText = if (newFileName.isNotEmpty()) {
                            val extension = if (newFileExtension.isNotEmpty()) ".$newFileExtension" else ""
                            "$newFileName$extension"
                        } else "未指定新名称"
                        val overwriteText = if (allowOverwrite) " (允许覆盖)" else ""
                        "重命名: $pathText/$oldNameText → $newNameText$overwriteText"
                    }
                    FileOperationType.CREATE_FOLDER -> {
                        val pathText = if (targetPath.isNotEmpty()) targetPath else "未指定路径"
                        val folderNameText = if (newFolderName.isNotEmpty()) newFolderName else "未指定名称"
                        val skipText = if (skipIfFolderExists) " (跳过已存在)" else ""
                        "新建文件夹: $pathText/$folderNameText$skipText"
                    }
                    FileOperationType.COMPRESS -> {
                        val sourceText = sourceSelectionMode.displayName
                        val formatText = compressionFormat.displayName
                        val levelText = compressionLevel.displayName
                        val locationText = when (compressionLocation) {
                            CompressionLocation.CURRENT_LOCATION -> "当前位置"
                            CompressionLocation.CUSTOM_PATH -> if (customCompressionPath.isNotEmpty()) customCompressionPath else "指定路径"
                        }
                        val deleteText = if (deleteOriginalAfterCompression) " (删除原文件)" else ""
                        "压缩: $sourceText → $formatText ($levelText) → $locationText$deleteText"
                    }
                    else -> {
                        val opText = fileOperationType.displayName
                        val sourceText = sourceSelectionMode.displayName
                        val targetText = if (fileOperationType != FileOperationType.DELETE)
                            " → ${targetSelectionMode.displayName}" else ""
                        "$opText: $sourceText$targetText"
                    }
                }
            }
        }
    }
}

/**
 * 相机操作类型
 */
enum class CameraOperation(val displayName: String) {
    OPEN_LAST_PHOTO("打开最后一张照片"),
    TAKE_PHOTO("拍照"),
    RECORD_VIDEO("录像"),
    SCREENSHOT("截屏")
}

/**
 * 相机类型
 */
enum class CameraType(val displayName: String) {
    FRONT("前置摄像头"),
    BACK("后置摄像头")
}

/**
 * 录像操作类型
 */
enum class VideoRecordingOperation(val displayName: String) {
    START("开始录像"),
    STOP("停止录像"),
    START_WITH_DURATION("定时录像")
}

/**
 * 保存位置类型
 */
enum class SaveLocation(val displayName: String) {
    DCIM_CAMERA("相机文件夹"),
    PICTURES("图片文件夹"),
    MOVIES("视频文件夹"),
    CUSTOM("自定义路径")
}

/**
 * 相机/照片任务
 *
 * @property id 任务唯一标识
 * @property operation 相机操作类型
 * @property cameraType 摄像头类型（拍照和录像时使用）
 * @property videoOperation 录像操作类型（录像时使用）
 * @property recordingDurationMinutes 录像时长分钟数（定时录像时使用）
 * @property recordingDurationSeconds 录像时长秒数（定时录像时使用）
 * @property photoSaveLocation 照片保存位置（拍照时使用）
 * @property videoSaveLocation 视频保存位置（录像时使用）
 * @property customPhotoPath 自定义照片保存路径（拍照时使用）
 * @property customVideoPath 自定义视频保存路径（录像时使用）
 */
data class CameraTask(
    override val id: String = UUID.randomUUID().toString(),
    val operation: CameraOperation = CameraOperation.OPEN_LAST_PHOTO,
    val cameraType: CameraType = CameraType.BACK,
    val videoOperation: VideoRecordingOperation = VideoRecordingOperation.START,
    val recordingDurationMinutes: Int = 0,
    val recordingDurationSeconds: Int = 30,
    val photoSaveLocation: SaveLocation = SaveLocation.DCIM_CAMERA,
    val videoSaveLocation: SaveLocation = SaveLocation.MOVIES,
    val customPhotoPath: String = "",
    val customVideoPath: String = ""
) : SharedTask {
    override val type: String = "camera"
    override val displayName: String = "照片查看"

    override fun getDescription(): String {
        return when (operation) {
            CameraOperation.OPEN_LAST_PHOTO -> "打开最后一张照片"
            CameraOperation.TAKE_PHOTO -> {
                val cameraText = cameraType.displayName
                val locationText = when (photoSaveLocation) {
                    SaveLocation.CUSTOM -> if (customPhotoPath.isNotEmpty()) customPhotoPath else "自定义路径"
                    else -> photoSaveLocation.displayName
                }
                "拍照: $cameraText -> $locationText"
            }
            CameraOperation.SCREENSHOT -> "截屏"
            CameraOperation.RECORD_VIDEO -> {
                val cameraText = cameraType.displayName
                val operationText = when (videoOperation) {
                    VideoRecordingOperation.START_WITH_DURATION -> {
                        "${videoOperation.displayName}(${recordingDurationMinutes}分${recordingDurationSeconds}秒)"
                    }
                    else -> videoOperation.displayName
                }
                val locationText = when (videoSaveLocation) {
                    SaveLocation.CUSTOM -> if (customVideoPath.isNotEmpty()) customVideoPath else "自定义路径"
                    else -> videoSaveLocation.displayName
                }
                "录像: $cameraText, $operationText -> $locationText"
            }
        }
    }
}






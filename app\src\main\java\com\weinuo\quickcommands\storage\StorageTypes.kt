package com.weinuo.quickcommands.storage

/**
 * 存储域枚举
 *
 * 定义了四个独立的存储域，每个域对应一个SharedPreferences文件。
 * 这种分域设计有助于：
 * - 数据隔离：不同类型的数据分别存储，避免冲突
 * - 性能优化：减少单个文件的大小，提高读写效率
 * - 维护便利：便于调试和数据管理
 * - 扩展性：新增数据类型时可以独立添加新域
 *
 * @property prefsName SharedPreferences文件名
 */
enum class StorageDomain(val prefsName: String) {
    /**
     * 触发条件存储域
     * 存储所有类型的SharedTriggerCondition数据
     */
    CONDITIONS("background_manager_conditions"),

    /**
     * 任务存储域
     * 存储所有类型的SharedTask数据
     */
    TASKS("background_manager_tasks"),

    /**
     * 快捷指令存储域
     * 存储QuickCommand的基本信息和关联关系
     */
    QUICK_COMMANDS("background_manager_quick_commands"),

    /**
     * 应用列表存储域
     * 存储List<SimpleAppInfo>的拆分数据
     */
    APP_LISTS("background_manager_app_lists"),

    /**
     * UI状态存储域
     * 存储UI组件的状态数据，替代rememberSaveable中的JSON序列化
     */
    UI_STATE("background_manager_ui_state"),

    /**
     * 导航数据存储域
     * 存储导航过程中传递的数据，替代savedStateHandle中的JSON序列化
     */
    NAVIGATION_DATA("background_manager_navigation_data"),

    /**
     * 手势录制存储域
     * 存储手势录制数据，包括TouchEvent、TouchPoint、GestureRecording等
     */
    GESTURE_RECORDINGS("background_manager_gesture_recordings")
}

/**
 * 存储操作类型枚举
 *
 * 定义了批量操作中支持的所有操作类型。
 * 支持五种原生数据类型的存储和删除操作。
 */
enum class StorageType {
    /**
     * 字符串类型存储
     */
    STRING,

    /**
     * 整数类型存储
     */
    INT,

    /**
     * 布尔类型存储
     */
    BOOLEAN,

    /**
     * 浮点数类型存储
     */
    FLOAT,

    /**
     * 长整数类型存储
     */
    LONG,

    /**
     * 删除操作
     */
    DELETE
}

/**
 * 存储操作数据类
 *
 * 表示一个原子性的存储操作，用于批量操作中。
 * 每个操作包含目标域、键名、值和操作类型。
 *
 * @property domain 目标存储域
 * @property key 键名
 * @property value 要存储的值（DELETE操作时可以为任意值）
 * @property type 操作类型
 */
data class StorageOperation(
    val domain: StorageDomain,
    val key: String,
    val value: Any,
    val type: StorageType
) {
    companion object {
        /**
         * 创建字符串存储操作
         *
         * @param domain 存储域
         * @param key 键名
         * @param value 字符串值
         * @return 存储操作实例
         */
        fun createStringOperation(domain: StorageDomain, key: String, value: String): StorageOperation {
            return StorageOperation(domain, key, value, StorageType.STRING)
        }

        /**
         * 创建整数存储操作
         *
         * @param domain 存储域
         * @param key 键名
         * @param value 整数值
         * @return 存储操作实例
         */
        fun createIntOperation(domain: StorageDomain, key: String, value: Int): StorageOperation {
            return StorageOperation(domain, key, value, StorageType.INT)
        }

        /**
         * 创建布尔存储操作
         *
         * @param domain 存储域
         * @param key 键名
         * @param value 布尔值
         * @return 存储操作实例
         */
        fun createBooleanOperation(domain: StorageDomain, key: String, value: Boolean): StorageOperation {
            return StorageOperation(domain, key, value, StorageType.BOOLEAN)
        }

        /**
         * 创建浮点数存储操作
         *
         * @param domain 存储域
         * @param key 键名
         * @param value 浮点数值
         * @return 存储操作实例
         */
        fun createFloatOperation(domain: StorageDomain, key: String, value: Float): StorageOperation {
            return StorageOperation(domain, key, value, StorageType.FLOAT)
        }

        /**
         * 创建长整数存储操作
         *
         * @param domain 存储域
         * @param key 键名
         * @param value 长整数值
         * @return 存储操作实例
         */
        fun createLongOperation(domain: StorageDomain, key: String, value: Long): StorageOperation {
            return StorageOperation(domain, key, value, StorageType.LONG)
        }

        /**
         * 创建删除操作
         *
         * @param domain 存储域
         * @param key 要删除的键名
         * @return 删除操作实例
         */
        fun createDeleteOperation(domain: StorageDomain, key: String): StorageOperation {
            return StorageOperation(domain, key, Unit, StorageType.DELETE)
        }
    }
}

/**
 * 存储键名生成器
 *
 * 提供统一的键名生成规则，确保键名的一致性和可预测性。
 * 所有键名都遵循特定的命名规范，便于管理和调试。
 */
object StorageKeyGenerator {

    /**
     * 生成条件存储键名
     * 格式：condition_{id}_{field_name}
     *
     * @param conditionId 条件ID
     * @param fieldName 字段名
     * @return 完整的键名
     */
    fun generateConditionKey(conditionId: String, fieldName: String): String {
        return "condition_${conditionId}_${fieldName}"
    }

    /**
     * 生成任务存储键名
     * 格式：task_{id}_{field_name}
     *
     * @param taskId 任务ID
     * @param fieldName 字段名
     * @return 完整的键名
     */
    fun generateTaskKey(taskId: String, fieldName: String): String {
        return "task_${taskId}_${fieldName}"
    }

    /**
     * 生成快捷指令存储键名
     * 格式：quick_command_{id}_{field_name}
     *
     * @param commandId 快捷指令ID
     * @param fieldName 字段名
     * @return 完整的键名
     */
    fun generateQuickCommandKey(commandId: String, fieldName: String): String {
        return "quick_command_${commandId}_${fieldName}"
    }

    /**
     * 生成应用列表存储键名
     * 格式：app_list_{key}_{suffix}
     *
     * @param listKey 列表键名
     * @param suffix 后缀（如count、index_0_package_name等）
     * @return 完整的键名
     */
    fun generateAppListKey(listKey: String, suffix: String): String {
        return "app_list_${listKey}_${suffix}"
    }

    /**
     * 生成集合存储键名
     * 格式：{base_key}_{suffix}
     *
     * @param baseKey 基础键名
     * @param suffix 后缀（如count、0、1等）
     * @return 完整的键名
     */
    fun generateCollectionKey(baseKey: String, suffix: String): String {
        return "${baseKey}_${suffix}"
    }

    /**
     * 获取条件存储键前缀
     * 格式：condition_{id}_
     *
     * @param conditionId 条件ID
     * @return 键前缀
     */
    fun getConditionPrefix(conditionId: String): String {
        return "condition_${conditionId}_"
    }

    /**
     * 获取任务存储键前缀
     * 格式：task_{id}_
     *
     * @param taskId 任务ID
     * @return 键前缀
     */
    fun getTaskPrefix(taskId: String): String {
        return "task_${taskId}_"
    }

    /**
     * 获取快捷指令存储键前缀
     * 格式：quick_command_{id}_
     *
     * @param commandId 快捷指令ID
     * @return 键前缀
     */
    fun getQuickCommandPrefix(commandId: String): String {
        return "quick_command_${commandId}_"
    }

    /**
     * 获取应用列表存储键前缀
     * 格式：app_list_{key}_
     *
     * @param listKey 列表键名
     * @return 键前缀
     */
    fun getAppListPrefix(listKey: String): String {
        return "app_list_${listKey}_"
    }


}

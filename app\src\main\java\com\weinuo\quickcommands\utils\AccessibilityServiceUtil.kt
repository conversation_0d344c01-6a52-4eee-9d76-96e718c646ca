package com.weinuo.quickcommands.utils

import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import com.weinuo.quickcommands.service.SystemPriorityEnhancementService

/**
 * 无障碍服务工具类
 * 用于处理系统优先级提升无障碍服务相关操作
 */
object AccessibilityServiceUtil {
    private const val TAG = "AccessibilityServiceUtil"

    /**
     * 打开无障碍服务设置页面
     */
    fun openAccessibilitySettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening accessibility settings", e)
        }
    }

    /**
     * 检查系统优先级提升无障碍服务是否已启用
     * 注意：此方法不会主动调用，仅供需要时使用，以节省电量
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        val expectedComponentName = SystemPriorityEnhancementService::class.java.name

        val enabledServicesSetting = Settings.Secure.getString(
            context.contentResolver,
            Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
        ) ?: return false

        val colonSplitter = TextUtils.SimpleStringSplitter(':')
        colonSplitter.setString(enabledServicesSetting)

        while (colonSplitter.hasNext()) {
            val componentNameString = colonSplitter.next()
            val enabledService = ComponentNameString(componentNameString)

            if (enabledService.packageName == context.packageName &&
                enabledService.className == expectedComponentName) {
                return true
            }
        }

        return false
    }

    /**
     * 解析组件名称字符串
     */
    private data class ComponentNameString(val packageName: String, val className: String) {
        constructor(flattenString: String) : this(
            flattenString.substring(0, flattenString.indexOf('/')),
            flattenString.substring(flattenString.indexOf('/') + 1)
        )
    }
}

package com.weinuo.quickcommands.storage

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * 原生数据类型存储管理器
 *
 * 负责统一管理所有原生数据类型的存储操作，提供类型安全的存储接口。
 * 支持4个分域的SharedPreferences管理，批量操作和完整的错误处理。
 *
 * 核心特性：
 * - 分域存储管理：条件、任务、快捷指令、应用列表分别存储
 * - 类型安全：提供String、Int、Boolean、Float、Long的类型安全存储方法
 * - 批量操作：支持原子性的批量存储操作
 * - 错误处理：完整的异常处理和日志记录
 * - 性能优化：SharedPreferences实例缓存，减少重复创建开销
 *
 * @param context Android上下文，用于获取SharedPreferences
 */
class NativeTypeStorageManager(private val context: Context) {

    companion object {
        private const val TAG = "NativeTypeStorageManager"

        // 存储域配置映射
        private val DOMAIN_CONFIGS = mapOf(
            StorageDomain.CONDITIONS to "background_manager_conditions",
            StorageDomain.TASKS to "background_manager_tasks",
            StorageDomain.QUICK_COMMANDS to "background_manager_quick_commands",
            StorageDomain.APP_LISTS to "background_manager_app_lists",
            StorageDomain.UI_STATE to "background_manager_ui_state",
            StorageDomain.NAVIGATION_DATA to "background_manager_navigation_data",
            StorageDomain.GESTURE_RECORDINGS to "background_manager_gesture_recordings"
        )
    }

    // SharedPreferences实例缓存，避免重复创建
    private val prefsCache = mutableMapOf<StorageDomain, SharedPreferences>()

    /**
     * 获取Context实例
     * 供适配器使用，用于创建其他管理器实例
     */
    fun getContext(): Context = context

    /**
     * 获取指定存储域的SharedPreferences实例
     * 使用缓存机制提高性能
     *
     * @param domain 存储域
     * @return SharedPreferences实例
     */
    private fun getPreferencesInternal(domain: StorageDomain): SharedPreferences {
        return prefsCache.getOrPut(domain) {
            val prefsName = DOMAIN_CONFIGS[domain]
                ?: throw IllegalArgumentException("Unknown storage domain: $domain")
            context.getSharedPreferences(prefsName, Context.MODE_PRIVATE)
        }
    }

    /**
     * 获取指定存储域的SharedPreferences实例（公开方法）
     * 供其他组件使用，如QuickCommandStorageCoordinator
     *
     * @param domain 存储域
     * @return SharedPreferences实例
     */
    fun getPreferences(domain: StorageDomain): SharedPreferences {
        return getPreferencesInternal(domain)
    }

    /**
     * 保存字符串值
     *
     * @param domain 存储域
     * @param key 键名
     * @param value 字符串值
     * @return 操作是否成功
     */
    fun saveString(domain: StorageDomain, key: String, value: String): Boolean {
        return try {
            getPreferencesInternal(domain).edit().putString(key, value).apply()
            Log.d(TAG, "Saved string: domain=$domain, key=$key, length=${value.length}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save string: domain=$domain, key=$key", e)
            false
        }
    }

    /**
     * 保存整数值
     *
     * @param domain 存储域
     * @param key 键名
     * @param value 整数值
     * @return 操作是否成功
     */
    fun saveInt(domain: StorageDomain, key: String, value: Int): Boolean {
        return try {
            getPreferencesInternal(domain).edit().putInt(key, value).apply()
            Log.d(TAG, "Saved int: domain=$domain, key=$key, value=$value")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save int: domain=$domain, key=$key", e)
            false
        }
    }

    /**
     * 保存布尔值
     *
     * @param domain 存储域
     * @param key 键名
     * @param value 布尔值
     * @return 操作是否成功
     */
    fun saveBoolean(domain: StorageDomain, key: String, value: Boolean): Boolean {
        return try {
            getPreferencesInternal(domain).edit().putBoolean(key, value).apply()
            Log.d(TAG, "Saved boolean: domain=$domain, key=$key, value=$value")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save boolean: domain=$domain, key=$key", e)
            false
        }
    }

    /**
     * 保存浮点数值
     *
     * @param domain 存储域
     * @param key 键名
     * @param value 浮点数值
     * @return 操作是否成功
     */
    fun saveFloat(domain: StorageDomain, key: String, value: Float): Boolean {
        return try {
            getPreferencesInternal(domain).edit().putFloat(key, value).apply()
            Log.d(TAG, "Saved float: domain=$domain, key=$key, value=$value")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save float: domain=$domain, key=$key", e)
            false
        }
    }

    /**
     * 保存长整数值
     *
     * @param domain 存储域
     * @param key 键名
     * @param value 长整数值
     * @return 操作是否成功
     */
    fun saveLong(domain: StorageDomain, key: String, value: Long): Boolean {
        return try {
            getPreferencesInternal(domain).edit().putLong(key, value).apply()
            Log.d(TAG, "Saved long: domain=$domain, key=$key, value=$value")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save long: domain=$domain, key=$key", e)
            false
        }
    }

    /**
     * 加载字符串值
     *
     * @param domain 存储域
     * @param key 键名
     * @param defaultValue 默认值
     * @return 字符串值
     */
    fun loadString(domain: StorageDomain, key: String, defaultValue: String = ""): String {
        return try {
            val value = getPreferencesInternal(domain).getString(key, defaultValue) ?: defaultValue
            Log.d(TAG, "Loaded string: domain=$domain, key=$key, length=${value.length}")
            value
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load string: domain=$domain, key=$key", e)
            defaultValue
        }
    }

    /**
     * 加载整数值
     *
     * @param domain 存储域
     * @param key 键名
     * @param defaultValue 默认值
     * @return 整数值
     */
    fun loadInt(domain: StorageDomain, key: String, defaultValue: Int = 0): Int {
        return try {
            val value = getPreferencesInternal(domain).getInt(key, defaultValue)
            Log.d(TAG, "Loaded int: domain=$domain, key=$key, value=$value")
            value
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load int: domain=$domain, key=$key", e)
            defaultValue
        }
    }

    /**
     * 加载布尔值
     *
     * @param domain 存储域
     * @param key 键名
     * @param defaultValue 默认值
     * @return 布尔值
     */
    fun loadBoolean(domain: StorageDomain, key: String, defaultValue: Boolean = false): Boolean {
        return try {
            val value = getPreferencesInternal(domain).getBoolean(key, defaultValue)
            Log.d(TAG, "Loaded boolean: domain=$domain, key=$key, value=$value")
            value
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load boolean: domain=$domain, key=$key", e)
            defaultValue
        }
    }

    /**
     * 加载浮点数值
     *
     * @param domain 存储域
     * @param key 键名
     * @param defaultValue 默认值
     * @return 浮点数值
     */
    fun loadFloat(domain: StorageDomain, key: String, defaultValue: Float = 0f): Float {
        return try {
            val value = getPreferencesInternal(domain).getFloat(key, defaultValue)
            Log.d(TAG, "Loaded float: domain=$domain, key=$key, value=$value")
            value
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load float: domain=$domain, key=$key", e)
            defaultValue
        }
    }

    /**
     * 加载长整数值
     *
     * @param domain 存储域
     * @param key 键名
     * @param defaultValue 默认值
     * @return 长整数值
     */
    fun loadLong(domain: StorageDomain, key: String, defaultValue: Long = 0L): Long {
        return try {
            val value = getPreferencesInternal(domain).getLong(key, defaultValue)
            Log.d(TAG, "Loaded long: domain=$domain, key=$key, value=$value")
            value
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load long: domain=$domain, key=$key", e)
            defaultValue
        }
    }

    /**
     * 删除指定前缀的所有键值对
     * 用于删除某个对象的所有相关数据
     *
     * @param domain 存储域
     * @param prefix 键名前缀
     * @return 操作是否成功
     */
    fun deleteByPrefix(domain: StorageDomain, prefix: String): Boolean {
        return try {
            val prefs = getPreferencesInternal(domain)
            val editor = prefs.edit()
            var deletedCount = 0

            // 查找所有匹配前缀的键
            prefs.all.keys.filter { it.startsWith(prefix) }.forEach { key ->
                editor.remove(key)
                deletedCount++
            }

            editor.apply()
            Log.d(TAG, "Deleted $deletedCount keys with prefix: domain=$domain, prefix=$prefix")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete by prefix: domain=$domain, prefix=$prefix", e)
            false
        }
    }

    /**
     * 删除指定键
     *
     * @param domain 存储域
     * @param key 键名
     * @return 操作是否成功
     */
    fun deleteKey(domain: StorageDomain, key: String): Boolean {
        return try {
            getPreferencesInternal(domain).edit().remove(key).apply()
            Log.d(TAG, "Deleted key: domain=$domain, key=$key")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete key: domain=$domain, key=$key", e)
            false
        }
    }

    /**
     * 检查键是否存在
     *
     * @param domain 存储域
     * @param key 键名
     * @return 键是否存在
     */
    fun containsKey(domain: StorageDomain, key: String): Boolean {
        return try {
            val exists = getPreferencesInternal(domain).contains(key)
            Log.d(TAG, "Key exists check: domain=$domain, key=$key, exists=$exists")
            exists
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check key existence: domain=$domain, key=$key", e)
            false
        }
    }

    /**
     * 获取指定域的所有键
     *
     * @param domain 存储域
     * @return 所有键的集合
     */
    fun getAllKeys(domain: StorageDomain): Set<String> {
        return try {
            val keys = getPreferencesInternal(domain).all.keys
            Log.d(TAG, "Retrieved ${keys.size} keys from domain: $domain")
            keys
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get all keys: domain=$domain", e)
            emptySet()
        }
    }

    /**
     * 执行批量存储操作
     * 所有操作在同一个事务中执行，保证原子性
     *
     * @param operations 存储操作列表
     * @return 操作是否成功
     */
    fun executeBatch(operations: List<StorageOperation>): Boolean {
        if (operations.isEmpty()) {
            Log.d(TAG, "Empty batch operation, skipping")
            return true
        }

        val editorsByDomain = mutableMapOf<StorageDomain, SharedPreferences.Editor>()

        return try {
            // 按域分组操作，每个域使用一个Editor
            operations.groupBy { it.domain }.forEach { (domain, ops) ->
                val editor = getPreferencesInternal(domain).edit()
                editorsByDomain[domain] = editor

                ops.forEach { op ->
                    when (op.type) {
                        StorageType.STRING -> editor.putString(op.key, op.value as String)
                        StorageType.INT -> editor.putInt(op.key, op.value as Int)
                        StorageType.BOOLEAN -> editor.putBoolean(op.key, op.value as Boolean)
                        StorageType.FLOAT -> editor.putFloat(op.key, op.value as Float)
                        StorageType.LONG -> editor.putLong(op.key, op.value as Long)
                        StorageType.DELETE -> editor.remove(op.key)
                    }
                }
            }

            // 原子性提交所有更改
            editorsByDomain.values.forEach { it.apply() }
            Log.d(TAG, "Batch operation completed successfully: ${operations.size} operations across ${editorsByDomain.size} domains")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Batch operation failed", e)
            false
        }
    }

    /**
     * 清空指定域的所有数据
     *
     * @param domain 存储域
     * @return 操作是否成功
     */
    fun clearDomain(domain: StorageDomain): Boolean {
        return try {
            getPreferencesInternal(domain).edit().clear().apply()
            Log.d(TAG, "Cleared domain: $domain")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear domain: $domain", e)
            false
        }
    }
}

package com.weinuo.quickcommands.model

import android.graphics.drawable.Drawable

/**
 * 应用信息数据模型
 *
 * @property packageName 应用包名
 * @property appName 应用名称
 * @property icon 应用图标
 * @property isSystemApp 是否为系统应用
 * @property isRunning 应用是否正在运行
 */
data class AppInfo(
    val packageName: String,
    val appName: String,
    val icon: Drawable,
    val isSystemApp: Boolean,
    var isRunning: Boolean = false
)



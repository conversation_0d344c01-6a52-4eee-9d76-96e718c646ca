package com.weinuo.quickcommands.ui.components.layered

import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.statusBars
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle

/**
 * 分层设计风格的顶部应用栏
 *
 * 特点：
 * - 使用标准Material 3 TopAppBar设计
 * - 简洁清晰的视觉分离
 * - 遵循Material Design 3规范
 * - 保持与old项目一致的简洁风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LayeredTopAppBar(
    config: TopAppBarConfig,
    modifier: Modifier = Modifier
) {
    // 如果启用了可折叠功能，使用可折叠版本
    if (config.collapsible && config.scrollBehavior != null) {
        LayeredCollapsibleTopAppBar(
            config = config,
            modifier = modifier
        )
    } else {
        // 只使用标准版本，移除Medium和Large变体
        TopAppBar(
            title = { Text(config.title) },
            modifier = modifier,
            navigationIcon = config.navigationIcon?.let { icon ->
                {
                    IconButton(onClick = config.onNavigationClick ?: {}) {
                        Icon(
                            imageVector = icon,
                            contentDescription = config.navigationContentDescription ?: "导航"
                        )
                    }
                }
            } ?: {},
            actions = {
                config.actions.forEach { action ->
                    IconButton(onClick = action.onClick) {
                        Icon(
                            imageVector = action.icon,
                            contentDescription = action.contentDescription
                        )
                    }
                }
            }
        )
    }
}

/**
 * 分层设计的可折叠顶部应用栏
 *
 * 特点：
 * - 使用标准Material 3 LargeTopAppBar设计
 * - 可折叠功能，从大型应用栏折叠为标准应用栏
 * - 简洁清晰的视觉分离
 * - 遵循Material Design 3规范
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LayeredCollapsibleTopAppBar(
    config: TopAppBarConfig,
    modifier: Modifier = Modifier
) {
    // 处理WindowInsets
    val windowInsets = config.windowInsets as? WindowInsets ?: WindowInsets.statusBars

    // 处理ScrollBehavior
    val scrollBehavior = config.scrollBehavior as? TopAppBarScrollBehavior

    // 使用标准的Material 3颜色
    val colors = TopAppBarDefaults.largeTopAppBarColors(
        containerColor = MaterialTheme.colorScheme.surface,
        titleContentColor = MaterialTheme.colorScheme.onSurface
    )

    LargeTopAppBar(
        title = {
            Text(
                text = config.title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        },
        navigationIcon = config.navigationIcon?.let { icon ->
            {
                IconButton(onClick = config.onNavigationClick ?: {}) {
                    Icon(
                        imageVector = icon,
                        contentDescription = config.navigationContentDescription ?: "导航"
                    )
                }
            }
        } ?: {},
        actions = {
            config.actions.forEach { action ->
                IconButton(onClick = action.onClick) {
                    Icon(
                        imageVector = action.icon,
                        contentDescription = action.contentDescription
                    )
                }
            }
        },
        scrollBehavior = scrollBehavior,
        windowInsets = windowInsets,
        colors = colors,
        modifier = modifier.fillMaxWidth()
    )
}



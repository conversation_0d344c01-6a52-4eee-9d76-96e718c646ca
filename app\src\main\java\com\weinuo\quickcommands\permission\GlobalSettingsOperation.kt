package com.weinuo.quickcommands.permission

/**
 * 全局设置操作类型枚举
 *
 * 定义全局设置界面中的各种操作类型，用于权限检查和操作控制。
 * 这个枚举与统一权限检查策略配合使用，区分需要和不需要Shizuku权限的设置项。
 *
 * 设计原则：
 * - WIDGET_UPDATE、DEBUG_MODE：独立于Shizuku权限的功能
 * - 其他操作：需要Shizuku权限才能执行
 *
 * 使用方法：
 * ```kotlin
 * var selectedOperation by remember { mutableStateOf<GlobalSettingsOperation?>(null) }
 *
 * // 统一权限检查
 * selectedOperation?.let { operation ->
 *     PermissionAwareOperationSelector(
 *         selectedOperation = operation,
 *         context = LocalContext.current
 *     )
 * }
 *
 * // 在用户操作时设置对应的操作类型
 * onClick = {
 *     selectedOperation = GlobalSettingsOperation.BACKGROUND_MANAGEMENT
 * }
 * ```
 *
 * <AUTHOR>
 * @since 1.0.0
 */
enum class GlobalSettingsOperation {
    // ========== 无需Shizuku权限的操作 ==========

    /**
     * 小组件更新
     * 无需权限 - 控制应用小组件的更新功能，独立于Shizuku
     */
    WIDGET_UPDATE,

    /**
     * 调试模式
     * 无需权限 - 控制应用的调试模式功能，独立于Shizuku
     */
    DEBUG_MODE,

    /**
     * 实验性功能
     * 无需权限 - 控制实验性功能的启用/禁用
     */
    EXPERIMENTAL_FEATURES
}

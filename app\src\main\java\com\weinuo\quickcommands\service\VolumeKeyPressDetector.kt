package com.weinuo.quickcommands.service

import android.content.Context
import android.media.AudioManager
import android.util.Log
import android.view.KeyEvent
import com.weinuo.quickcommands.model.ManualTriggerCondition
import com.weinuo.quickcommands.model.ManualTriggerType
import com.weinuo.quickcommands.model.VolumeButtonType
import com.weinuo.quickcommands.repository.ConditionRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 音量键按下检测器
 *
 * 负责检测音量键按下事件，并根据配置的条件触发相应的快捷指令。
 * 支持音量加键、音量减键以及两者的组合检测。
 * 可配置是否保留原音量（即是否实际改变音量）。
 *
 * 主要功能：
 * - 检测音量键按下事件
 * - 根据条件配置决定是否拦截音量变化
 * - 触发匹配的手动触发条件
 * - 支持动态加载和更新条件
 *
 * @param context Android上下文
 * @param conditionRepository 条件仓库，用于加载音量键按下条件
 */
class VolumeKeyPressDetector(
    private val context: Context,
    private val conditionRepository: ConditionRepository
) {

    companion object {
        private const val TAG = "VolumeKeyPressDetector"
    }

    /**
     * 音量键按下回调接口
     */
    interface VolumeKeyPressCallback {
        /**
         * 当音量键按下条件满足时调用
         *
         * @param condition 满足的音量键按下条件
         */
        fun onVolumeKeyPress(condition: ManualTriggerCondition)
    }

    // 注册的条件列表
    private val registeredConditions = mutableListOf<ManualTriggerCondition>()

    // 回调列表
    private val callbacks = mutableSetOf<VolumeKeyPressCallback>()

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    // 音频管理器
    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    init {
        Log.d(TAG, "音量键按下检测器已初始化")
        loadVolumeKeyPressConditions()
    }

    /**
     * 注册音量键按下回调
     */
    fun registerCallback(callback: VolumeKeyPressCallback) {
        callbacks.add(callback)
        Log.d(TAG, "注册音量键按下回调，当前回调数量: ${callbacks.size}")
    }

    /**
     * 取消注册音量键按下回调
     */
    fun unregisterCallback(callback: VolumeKeyPressCallback) {
        callbacks.remove(callback)
        Log.d(TAG, "取消注册音量键按下回调，当前回调数量: ${callbacks.size}")
    }

    /**
     * 处理音量键事件
     *
     * @param keyCode 按键代码
     * @param event 按键事件
     * @return 是否处理了该事件（如果需要拦截音量变化则返回true）
     */
    fun onVolumeKeyEvent(keyCode: Int, event: KeyEvent): Boolean {
        // 只处理按键释放事件，避免重复触发
        if (event.action != KeyEvent.ACTION_UP) {
            return false
        }

        val volumeKeyType = getVolumeKeyType(keyCode) ?: return false

        Log.d(TAG, "检测到音量键按下: ${volumeKeyType.displayName}")

        // 加载相关条件
        loadVolumeKeyPressConditions()

        // 处理匹配的条件
        val matchingConditions = registeredConditions.filter { 
            it.volumeKeyType == volumeKeyType || it.volumeKeyType == VolumeButtonType.BOTH 
        }

        var shouldInterceptVolumeChange = false

        matchingConditions.forEach { condition ->
            // 触发条件
            triggerVolumeKeyCondition(condition)
            
            // 如果条件配置为保留原音量，则需要拦截音量变化
            if (condition.volumeKeyPreserveVolume) {
                shouldInterceptVolumeChange = true
            }
        }

        return shouldInterceptVolumeChange
    }

    /**
     * 根据按键代码获取音量键类型
     */
    private fun getVolumeKeyType(keyCode: Int): VolumeButtonType? {
        return when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP -> VolumeButtonType.VOLUME_UP
            KeyEvent.KEYCODE_VOLUME_DOWN -> VolumeButtonType.VOLUME_DOWN
            else -> null
        }
    }

    /**
     * 加载音量键按下条件
     */
    private fun loadVolumeKeyPressConditions() {
        coroutineScope.launch {
            try {
                val allConditions = conditionRepository.loadAllConditions()
                val volumeKeyConditions = allConditions.filterIsInstance<ManualTriggerCondition>()
                    .filter { it.triggerType == ManualTriggerType.VOLUME_KEY_PRESS }

                registeredConditions.clear()
                registeredConditions.addAll(volumeKeyConditions)

                Log.d(TAG, "加载了 ${registeredConditions.size} 个音量键按下条件")
            } catch (e: Exception) {
                Log.e(TAG, "加载音量键按下条件失败", e)
            }
        }
    }

    /**
     * 触发音量键条件
     */
    private fun triggerVolumeKeyCondition(condition: ManualTriggerCondition) {
        Log.d(TAG, "触发音量键按下条件: ${condition.id} (${condition.volumeKeyType.displayName}, 保留音量: ${condition.volumeKeyPreserveVolume})")

        // 通知所有注册的回调
        callbacks.forEach { callback ->
            try {
                callback.onVolumeKeyPress(condition)
            } catch (e: Exception) {
                Log.e(TAG, "音量键按下回调执行失败", e)
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 清理状态
        registeredConditions.clear()
        callbacks.clear()

        Log.d(TAG, "音量键按下检测器已清理")
    }
}

package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import java.util.*

/**
 * 时间条件配置数据提供器
 *
 * 提供统一时间条件的配置项列表，支持6种时间条件类型：
 * 秒表、日出日落、日程时间、周期时间、延迟触发、周期触发
 *
 * 使用通用组件复用架构，每个时间条件类型对应一个配置卡片，
 * 支持权限检查、配置内容组件和完成回调。
 */
object TimeConditionConfigProvider {

    /**
     * 获取时间条件配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 时间条件配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<TimeConditionType>> {
        return listOf(
            // 秒表配置项
            ConfigurationCardItem(
                id = "stopwatch",
                title = context.getString(R.string.time_condition_stopwatch),
                description = context.getString(R.string.time_condition_stopwatch_description),
                operationType = TimeConditionType.STOPWATCH,
                permissionRequired = false,
                content = { type, onComplete ->
                    StopwatchConfigContent(type, onComplete)
                }
            ),

            // 日出日落配置项
            ConfigurationCardItem(
                id = "sun_event",
                title = context.getString(R.string.time_condition_sun_event),
                description = context.getString(R.string.time_condition_sun_event_description),
                operationType = TimeConditionType.SUN_EVENT,
                permissionRequired = true, // 需要位置权限
                content = { type, onComplete ->
                    SunEventConfigContent(type, onComplete)
                }
            ),

            // 日程时间配置项
            ConfigurationCardItem(
                id = "scheduled_time",
                title = context.getString(R.string.time_condition_scheduled_time),
                description = context.getString(R.string.time_condition_scheduled_time_description),
                operationType = TimeConditionType.SCHEDULED_TIME,
                permissionRequired = false,
                content = { type, onComplete ->
                    ScheduledTimeConfigContent(type, onComplete)
                }
            ),

            // 周期时间配置项
            ConfigurationCardItem(
                id = "periodic_time",
                title = context.getString(R.string.time_condition_periodic_time),
                description = context.getString(R.string.time_condition_periodic_time_description),
                operationType = TimeConditionType.PERIODIC_TIME,
                permissionRequired = false,
                content = { type, onComplete ->
                    PeriodicTimeConfigContent(type, onComplete)
                }
            ),

            // 延迟触发配置项
            ConfigurationCardItem(
                id = "delayed_trigger",
                title = context.getString(R.string.time_condition_delayed_trigger),
                description = context.getString(R.string.time_condition_delayed_trigger_description),
                operationType = TimeConditionType.DELAYED_TRIGGER,
                permissionRequired = false,
                content = { type, onComplete ->
                    DelayedTriggerConfigContent(type, onComplete)
                }
            ),

            // 周期触发配置项
            ConfigurationCardItem(
                id = "periodic_trigger",
                title = "周期触发",
                description = "按指定间隔重复触发",
                operationType = TimeConditionType.PERIODIC_TRIGGER,
                permissionRequired = false,
                content = { type, onComplete ->
                    PeriodicTriggerConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * 秒表配置内容组件
 *
 * 提供秒表倒计时的时间设置界面，支持小时、分钟、秒的输入
 */
@Composable
private fun StopwatchConfigContent(type: TimeConditionType, onComplete: (Any) -> Unit) {
    var hours by rememberSaveable { mutableStateOf("0") }
    var minutes by rememberSaveable { mutableStateOf("0") }
    var seconds by rememberSaveable { mutableStateOf("30") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(max = 350.dp), // 移除verticalScroll，使用heightIn约束高度
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "设置倒计时时间",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 时间输入区域
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 小时输入
            OutlinedTextField(
                value = hours,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) hours = it },
                label = { Text("小时") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )

            // 分钟输入
            OutlinedTextField(
                value = minutes,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) minutes = it },
                label = { Text("分钟") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )

            // 秒输入
            OutlinedTextField(
                value = seconds,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) seconds = it },
                label = { Text("秒") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }

        // 确认按钮
        Button(
            onClick = {
                android.util.Log.d("StopwatchConfigContent", "确认按钮被点击")
                val condition = TimeBasedCondition(
                    timeConditionType = type,
                    stopwatchHours = hours.toIntOrNull() ?: 0,
                    stopwatchMinutes = minutes.toIntOrNull() ?: 0,
                    stopwatchSeconds = seconds.toIntOrNull() ?: 30,
                    startTime = System.currentTimeMillis()
                )
                android.util.Log.d("StopwatchConfigContent", "创建条件对象: $condition")
                android.util.Log.d("StopwatchConfigContent", "调用onComplete回调")
                onComplete(condition)
                android.util.Log.d("StopwatchConfigContent", "onComplete回调已调用")
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 日出日落配置内容组件
 *
 * 提供日出日落事件类型选择和地理位置设置界面
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SunEventConfigContent(type: TimeConditionType, onComplete: (Any) -> Unit) {
    var sunEventType by rememberSaveable { mutableStateOf(SunEventType.SUNRISE) }
    var useSystemLocation by rememberSaveable { mutableStateOf(true) }
    var latitude by rememberSaveable { mutableStateOf("") }
    var longitude by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置日出日落事件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 事件类型选择
        Text(
            text = "事件类型",
            style = MaterialTheme.typography.bodyMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { sunEventType = SunEventType.SUNRISE },
                label = { Text("日出") },
                selected = sunEventType == SunEventType.SUNRISE,
                modifier = Modifier.weight(1f)
            )
            FilterChip(
                onClick = { sunEventType = SunEventType.SUNSET },
                label = { Text("日落") },
                selected = sunEventType == SunEventType.SUNSET,
                modifier = Modifier.weight(1f)
            )
        }

        // 位置设置
        Row(
            verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useSystemLocation,
                onCheckedChange = { useSystemLocation = it }
            )
            Text(
                text = "使用系统位置",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        if (!useSystemLocation) {
            OutlinedTextField(
                value = latitude,
                onValueChange = { latitude = it },
                label = { Text("纬度") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            OutlinedTextField(
                value = longitude,
                onValueChange = { longitude = it },
                label = { Text("经度") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = TimeBasedCondition(
                    timeConditionType = type,
                    sunEventType = sunEventType,
                    latitude = if (useSystemLocation) null else latitude.toDoubleOrNull(),
                    longitude = if (useSystemLocation) null else longitude.toDoubleOrNull(),
                    startTime = System.currentTimeMillis()
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 日程时间配置内容组件
 *
 * 提供日程时间的日期、时间和重复模式设置界面
 */
@Composable
private fun ScheduledTimeConfigContent(type: TimeConditionType, onComplete: (Any) -> Unit) {
    val currentCalendar = Calendar.getInstance()
    var year by rememberSaveable { mutableStateOf(currentCalendar.get(Calendar.YEAR).toString()) }
    var month by rememberSaveable { mutableStateOf((currentCalendar.get(Calendar.MONTH) + 1).toString()) }
    var day by rememberSaveable { mutableStateOf(currentCalendar.get(Calendar.DAY_OF_MONTH).toString()) }
    var hour by rememberSaveable { mutableStateOf("8") }
    var minute by rememberSaveable { mutableStateOf("0") }
    var timeRepeatMode by rememberSaveable { mutableStateOf(TimeRepeatMode.ONCE) }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "设置日程时间",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 日期设置
        Text(
            text = "日期",
            style = MaterialTheme.typography.bodyMedium
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = year,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 4) year = it },
                label = { Text("年") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            OutlinedTextField(
                value = month,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) month = it },
                label = { Text("月") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            OutlinedTextField(
                value = day,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) day = it },
                label = { Text("日") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }

        // 时间设置
        Text(
            text = "时间",
            style = MaterialTheme.typography.bodyMedium
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = hour,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) hour = it },
                label = { Text("时") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            OutlinedTextField(
                value = minute,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) minute = it },
                label = { Text("分") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }

        // 重复模式选择
        Text(
            text = "重复模式",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TimeRepeatMode.values().forEach { mode ->
                FilterChip(
                    onClick = { timeRepeatMode = mode },
                    label = { Text(mode.displayName) },
                    selected = timeRepeatMode == mode,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = TimeBasedCondition(
                    timeConditionType = type,
                    year = year.toIntOrNull() ?: currentCalendar.get(Calendar.YEAR),
                    month = month.toIntOrNull() ?: (currentCalendar.get(Calendar.MONTH) + 1),
                    day = day.toIntOrNull() ?: currentCalendar.get(Calendar.DAY_OF_MONTH),
                    hour = hour.toIntOrNull() ?: 8,
                    minute = minute.toIntOrNull() ?: 0,
                    timeRepeatMode = timeRepeatMode,
                    startTime = System.currentTimeMillis()
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 周期时间配置内容组件
 *
 * 提供周期时间的时间和星期选择界面
 */
@Composable
private fun PeriodicTimeConfigContent(type: TimeConditionType, onComplete: (Any) -> Unit) {
    var hour by rememberSaveable { mutableStateOf("8") }
    var minute by rememberSaveable { mutableStateOf("0") }
    var scheduledRepeatMode by rememberSaveable { mutableStateOf(ScheduledRepeatMode.ONCE) }
    var selectedDays by rememberSaveable { mutableStateOf<Set<DayOfWeek>>(emptySet()) }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "设置周期时间",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 时间设置
        Text(
            text = "时间",
            style = MaterialTheme.typography.bodyMedium
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = hour,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) hour = it },
                label = { Text("时") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            OutlinedTextField(
                value = minute,
                onValueChange = { if (it.all { char -> char.isDigit() } && it.length <= 2) minute = it },
                label = { Text("分") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }

        // 重复模式选择
        Text(
            text = "重复模式",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            ScheduledRepeatMode.values().forEach { mode ->
                FilterChip(
                    onClick = { scheduledRepeatMode = mode },
                    label = { Text(mode.displayName) },
                    selected = scheduledRepeatMode == mode,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // 星期选择（仅在自定义模式下显示）
        if (scheduledRepeatMode == ScheduledRepeatMode.CUSTOM) {
            Text(
                text = "选择星期",
                style = MaterialTheme.typography.bodyMedium
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                DayOfWeek.values().forEach { dayOfWeek ->
                    Row(
                        verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
                    ) {
                        Checkbox(
                            checked = selectedDays.contains(dayOfWeek),
                            onCheckedChange = { checked ->
                                selectedDays = if (checked) {
                                    selectedDays + dayOfWeek
                                } else {
                                    selectedDays - dayOfWeek
                                }
                            }
                        )
                        Text(
                            text = dayOfWeek.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = TimeBasedCondition(
                    timeConditionType = type,
                    hour = hour.toIntOrNull() ?: 8,
                    minute = minute.toIntOrNull() ?: 0,
                    scheduledRepeatMode = scheduledRepeatMode,
                    selectedDays = selectedDays,
                    startTime = System.currentTimeMillis()
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 延迟触发配置内容组件
 *
 * 提供延迟触发的时间间隔设置界面
 */
@Composable
private fun DelayedTriggerConfigContent(type: TimeConditionType, onComplete: (Any) -> Unit) {
    var interval by rememberSaveable { mutableStateOf("30") }
    var unit by rememberSaveable { mutableStateOf(TimeIntervalUnit.SECONDS) }

    Column(
        modifier = Modifier
            .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "设置延迟触发",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 间隔时间输入
        OutlinedTextField(
            value = interval,
            onValueChange = { if (it.all { char -> char.isDigit() }) interval = it },
            label = { Text("延迟时间") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 时间单位选择
        Text(
            text = "时间单位",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TimeIntervalUnit.values().forEach { timeUnit ->
                FilterChip(
                    onClick = { unit = timeUnit },
                    label = { Text(timeUnit.displayName) },
                    selected = unit == timeUnit,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = TimeBasedCondition(
                    timeConditionType = type,
                    interval = interval.toIntOrNull() ?: 30,
                    unit = unit,
                    startTime = System.currentTimeMillis()
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 周期触发配置内容组件
 *
 * 提供周期触发的时间间隔设置界面
 */
@Composable
private fun PeriodicTriggerConfigContent(type: TimeConditionType, onComplete: (Any) -> Unit) {
    var interval by rememberSaveable { mutableStateOf("30") }
    var unit by rememberSaveable { mutableStateOf(TimeIntervalUnit.SECONDS) }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "设置周期触发",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 间隔时间输入
        OutlinedTextField(
            value = interval,
            onValueChange = { if (it.all { char -> char.isDigit() }) interval = it },
            label = { Text("触发间隔") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 时间单位选择
        Text(
            text = "时间单位",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TimeIntervalUnit.values().forEach { timeUnit ->
                FilterChip(
                    onClick = { unit = timeUnit },
                    label = { Text(timeUnit.displayName) },
                    selected = unit == timeUnit,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = TimeBasedCondition(
                    timeConditionType = type,
                    interval = interval.toIntOrNull() ?: 30,
                    unit = unit,
                    startTime = System.currentTimeMillis()
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

package com.weinuo.quickcommands.floating

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.utils.OverlayPermissionUtil

/**
 * 悬浮录制管理器
 *
 * 统一管理悬浮窗录制功能，提供简单的API接口。
 * 负责权限检查、服务管理和状态跟踪。
 */
object FloatingRecordingManager {

    private const val TAG = "FloatingRecordingManager"

    private var isFloatingWindowActive = false

    /**
     * 启动悬浮录制窗口
     *
     * @param context 上下文
     * @return 是否成功启动
     */
    fun startFloatingRecording(context: Context): Boolean {
        Log.d(TAG, "尝试启动悬浮录制窗口")

        // 检查悬浮窗权限
        if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
            Log.w(TAG, "没有悬浮窗权限，无法启动悬浮录制")
            return false
        }

        try {
            // 启动悬浮窗服务
            FloatingRecordingService.startFloatingWindow(context)
            isFloatingWindowActive = true
            Log.d(TAG, "悬浮录制窗口启动成功")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "启动悬浮录制窗口失败", e)
            isFloatingWindowActive = false
            return false
        }
    }

    /**
     * 停止悬浮录制窗口
     *
     * @param context 上下文
     */
    fun stopFloatingRecording(context: Context) {
        if (!isFloatingWindowActive) {
            Log.d(TAG, "悬浮录制窗口未启动，无需停止")
            return
        }

        try {
            FloatingRecordingService.stopFloatingWindow(context)
            isFloatingWindowActive = false
            Log.d(TAG, "悬浮录制窗口已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止悬浮录制窗口失败", e)
        }
    }

    /**
     * 检查悬浮录制窗口是否活跃
     *
     * @return 是否活跃
     */
    fun isFloatingRecordingActive(): Boolean {
        return isFloatingWindowActive
    }

    /**
     * 重置悬浮窗状态
     * 供FloatingRecordingService在服务销毁时调用
     */
    fun resetFloatingWindowState() {
        isFloatingWindowActive = false
        Log.d(TAG, "悬浮窗状态已重置")
    }

    /**
     * 检查是否具备启动悬浮录制的条件
     *
     * @param context 上下文
     * @return 检查结果
     */
    fun checkFloatingRecordingRequirements(context: Context): FloatingRecordingRequirement {
        // 检查悬浮窗权限
        if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
            return FloatingRecordingRequirement.OVERLAY_PERMISSION_REQUIRED
        }

        // 所有条件满足
        return FloatingRecordingRequirement.READY
    }

    /**
     * 请求必要的权限
     *
     * @param context 上下文
     * @param requirement 需要的权限
     */
    fun requestRequiredPermission(context: Context, requirement: FloatingRecordingRequirement) {
        when (requirement) {
            FloatingRecordingRequirement.OVERLAY_PERMISSION_REQUIRED -> {
                OverlayPermissionUtil.requestOverlayPermission(context)
            }
            FloatingRecordingRequirement.READY -> {
                // 无需请求权限
            }
        }
    }
}

/**
 * 悬浮录制需求枚举
 */
enum class FloatingRecordingRequirement {
    /** 准备就绪 */
    READY,

    /** 需要悬浮窗权限 */
    OVERLAY_PERMISSION_REQUIRED
}

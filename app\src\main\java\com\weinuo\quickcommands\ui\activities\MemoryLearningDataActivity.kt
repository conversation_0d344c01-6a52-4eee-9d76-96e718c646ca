package com.weinuo.quickcommands.ui.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.weinuo.quickcommands.ui.screens.MemoryLearningDataScreen
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme

/**
 * 内存学习数据界面Activity
 * 
 * 独立Activity，用于查看和管理智能内存学习收集的数据。
 * 显示各应用的内存使用学习记录，支持数据刷新和清除功能。
 */
class MemoryLearningDataActivity : ComponentActivity() {
    
    companion object {
        /**
         * 启动内存学习数据查看界面
         * 
         * @param context 上下文
         */
        fun startForView(context: Context) {
            val intent = Intent(context, MemoryLearningDataActivity::class.java)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            QuickCommandsTheme {
                MemoryLearningDataScreen(
                    onNavigateBack = { finish() }
                )
            }
        }
    }
}

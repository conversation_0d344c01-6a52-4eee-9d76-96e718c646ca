package com.weinuo.quickcommands.ui.configuration

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 配置模式枚举
 * 
 * 定义统一配置选择系统支持的三种配置模式：
 * - TRIGGER_CONDITION: 添加触发条件
 * - ABORT_CONDITION: 添加中止条件  
 * - TASK: 添加任务
 */
enum class ConfigurationMode {
    /** 添加触发条件模式 */
    TRIGGER_CONDITION,
    
    /** 添加中止条件模式 */
    ABORT_CONDITION,
    
    /** 添加任务模式 */
    TASK
}

/**
 * 配置项数据类
 *
 * 封装统一配置选择系统中每个配置项的基本信息和配置组件。
 *
 * @param id 唯一标识符，用于区分不同的配置项
 * @param title 显示标题，在网格卡片中显示
 * @param description 描述文字，在网格卡片中显示的详细说明
 * @param icon 图标，在网格卡片中显示的Material Design图标
 * @param type 类型对象，用于权限检查的操作类型（如CommunicationStateType、PhoneOperation等）
 * @param isExperimental 是否为实验性功能，默认为false
 * @param configComposable 配置组件，动态加载的Composable配置界面
 */
data class ConfigurationItem(
    val id: String,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val type: Any,  // 用于权限检查的类型，支持各种枚举类型
    val isExperimental: Boolean = false,  // 是否为实验性功能
    val configComposable: @Composable (
        initial: Any?,           // 初始配置对象（可为null）
        onConfigured: (Any) -> Unit,  // 配置完成回调
        onDismiss: () -> Unit    // 取消配置回调
    ) -> Unit
)

/**
 * ConfigurationMode扩展函数
 *
 * 获取配置模式对应的显示信息，只返回主标题。
 * 用于在UnifiedConfigurationScreen中显示不同模式的标题。
 *
 * @return String 主标题
 */
fun ConfigurationMode.getDisplayInfo(): String = when (this) {
    ConfigurationMode.TRIGGER_CONDITION -> "添加条件"
    ConfigurationMode.ABORT_CONDITION -> "添加中止条件"
    ConfigurationMode.TASK -> "添加任务"
}

/**
 * ConfigurationMode扩展函数
 * 
 * 获取配置模式对应的配置面板标题。
 * 用于在ConfigurationPanel中显示不同模式的配置标题。
 * 
 * @return String 配置面板标题
 */
fun ConfigurationMode.getConfigTitle(): String = when (this) {
    ConfigurationMode.TRIGGER_CONDITION -> "配置触发条件"
    ConfigurationMode.ABORT_CONDITION -> "配置中止条件"
    ConfigurationMode.TASK -> "配置任务"
}

/**
 * ConfigurationMode扩展函数
 *
 * 判断当前模式是否为条件模式（触发条件或中止条件）。
 * 用于数据提供器中判断是否使用相同的条件数据源。
 *
 * @return Boolean true表示是条件模式，false表示是任务模式
 */
fun ConfigurationMode.isConditionMode(): Boolean = when (this) {
    ConfigurationMode.TRIGGER_CONDITION,
    ConfigurationMode.ABORT_CONDITION -> true
    ConfigurationMode.TASK -> false
}

/**
 * ConfigurationMode扩展函数
 *
 * 获取配置模式对应的搜索框提示文字。
 * 用于在UnifiedConfigurationScreen中显示不同模式的搜索提示。
 *
 * @return String 搜索框提示文字
 */
fun ConfigurationMode.getSearchPlaceholder(): String = when (this) {
    ConfigurationMode.TRIGGER_CONDITION -> "搜索触发条件..."
    ConfigurationMode.ABORT_CONDITION -> "搜索中止条件..."
    ConfigurationMode.TASK -> "搜索任务..."
}

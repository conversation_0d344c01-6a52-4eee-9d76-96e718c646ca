package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import android.net.Uri
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.components.RingtoneConfigurationContent
import com.weinuo.quickcommands.ui.screens.LocalNavController
import com.weinuo.quickcommands.utils.RingtoneHelper
import com.weinuo.quickcommands.utils.FilePickerUtil

/**
 * 通知任务配置数据提供器
 *
 * 提供通知任务特定的配置项列表，为每个操作类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object NotificationTaskConfigProvider {

    /**
     * 获取通知任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 通知任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<NotificationOperation>> {
        return listOf(
            // 显示通知
            ConfigurationCardItem(
                id = "show_notification",
                title = context.getString(R.string.notification_show_notification),
                description = context.getString(R.string.notification_show_notification_description),
                operationType = NotificationOperation.SHOW_NOTIFICATION,
                permissionRequired = true, // 需要通知权限（Android 13+）
                content = { operation, onComplete ->
                    ShowNotificationConfigContent(operation, onComplete)
                }
            ),

            // 取消通知
            ConfigurationCardItem(
                id = "cancel_notification",
                title = context.getString(R.string.notification_cancel_notification),
                description = context.getString(R.string.notification_cancel_notification_description),
                operationType = NotificationOperation.CLEAR_NOTIFICATIONS,
                permissionRequired = true, // 需要通知使用权限
                content = { operation, onComplete ->
                    CancelNotificationConfigContent(operation, onComplete)
                }
            ),

            // 清除通知
            ConfigurationCardItem(
                id = "clear_notifications",
                title = "清除通知",
                description = "清除通知栏中的通知",
                operationType = NotificationOperation.CLEAR_NOTIFICATIONS,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ClearNotificationsConfigContent(operation, onComplete)
                }
            ),

            // Toast消息
            ConfigurationCardItem(
                id = "show_toast",
                title = "Toast消息",
                description = "显示短暂的Toast提示消息",
                operationType = NotificationOperation.SHOW_TOAST,
                permissionRequired = false,
                content = { operation, onComplete ->
                    ShowToastConfigContent(operation, onComplete)
                }
            ),

            // 恢复隐藏的通知
            ConfigurationCardItem(
                id = "restore_hidden_notifications",
                title = "恢复隐藏的通知",
                description = "恢复之前隐藏的通知",
                operationType = NotificationOperation.RESTORE_HIDDEN_NOTIFICATIONS,
                permissionRequired = true,
                content = { operation, onComplete ->
                    RestoreHiddenNotificationsConfigContent(operation, onComplete)
                }
            ),

            // 显示对话框
            ConfigurationCardItem(
                id = "show_dialog",
                title = "显示对话框",
                description = "显示自定义对话框",
                operationType = NotificationOperation.SHOW_DIALOG,
                permissionRequired = false,
                content = { operation, onComplete ->
                    ShowDialogConfigContent(operation, onComplete)
                }
            ),

            // 显示气泡通知
            ConfigurationCardItem(
                id = "show_bubble",
                title = "显示气泡通知",
                description = "显示气泡样式的通知",
                operationType = NotificationOperation.SHOW_BUBBLE,
                permissionRequired = true, // 需要通知权限和气泡通知权限
                content = { operation, onComplete ->
                    ShowBubbleConfigContent(operation, onComplete)
                }
            ),

            // 浮动通知控制
            ConfigurationCardItem(
                id = "floating_notification_control",
                title = "浮动通知控制",
                description = "启用或禁用浮动通知",
                operationType = NotificationOperation.FLOATING_NOTIFICATION_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    FloatingNotificationControlConfigContent(operation, onComplete)
                }
            ),

            // 设置通知铃声
            ConfigurationCardItem(
                id = "ringtone_settings",
                title = "设置通知铃声",
                description = "配置通知铃声设置",
                operationType = NotificationOperation.RINGTONE_SETTINGS,
                permissionRequired = false,
                content = { operation, onComplete ->
                    RingtoneSettingsConfigContent(operation, onComplete)
                }
            ),

            // 通知回复
            ConfigurationCardItem(
                id = "reply_notification",
                title = "通知回复",
                description = "自动回复指定应用的通知",
                operationType = NotificationOperation.REPLY_NOTIFICATION,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ReplyNotificationConfigContent(operation, onComplete)
                }
            ),

            // 通知交互
            ConfigurationCardItem(
                id = "interact_notification",
                title = "通知交互",
                description = "与指定应用的通知进行交互",
                operationType = NotificationOperation.INTERACT_NOTIFICATION,
                permissionRequired = true,
                content = { operation, onComplete ->
                    InteractNotificationConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 显示通知配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ShowNotificationConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    var notificationTitle by rememberSaveable { mutableStateOf("") }
    var notificationText by rememberSaveable { mutableStateOf("") }
    var notificationIconPath by rememberSaveable { mutableStateOf("") }
    var notificationChannel by rememberSaveable { mutableStateOf("default") }
    var notificationPriority by rememberSaveable { mutableStateOf(NotificationPriority.DEFAULT) }
    var notificationId by rememberSaveable { mutableStateOf("1") }
    var autoCancel by rememberSaveable { mutableStateOf(true) }
    var ongoing by rememberSaveable { mutableStateOf(false) }

    // 图标文件选择器启动器
    val iconFileLauncher = FilePickerUtil.rememberFilePickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getRealPath(context, it)
            notificationIconPath = realPath ?: it.toString()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "显示通知设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = notificationTitle,
            onValueChange = { notificationTitle = it },
            label = { Text("通知标题") },
            placeholder = { Text("输入通知标题") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = notificationText,
            onValueChange = { notificationText = it },
            label = { Text("通知内容") },
            placeholder = { Text("输入通知内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4
        )

        // 通知图标选择
        Text(
            text = "通知图标 (可选)",
            style = MaterialTheme.typography.bodyLarge
        )

        Button(
            onClick = {
                FilePickerUtil.launchFilePicker(iconFileLauncher, arrayOf("image/*"))
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (notificationIconPath.isEmpty()) {
                    "选择图标文件"
                } else {
                    "已选择: ${FilePickerUtil.getDisplayPath(context, Uri.parse(notificationIconPath))}"
                }
            )
        }

        if (notificationIconPath.isNotEmpty()) {
            Text(
                text = "路径: $notificationIconPath",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        OutlinedTextField(
            value = notificationChannel,
            onValueChange = { notificationChannel = it },
            label = { Text("通知渠道") },
            placeholder = { Text("default") },
            modifier = Modifier.fillMaxWidth()
        )

        // 优先级选择
        Text(
            text = "通知优先级",
            style = MaterialTheme.typography.bodyLarge
        )

        NotificationPriority.values().forEach { priority ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (notificationPriority == priority),
                        onClick = { notificationPriority = priority }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (notificationPriority == priority),
                    onClick = { notificationPriority = priority }
                )
                Text(
                    text = when (priority) {
                        NotificationPriority.MIN -> "最低"
                        NotificationPriority.LOW -> "低"
                        NotificationPriority.DEFAULT -> "默认"
                        NotificationPriority.HIGH -> "高"
                        NotificationPriority.MAX -> "最高"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        OutlinedTextField(
            value = notificationId,
            onValueChange = { notificationId = it },
            label = { Text("通知ID") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )

        // 自动取消选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = autoCancel,
                    onValueChange = { autoCancel = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = autoCancel,
                onCheckedChange = { autoCancel = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "点击后自动取消",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 持续显示选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = ongoing,
                    onValueChange = { ongoing = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = ongoing,
                onCheckedChange = { ongoing = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "持续显示（无法滑动删除）",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    notificationTitle = notificationTitle,
                    notificationText = notificationText,
                    notificationIcon = "", // 保留原字段兼容性
                    notificationIconPath = notificationIconPath,
                    notificationChannel = notificationChannel,
                    notificationPriority = notificationPriority.value,
                    notificationId = notificationId.toIntOrNull() ?: 1,
                    autoCancel = autoCancel,
                    ongoing = ongoing
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = notificationTitle.isNotBlank() && notificationText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 取消通知配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun CancelNotificationConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {

    var notificationId by rememberSaveable { mutableStateOf("1") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "取消通知设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = notificationId,
            onValueChange = { notificationId = it },
            label = { Text("通知ID") },
            placeholder = { Text("要取消的通知ID") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    notificationId = notificationId.toIntOrNull() ?: 1
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = notificationId.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 清除通知配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ClearNotificationsConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }
    var clearMode by rememberSaveable { mutableStateOf(NotificationClearMode.ALL) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                // 🔑 关键：自动切换到指定应用模式
                clearMode = NotificationClearMode.SPECIFIC_APP
                // 更新选中的应用
                val app = selectedAppsResult.first()
                selectedAppPackageName = app.packageName
                selectedAppName = app.appName
                selectedAppIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "清除通知设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 清除模式选择
        Text(
            text = "清除模式",
            style = MaterialTheme.typography.bodyLarge
        )

        NotificationClearMode.values().forEach { mode ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (clearMode == mode),
                        onClick = { clearMode = mode }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (clearMode == mode),
                    onClick = { clearMode = mode }
                )
                Text(
                    text = mode.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 指定应用选择（仅在SPECIFIC_APP模式下显示）
        if (clearMode == NotificationClearMode.SPECIFIC_APP) {
            Text(
                text = "选择应用",
                style = MaterialTheme.typography.titleMedium
            )

            OutlinedButton(
                onClick = {
                    if (navController != null) {
                        val route = Screen.AppSelection.createSingleSelectionRoute()
                        navController.navigate(route)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedApp != null) {
                        "已选择: ${selectedApp.appName}"
                    } else {
                        "点击选择应用"
                    }
                )
            }

            if (selectedApp != null) {
                Text(
                    text = "包名: ${selectedApp.packageName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 说明文字
        Text(
            text = when (clearMode) {
                NotificationClearMode.ALL -> "此操作将清除通知栏中的所有通知。需要通知访问权限。"
                NotificationClearMode.SPECIFIC_APP -> "此操作将清除指定应用的通知。需要通知访问权限。"
                NotificationClearMode.TRIGGER_BASED -> "此操作将使用通知触发器来清除通知。需要通知访问权限。"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    clearNotificationMode = clearMode,
                    clearNotificationPackage = selectedApp?.packageName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (clearMode) {
                NotificationClearMode.ALL, NotificationClearMode.TRIGGER_BASED -> true
                NotificationClearMode.SPECIFIC_APP -> selectedApp != null
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 显示Toast配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ShowToastConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {

    var toastMessage by rememberSaveable { mutableStateOf("") }
    var toastDuration by rememberSaveable { mutableStateOf(ToastDuration.SHORT) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Toast消息设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = toastMessage,
            onValueChange = { toastMessage = it },
            label = { Text("Toast消息") },
            placeholder = { Text("输入要显示的Toast消息") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 1,
            maxLines = 3
        )

        // 持续时间选择
        Text(
            text = "显示时长",
            style = MaterialTheme.typography.bodyLarge
        )

        ToastDuration.values().forEach { duration ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (toastDuration == duration),
                        onClick = { toastDuration = duration }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (toastDuration == duration),
                    onClick = { toastDuration = duration }
                )
                Text(
                    text = when (duration) {
                        ToastDuration.SHORT -> "短时间"
                        ToastDuration.LONG -> "长时间"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    toastMessage = toastMessage,
                    toastDuration = when (toastDuration) {
                        ToastDuration.SHORT -> 2000
                        ToastDuration.LONG -> 3500
                    }
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = toastMessage.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 恢复隐藏的通知配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun RestoreHiddenNotificationsConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }
    var restoreMode by rememberSaveable { mutableStateOf(NotificationRestoreMode.ALL) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current

    // 监听应用选择结果
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 使用字段分离的方式获取应用选择结果
        val selectedAppPackageNameResult = savedStateHandle?.get<String>("selected_app_package_name")
        val selectedAppNameResult = savedStateHandle?.get<String>("selected_app_name")
        val selectedAppIsSystemAppResult = savedStateHandle?.get<Boolean>("selected_app_is_system_app")

        if (!selectedAppPackageNameResult.isNullOrEmpty()) {
            // 🔑 关键：自动切换到指定应用模式
            restoreMode = NotificationRestoreMode.SPECIFIC_APP
            // 更新选中的应用
            selectedAppPackageName = selectedAppPackageNameResult
            selectedAppName = selectedAppNameResult ?: ""
            selectedAppIsSystemApp = selectedAppIsSystemAppResult ?: false
            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_app_package_name")
            savedStateHandle.remove<String>("selected_app_name")
            savedStateHandle.remove<Boolean>("selected_app_is_system_app")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "恢复隐藏的通知",
            style = MaterialTheme.typography.titleMedium
        )

        // 恢复模式选择
        Text(
            text = "恢复模式",
            style = MaterialTheme.typography.bodyLarge
        )

        NotificationRestoreMode.values().forEach { mode ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (restoreMode == mode),
                        onClick = { restoreMode = mode }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (restoreMode == mode),
                    onClick = { restoreMode = mode }
                )
                Text(
                    text = mode.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 指定应用选择（仅在SPECIFIC_APP模式下显示）
        if (restoreMode == NotificationRestoreMode.SPECIFIC_APP) {
            Text(
                text = "选择应用",
                style = MaterialTheme.typography.titleMedium
            )

            OutlinedButton(
                onClick = {
                    if (navController != null) {
                        val route = Screen.AppSelection.createSingleSelectionRoute()
                        navController.navigate(route)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedApp != null) {
                        "已选择: ${selectedApp.appName}"
                    } else {
                        "点击选择应用"
                    }
                )
            }

            if (selectedApp != null) {
                Text(
                    text = "包名: ${selectedApp.packageName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 说明文字
        Text(
            text = when (restoreMode) {
                NotificationRestoreMode.ALL -> "此操作将恢复所有之前隐藏的通知。需要通知访问权限。"
                NotificationRestoreMode.SPECIFIC_APP -> "此操作将恢复指定应用的隐藏通知。需要通知访问权限。"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    restoreNotificationMode = restoreMode,
                    restoreNotificationPackage = selectedApp?.packageName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (restoreMode) {
                NotificationRestoreMode.ALL -> true
                NotificationRestoreMode.SPECIFIC_APP -> selectedApp != null
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 显示对话框配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ShowDialogConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {

    var dialogTitle by rememberSaveable { mutableStateOf("") }
    var dialogText by rememberSaveable { mutableStateOf("") }
    var dialogHtmlFormat by rememberSaveable { mutableStateOf(false) }
    var dialogPreventBackClose by rememberSaveable { mutableStateOf(false) }
    var dialogRequireCompletion by rememberSaveable { mutableStateOf(false) }
    var dialogNotificationSound by rememberSaveable { mutableStateOf(NotificationSoundType.DEFAULT) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "显示对话框设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = dialogTitle,
            onValueChange = { dialogTitle = it },
            label = { Text("对话框标题") },
            placeholder = { Text("输入对话框标题") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = dialogText,
            onValueChange = { dialogText = it },
            label = { Text("对话框内容") },
            placeholder = { Text("输入对话框内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 6
        )

        // HTML格式化选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = dialogHtmlFormat,
                    onValueChange = { dialogHtmlFormat = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = dialogHtmlFormat,
                onCheckedChange = { dialogHtmlFormat = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "启用HTML格式化",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 防止后退关闭选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = dialogPreventBackClose,
                    onValueChange = { dialogPreventBackClose = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = dialogPreventBackClose,
                onCheckedChange = { dialogPreventBackClose = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "防止按后退键关闭",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 要求完成选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = dialogRequireCompletion,
                    onValueChange = { dialogRequireCompletion = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = dialogRequireCompletion,
                onCheckedChange = { dialogRequireCompletion = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "完成后才能执行后续动作",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 通知声音选择
        Text(
            text = "通知声音",
            style = MaterialTheme.typography.bodyLarge
        )

        NotificationSoundType.values().forEach { soundType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (dialogNotificationSound == soundType),
                        onClick = { dialogNotificationSound = soundType }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (dialogNotificationSound == soundType),
                    onClick = { dialogNotificationSound = soundType }
                )
                Text(
                    text = soundType.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    dialogTitle = dialogTitle,
                    dialogText = dialogText,
                    dialogHtmlFormat = dialogHtmlFormat,
                    dialogPreventBackClose = dialogPreventBackClose,
                    dialogRequireCompletion = dialogRequireCompletion,
                    dialogNotificationSound = dialogNotificationSound
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = dialogTitle.isNotBlank() && dialogText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 显示气泡通知配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ShowBubbleConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {

    var bubbleTitle by rememberSaveable { mutableStateOf("") }
    var bubbleText by rememberSaveable { mutableStateOf("") }
    var bubbleHtmlFormat by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "显示气泡通知设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = bubbleTitle,
            onValueChange = { bubbleTitle = it },
            label = { Text("气泡通知标题") },
            placeholder = { Text("输入气泡通知标题") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = bubbleText,
            onValueChange = { bubbleText = it },
            label = { Text("气泡通知内容") },
            placeholder = { Text("输入气泡通知内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4
        )

        // HTML格式化选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = bubbleHtmlFormat,
                    onValueChange = { bubbleHtmlFormat = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = bubbleHtmlFormat,
                onCheckedChange = { bubbleHtmlFormat = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "启用HTML格式化",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    bubbleTitle = bubbleTitle,
                    bubbleText = bubbleText,
                    bubbleHtmlFormat = bubbleHtmlFormat
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = bubbleTitle.isNotBlank() && bubbleText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 浮动通知控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun FloatingNotificationControlConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {

    var floatingNotificationOperation by rememberSaveable { mutableStateOf(SwitchOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "浮动通知控制设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 浮动通知操作选择
        SwitchOperation.values().forEach { switchOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (floatingNotificationOperation == switchOp),
                        onClick = { floatingNotificationOperation = switchOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (floatingNotificationOperation == switchOp),
                    onClick = { floatingNotificationOperation = switchOp }
                )
                Text(
                    text = switchOp.displayName + "浮动通知",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    floatingNotificationOperation = floatingNotificationOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 通知铃声配置内容组件
 *
 * 使用通用铃声配置组件，专门用于通知铃声设置
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun RingtoneSettingsConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {
    // 使用通用铃声配置组件，固定为通知铃声类型
    RingtoneConfigurationContent(
        title = "配置通知铃声",
        description = "选择系统通知时使用的铃声",
        ringtoneType = RingtoneHelper.RingtoneType.NOTIFICATION,
        showTypeSelection = false, // 不显示类型选择，固定为通知铃声
        onComplete = { selectedRingtoneUri, selectedRingtoneName ->
            val task = NotificationTask(
                operation = operation,
                selectedRingtoneUri = selectedRingtoneUri,
                selectedRingtoneName = selectedRingtoneName
            )
            onComplete(task)
        }
    )
}

/**
 * 通知回复配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ReplyNotificationConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }
    var replyNotificationText by rememberSaveable { mutableStateOf("") }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedAppPackageName = app.packageName
                selectedAppName = app.appName
                selectedAppIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "通知回复设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 应用选择
        Text(
            text = "选择应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedApp != null) {
                    "已选择: ${selectedApp.appName}"
                } else {
                    "点击选择应用"
                }
            )
        }

        if (selectedApp != null) {
            Text(
                text = "包名: ${selectedApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        OutlinedTextField(
            value = replyNotificationText,
            onValueChange = { replyNotificationText = it },
            label = { Text("回复内容") },
            placeholder = { Text("输入自动回复的内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4
        )

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    replyNotificationPackage = selectedApp?.packageName ?: "",
                    replyNotificationText = replyNotificationText
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedApp != null && replyNotificationText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 通知交互配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun InteractNotificationConfigContent(
    operation: NotificationOperation,
    onComplete: (Any) -> Unit
) {
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }
    var interactionNotificationAction by rememberSaveable { mutableStateOf(NotificationActionType.CLICK) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedAppPackageName = app.packageName
                selectedAppName = app.appName
                selectedAppIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "通知交互设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 应用选择
        Text(
            text = "选择应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedApp != null) {
                    "已选择: ${selectedApp.appName}"
                } else {
                    "点击选择应用"
                }
            )
        }

        if (selectedApp != null) {
            Text(
                text = "包名: ${selectedApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 交互动作选择
        Text(
            text = "交互动作",
            style = MaterialTheme.typography.bodyLarge
        )

        NotificationActionType.values().forEach { actionType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (interactionNotificationAction == actionType),
                        onClick = { interactionNotificationAction = actionType }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (interactionNotificationAction == actionType),
                    onClick = { interactionNotificationAction = actionType }
                )
                Text(
                    text = actionType.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = NotificationTask(
                    operation = operation,
                    interactionNotificationPackage = selectedApp?.packageName ?: "",
                    interactionNotificationAction = interactionNotificationAction
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedApp != null
        ) {
            Text("确认配置")
        }
    }
}

package com.weinuo.quickcommands.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import java.lang.ref.WeakReference

/**
 * 高性能语言管理器
 * 
 * 设计原则：
 * 1. 延迟初始化：只在需要时才创建实例
 * 2. 缓存机制：缓存当前语言设置，避免重复读取SharedPreferences
 * 3. 最小化Context操作：仅在必要时更新Context的Locale
 * 4. 防抖机制：语言设置变更时使用防抖，避免频繁更新
 * 5. 零性能损耗：系统默认语言时不进行任何额外操作
 * 
 * 性能优化特性：
 * - 30秒缓存机制，减少SharedPreferences读取
 * - WeakReference避免内存泄漏
 * - 条件性操作，系统默认时零开销
 * - 单例模式，避免重复初始化
 */
object LanguageManager {
    private const val TAG = "LanguageManager"
    private const val PREFS_NAME = "background_manager_settings"
    private const val KEY_APP_LANGUAGE = "app_language"
    private const val CACHE_DURATION = 30_000L // 30秒缓存，平衡性能和实时性
    
    // 支持的语言
    const val LANGUAGE_SYSTEM = "system"
    const val LANGUAGE_CHINESE = "zh"
    const val LANGUAGE_ENGLISH = "en"
    
    // 缓存相关
    @Volatile
    private var cachedLanguage: String? = null
    @Volatile
    private var lastReadTime: Long = 0L
    private var contextRef: WeakReference<Context>? = null
    
    // 防抖相关
    @Volatile
    private var lastUpdateTime: Long = 0L
    private const val UPDATE_DEBOUNCE_DURATION = 100L // 100ms防抖
    
    /**
     * 获取当前语言设置
     * 
     * 性能优化：
     * 1. 使用缓存机制，30秒内不重复读取SharedPreferences
     * 2. 系统默认语言时直接返回，无额外开销
     * 
     * @param context 上下文
     * @return 当前语言设置 (system/zh/en)
     */
    fun getCurrentLanguage(context: Context): String {
        val now = System.currentTimeMillis()
        
        // 检查缓存是否有效
        if (cachedLanguage == null || (now - lastReadTime) > CACHE_DURATION) {
            try {
                // 缓存失效，重新读取
                cachedLanguage = readLanguageFromPrefs(context)
                lastReadTime = now
                
                // 更新Context引用（使用WeakReference避免内存泄漏）
                contextRef = WeakReference(context.applicationContext)
                
                Log.d(TAG, "Language cache refreshed: $cachedLanguage")
            } catch (e: Exception) {
                Log.e(TAG, "Error reading language from preferences", e)
                cachedLanguage = LANGUAGE_SYSTEM // 出错时默认使用系统语言
            }
        }
        
        return cachedLanguage ?: LANGUAGE_SYSTEM
    }
    
    /**
     * 设置应用语言
     * 
     * 性能优化：
     * 1. 防抖机制，避免频繁更新
     * 2. 条件性保存，只在语言真正改变时才写入
     * 3. 立即更新缓存，避免下次读取时的延迟
     * 
     * @param context 上下文
     * @param language 语言代码 (system/zh/en)
     * @return 是否成功设置
     */
    fun setLanguage(context: Context, language: String): Boolean {
        val now = System.currentTimeMillis()
        
        // 防抖检查
        if (now - lastUpdateTime < UPDATE_DEBOUNCE_DURATION) {
            Log.d(TAG, "Language update debounced")
            return false
        }
        
        try {
            // 检查是否真的需要更新
            val currentLanguage = getCurrentLanguage(context)
            if (currentLanguage == language) {
                Log.d(TAG, "Language already set to: $language")
                return true
            }
            
            // 验证语言代码
            if (!isValidLanguage(language)) {
                Log.w(TAG, "Invalid language code: $language")
                return false
            }
            
            // 保存到SharedPreferences
            val success = saveLanguageToPrefs(context, language)
            if (success) {
                // 立即更新缓存
                cachedLanguage = language
                lastReadTime = now
                lastUpdateTime = now
                
                Log.i(TAG, "Language updated to: $language")
            }
            
            return success
        } catch (e: Exception) {
            Log.e(TAG, "Error setting language to: $language", e)
            return false
        }
    }
    
    /**
     * 检查是否需要应用语言设置
     * 
     * 性能优化：
     * 系统默认语言时返回false，调用方可以跳过Context包装，实现零性能损耗
     * 
     * @param context 上下文
     * @return 是否需要应用自定义语言设置
     */
    fun needsLanguageOverride(context: Context): Boolean {
        val language = getCurrentLanguage(context)
        return language != LANGUAGE_SYSTEM
    }
    
    /**
     * 获取语言显示名称
     * 
     * @param language 语言代码
     * @return 显示名称
     */
    fun getLanguageDisplayName(language: String): String {
        return when (language) {
            LANGUAGE_SYSTEM -> "系统默认"
            LANGUAGE_CHINESE -> "中文"
            LANGUAGE_ENGLISH -> "English"
            else -> language
        }
    }
    
    /**
     * 清除语言缓存
     * 
     * 在应用设置被外部修改时调用，强制重新读取
     */
    fun clearCache() {
        cachedLanguage = null
        lastReadTime = 0L
        Log.d(TAG, "Language cache cleared")
    }
    
    /**
     * 从SharedPreferences读取语言设置
     */
    private fun readLanguageFromPrefs(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getString(KEY_APP_LANGUAGE, LANGUAGE_SYSTEM) ?: LANGUAGE_SYSTEM
    }
    
    /**
     * 保存语言设置到SharedPreferences
     */
    private fun saveLanguageToPrefs(context: Context, language: String): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit()
                .putString(KEY_APP_LANGUAGE, language)
                .apply()
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error saving language to preferences", e)
            false
        }
    }
    
    /**
     * 验证语言代码是否有效
     */
    private fun isValidLanguage(language: String): Boolean {
        return language in listOf(LANGUAGE_SYSTEM, LANGUAGE_CHINESE, LANGUAGE_ENGLISH)
    }
}

package com.weinuo.quickcommands.ui.components.skyblue

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueDialogConfigManager

/**
 * 天空蓝主题对话框组件使用示例
 *
 * 展示SkyBlueDialog组件的各种用法和配置效果
 * 包括：
 * - 基本文本对话框
 * - 带图标的对话框
 * - 选择列表对话框
 * - 不同模糊效果和圆角配置的对话框
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */

/**
 * 自定义卡片组件，不依赖MD3
 */
@Composable
fun SkyBlueCard(
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    backgroundColor: Color = Color.White,
    cornerRadius: androidx.compose.ui.unit.Dp = 12.dp,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
            .then(
                if (onClick != null) {
                    Modifier.clickable { onClick() }
                } else {
                    Modifier
                }
            )
    ) {
        content()
    }
}

/**
 * 对话框示例数据类
 */
data class DialogExample(
    val title: String,
    val description: String,
    val action: @Composable (SettingsRepository, () -> Unit) -> Unit
)

/**
 * 天空蓝对话框示例屏幕
 *
 * @param settingsRepository 设置仓库
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SkyBlueDialogExamplesScreen(
    settingsRepository: SettingsRepository
) {
    var showDialog by remember { mutableStateOf<DialogExample?>(null) }

    val examples = remember {
        listOf(
            DialogExample(
                title = "基本文本对话框",
                description = "最简单的文本对话框，支持模糊效果和动态圆角"
            ) { repo, onDismiss ->
                SkyBlueDialog(
                    onDismissRequest = onDismiss,
                    settingsRepository = repo,
                    title = "基本对话框",
                    message = "这是一个基本的天空蓝主题对话框示例。它支持从全局设置中读取圆角大小和模糊效果配置。",
                    confirmText = "确定",
                    onConfirm = onDismiss,
                    dismissText = "取消",
                    onDismiss = onDismiss
                )
            },
            DialogExample(
                title = "带图标的对话框",
                description = "包含图标的对话框，增强视觉效果"
            ) { repo, onDismiss ->
                SkyBlueDialog(
                    onDismissRequest = onDismiss,
                    settingsRepository = repo,
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = "信息",
                            tint = Color(0xFF0A59F7)
                        )
                    },
                    title = { Text("重要信息") },
                    text = {
                        Text("这是一个带有图标的对话框示例。图标可以帮助用户快速理解对话框的类型和重要性。")
                    },
                    confirmButton = {
                        SkyBlueDialogButton(
                            text = "我知道了",
                            onClick = onDismiss,
                            isPrimary = true,
                            settingsRepository = repo
                        )
                    }
                )
            },
            DialogExample(
                title = "警告对话框",
                description = "用于显示警告信息的对话框"
            ) { repo, onDismiss ->
                SkyBlueDialog(
                    onDismissRequest = onDismiss,
                    settingsRepository = repo,
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = "警告",
                            tint = Color(0xFFE84026)
                        )
                    },
                    title = { Text("警告") },
                    text = {
                        Text("此操作不可撤销，请确认是否继续？模糊效果和圆角大小会根据您的全局设置自动调整。")
                    },
                    confirmButton = {
                        SkyBlueDialogButton(
                            text = "继续",
                            onClick = onDismiss,
                            isPrimary = true,
                            settingsRepository = repo
                        )
                    },
                    dismissButton = {
                        SkyBlueDialogButton(
                            text = "取消",
                            onClick = onDismiss,
                            isPrimary = false,
                            settingsRepository = repo
                        )
                    }
                )
            },
            DialogExample(
                title = "选择列表对话框",
                description = "显示选项列表的对话框，支持滚动"
            ) { repo, onDismiss ->
                val options = listOf(
                    "选项一：启用模糊效果",
                    "选项二：调整圆角大小",
                    "选项三：修改模糊强度",
                    "选项四：重置为默认设置",
                    "选项五：保存当前配置",
                    "选项六：导出设置",
                    "选项七：导入设置",
                    "选项八：高级配置"
                )
                
                SkyBlueSelectionDialog(
                    onDismissRequest = onDismiss,
                    settingsRepository = repo,
                    title = "选择操作",
                    description = "请选择您要执行的操作：",
                    items = options,
                    onItemSelected = { option ->
                        // 处理选择
                        onDismiss()
                    },
                    itemContent = { option ->
                        SkyBlueCard(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            onClick = { onDismiss() },
                            backgroundColor = Color(0xFFF1F3F5)
                        ) {
                            Text(
                                text = option,
                                modifier = Modifier.padding(16.dp),
                                color = Color(0xE5000000),
                                fontSize = 14.sp
                            )
                        }
                    }
                )
            },
            DialogExample(
                title = "长文本对话框",
                description = "包含大量文本的对话框，支持滚动"
            ) { repo, onDismiss ->
                SkyBlueDialog(
                    onDismissRequest = onDismiss,
                    settingsRepository = repo,
                    title = "使用说明",
                    message = """
                        天空蓝主题对话框组件特点：
                        
                        1. 动态圆角大小
                        - 圆角大小可通过全局设置控制
                        - 支持0-50dp的范围调整
                        - 实时生效，无需重启应用
                        
                        2. 智能模糊效果
                        - 使用Haze库实现iOS风格模糊
                        - 支持所有Android版本
                        - 可通过全局设置开关控制
                        - 支持模糊强度调节（0.0-1.0）
                        
                        3. 完整的可访问性支持
                        - 支持屏幕阅读器
                        - 键盘导航友好
                        - 符合Material Design规范
                        
                        4. 高度可定制
                        - 支持自定义图标
                        - 支持自定义按钮
                        - 支持自定义内容
                        - 支持滚动长内容
                        
                        5. 性能优化
                        - 智能模糊渲染
                        - 内存使用优化
                        - 动画性能优化
                        
                        这个对话框会根据内容长度自动启用滚动功能。
                    """.trimIndent(),
                    confirmText = "明白了",
                    onConfirm = onDismiss,
                    maxHeight = 300.dp
                )
            }
        )
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 自定义顶部栏
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF1F3F5))
                .padding(16.dp)
        ) {
            Text(
                text = "天空蓝对话框示例",
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xE5000000)
            )
        }

        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            item {
                SkyBlueCard(
                    modifier = Modifier.fillMaxWidth(),
                    backgroundColor = Color.White
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "对话框配置说明",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xE5000000)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "所有对话框都会自动读取全局设置中的圆角大小和模糊效果配置。您可以在设置中调整这些参数来查看效果。",
                            fontSize = 14.sp,
                            color = Color(0x99000000),
                            lineHeight = 20.sp
                        )
                    }
                }
            }

            items(examples) { example ->
                SkyBlueCard(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = { showDialog = example },
                    backgroundColor = Color.White
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = example.title,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xE5000000)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = example.description,
                            fontSize = 14.sp,
                            color = Color(0x99000000),
                            lineHeight = 20.sp
                        )
                    }
                }
            }
        }
    }

    // 显示选中的对话框
    showDialog?.let { example ->
        example.action(settingsRepository) {
            showDialog = null
        }
    }
}

/**
 * 对话框配置预览组件
 *
 * 显示当前对话框配置的实时预览
 */
@Composable
fun DialogConfigPreview(
    settingsRepository: SettingsRepository
) {
    val dialogConfigManager: SkyBlueDialogConfigManager = remember { SkyBlueDialogConfigManager(settingsRepository) }
    val configSummary: String = dialogConfigManager.getConfigSummary()

    SkyBlueCard(
        modifier = Modifier.fillMaxWidth(),
        backgroundColor = Color.White
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "当前配置",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xE5000000)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = configSummary,
                fontSize = 14.sp,
                color = Color(0xFF0A59F7)
            )
        }
    }
}

# AppSelectionScreen 与 AppsScreen 一致性测试指南

## 修改摘要

### 已完成的修改

1. **SimpleAppInfo 数据类增强**
   - 添加了 `icon: Drawable?` 属性
   - 更新了序列化方法以支持图标（图标不序列化，需要重新加载）
   - 更新了 `fromFields` 方法以支持图标参数

2. **AppSelectionScreen 运行状态检测统一**
   - 添加了与 AppRepository 一致的 UsageStatsManager 检测逻辑
   - 实现了相同的缓存机制（5秒缓存有效期）
   - 添加了 `hasUsageStatsPermission()` 方法
   - 添加了 `isAppRunning()` 方法，与 AppRepository 完全一致

3. **应用图标加载统一**
   - 使用 `applicationInfo.loadIcon(packageManager)` 加载图标
   - 在 AppItem 组件中使用 `ImageUtils.DrawableImage` 显示图标
   - 图标加载失败时显示首字母占位符

4. **排序逻辑保持一致**
   - 按类型分组（用户应用在前，系统应用在后）
   - 组内按运行状态排序（运行中的在前）
   - 最后按应用名称排序

5. **UI显示优化**
   - 移除了系统应用标识（因为已经分组显示）
   - 系统应用和用户应用的对齐方式保持一致

## 一致性验证清单

### 核心逻辑一致性
- [x] 应用获取方式：`PackageManager.getInstalledPackages(0)`
- [x] 系统应用判断：`ApplicationInfo.FLAG_SYSTEM`
- [x] 运行状态检测：UsageStatsManager + 缓存机制
- [x] 应用图标加载：`applicationInfo.loadIcon(packageManager)`
- [x] 排序逻辑：类型 -> 运行状态 -> 名称

### 数据模型一致性
- [x] SimpleAppInfo 包含图标属性
- [x] 序列化机制保持项目一致性（不使用 Parcelable）
- [x] 图标在序列化时不保存，需要重新加载

### UI 显示一致性
- [x] 应用图标显示方式与 AppsScreen 一致
- [x] 运行状态指示器显示
- [x] 应用名称和包名显示
- [x] 移除系统应用标签（已通过分组区分）
- [x] 系统应用和用户应用对齐方式一致

## 测试步骤

### 1. 编译测试
```bash
./gradlew assembleDebug
```

### 2. 功能测试
1. **应用列表加载**
   - 打开强制停止应用配置
   - 点击"选择要停止的应用"
   - 验证应用列表正确加载
   - 验证应用图标正确显示

2. **运行状态检测**
   - 确保有使用情况访问权限
   - 验证正在运行的应用显示"正在运行"标签
   - 验证运行中的应用排在前面

3. **应用分类**
   - 验证用户应用和系统应用正确分组
   - 验证系统应用不再显示"系统应用"标签
   - 验证系统应用和用户应用对齐方式一致

4. **搜索功能**
   - 测试应用名称搜索
   - 测试包名搜索
   - 验证搜索结果正确

5. **选择功能**
   - 测试单选模式
   - 测试多选模式
   - 验证选择结果正确传递

### 3. 性能测试
1. **缓存机制**
   - 验证运行状态检测使用缓存
   - 验证缓存5秒后自动刷新

2. **图标加载**
   - 验证图标加载不会导致 OOM
   - 验证图标加载失败时的降级处理

## 预期结果

### 成功标准
1. **编译成功**：项目能够正常编译，无语法错误
2. **功能正常**：应用选择功能正常工作，与之前行为一致
3. **显示一致**：应用列表显示与 AppsScreen 保持一致
4. **性能稳定**：运行状态检测性能良好，无明显延迟

### 可能的问题和解决方案

#### 1. 权限问题
**问题**：UsageStatsManager 需要使用情况访问权限
**解决**：在应用设置中授予使用情况访问权限

#### 2. 图标显示问题
**问题**：某些应用图标无法显示
**解决**：已实现降级机制，显示首字母占位符

#### 3. 性能问题
**问题**：应用列表加载缓慢
**解决**：已实现缓存机制，减少重复查询

## 后续维护

### 保持一致性
1. 如果 AppRepository 的逻辑发生变化，需要同步更新 AppSelectionScreen
2. 新增的应用属性需要评估是否需要在 SimpleAppInfo 中添加
3. 运行状态检测逻辑的优化需要在两处同步应用

### 代码重构建议
考虑将应用加载和运行状态检测逻辑提取为共享工具类，避免代码重复，但需要注意：
1. 保持现有的独立性要求
2. 不影响现有的序列化机制
3. 确保向后兼容性

package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import com.weinuo.quickcommands.model.SimpleAppInfo
import com.weinuo.quickcommands.ui.screens.AppSelectionMode
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.storage.UIStateStorageManager

/**
 * 应用选择独立Activity
 * 
 * 提供全屏应用选择界面，支持单选和多选模式
 * 使用ActivityResult API返回选择结果
 */
class AppSelectionActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_SELECTION_MODE = "selection_mode"
        private const val EXTRA_INITIAL_SELECTED_APPS = "initial_selected_apps"
        private const val EXTRA_FILTER_TYPE = "filter_type"
        private const val EXTRA_RESULT_KEY = "result_key"
        
        const val RESULT_SELECTED_APPS = "selected_apps"
        
        /**
         * 启动单选应用选择
         */
        fun startForSingleSelection(
            context: Context,
            filterType: String? = null,
            resultKey: String = "selected_apps"
        ) {
            val intent = Intent(context, AppSelectionActivity::class.java).apply {
                putExtra(EXTRA_SELECTION_MODE, AppSelectionMode.SINGLE.name)
                putExtra(EXTRA_FILTER_TYPE, filterType)
                putExtra(EXTRA_RESULT_KEY, resultKey)
            }
            context.startActivity(intent)
        }
        
        /**
         * 启动多选应用选择
         */
        fun startForMultiSelection(
            context: Context,
            selectedAppPackageNames: List<String> = emptyList(),
            filterType: String? = null,
            resultKey: String = "selected_apps"
        ) {
            val intent = Intent(context, AppSelectionActivity::class.java).apply {
                putExtra(EXTRA_SELECTION_MODE, AppSelectionMode.MULTI.name)
                putStringArrayListExtra(EXTRA_INITIAL_SELECTED_APPS, ArrayList(selectedAppPackageNames))
                putExtra(EXTRA_FILTER_TYPE, filterType)
                putExtra(EXTRA_RESULT_KEY, resultKey)
            }
            context.startActivity(intent)
        }
        
        /**
         * 使用ActivityResultLauncher启动单选应用选择
         */
        fun createSingleSelectionIntent(
            context: Context,
            filterType: String? = null,
            resultKey: String = "selected_apps"
        ): Intent {
            return Intent(context, AppSelectionActivity::class.java).apply {
                putExtra(EXTRA_SELECTION_MODE, AppSelectionMode.SINGLE.name)
                putExtra(EXTRA_FILTER_TYPE, filterType)
                putExtra(EXTRA_RESULT_KEY, resultKey)
            }
        }
        
        /**
         * 使用ActivityResultLauncher启动多选应用选择
         */
        fun createMultiSelectionIntent(
            context: Context,
            selectedAppPackageNames: List<String> = emptyList(),
            filterType: String? = null,
            resultKey: String = "selected_apps"
        ): Intent {
            return Intent(context, AppSelectionActivity::class.java).apply {
                putExtra(EXTRA_SELECTION_MODE, AppSelectionMode.MULTI.name)
                putStringArrayListExtra(EXTRA_INITIAL_SELECTED_APPS, ArrayList(selectedAppPackageNames))
                putExtra(EXTRA_FILTER_TYPE, filterType)
                putExtra(EXTRA_RESULT_KEY, resultKey)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传递的参数
        val selectionModeString = intent.getStringExtra(EXTRA_SELECTION_MODE) ?: AppSelectionMode.SINGLE.name
        val selectionMode = AppSelectionMode.valueOf(selectionModeString)
        val initialSelectedApps = intent.getStringArrayListExtra(EXTRA_INITIAL_SELECTED_APPS) ?: emptyList()
        val filterType = intent.getStringExtra(EXTRA_FILTER_TYPE)
        val resultKey = intent.getStringExtra(EXTRA_RESULT_KEY) ?: "selected_apps"
        
        setContent {
            QuickCommandsTheme {
                AppSelectionActivityContent(
                    selectionMode = selectionMode,
                    initialSelectedAppPackageNames = initialSelectedApps,
                    filterType = filterType,
                    onAppsSelected = { selectedApps ->
                        finishWithResult(selectedApps)
                    },
                    onFinish = { finish() }
                )
            }
        }
    }
    
    /**
     * 返回选择结果并结束Activity
     */
    private fun finishWithResult(selectedApps: List<SimpleAppInfo>) {
        val resultKey = intent.getStringExtra(EXTRA_RESULT_KEY) ?: "selected_apps"

        // 使用UIStateStorageManager存储结果
        val uiStateManager = UIStateStorageManager(this)
        uiStateManager.saveAppListState(resultKey, "selected_apps", selectedApps)

        val intent = Intent().apply {
            putExtra(RESULT_SELECTED_APPS, resultKey)
        }
        setResult(Activity.RESULT_OK, intent)
        finish()
    }
}

/**
 * 应用选择Activity的内容组件
 * 
 * 不依赖NavController的可复用组件
 */
@androidx.compose.runtime.Composable
fun AppSelectionActivityContent(
    selectionMode: AppSelectionMode,
    initialSelectedAppPackageNames: List<String> = emptyList(),
    filterType: String? = null,
    onAppsSelected: (List<SimpleAppInfo>) -> Unit,
    onFinish: () -> Unit
) {
    com.weinuo.quickcommands.ui.screens.AppSelectionScreen(
        selectionMode = selectionMode,
        initialSelectedAppPackageNames = initialSelectedAppPackageNames,
        filterType = filterType,
        onAppsSelected = onAppsSelected,
        onDismiss = onFinish
    )
}

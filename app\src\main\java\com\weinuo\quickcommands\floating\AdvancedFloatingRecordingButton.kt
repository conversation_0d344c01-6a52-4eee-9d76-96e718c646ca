package com.weinuo.quickcommands.floating

import android.content.Context
import android.graphics.Color
import android.graphics.PixelFormat
import android.view.*
import android.widget.*
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.AdvancedGestureType
import com.weinuo.quickcommands.utils.OverlayPermissionUtil

/**
 * 高级悬浮窗录制按钮
 *
 * 提供可视化的手势录制控制界面，包括：
 * - 带加号的主按钮
 * - 手势类型选择菜单
 * - 保存、清空、设置等功能按钮
 *
 * 与传统悬浮窗录制不同，此按钮专注于手势类型选择和录制控制，
 * 不监听用户的实际触摸操作。
 */
class AdvancedFloatingRecordingButton(
    private val context: Context,
    private val onAddGesture: (AdvancedGestureType, Float, Float) -> Unit,
    private val onAddSwipeGesture: (Float, Float, Float, Float) -> Unit,
    private val onSaveRecording: () -> Unit,
    private val onClearAll: () -> Unit,
    private val onShowSettings: () -> Unit
) {
    companion object {
        private const val TAG = "AdvancedFloatingRecordingButton"
        private const val BUTTON_SIZE_DP = 56
        private const val MENU_ITEM_WIDTH_DP = 120
        private const val MENU_ITEM_HEIGHT_DP = 48
    }

    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var buttonContainer: ViewGroup? = null
    private var gestureMenuContainer: ViewGroup? = null
    private var isMenuVisible = false
    private var buttonParams: WindowManager.LayoutParams? = null
    private var selectedGestureType: AdvancedGestureType? = null
    private var isWaitingForTouch = false
    private var swipeStartX: Float = 0f
    private var swipeStartY: Float = 0f
    private var isWaitingForSwipeEnd = false

    // 主按钮
    private var mainButton: ImageButton? = null

    // 手势类型菜单按钮
    private var tapButton: ImageButton? = null
    private var longPressButton: ImageButton? = null
    private var swipeButton: ImageButton? = null

    // 功能按钮
    private var saveButton: ImageButton? = null
    private var clearButton: ImageButton? = null
    private var settingsButton: ImageButton? = null

    /**
     * 显示悬浮按钮
     */
    fun show(): Boolean {
        if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
            return false
        }

        try {
            createMainButton()
            createGestureMenu()
            return true
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 隐藏悬浮按钮
     */
    fun hide() {
        try {
            hideGestureMenu()
            hideMainButton()
        } catch (e: Exception) {
            // 忽略异常
        }
    }

    /**
     * 创建主按钮
     */
    private fun createMainButton() {
        hideMainButton() // 先移除现有按钮

        // 创建按钮容器
        buttonContainer = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
        }

        // 创建主按钮（加号图标）
        mainButton = ImageButton(context).apply {
            setImageResource(R.drawable.ic_add_skyblue)
            background = ContextCompat.getDrawable(context, R.drawable.floating_button_background)
            scaleType = ImageView.ScaleType.CENTER
            setColorFilter(ContextCompat.getColor(context, android.R.color.white))

            // 确保按钮是完美的圆形
            val density = context.resources.displayMetrics.density
            val sizeInPx = (BUTTON_SIZE_DP * density).toInt()
            layoutParams = LinearLayout.LayoutParams(sizeInPx, sizeInPx)

            setOnClickListener {
                toggleGestureMenu()
            }
        }

        buttonContainer?.addView(mainButton)

        // 设置窗口参数
        val density = context.resources.displayMetrics.density
        val sizeInPx = (BUTTON_SIZE_DP * density).toInt()

        buttonParams = WindowManager.LayoutParams(
            sizeInPx,
            sizeInPx,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 100
            y = 200
        }

        // 添加拖拽功能
        addDragFunctionality(buttonContainer!!, buttonParams!!)

        // 显示按钮
        windowManager.addView(buttonContainer, buttonParams)
    }

    /**
     * 隐藏主按钮
     */
    private fun hideMainButton() {
        buttonContainer?.let { container ->
            try {
                windowManager.removeView(container)
            } catch (e: Exception) {
                // 忽略异常
            }
        }
        buttonContainer = null
        mainButton = null
    }

    /**
     * 创建手势类型选择菜单
     */
    private fun createGestureMenu() {
        // 创建菜单容器
        gestureMenuContainer = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            visibility = View.GONE
            background = ContextCompat.getDrawable(context, R.drawable.menu_background)
            setPadding(16, 16, 16, 16)
        }

        val density = context.resources.displayMetrics.density
        val itemWidthInPx = (MENU_ITEM_WIDTH_DP * density).toInt()
        val itemHeightInPx = (MENU_ITEM_HEIGHT_DP * density).toInt()

        // 创建手势类型按钮
        val gestureTypes = AdvancedGestureType.getAvailableTypes()
        gestureTypes.forEach { gestureType ->
            val button = createGestureTypeButton(gestureType, itemWidthInPx, itemHeightInPx)
            gestureMenuContainer?.addView(button)
        }

        // 添加分隔线
        val divider = View(context).apply {
            setBackgroundColor(Color.argb(100, 255, 255, 255))
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                (1 * density).toInt()
            ).apply {
                setMargins(0, 8, 0, 8)
            }
        }
        gestureMenuContainer?.addView(divider)

        // 添加功能按钮
        val saveButton = createFunctionButton("💾 保存录制", itemWidthInPx, itemHeightInPx) {
            onSaveRecording()
            hideGestureMenu()
        }
        gestureMenuContainer?.addView(saveButton)

        val clearButton = createFunctionButton("🗑️ 清空所有", itemWidthInPx, itemHeightInPx) {
            onClearAll()
            hideGestureMenu()
        }
        gestureMenuContainer?.addView(clearButton)

        // 设置菜单窗口参数
        val menuParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            // 菜单显示在主按钮右侧
            x = (buttonParams?.x ?: 100) + (BUTTON_SIZE_DP * context.resources.displayMetrics.density).toInt() + 20
            y = buttonParams?.y ?: 200
        }

        // 显示菜单（初始隐藏）
        windowManager.addView(gestureMenuContainer, menuParams)
    }

    /**
     * 创建功能按钮
     */
    private fun createFunctionButton(text: String, widthInPx: Int, heightInPx: Int, onClick: () -> Unit): LinearLayout {
        return LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
            background = ContextCompat.getDrawable(context, R.drawable.menu_item_background)
            setPadding(16, 12, 16, 12)

            layoutParams = LinearLayout.LayoutParams(widthInPx, heightInPx).apply {
                setMargins(0, 4, 0, 4)
            }

            // 创建文字标签
            val label = TextView(context).apply {
                this.text = text
                setTextColor(ContextCompat.getColor(context, android.R.color.white))
                textSize = 14f
                gravity = Gravity.CENTER
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
            }

            addView(label)

            setOnClickListener {
                onClick()
            }
        }
    }

    /**
     * 创建手势类型按钮
     */
    private fun createGestureTypeButton(gestureType: AdvancedGestureType, widthInPx: Int, heightInPx: Int): LinearLayout {
        return LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            background = ContextCompat.getDrawable(context, R.drawable.menu_item_background)
            setPadding(16, 12, 16, 12)

            layoutParams = LinearLayout.LayoutParams(widthInPx, heightInPx).apply {
                setMargins(0, 4, 0, 4)
            }

            // 创建图标
            val icon = ImageView(context).apply {
                val iconRes = when (gestureType) {
                    AdvancedGestureType.TAP -> R.drawable.ic_touch_app
                    AdvancedGestureType.LONG_PRESS -> R.drawable.ic_long_press
                    AdvancedGestureType.SWIPE -> R.drawable.ic_swipe
                    AdvancedGestureType.MULTI_TOUCH -> R.drawable.ic_touch_app
                }
                setImageResource(iconRes)
                setColorFilter(ContextCompat.getColor(context, android.R.color.white))
                layoutParams = LinearLayout.LayoutParams(
                    (24 * context.resources.displayMetrics.density).toInt(),
                    (24 * context.resources.displayMetrics.density).toInt()
                )
            }

            // 创建文字标签
            val label = TextView(context).apply {
                text = gestureType.displayName
                setTextColor(ContextCompat.getColor(context, android.R.color.white))
                textSize = 14f
                layoutParams = LinearLayout.LayoutParams(
                    0,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    1f
                ).apply {
                    setMargins(12, 0, 0, 0)
                }
            }

            addView(icon)
            addView(label)

            setOnClickListener {
                selectGestureType(gestureType)
                hideGestureMenu()
            }

            // 添加工具提示
            setOnLongClickListener {
                Toast.makeText(context, gestureType.description, Toast.LENGTH_SHORT).show()
                true
            }
        }
    }

    /**
     * 切换手势菜单显示状态
     */
    private fun toggleGestureMenu() {
        if (isMenuVisible) {
            hideGestureMenu()
        } else {
            showGestureMenu()
        }
    }

    /**
     * 显示手势菜单
     */
    private fun showGestureMenu() {
        gestureMenuContainer?.visibility = View.VISIBLE
        isMenuVisible = true

        // 更新主按钮图标为关闭图标
        mainButton?.setImageResource(R.drawable.ic_close)
    }

    /**
     * 隐藏手势菜单
     */
    private fun hideGestureMenu() {
        gestureMenuContainer?.visibility = View.GONE
        isMenuVisible = false

        // 恢复主按钮图标为加号
        mainButton?.setImageResource(R.drawable.ic_add_skyblue)
    }



    /**
     * 选择手势类型
     */
    private fun selectGestureType(gestureType: AdvancedGestureType) {
        selectedGestureType = gestureType
        isWaitingForTouch = true

        // 更新主按钮图标，表示等待用户点击
        mainButton?.setImageResource(R.drawable.ic_touch_app)

        val message = when (gestureType) {
            AdvancedGestureType.SWIPE -> "请点击屏幕设置滑动起始点"
            else -> "请点击屏幕添加${gestureType.displayName}操作"
        }
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()

        // 创建全屏透明覆盖层来捕获触摸
        createTouchCaptureOverlay()
    }

    /**
     * 创建触摸捕获覆盖层
     */
    private fun createTouchCaptureOverlay() {
        val overlay = View(context)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )

        overlay.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN && isWaitingForTouch && selectedGestureType != null) {

                if (selectedGestureType == AdvancedGestureType.SWIPE) {
                    if (!isWaitingForSwipeEnd) {
                        // 第一次点击，设置起始点
                        // 修正坐标：减去状态栏高度
                        swipeStartX = event.rawX
                        swipeStartY = event.rawY - getStatusBarHeight()
                        isWaitingForSwipeEnd = true

                        Toast.makeText(context, "起始点已设置，请点击终点位置", Toast.LENGTH_SHORT).show()
                        return@setOnTouchListener true
                    } else {
                        // 第二次点击，设置终点并完成滑动
                        // 修正坐标：减去状态栏高度
                        val correctedEndY = event.rawY - getStatusBarHeight()
                        addSwipeGesture(swipeStartX, swipeStartY, event.rawX, correctedEndY)

                        // 重置状态
                        resetTouchState()

                        // 移除覆盖层
                        try {
                            windowManager.removeView(overlay)
                        } catch (e: Exception) {
                            // 忽略异常
                        }

                        return@setOnTouchListener true
                    }
                } else {
                    // 非滑动手势，直接添加
                    // 修正坐标：减去状态栏高度
                    val correctedY = event.rawY - getStatusBarHeight()
                    onAddGesture(selectedGestureType!!, event.rawX, correctedY)

                    // 重置状态
                    resetTouchState()

                    // 移除覆盖层
                    try {
                        windowManager.removeView(overlay)
                    } catch (e: Exception) {
                        // 忽略异常
                    }

                    return@setOnTouchListener true
                }
            }
            false
        }

        try {
            windowManager.addView(overlay, params)
        } catch (e: Exception) {
            // 如果添加覆盖层失败，回退到屏幕中心
            selectedGestureType?.let { gestureType ->
                val screenWidth = context.resources.displayMetrics.widthPixels
                val screenHeight = context.resources.displayMetrics.heightPixels
                onAddGesture(gestureType, screenWidth / 2f, screenHeight / 2f)
            }
            resetTouchState()
        }
    }

    /**
     * 重置触摸状态
     */
    private fun resetTouchState() {
        isWaitingForTouch = false
        isWaitingForSwipeEnd = false
        selectedGestureType = null
        swipeStartX = 0f
        swipeStartY = 0f
        mainButton?.setImageResource(R.drawable.ic_add_skyblue)
    }

    /**
     * 添加滑动手势
     */
    private fun addSwipeGesture(startX: Float, startY: Float, endX: Float, endY: Float) {
        onAddSwipeGesture(startX, startY, endX, endY)
        Toast.makeText(context, "滑动手势已添加：从(${startX.toInt()}, ${startY.toInt()})到(${endX.toInt()}, ${endY.toInt()})", Toast.LENGTH_SHORT).show()
    }

    /**
     * 获取状态栏高度
     */
    private fun getStatusBarHeight(): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    /**
     * 添加拖拽功能
     */
    private fun addDragFunctionality(view: View, params: WindowManager.LayoutParams) {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f

        view.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    params.x = initialX + (event.rawX - initialTouchX).toInt()
                    params.y = initialY + (event.rawY - initialTouchY).toInt()
                    windowManager.updateViewLayout(view, params)
                    true
                }
                else -> false
            }
        }
    }
}

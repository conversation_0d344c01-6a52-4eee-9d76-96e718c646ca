package com.weinuo.quickcommands.ui.configuration

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp

/**
 * 单点点击配置组件
 */
@Composable
fun SingleClickConfig(
    clickX: Float,
    clickY: Float,
    onClickXChange: (Float) -> Unit,
    onClickYChange: (Float) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "点击位置（相对坐标 0.0-1.0）",
            style = MaterialTheme.typography.bodySmall
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = String.format("%.2f", clickX),
                onValueChange = { value ->
                    value.toFloatOrNull()?.let { x ->
                        onClickXChange(x.coerceIn(0f, 1f))
                    }
                },
                label = { Text("X坐标") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
            
            OutlinedTextField(
                value = String.format("%.2f", clickY),
                onValueChange = { value ->
                    value.toFloatOrNull()?.let { y ->
                        onClickYChange(y.coerceIn(0f, 1f))
                    }
                },
                label = { Text("Y坐标") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
        }
        
        Text(
            text = "0.5, 0.5 表示屏幕中心；0.0, 0.0 表示左上角；1.0, 1.0 表示右下角",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 连续点击配置组件
 */
@Composable
fun ContinuousClickConfig(
    clickX: Float,
    clickY: Float,
    clickCount: Int,
    clickInterval: Long,
    onClickXChange: (Float) -> Unit,
    onClickYChange: (Float) -> Unit,
    onClickCountChange: (Int) -> Unit,
    onClickIntervalChange: (Long) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 复用单点点击的位置配置
        SingleClickConfig(
            clickX = clickX,
            clickY = clickY,
            onClickXChange = onClickXChange,
            onClickYChange = onClickYChange
        )
        
        // 点击次数
        OutlinedTextField(
            value = clickCount.toString(),
            onValueChange = { value ->
                value.toIntOrNull()?.let { count ->
                    onClickCountChange(count.coerceIn(1, 100))
                }
            },
            label = { Text("点击次数 (1-100)") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            singleLine = true
        )
        
        // 点击间隔
        OutlinedTextField(
            value = clickInterval.toString(),
            onValueChange = { value ->
                value.toLongOrNull()?.let { interval ->
                    onClickIntervalChange(interval.coerceIn(50L, 5000L))
                }
            },
            label = { Text("点击间隔 (50-5000毫秒)") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            singleLine = true
        )
    }
}

/**
 * 滑动操作配置组件
 */
@Composable
fun SwipeOperationConfig(
    swipeStartX: Float,
    swipeStartY: Float,
    swipeEndX: Float,
    swipeEndY: Float,
    swipeDuration: Long,
    onSwipeStartXChange: (Float) -> Unit,
    onSwipeStartYChange: (Float) -> Unit,
    onSwipeEndXChange: (Float) -> Unit,
    onSwipeEndYChange: (Float) -> Unit,
    onSwipeDurationChange: (Long) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "起始位置",
            style = MaterialTheme.typography.bodySmall
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = String.format("%.2f", swipeStartX),
                onValueChange = { value ->
                    value.toFloatOrNull()?.let { x ->
                        onSwipeStartXChange(x.coerceIn(0f, 1f))
                    }
                },
                label = { Text("起始X") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
            
            OutlinedTextField(
                value = String.format("%.2f", swipeStartY),
                onValueChange = { value ->
                    value.toFloatOrNull()?.let { y ->
                        onSwipeStartYChange(y.coerceIn(0f, 1f))
                    }
                },
                label = { Text("起始Y") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
        }
        
        Text(
            text = "结束位置",
            style = MaterialTheme.typography.bodySmall
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = String.format("%.2f", swipeEndX),
                onValueChange = { value ->
                    value.toFloatOrNull()?.let { x ->
                        onSwipeEndXChange(x.coerceIn(0f, 1f))
                    }
                },
                label = { Text("结束X") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
            
            OutlinedTextField(
                value = String.format("%.2f", swipeEndY),
                onValueChange = { value ->
                    value.toFloatOrNull()?.let { y ->
                        onSwipeEndYChange(y.coerceIn(0f, 1f))
                    }
                },
                label = { Text("结束Y") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true
            )
        }
        
        // 滑动持续时间
        OutlinedTextField(
            value = swipeDuration.toString(),
            onValueChange = { value ->
                value.toLongOrNull()?.let { duration ->
                    onSwipeDurationChange(duration.coerceIn(100L, 3000L))
                }
            },
            label = { Text("滑动持续时间 (100-3000毫秒)") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            singleLine = true
        )
        
        Text(
            text = "默认设置为从屏幕底部向上滑动，您可以根据需要调整坐标",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 长按操作配置组件
 */
@Composable
fun LongPressConfig(
    clickX: Float,
    clickY: Float,
    longPressDuration: Long,
    onClickXChange: (Float) -> Unit,
    onClickYChange: (Float) -> Unit,
    onLongPressDurationChange: (Long) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 复用单点点击的位置配置
        SingleClickConfig(
            clickX = clickX,
            clickY = clickY,
            onClickXChange = onClickXChange,
            onClickYChange = onClickYChange
        )
        
        // 长按持续时间
        OutlinedTextField(
            value = longPressDuration.toString(),
            onValueChange = { value ->
                value.toLongOrNull()?.let { duration ->
                    onLongPressDurationChange(duration.coerceIn(500L, 10000L))
                }
            },
            label = { Text("长按持续时间 (500-10000毫秒)") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            singleLine = true
        )
        
        Text(
            text = "长按时间建议设置为1000毫秒以上，确保能够触发长按事件",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 回放配置组件
 */
@Composable
fun PlaybackConfig(
    playbackLoopCount: Int,
    playbackSpeed: Float,
    delayBetweenLoops: Long,
    onPlaybackLoopCountChange: (Int) -> Unit,
    onPlaybackSpeedChange: (Float) -> Unit,
    onDelayBetweenLoopsChange: (Long) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "回放配置",
            style = MaterialTheme.typography.titleSmall
        )
        
        // 循环次数
        OutlinedTextField(
            value = if (playbackLoopCount == 0) "" else playbackLoopCount.toString(),
            onValueChange = { value ->
                when {
                    value.isEmpty() -> onPlaybackLoopCountChange(0)
                    value == "0" -> onPlaybackLoopCountChange(0)
                    else -> value.toIntOrNull()?.let { count ->
                        onPlaybackLoopCountChange(count.coerceAtLeast(1))
                    }
                }
            },
            label = { Text("循环次数") },
            placeholder = { Text("0表示无限循环") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            singleLine = true
        )
        
        // 回放速度
        Text(
            text = "回放速度: ${String.format("%.1f", playbackSpeed)}x",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Slider(
            value = playbackSpeed,
            onValueChange = onPlaybackSpeedChange,
            valueRange = 0.1f..5.0f,
            steps = 48,
            modifier = Modifier.fillMaxWidth()
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text("0.1x", style = MaterialTheme.typography.bodySmall)
            Text("1.0x", style = MaterialTheme.typography.bodySmall)
            Text("5.0x", style = MaterialTheme.typography.bodySmall)
        }
        
        // 循环间延时
        OutlinedTextField(
            value = delayBetweenLoops.toString(),
            onValueChange = { value ->
                value.toLongOrNull()?.let { delay ->
                    onDelayBetweenLoopsChange(delay.coerceIn(0L, 10000L))
                }
            },
            label = { Text("循环间延时 (0-10000毫秒)") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            singleLine = true
        )
        
        Text(
            text = "循环间延时可以避免操作过于频繁，建议设置为1000毫秒以上",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

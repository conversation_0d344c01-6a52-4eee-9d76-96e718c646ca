package com.weinuo.quickcommands.utils

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.core.graphics.drawable.toBitmap

/**
 * 图像工具类，用于处理图像相关操作
 */
object ImageUtils {

    /**
     * 安全地将Drawable转换为Bitmap，限制最大尺寸
     * 
     * @param drawable 要转换的Drawable
     * @param maxWidth 最大宽度（默认为128像素）
     * @param maxHeight 最大高度（默认为128像素）
     * @param config Bitmap配置（默认为ARGB_8888）
     * @return 转换后的Bitmap
     */
    fun safeDrawableToBitmap(
        drawable: Drawable,
        maxWidth: Int = 128,
        maxHeight: Int = 128,
        config: Bitmap.Config = Bitmap.Config.ARGB_8888
    ): Bitmap {
        // 获取drawable的原始尺寸
        val width = if (drawable.intrinsicWidth > 0) drawable.intrinsicWidth else maxWidth
        val height = if (drawable.intrinsicHeight > 0) drawable.intrinsicHeight else maxHeight
        
        // 计算缩放后的尺寸，确保不超过最大限制
        val scaledWidth = width.coerceAtMost(maxWidth)
        val scaledHeight = height.coerceAtMost(maxHeight)
        
        // 使用安全的尺寸创建Bitmap
        return drawable.toBitmap(
            width = scaledWidth,
            height = scaledHeight,
            config = config
        )
    }
}

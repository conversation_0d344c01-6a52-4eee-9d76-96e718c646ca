package com.weinuo.quickcommands.ui.components.layered

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.config.SearchTextFieldConfig
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext

/**
 * 分层设计风格的搜索框组件
 *
 * 海洋蓝主题专用的搜索框实现
 * 使用与old文件夹中UnifiedSearchTextField完全相同的样式
 * 特点：
 * - 固定28dp圆角半径
 * - 统一的颜色主题
 * - 搜索图标和清除按钮
 * - 键盘搜索操作支持
 * - 与原始UnifiedSearchTextField保持完全一致
 */
@Composable
fun LayeredSearchTextField(
    config: SearchTextFieldConfig
) {
    val focusManager = LocalFocusManager.current

    // 使用与old文件夹中UnifiedSearchTextField完全相同的样式
    // 固定28dp圆角，不使用主题配置覆盖

    TextField(
        value = config.searchQuery,
        onValueChange = config.onSearchQueryChange,
        modifier = config.modifier,
        enabled = config.enabled,
        placeholder = { Text(config.placeholder) },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "搜索",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        trailingIcon = if (config.searchQuery.isNotEmpty()) {
            {
                IconButton(onClick = config.onClearSearch) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清除",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else null,
        singleLine = true,
        shape = RoundedCornerShape(28.dp),
        keyboardOptions = KeyboardOptions(
            imeAction = ImeAction.Search
        ),
        keyboardActions = KeyboardActions(
            onSearch = {
                focusManager.clearFocus()
            }
        ),
        colors = TextFieldDefaults.colors(
            focusedContainerColor = MaterialTheme.colorScheme.surfaceContainerHigh,
            unfocusedContainerColor = MaterialTheme.colorScheme.surfaceContainer,
            disabledContainerColor = MaterialTheme.colorScheme.surfaceContainer,
            focusedIndicatorColor = androidx.compose.ui.graphics.Color.Transparent,
            unfocusedIndicatorColor = androidx.compose.ui.graphics.Color.Transparent,
            disabledIndicatorColor = androidx.compose.ui.graphics.Color.Transparent,
        )
    )
}

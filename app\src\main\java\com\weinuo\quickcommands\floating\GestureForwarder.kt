package com.weinuo.quickcommands.floating

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.weinuo.quickcommands.model.TouchEvent
import com.weinuo.quickcommands.model.TouchEventType
import com.weinuo.quickcommands.service.AutoClickerAccessibilityService
import kotlinx.coroutines.delay
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 手势转发器
 *
 * 负责将捕获的手势转发到下层应用，通过AccessibilityService实现。
 * 确保用户在透明覆盖层上的操作能够正常传递给目标应用。
 */
class GestureForwarder(
    private val accessibilityService: AutoClickerAccessibilityService
) {
    companion object {
        private const val TAG = "GestureForwarder"
    }

    /**
     * 转发手势到下层应用
     */
    @RequiresApi(Build.VERSION_CODES.N)
    suspend fun forwardGesture(touchEvent: TouchEvent) {
        try {
            val gestureDescription = createGestureDescription(touchEvent)
            if (gestureDescription != null) {
                executeGesture(gestureDescription)
            }
        } catch (e: Exception) {
            Log.e(TAG, "转发手势失败", e)
        }
    }

    /**
     * 创建手势描述
     */
    @RequiresApi(Build.VERSION_CODES.N)
    private fun createGestureDescription(event: TouchEvent): GestureDescription? {
        // 获取屏幕尺寸用于坐标转换
        val screenWidth = accessibilityService.resources.displayMetrics.widthPixels
        val screenHeight = accessibilityService.resources.displayMetrics.heightPixels

        val builder = GestureDescription.Builder()

        when (event.type) {
            TouchEventType.TAP -> {
                // 单击 - 按照Android官方实现，需要有实际路径长度
                val path = Path().apply {
                    val x = event.position.startX * screenWidth
                    val y = event.position.startY * screenHeight
                    moveTo(x, y)
                    // 添加微小的移动以创建有效路径，这是Android官方推荐的做法
                    lineTo(x + 1, y)
                }
                // 使用系统默认的点击超时时间
                val tapTimeout = android.view.ViewConfiguration.getTapTimeout().toLong()
                val stroke = GestureDescription.StrokeDescription(path, 0, tapTimeout)
                builder.addStroke(stroke)
            }

            TouchEventType.LONG_PRESS -> {
                // 长按
                val path = Path().apply {
                    val x = event.position.startX * screenWidth
                    val y = event.position.startY * screenHeight
                    moveTo(x, y)
                }
                val duration = maxOf(event.duration, 500L) // 最少500ms
                val stroke = GestureDescription.StrokeDescription(path, 0, duration)
                builder.addStroke(stroke)
            }

            TouchEventType.SWIPE -> {
                // 滑动
                val path = Path().apply {
                    val startX = event.position.startX * screenWidth
                    val startY = event.position.startY * screenHeight
                    val endX = event.position.endX * screenWidth
                    val endY = event.position.endY * screenHeight
                    moveTo(startX, startY)
                    lineTo(endX, endY)
                }
                val duration = maxOf(event.duration, 300L) // 最少300ms
                val stroke = GestureDescription.StrokeDescription(path, 0, duration)
                builder.addStroke(stroke)
            }

            TouchEventType.MULTI_TAP -> {
                // 多点触控
                val tapTimeout = android.view.ViewConfiguration.getTapTimeout().toLong()
                repeat(event.position.touchPoints) {
                    val path = Path().apply {
                        val x = event.position.startX * screenWidth
                        val y = event.position.startY * screenHeight
                        moveTo(x, y)
                        // 添加微小的移动以创建有效路径
                        lineTo(x + 1, y)
                    }
                    val stroke = GestureDescription.StrokeDescription(path, 0, tapTimeout)
                    builder.addStroke(stroke)
                }
            }
        }

        return builder.build()
    }

    /**
     * 执行手势
     */
    @RequiresApi(Build.VERSION_CODES.N)
    private suspend fun executeGesture(gestureDescription: GestureDescription) {
        try {
            // 使用回调等待手势完成
            val success = suspendCoroutine<Boolean> { continuation ->
                val callback = object : AccessibilityService.GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        continuation.resume(true)
                    }

                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        continuation.resume(false)
                    }
                }

                // 执行手势
                val result = accessibilityService.dispatchGesture(gestureDescription, callback, null)
                if (!result) {
                    continuation.resume(false)
                }
            }

            if (!success) {
                Log.w(TAG, "手势执行失败")
            }

            // 添加小延时确保手势完成
            delay(50)
        } catch (e: Exception) {
            Log.e(TAG, "执行手势时发生错误", e)
        }
    }
}



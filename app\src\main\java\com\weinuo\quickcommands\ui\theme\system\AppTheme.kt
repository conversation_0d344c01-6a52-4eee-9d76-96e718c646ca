package com.weinuo.quickcommands.ui.theme.system

import com.weinuo.quickcommands.ui.theme.oceanblue.OceanBlueThemeProvider
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueThemeProvider

/**
 * 应用主题枚举
 *
 * 定义应用中所有可用的主题
 * 每个主题都有唯一的ID、显示名称、设计方法和主题提供者
 */
enum class AppTheme(
    /**
     * 主题的唯一标识符
     * 用于持久化存储和主题识别
     */
    val id: String,

    /**
     * 主题的显示名称
     * 在用户界面中显示给用户
     */
    val displayName: String,

    /**
     * 主题采用的设计方法
     * 决定了主题的整体视觉风格
     */
    val designApproach: DesignApproach,

    /**
     * 主题提供者
     * 负责提供主题的具体实现
     */
    val themeProvider: ThemeProvider
) {
    /**
     * 海洋蓝主题
     * 
     * 特点：
     * - 分层设计风格
     * - 使用阴影表现层次
     * - 清晰的组件边界
     * - 适合复杂信息展示
     */
    OCEAN_BLUE(
        id = "ocean_blue",
        displayName = "海洋蓝",
        designApproach = DesignApproach.LAYERED_DESIGN,
        themeProvider = OceanBlueThemeProvider()
    ),

    /**
     * 天空蓝主题
     * 
     * 特点：
     * - 整合设计风格
     * - 支持模糊效果
     * - 统一的视觉体验
     * - 现代化的扁平设计
     */
    SKY_BLUE(
        id = "sky_blue",
        displayName = "天空蓝",
        designApproach = DesignApproach.INTEGRATED_DESIGN,
        themeProvider = SkyBlueThemeProvider()
    );

    /**
     * 获取主题的详细描述
     */
    val description: String
        get() = when (this) {
            OCEAN_BLUE -> "分层设计风格，清晰的层次结构和组件边界"
            SKY_BLUE -> "整合设计风格，支持模糊效果的现代化体验"
        }

    /**
     * 获取主题的特性列表
     */
    val features: List<String>
        get() = when (this) {
            OCEAN_BLUE -> listOf("分层设计", "阴影效果", "清晰边界")
            SKY_BLUE -> listOf("整合设计", "模糊效果", "现代扁平")
        }

    /**
     * 检查主题是否支持特定功能
     */
    fun supportsFeature(feature: ThemeFeature): Boolean {
        return themeProvider.supportsFeature(feature)
    }

    /**
     * 获取主题版本
     */
    val version: String
        get() = themeProvider.getVersion()

    /**
     * 检查主题是否支持模糊效果
     */
    val supportsBlur: Boolean
        get() = designApproach.supportsBlur

    /**
     * 检查主题是否支持阴影效果
     */
    val supportsShadows: Boolean
        get() = designApproach.supportsShadows

    /**
     * 检查主题是否支持透明度
     */
    val supportsTransparency: Boolean
        get() = designApproach.supportsTransparency

    companion object {
        /**
         * 根据ID查找主题
         */
        fun findById(id: String): AppTheme? {
            return values().find { it.id == id }
        }

        /**
         * 根据显示名称查找主题
         */
        fun findByDisplayName(displayName: String): AppTheme? {
            return values().find { it.displayName == displayName }
        }

        /**
         * 获取默认主题
         */
        fun getDefault(): AppTheme {
            return OCEAN_BLUE
        }

        /**
         * 获取所有可用主题的ID列表
         */
        fun getAllIds(): List<String> {
            return values().map { it.id }
        }

        /**
         * 获取所有可用主题的显示名称列表
         */
        fun getAllDisplayNames(): List<String> {
            return values().map { it.displayName }
        }

        /**
         * 根据设计方法筛选主题
         */
        fun filterByDesignApproach(approach: DesignApproach): List<AppTheme> {
            return values().filter { it.designApproach == approach }
        }

        /**
         * 获取支持特定功能的主题列表
         */
        fun filterByFeature(feature: ThemeFeature): List<AppTheme> {
            return values().filter { it.supportsFeature(feature) }
        }

        /**
         * 验证主题ID的有效性
         */
        fun isValidId(id: String): Boolean {
            return findById(id) != null
        }

        /**
         * 获取主题的总数
         */
        fun getCount(): Int {
            return values().size
        }
    }
}

package com.weinuo.quickcommands.ui.components.layered

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.NavController
import com.weinuo.quickcommands.ui.theme.config.BottomNavigationConfig

/**
 * 分层设计的主布局组件
 *
 * 特点：
 * - 传统的Material Design布局，导航栏和内容分离
 * - 内容区域不延伸到导航栏下方
 * - 使用标准的Scaffold布局
 * - 保持与old项目一致的简洁分离风格
 * - 正确处理状态栏和导航栏的WindowInsets
 * - 统一处理TopAppBar的布局策略
 */
@Composable
fun LayeredMainLayout(
    navController: NavController,
    bottomNavConfig: BottomNavigationConfig,
    content: @Composable (PaddingValues) -> Unit
) {
    Scaffold(
        bottomBar = {
            LayeredBottomNavigation(
                config = bottomNavConfig
            )
        },
        // 设置为空的WindowInsets，让内容可以延伸到系统栏区域，实现沉浸式状态栏
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        // 内容区域延伸到状态栏区域，实现沉浸式状态栏
        // 只使用Scaffold提供的padding（底部导航栏和顶部应用栏），不添加状态栏padding
        content(paddingValues)
    }
}

package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 媒体任务存储适配器
 *
 * 负责MediaTask的原生数据类型存储和重建。
 * 将复杂的媒体任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 多媒体控制相关：multimediaControlType、mediaButtonType、audioButtonType、playerControlType
 * - 播放声音相关：soundPlayType、selectedFilePath、selectedFileName、selectedRingtoneUri、selectedRingtoneName、audioStreamType、waitForCompletion
 * - 录音相关：recordingDurationType、recordingMinutes、recordingSeconds、recordingSource、recordingFormat、recordingWaitForCompletion、recordingFolderPath、recordingFileName
 *
 * 存储格式示例：
 * task_{id}_type = "media"
 * task_{id}_operation = "MULTIMEDIA_CONTROL"
 * task_{id}_multimedia_control_type = "SIMULATE_MEDIA_BUTTON"
 * task_{id}_media_button_type = "PLAY_PAUSE"
 * task_{id}_audio_stream_type = "MEDIA_MUSIC"
 * task_{id}_wait_for_completion = true
 *
 * @param storageManager 原生类型存储管理器
 */
class MediaTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<MediaTask>(storageManager) {

    companion object {
        private const val TAG = "MediaTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"

        // 多媒体控制字段
        private const val FIELD_MULTIMEDIA_CONTROL_TYPE = "multimedia_control_type"
        private const val FIELD_MEDIA_BUTTON_TYPE = "media_button_type"
        private const val FIELD_AUDIO_BUTTON_TYPE = "audio_button_type"
        private const val FIELD_PLAYER_CONTROL_TYPE = "player_control_type"

        // 播放声音字段
        private const val FIELD_SOUND_PLAY_TYPE = "sound_play_type"
        private const val FIELD_SELECTED_FILE_PATH = "selected_file_path"
        private const val FIELD_SELECTED_FILE_NAME = "selected_file_name"
        private const val FIELD_SELECTED_RINGTONE_URI = "selected_ringtone_uri"
        private const val FIELD_SELECTED_RINGTONE_NAME = "selected_ringtone_name"
        private const val FIELD_AUDIO_STREAM_TYPE = "audio_stream_type"
        private const val FIELD_WAIT_FOR_COMPLETION = "wait_for_completion"

        // 录音字段
        private const val FIELD_RECORDING_DURATION_TYPE = "recording_duration_type"
        private const val FIELD_RECORDING_MINUTES = "recording_minutes"
        private const val FIELD_RECORDING_SECONDS = "recording_seconds"
        private const val FIELD_RECORDING_SOURCE = "recording_source"
        private const val FIELD_RECORDING_FORMAT = "recording_format"
        private const val FIELD_RECORDING_WAIT_FOR_COMPLETION = "recording_wait_for_completion"
        private const val FIELD_RECORDING_FOLDER_PATH = "recording_folder_path"
        private const val FIELD_RECORDING_FILE_NAME = "recording_file_name"
    }

    override fun getTaskType() = "media"

    /**
     * 保存媒体任务
     * 将MediaTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的媒体任务
     * @return 操作是否成功
     */
    override fun save(task: MediaTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存媒体任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存媒体任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),

                // 多媒体控制字段
                saveEnum(generateKey(task.id, FIELD_MULTIMEDIA_CONTROL_TYPE), task.multimediaControlType),
                saveEnum(generateKey(task.id, FIELD_MEDIA_BUTTON_TYPE), task.mediaButtonType),
                saveEnum(generateKey(task.id, FIELD_AUDIO_BUTTON_TYPE), task.audioButtonType),
                saveEnum(generateKey(task.id, FIELD_PLAYER_CONTROL_TYPE), task.playerControlType),

                // 播放声音字段
                saveEnum(generateKey(task.id, FIELD_SOUND_PLAY_TYPE), task.soundPlayType),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_FILE_PATH),
                    task.selectedFilePath
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_FILE_NAME),
                    task.selectedFileName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_URI),
                    task.selectedRingtoneUri
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_NAME),
                    task.selectedRingtoneName
                ),
                saveEnum(generateKey(task.id, FIELD_AUDIO_STREAM_TYPE), task.audioStreamType),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_WAIT_FOR_COMPLETION),
                    task.waitForCompletion
                ),

                // 录音字段
                saveEnum(generateKey(task.id, FIELD_RECORDING_DURATION_TYPE), task.recordingDurationType),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDING_MINUTES),
                    task.recordingMinutes
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDING_SECONDS),
                    task.recordingSeconds
                ),
                saveEnum(generateKey(task.id, FIELD_RECORDING_SOURCE), task.recordingSource),
                saveEnum(generateKey(task.id, FIELD_RECORDING_FORMAT), task.recordingFormat),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDING_WAIT_FOR_COMPLETION),
                    task.recordingWaitForCompletion
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDING_FOLDER_PATH),
                    task.recordingFolderPath
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDING_FILE_NAME),
                    task.recordingFileName
                )
            ))

            // 执行批量存储操作
            val success = storageManager.executeBatch(operations)

            if (success) {
                logSaveSuccess(task.id)
            } else {
                logSaveError(task.id, "Failed to execute batch operations")
            }

            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception: ${e.message}")
            Log.e(TAG, "Exception while saving MediaTask: ${task.id}", e)
            false
        }
    }

    /**
     * 加载媒体任务
     * 从原生数据类型重建MediaTask对象
     *
     * @param taskId 任务ID
     * @return 加载的媒体任务，失败时返回null
     */
    override fun load(taskId: String): MediaTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载媒体任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "媒体任务不存在: $taskId")
                return null
            }

            MediaTask(
                id = taskId,
                operation = loadEnum(
                    generateKey(taskId, FIELD_OPERATION),
                    MediaOperation::class.java,
                    MediaOperation.MULTIMEDIA_CONTROL
                ),

                // 多媒体控制字段
                multimediaControlType = loadEnum(
                    generateKey(taskId, FIELD_MULTIMEDIA_CONTROL_TYPE),
                    MultimediaControlType::class.java,
                    MultimediaControlType.SIMULATE_MEDIA_BUTTON
                ),
                mediaButtonType = loadEnum(
                    generateKey(taskId, FIELD_MEDIA_BUTTON_TYPE),
                    MediaButtonType::class.java,
                    MediaButtonType.PLAY_PAUSE
                ),
                audioButtonType = loadEnum(
                    generateKey(taskId, FIELD_AUDIO_BUTTON_TYPE),
                    AudioButtonType::class.java,
                    AudioButtonType.VOLUME_UP
                ),
                playerControlType = loadEnum(
                    generateKey(taskId, FIELD_PLAYER_CONTROL_TYPE),
                    PlayerControlType::class.java,
                    PlayerControlType.PLAY
                ),

                // 播放声音字段
                soundPlayType = loadEnum(
                    generateKey(taskId, FIELD_SOUND_PLAY_TYPE),
                    SoundPlayType::class.java,
                    SoundPlayType.SELECT_RINGTONE
                ),
                selectedFilePath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_FILE_PATH)
                ),
                selectedFileName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_FILE_NAME)
                ),
                selectedRingtoneUri = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_URI)
                ),
                selectedRingtoneName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_NAME)
                ),
                audioStreamType = loadEnum(
                    generateKey(taskId, FIELD_AUDIO_STREAM_TYPE),
                    AudioStreamType::class.java,
                    AudioStreamType.MEDIA_MUSIC
                ),
                waitForCompletion = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_WAIT_FOR_COMPLETION),
                    false
                ),

                // 录音字段
                recordingDurationType = loadEnum(
                    generateKey(taskId, FIELD_RECORDING_DURATION_TYPE),
                    RecordingDurationType::class.java,
                    RecordingDurationType.CUSTOM_TIME
                ),
                recordingMinutes = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDING_MINUTES),
                    0
                ),
                recordingSeconds = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDING_SECONDS),
                    30
                ),
                recordingSource = loadEnum(
                    generateKey(taskId, FIELD_RECORDING_SOURCE),
                    RecordingSource::class.java,
                    RecordingSource.STANDARD_MIC
                ),
                recordingFormat = loadEnum(
                    generateKey(taskId, FIELD_RECORDING_FORMAT),
                    RecordingFormat::class.java,
                    RecordingFormat.THREE_GPP
                ),
                recordingWaitForCompletion = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDING_WAIT_FOR_COMPLETION),
                    false
                ),
                recordingFolderPath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDING_FOLDER_PATH)
                ),
                recordingFileName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDING_FILE_NAME)
                )
            ).also {
                Log.d(TAG, "媒体任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception: ${e.message}")
            Log.e(TAG, "Exception while loading MediaTask: $taskId", e)
            null
        }
    }
}

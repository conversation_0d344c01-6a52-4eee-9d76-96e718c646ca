package com.weinuo.quickcommands.smartreminder

import android.util.Log

/**
 * 分享网址检测器
 *
 * 检测剪贴板中的网址链接，用于分享网址提醒功能。
 * 当用户复制网址时，自动识别并建议分享。
 *
 * 功能特点：
 * - 智能识别各种网址格式
 * - 支持HTTP和HTTPS协议
 * - 过滤掉特定应用的链接（避免与应用链接提醒冲突）
 * - 支持自定义URL模式配置
 *
 * 设计原则：
 * - 遵循现有架构模式
 * - 与应用链接提醒保持一致的检测逻辑
 * - 最小化误判率
 * - 高性能检测
 *
 * <AUTHOR>
 * @since 1.0.0
 */
object ShareUrlDetector {

    private const val TAG = "ShareUrlDetector"

    /**
     * 检测结果数据类
     */
    data class DetectionResult(
        val isUrl: Boolean,
        val url: String? = null,
        val domain: String? = null,
        val originalText: String? = null
    )

    /**
     * 需要排除的应用域名列表（避免与应用链接提醒冲突）
     */
    private val excludedDomains = setOf(
        // 抖音相关
        "douyin.com", "iesdouyin.com", "amemv.com",
        // 快手相关
        "kuaishou.com", "gifshow.com", "kwai.com",
        // 小红书相关
        "xiaohongshu.com", "xhslink.com",
        // 百度网盘相关
        "pan.baidu.com", "yun.baidu.com",
        // 夸克网盘相关
        "pan.quark.cn", "drive.quark.cn",
        // 购物平台相关
        "taobao.com", "tmall.com", "jd.com", "pdd.com", "pinduoduo.com"
    )

    /**
     * 检测文本中是否包含可分享的网址
     *
     * @param text 要检测的文本内容
     * @param customUrlPatterns 自定义URL模式列表（可选）
     * @return 检测结果，包含是否为网址和相关信息
     */
    fun detectShareableUrl(
        text: String,
        customUrlPatterns: List<String> = emptyList()
    ): DetectionResult {
        if (text.isBlank()) {
            return DetectionResult(false)
        }

        try {
            Log.d(TAG, "Detecting shareable URL in text: ${text.take(100)}...")

            // 预处理文本，移除多余的空白字符
            val cleanText = text.trim()

            // 检查是否包含URL
            val urlMatch = findUrl(cleanText)
            if (urlMatch == null) {
                Log.d(TAG, "No URL found in text")
                return DetectionResult(false, originalText = cleanText)
            }

            val url = urlMatch.value
            val domain = extractDomain(url)

            Log.d(TAG, "Found URL: $url, domain: $domain")

            // 检查是否为排除的应用域名
            if (domain != null && isExcludedDomain(domain)) {
                Log.d(TAG, "URL belongs to excluded domain: $domain")
                return DetectionResult(false, originalText = cleanText)
            }

            // 检查自定义URL模式
            if (customUrlPatterns.isNotEmpty()) {
                val matchesCustomPattern = customUrlPatterns.any { pattern ->
                    try {
                        val regex = pattern.toRegex(RegexOption.IGNORE_CASE)
                        regex.containsMatchIn(url)
                    } catch (e: Exception) {
                        Log.w(TAG, "Invalid custom URL pattern: $pattern", e)
                        false
                    }
                }
                if (!matchesCustomPattern) {
                    Log.d(TAG, "URL does not match any custom patterns")
                    return DetectionResult(false, originalText = cleanText)
                }
            }

            Log.d(TAG, "Detected shareable URL: $url")
            return DetectionResult(
                isUrl = true,
                url = url,
                domain = domain,
                originalText = cleanText
            )

        } catch (e: Exception) {
            Log.e(TAG, "Error detecting shareable URL", e)
            return DetectionResult(false, originalText = text)
        }
    }

    /**
     * 在文本中查找URL
     */
    private fun findUrl(text: String): MatchResult? {
        // 更精确的URL正则表达式
        val urlPattern = """https?://[^\s<>"{}|\\^`\[\]]+""".toRegex(RegexOption.IGNORE_CASE)
        return urlPattern.find(text)
    }

    /**
     * 从URL中提取域名
     */
    private fun extractDomain(url: String): String? {
        return try {
            val domainPattern = """https?://(?:www\.)?([^/]+)""".toRegex(RegexOption.IGNORE_CASE)
            val matchResult = domainPattern.find(url)
            matchResult?.groupValues?.get(1)?.lowercase()
        } catch (e: Exception) {
            Log.w(TAG, "Error extracting domain from URL: $url", e)
            null
        }
    }

    /**
     * 检查域名是否在排除列表中
     */
    private fun isExcludedDomain(domain: String): Boolean {
        val lowerDomain = domain.lowercase()
        return excludedDomains.any { excludedDomain ->
            lowerDomain == excludedDomain || lowerDomain.endsWith(".$excludedDomain")
        }
    }

    /**
     * 检查文本是否包含URL（简单检查）
     */
    fun containsUrl(text: String): Boolean {
        val urlPattern = """https?://[^\s]+""".toRegex(RegexOption.IGNORE_CASE)
        return urlPattern.containsMatchIn(text)
    }

    /**
     * 获取排除的域名列表（用于配置界面显示）
     */
    fun getExcludedDomains(): Set<String> {
        return excludedDomains.toSet()
    }

    /**
     * 验证URL格式是否正确
     */
    fun isValidUrl(url: String): Boolean {
        return try {
            val urlPattern = """^https?://[^\s<>"{}|\\^`\[\]]+$""".toRegex(RegexOption.IGNORE_CASE)
            urlPattern.matches(url.trim())
        } catch (e: Exception) {
            false
        }
    }
}

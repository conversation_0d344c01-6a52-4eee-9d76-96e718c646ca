package com.weinuo.quickcommands.smartreminder

import android.content.BroadcastReceiver
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import android.util.Log
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.service.SmartReminderOverlayService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 购物应用提醒处理器
 *
 * 监听剪贴板变化，检测购物链接并建议打开对应的购物应用。
 * 当用户复制商品链接时，自动识别购物平台并显示提醒悬浮窗。
 *
 * 功能特点：
 * - 实时监听剪贴板变化
 * - 智能识别购物链接
 * - 检查应用安装状态
 * - 显示应用图标的提醒悬浮窗
 * - 防重复提醒机制
 *
 * 设计原则：
 * - 遵循现有架构模式
 * - 与新应用提醒保持一致的显示方式
 * - 最小化资源消耗
 * - 用户体验优先
 */
class ShoppingAppReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "ShoppingAppReminder"
        private const val DEFAULT_REMINDER_COOLDOWN = 30000L // 30秒冷却时间
        private const val DEFAULT_DETECTION_DELAY = 500L // 检测延迟，避免频繁触发
    }

    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    private val packageManager = context.packageManager
    private val configAdapter = SmartReminderConfigAdapter(context)

    private var isMonitoring = false
    private var lastReminderTime = 0L
    private var clipboardListener: ClipboardManager.OnPrimaryClipChangedListener? = null

    // 配置缓存
    private var currentConfig: SmartReminderConfigAdapter.ShoppingAppReminderConfig? = null

    /**
     * 应用信息数据类
     */
    data class AppInfo(
        val packageName: String,
        val appName: String,
        val icon: Drawable?
    )

    /**
     * 开始监听剪贴板变化
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Already monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Starting shopping app reminder monitoring")

            // 加载配置
            loadConfiguration()

            // 创建剪贴板监听器
            clipboardListener = ClipboardManager.OnPrimaryClipChangedListener {
                handlerScope.launch {
                    // 使用配置中的延迟时间
                    val delayTime = currentConfig?.detectionDelay?.toLong() ?: DEFAULT_DETECTION_DELAY
                    delay(delayTime)
                    handleClipboardChange()
                }
            }

            // 注册监听器
            clipboardManager.addPrimaryClipChangedListener(clipboardListener)
            isMonitoring = true

            Log.d(TAG, "Shopping app reminder monitoring started")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting shopping app reminder monitoring", e)
        }
    }

    /**
     * 停止监听剪贴板变化
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Not monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Stopping shopping app reminder monitoring")

            // 移除剪贴板监听器
            clipboardListener?.let { listener ->
                clipboardManager.removePrimaryClipChangedListener(listener)
            }
            clipboardListener = null
            isMonitoring = false

            Log.d(TAG, "Shopping app reminder monitoring stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping shopping app reminder monitoring", e)
        }
    }

    /**
     * 处理剪贴板变化事件
     */
    private suspend fun handleClipboardChange() {
        try {
            // 确保配置已加载
            if (currentConfig == null) {
                loadConfiguration()
            }

            val config = currentConfig ?: return

            // 检查冷却时间
            val currentTime = System.currentTimeMillis()
            val cooldownTime = config.cooldownTime * 1000L // 转换为毫秒
            if (currentTime - lastReminderTime < cooldownTime) {
                Log.d(TAG, "Reminder in cooldown period, skipping")
                return
            }

            // 获取剪贴板内容
            val clipText = getClipboardText() ?: return

            Log.d(TAG, "Clipboard changed, checking for shopping links: ${clipText.take(50)}...")

            // 使用配置检测购物链接
            val detectionResult = ShoppingLinkDetector.detectShoppingLink(
                text = clipText,
                enabledBuiltInPlatforms = config.enabledBuiltInPlatforms,
                customPlatforms = config.customPlatforms
            )
            if (!detectionResult.isShoppingLink || detectionResult.platform == null) {
                Log.d(TAG, "No shopping link detected")
                return
            }

            val platform = detectionResult.platform
            Log.d(TAG, "Detected ${platform.name} shopping link")

            // 检查对应应用是否已安装
            if (!isAppInstalled(platform.packageName)) {
                Log.d(TAG, "App ${platform.name} (${platform.packageName}) not installed, skipping reminder")
                return
            }

            // 获取应用信息
            val appInfo = getAppInfo(platform.packageName) ?: return

            Log.d(TAG, "Showing shopping app reminder for: ${appInfo.appName}")

            // 显示提醒
            showShoppingAppReminder(appInfo, platform.name)

            // 更新最后提醒时间
            lastReminderTime = currentTime

            // 触发回调
            onReminderTriggered(mapOf(
                "type" to "shopping_app_reminder",
                "timestamp" to currentTime,
                "packageName" to appInfo.packageName,
                "appName" to appInfo.appName,
                "platform" to platform.name,
                "clipboardText" to clipText.take(100)
            ))

        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard change", e)
        }
    }

    /**
     * 获取剪贴板文本内容
     */
    private fun getClipboardText(): String? {
        return try {
            if (!clipboardManager.hasPrimaryClip()) {
                return null
            }

            val clipData = clipboardManager.primaryClip
            if (clipData == null || clipData.itemCount == 0) {
                return null
            }

            clipData.getItemAt(0).text?.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting clipboard text", e)
            null
        }
    }

    /**
     * 检查应用是否已安装
     */
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if app is installed: $packageName", e)
            false
        }
    }

    /**
     * 获取应用信息
     */
    private fun getAppInfo(packageName: String): AppInfo? {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val applicationInfo = packageInfo.applicationInfo
            if (applicationInfo != null) {
                val appName = packageManager.getApplicationLabel(applicationInfo).toString()
                val appIcon = packageManager.getApplicationIcon(applicationInfo)

                AppInfo(
                    packageName = packageName,
                    appName = appName,
                    icon = appIcon
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting app info for: $packageName", e)
            null
        }
    }

    /**
     * 显示购物应用提醒悬浮窗
     */
    private fun showShoppingAppReminder(appInfo: AppInfo, platformName: String) {
        try {
            Log.d(TAG, "Attempting to show shopping app reminder for: ${appInfo.appName}")

            val title = context.getString(R.string.shopping_app_reminder_title)
            val message = "检测到${platformName}商品链接，建议打开「${appInfo.appName}」"

            Log.d(TAG, "Calling SmartReminderOverlayService.showShoppingAppReminder")

            SmartReminderOverlayService.showShoppingAppReminder(
                context = context,
                title = title,
                message = message,
                appInfo = appInfo
            )

            Log.d(TAG, "SmartReminderOverlayService.showShoppingAppReminder called successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing shopping app reminder overlay", e)
        }
    }

    /**
     * 检查是否正在监听
     */
    fun isMonitoring(): Boolean {
        return isMonitoring
    }

    /**
     * 加载配置参数
     */
    private fun loadConfiguration() {
        try {
            currentConfig = configAdapter.loadShoppingAppReminderConfig(SmartReminderType.SHOPPING_APP_REMINDER.id)
            Log.d(TAG, "Configuration loaded: ${currentConfig}")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading configuration", e)
            // 使用默认配置
            currentConfig = SmartReminderConfigAdapter.ShoppingAppReminderConfig()
        }
    }

    /**
     * 重新加载配置（用于配置更新后的刷新）
     */
    fun reloadConfiguration() {
        loadConfiguration()
    }
}

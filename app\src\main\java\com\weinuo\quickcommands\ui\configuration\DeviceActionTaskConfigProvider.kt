package com.weinuo.quickcommands.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.ui.activities.ShareTargetSelectionActivity
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.screens.LocalNavController

/**
 * 设备动作任务配置数据提供器
 *
 * 提供设备动作任务特定的配置项列表，为每个操作类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object DeviceActionTaskConfigProvider {

    /**
     * 获取设备动作任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 设备动作任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<DeviceActionOperation>> {
        return listOf(
            // 分享文本
            ConfigurationCardItem(
                id = "share_text",
                title = context.getString(R.string.device_action_share_text),
                description = context.getString(R.string.device_action_share_text_description),
                operationType = DeviceActionOperation.SHARE_TEXT,
                permissionRequired = false,
                content = { operation, onComplete ->
                    ShareTextConfigContent(operation, onComplete)
                },
                editableContent = { operation, initialConfig, onComplete ->
                    ShareTextConfigContent(operation, onComplete, initialConfig as? DeviceActionTask)
                }
            ),

            // 剪贴板刷新
            ConfigurationCardItem(
                id = "clipboard_refresh",
                title = "剪贴板刷新",
                description = "刷新剪贴板内容",
                operationType = DeviceActionOperation.CLIPBOARD_REFRESH,
                permissionRequired = false,
                content = { operation, onComplete ->
                    ClipboardRefreshConfigContent(operation, onComplete)
                }
            ),

            // 启动主屏幕
            ConfigurationCardItem(
                id = "launch_home",
                title = "启动主屏幕",
                description = "返回到主屏幕",
                operationType = DeviceActionOperation.LAUNCH_HOME,
                permissionRequired = false,
                content = { operation, onComplete ->
                    LaunchHomeConfigContent(operation, onComplete)
                }
            ),

            // 设置剪贴板
            ConfigurationCardItem(
                id = "set_clipboard",
                title = "设置剪贴板",
                description = "将文本内容复制到系统剪贴板",
                operationType = DeviceActionOperation.SET_CLIPBOARD,
                permissionRequired = false,
                content = { operation, onComplete ->
                    SetClipboardConfigContent(operation, onComplete)
                }
            ),

            // 展开/折叠状态栏
            ConfigurationCardItem(
                id = "toggle_status_bar",
                title = "展开/折叠状态栏",
                description = "控制状态栏的展开和折叠",
                operationType = DeviceActionOperation.TOGGLE_STATUS_BAR,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ToggleStatusBarConfigContent(operation, onComplete)
                }
            ),

            // 语音搜索
            ConfigurationCardItem(
                id = "voice_search",
                title = "语音搜索",
                description = "启动语音搜索功能",
                operationType = DeviceActionOperation.VOICE_SEARCH,
                permissionRequired = true,
                content = { operation, onComplete ->
                    VoiceSearchConfigContent(operation, onComplete)
                }
            ),

            // 手电筒控制
            ConfigurationCardItem(
                id = "flashlight_control",
                title = "手电筒控制",
                description = "控制手电筒的开启、关闭或切换",
                operationType = DeviceActionOperation.FLASHLIGHT_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    FlashlightControlConfigContent(operation, onComplete)
                }
            ),

            // 震动
            ConfigurationCardItem(
                id = "vibration",
                title = "震动",
                description = "触发设备震动",
                operationType = DeviceActionOperation.VIBRATION,
                permissionRequired = true,
                content = { operation, onComplete ->
                    VibrationConfigContent(operation, onComplete)
                }
            ),

            // 文字转语音
            ConfigurationCardItem(
                id = "text_to_speech",
                title = "文字转语音",
                description = "使用TTS引擎朗读文本内容",
                operationType = DeviceActionOperation.TEXT_TO_SPEECH,
                permissionRequired = false,
                content = { operation, onComplete ->
                    TextToSpeechConfigContent(operation, onComplete)
                }
            ),

            // Shizuku命令
            ConfigurationCardItem(
                id = "shizuku_command",
                title = "Shizuku命令",
                description = "执行Shizuku Shell命令",
                operationType = DeviceActionOperation.SHIZUKU_COMMAND,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ShizukuCommandConfigContent(operation, onComplete)
                }
            ),

            // 等待延迟
            ConfigurationCardItem(
                id = "wait_delay",
                title = "等待延迟",
                description = "暂停执行指定的时间",
                operationType = DeviceActionOperation.WAIT_DELAY,
                permissionRequired = false,
                content = { operation, onComplete ->
                    WaitDelayConfigContent(operation, onComplete)
                }
            ),

            // 系统操作功能
            ConfigurationCardItem(
                id = "quick_settings",
                title = "打开快速设置面板",
                description = "打开系统快速设置面板",
                operationType = DeviceActionOperation.QUICK_SETTINGS,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SystemOperationConfigContent(operation, onComplete)
                }
            ),

            ConfigurationCardItem(
                id = "power_menu",
                title = "显示电源菜单",
                description = "显示系统电源选项菜单",
                operationType = DeviceActionOperation.POWER_MENU,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SystemOperationConfigContent(operation, onComplete)
                }
            ),

            ConfigurationCardItem(
                id = "recent_tasks",
                title = "打开最近任务",
                description = "打开最近使用的应用任务列表",
                operationType = DeviceActionOperation.RECENT_TASKS,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SystemOperationConfigContent(operation, onComplete)
                }
            ),

            ConfigurationCardItem(
                id = "app_drawer",
                title = "打开应用抽屉",
                description = "打开所有应用的应用抽屉",
                operationType = DeviceActionOperation.APP_DRAWER,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SystemOperationConfigContent(operation, onComplete)
                }
            ),

            ConfigurationCardItem(
                id = "accessibility_toggle",
                title = "切换无障碍对讲功能",
                description = "切换系统无障碍对讲功能的开启/关闭状态",
                operationType = DeviceActionOperation.ACCESSIBILITY_TOGGLE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SystemOperationConfigContent(operation, onComplete)
                }
            ),

            // 按返回键
            ConfigurationCardItem(
                id = "back_key",
                title = "按返回键",
                description = "模拟按下系统返回键",
                operationType = DeviceActionOperation.BACK_KEY,
                permissionRequired = true,
                content = { operation, onComplete ->
                    BackKeyConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 分享文本配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 *
 * @param operation 设备动作操作类型
 * @param onComplete 配置完成回调
 * @param initialTask 初始任务配置（编辑模式使用）
 */
@Composable
private fun ShareTextConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit,
    initialTask: DeviceActionTask? = null
) {

    var shareText by rememberSaveable { mutableStateOf(initialTask?.shareText ?: "") }
    // 分别保存ShareTarget的各个字段，避免自定义Saver的复杂性
    var selectedTargetPackageName by rememberSaveable { mutableStateOf(initialTask?.shareAppPackage ?: "") }
    var selectedTargetAppName by rememberSaveable { mutableStateOf(initialTask?.shareAppName ?: "") }
    var selectedTargetActivityName by rememberSaveable { mutableStateOf(initialTask?.shareTargetActivityName ?: "") }
    var selectedTargetLabel by rememberSaveable { mutableStateOf(initialTask?.shareTargetLabel ?: "") }
    var selectedTargetIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建ShareTarget对象
    val selectedShareTarget = if (selectedTargetPackageName.isNotEmpty()) {
        ShareTarget(
            packageName = selectedTargetPackageName,
            appName = selectedTargetAppName,
            activityName = selectedTargetActivityName,
            targetLabel = selectedTargetLabel,
            isSystemApp = selectedTargetIsSystemApp
        )
    } else {
        null
    }

    val context = LocalContext.current

    // 分享目标选择启动器
    val shareTargetSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            val packageName = data?.getStringExtra(ShareTargetSelectionActivity.RESULT_PACKAGE_NAME)
            val appName = data?.getStringExtra(ShareTargetSelectionActivity.RESULT_APP_NAME)
            val activityName = data?.getStringExtra(ShareTargetSelectionActivity.RESULT_ACTIVITY_NAME)
            val targetLabel = data?.getStringExtra(ShareTargetSelectionActivity.RESULT_TARGET_LABEL)
            val isSystemApp = data?.getBooleanExtra(ShareTargetSelectionActivity.RESULT_IS_SYSTEM_APP, false) ?: false

            if (packageName != null && appName != null && activityName != null && targetLabel != null) {
                selectedTargetPackageName = packageName
                selectedTargetAppName = appName
                selectedTargetActivityName = activityName
                selectedTargetLabel = targetLabel
                selectedTargetIsSystemApp = isSystemApp
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "分享文本设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = shareText,
            onValueChange = { shareText = it },
            label = { Text("分享内容") },
            placeholder = { Text("输入要分享的文本内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4
        )

        // 分享目标选择（可选）
        Text(
            text = "分享目标 (可选)",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                // 启动分享目标选择Activity
                ShareTargetSelectionActivity.startForSelection(context)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedShareTarget != null) {
                    "已选择: ${selectedShareTarget.getDisplayText()}"
                } else {
                    "选择分享方式（可选）"
                }
            )
        }

        if (selectedShareTarget != null) {
            Text(
                text = "包名: ${selectedShareTarget.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Text(
            text = "留空则显示系统分享选择器",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    shareText = shareText,
                    shareAppPackage = selectedShareTarget?.packageName ?: "",
                    shareAppName = selectedShareTarget?.appName ?: "",
                    shareTargetActivityName = selectedShareTarget?.activityName ?: "",
                    shareTargetLabel = selectedShareTarget?.targetLabel ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = shareText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 设置剪贴板配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SetClipboardConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {

    var clipboardText by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "剪贴板设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = clipboardText,
            onValueChange = { clipboardText = it },
            label = { Text("剪贴板内容") },
            placeholder = { Text("输入要复制到剪贴板的文本") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    clipboardText = clipboardText
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = clipboardText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 文字转语音配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun TextToSpeechConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {

    var ttsText by rememberSaveable { mutableStateOf("") }
    var ttsLanguage by rememberSaveable { mutableStateOf("zh-CN") }
    var ttsPitch by rememberSaveable { mutableStateOf("1.0") }
    var ttsSpeed by rememberSaveable { mutableStateOf("1.0") }
    var ttsAudioStream by rememberSaveable { mutableStateOf(TTSAudioStream.MUSIC) }
    var ttsQueueMode by rememberSaveable { mutableStateOf(true) }
    var ttsWaitForCompletion by rememberSaveable { mutableStateOf(false) }
    var ttsSpellOutNumbers by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "文字转语音设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = ttsText,
            onValueChange = { ttsText = it },
            label = { Text("朗读内容") },
            placeholder = { Text("输入要朗读的文本") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4
        )

        OutlinedTextField(
            value = ttsLanguage,
            onValueChange = { ttsLanguage = it },
            label = { Text("语言代码") },
            placeholder = { Text("zh-CN") },
            modifier = Modifier.fillMaxWidth()
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = ttsPitch,
                onValueChange = { ttsPitch = it },
                label = { Text("音调 (0.5-2.0)") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal)
            )
            OutlinedTextField(
                value = ttsSpeed,
                onValueChange = { ttsSpeed = it },
                label = { Text("语速 (0.5-2.0)") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal)
            )
        }

        // 音频流选择
        Text(
            text = "音频流类型",
            style = MaterialTheme.typography.bodyLarge
        )

        TTSAudioStream.values().forEach { stream ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (ttsAudioStream == stream),
                        onClick = { ttsAudioStream = stream }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (ttsAudioStream == stream),
                    onClick = { ttsAudioStream = stream }
                )
                Text(
                    text = stream.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 高级选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = ttsQueueMode,
                    onValueChange = { ttsQueueMode = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = ttsQueueMode,
                onCheckedChange = { ttsQueueMode = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "排队朗读（否则立即打断）",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = ttsWaitForCompletion,
                    onValueChange = { ttsWaitForCompletion = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = ttsWaitForCompletion,
                onCheckedChange = { ttsWaitForCompletion = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "等待完成后才能执行后续动作",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = ttsSpellOutNumbers,
                    onValueChange = { ttsSpellOutNumbers = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = ttsSpellOutNumbers,
                onCheckedChange = { ttsSpellOutNumbers = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "逐个说出数字",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    ttsText = ttsText,
                    ttsLanguage = ttsLanguage,
                    ttsPitch = ttsPitch.toFloatOrNull() ?: 1.0f,
                    ttsSpeed = ttsSpeed.toFloatOrNull() ?: 1.0f,
                    ttsAudioStream = ttsAudioStream,
                    ttsQueueMode = ttsQueueMode,
                    ttsWaitForCompletion = ttsWaitForCompletion,
                    ttsSpellOutNumbers = ttsSpellOutNumbers
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = ttsText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * Shizuku命令配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ShizukuCommandConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {

    var shizukuCommands by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Shizuku命令设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = shizukuCommands,
            onValueChange = { shizukuCommands = it },
            label = { Text("Shell命令") },
            placeholder = { Text("输入要执行的Shell命令，多个命令用换行分隔") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 8
        )

        Text(
            text = "提示：每行一个命令，支持多个命令顺序执行",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    shizukuCommands = shizukuCommands
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = shizukuCommands.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 等待延迟配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun WaitDelayConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {

    var waitMinutes by rememberSaveable { mutableStateOf("0") }
    var waitSeconds by rememberSaveable { mutableStateOf("5") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "等待延迟设置",
            style = MaterialTheme.typography.titleMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = waitMinutes,
                onValueChange = { waitMinutes = it },
                label = { Text("等待分钟") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
            OutlinedTextField(
                value = waitSeconds,
                onValueChange = { waitSeconds = it },
                label = { Text("等待秒数") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
        }

        Text(
            text = "总等待时间：${(waitMinutes.toIntOrNull() ?: 0) * 60 + (waitSeconds.toIntOrNull() ?: 0)} 秒",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    waitMinutes = waitMinutes.toIntOrNull() ?: 0,
                    waitSeconds = waitSeconds.toIntOrNull() ?: 0
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = (waitMinutes.toIntOrNull() ?: 0) > 0 || (waitSeconds.toIntOrNull() ?: 0) > 0
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 剪贴板刷新配置内容组件
 */
@Composable
private fun ClipboardRefreshConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "剪贴板刷新",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "此操作将刷新剪贴板内容，无需额外配置。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(operation = operation)
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 启动主屏幕配置内容组件
 */
@Composable
private fun LaunchHomeConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "启动主屏幕",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "此操作将返回到主屏幕，无需额外配置。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(operation = operation)
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 展开/折叠状态栏配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ToggleStatusBarConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {

    var statusBarOperation by rememberSaveable { mutableStateOf(StatusBarOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "状态栏控制设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 状态栏操作选择
        StatusBarOperation.values().forEach { statusOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (statusBarOperation == statusOp),
                        onClick = { statusBarOperation = statusOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (statusBarOperation == statusOp),
                    onClick = { statusBarOperation = statusOp }
                )
                Text(
                    text = when (statusOp) {
                        StatusBarOperation.EXPAND -> "展开状态栏"
                        StatusBarOperation.COLLAPSE -> "折叠状态栏"
                        StatusBarOperation.TOGGLE -> "切换状态栏状态"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    statusBarOperation = statusBarOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 语音搜索配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VoiceSearchConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "语音搜索",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "此操作将启动语音搜索功能，无需额外配置。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(operation = operation)
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 手电筒控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun FlashlightControlConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {

    var flashlightOperation by rememberSaveable { mutableStateOf(FlashlightControlType.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "手电筒控制设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 手电筒操作选择
        FlashlightControlType.values().forEach { flashlightOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (flashlightOperation == flashlightOp),
                        onClick = { flashlightOperation = flashlightOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (flashlightOperation == flashlightOp),
                    onClick = { flashlightOperation = flashlightOp }
                )
                Text(
                    text = when (flashlightOp) {
                        FlashlightControlType.TURN_ON -> "开启手电筒"
                        FlashlightControlType.TURN_OFF -> "关闭手电筒"
                        FlashlightControlType.TOGGLE -> "切换手电筒状态"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    flashlightOperation = flashlightOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 震动配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VibrationConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {
    var vibrationPattern by rememberSaveable { mutableStateOf(DeviceVibrationPattern.SHORT_BUZZ) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "震动设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 震动模式选择
        DeviceVibrationPattern.values().forEach { pattern ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (vibrationPattern == pattern),
                        onClick = { vibrationPattern = pattern }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (vibrationPattern == pattern),
                    onClick = { vibrationPattern = pattern }
                )
                Text(
                    text = when (pattern) {
                        DeviceVibrationPattern.WAVE -> "波动"
                        DeviceVibrationPattern.SHORT_BUZZ -> "短震动"
                        DeviceVibrationPattern.LONG_BUZZ -> "长震动"
                        DeviceVibrationPattern.DOUBLE_BUZZ -> "双震动"
                        DeviceVibrationPattern.TRIPLE_BUZZ -> "三震动"
                        DeviceVibrationPattern.HEARTBEAT -> "心跳震动"
                        DeviceVibrationPattern.NOTIFICATION -> "通知震动"
                        DeviceVibrationPattern.CUSTOM -> "自定义震动"
                        DeviceVibrationPattern.QUICK -> "快速"
                        DeviceVibrationPattern.SLOW -> "慢速"
                        DeviceVibrationPattern.INCREASING -> "递增"
                        DeviceVibrationPattern.CONSTANT -> "恒定"
                        DeviceVibrationPattern.DECREASING -> "递减"
                        DeviceVibrationPattern.MINIMAL -> "极轻微"
                        DeviceVibrationPattern.LIGHT -> "轻微"
                        DeviceVibrationPattern.FANTASY -> "幻想"
                        DeviceVibrationPattern.GAME_OVER -> "游戏结束"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    vibrationPattern = vibrationPattern
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 系统操作配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SystemOperationConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {
    var systemOperationType by rememberSaveable {
        mutableStateOf(
            when (operation) {
                DeviceActionOperation.QUICK_SETTINGS -> SystemOperationType.QUICK_SETTINGS
                DeviceActionOperation.POWER_MENU -> SystemOperationType.POWER_MENU
                DeviceActionOperation.RECENT_TASKS -> SystemOperationType.RECENT_TASKS
                DeviceActionOperation.APP_DRAWER -> SystemOperationType.APP_DRAWER
                DeviceActionOperation.ACCESSIBILITY_TOGGLE -> SystemOperationType.ACCESSIBILITY_TOGGLE
                else -> SystemOperationType.QUICK_SETTINGS
            }
        )
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "系统操作设置",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = when (systemOperationType) {
                SystemOperationType.QUICK_SETTINGS -> "打开系统快速设置面板，可以快速调整WiFi、蓝牙、亮度等设置"
                SystemOperationType.POWER_MENU -> "显示系统电源菜单，包含关机、重启、截屏等选项"
                SystemOperationType.RECENT_TASKS -> "打开最近使用的应用任务列表，可以快速切换应用"
                SystemOperationType.APP_DRAWER -> "打开应用抽屉，显示所有已安装的应用"
                SystemOperationType.ACCESSIBILITY_TOGGLE -> "切换系统无障碍对讲功能的开启/关闭状态"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )



        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation,
                    systemOperationType = systemOperationType
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 按返回键配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun BackKeyConfigContent(
    operation: DeviceActionOperation,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "按返回键设置",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "模拟按下系统返回键，相当于用户按下设备的返回按钮。此功能通过无障碍服务实现，需要启用'后台管理-系统操作服务'。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = operation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}
package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.utils.FilePickerUtil

/**
 * 相机任务配置数据提供器
 *
 * 提供相机任务特定的配置项列表，为每个相机操作类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 *
 * 支持三种相机操作类型：
 * 1. 打开最后一张照片 - 快速访问相册中最后一张照片
 * 2. 拍照 - 支持前置/后置摄像头选择，可配置照片保存位置
 * 3. 录像 - 支持开始录像、停止录像、定时录像，可配置视频保存位置
 */
object CameraTaskConfigProvider {

    /**
     * 获取相机任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 相机任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<CameraOperation>> {
        return listOf(
            // 打开最后一张照片
            ConfigurationCardItem(
                id = "open_last_photo",
                title = context.getString(R.string.camera_open_last_photo),
                description = context.getString(R.string.camera_open_last_photo_description),
                operationType = CameraOperation.OPEN_LAST_PHOTO,
                permissionRequired = true,
                content = { operation, onComplete ->
                    OpenLastPhotoConfigContent(operation, onComplete)
                }
            ),

            // 拍照 - 实验性功能
            ConfigurationCardItem(
                id = "take_photo",
                title = context.getString(R.string.camera_take_photo),
                description = context.getString(R.string.camera_take_photo_description),
                operationType = CameraOperation.TAKE_PHOTO,
                permissionRequired = true,
                isExperimental = true,
                content = { operation, onComplete ->
                    TakePhotoConfigContent(operation, onComplete)
                }
            ),

            // 录像 - 实验性功能
            ConfigurationCardItem(
                id = "record_video",
                title = context.getString(R.string.camera_record_video),
                description = context.getString(R.string.camera_record_video_description),
                operationType = CameraOperation.RECORD_VIDEO,
                permissionRequired = true,
                isExperimental = true,
                content = { operation, onComplete ->
                    RecordVideoConfigContent(operation, onComplete)
                }
            ),

            // 截屏
            ConfigurationCardItem(
                id = "screenshot",
                title = context.getString(R.string.camera_screenshot),
                description = context.getString(R.string.camera_screenshot_description),
                operationType = CameraOperation.SCREENSHOT,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ScreenshotConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 打开最后一张照片配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun OpenLastPhotoConfigContent(
    operation: CameraOperation,
    onComplete: (Any) -> Unit
) {

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置打开最后一张照片",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "此操作将打开系统相册中最后一张照片，无需额外配置。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = CameraTask(
                    operation = operation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 拍照配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun TakePhotoConfigContent(
    operation: CameraOperation,
    onComplete: (Any) -> Unit
) {

    var selectedCameraType by rememberSaveable { mutableStateOf(CameraType.BACK) }
    var selectedSaveLocation by rememberSaveable { mutableStateOf(SaveLocation.DCIM_CAMERA) }
    var customPath by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置拍照设置",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 摄像头类型选择
        CameraTypeSelector(
            selectedCameraType = selectedCameraType,
            onCameraTypeChanged = { selectedCameraType = it }
        )

        // 保存位置选择
        SaveLocationSelector(
            selectedSaveLocation = selectedSaveLocation,
            onSaveLocationChanged = { selectedSaveLocation = it },
            customPath = customPath,
            onCustomPathChanged = { customPath = it },
            isPhoto = true
        )

        // 确认按钮
        Button(
            onClick = {
                val task = CameraTask(
                    operation = operation,
                    cameraType = selectedCameraType,
                    photoSaveLocation = selectedSaveLocation,
                    customPhotoPath = if (selectedSaveLocation == SaveLocation.CUSTOM) customPath else ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 录像配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun RecordVideoConfigContent(
    operation: CameraOperation,
    onComplete: (Any) -> Unit
) {

    var selectedCameraType by rememberSaveable { mutableStateOf(CameraType.BACK) }
    var selectedVideoOperation by rememberSaveable { mutableStateOf(VideoRecordingOperation.START) }
    var recordingMinutes by rememberSaveable { mutableStateOf(0) }
    var recordingSeconds by rememberSaveable { mutableStateOf(30) }
    var selectedSaveLocation by rememberSaveable { mutableStateOf(SaveLocation.MOVIES) }
    var customPath by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置录像设置",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 摄像头类型选择
        CameraTypeSelector(
            selectedCameraType = selectedCameraType,
            onCameraTypeChanged = { selectedCameraType = it }
        )

        // 录像操作类型选择
        VideoOperationSelector(
            selectedVideoOperation = selectedVideoOperation,
            onVideoOperationChanged = { selectedVideoOperation = it }
        )

        // 录像时长设置（仅在定时录像时显示）
        if (selectedVideoOperation == VideoRecordingOperation.START_WITH_DURATION) {
            RecordingDurationSelector(
                recordingMinutes = recordingMinutes,
                recordingSeconds = recordingSeconds,
                onMinutesChanged = { recordingMinutes = it },
                onSecondsChanged = { recordingSeconds = it }
            )
        }

        // 保存位置选择
        SaveLocationSelector(
            selectedSaveLocation = selectedSaveLocation,
            onSaveLocationChanged = { selectedSaveLocation = it },
            customPath = customPath,
            onCustomPathChanged = { customPath = it },
            isPhoto = false
        )

        // 确认按钮
        Button(
            onClick = {
                val task = CameraTask(
                    operation = operation,
                    cameraType = selectedCameraType,
                    videoOperation = selectedVideoOperation,
                    recordingDurationMinutes = recordingMinutes,
                    recordingDurationSeconds = recordingSeconds,
                    videoSaveLocation = selectedSaveLocation,
                    customVideoPath = if (selectedSaveLocation == SaveLocation.CUSTOM) customPath else ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 摄像头类型选择器
 */
@Composable
private fun CameraTypeSelector(
    selectedCameraType: CameraType,
    onCameraTypeChanged: (CameraType) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "摄像头类型",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        val cameraOptions = listOf(
            CameraType.BACK to "后置摄像头",
            CameraType.FRONT to "前置摄像头"
        )

        cameraOptions.forEach { (type, label) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedCameraType == type,
                        onClick = { onCameraTypeChanged(type) }
                    )
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedCameraType == type,
                    onClick = { onCameraTypeChanged(type) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * 录像操作类型选择器
 */
@Composable
private fun VideoOperationSelector(
    selectedVideoOperation: VideoRecordingOperation,
    onVideoOperationChanged: (VideoRecordingOperation) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "录像操作类型",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        val videoOptions = listOf(
            VideoRecordingOperation.START to "开始录像",
            VideoRecordingOperation.STOP to "停止录像",
            VideoRecordingOperation.START_WITH_DURATION to "定时录像"
        )

        videoOptions.forEach { (operation, label) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedVideoOperation == operation,
                        onClick = { onVideoOperationChanged(operation) }
                    )
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedVideoOperation == operation,
                    onClick = { onVideoOperationChanged(operation) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * 录像时长选择器
 */
@Composable
private fun RecordingDurationSelector(
    recordingMinutes: Int,
    recordingSeconds: Int,
    onMinutesChanged: (Int) -> Unit,
    onSecondsChanged: (Int) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "录像时长",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 分钟输入
            OutlinedTextField(
                value = recordingMinutes.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { minutes ->
                        if (minutes >= 0 && minutes <= 60) {
                            onMinutesChanged(minutes)
                        }
                    }
                },
                label = { Text("分钟") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )

            Text(
                text = ":",
                style = MaterialTheme.typography.bodyLarge
            )

            // 秒钟输入
            OutlinedTextField(
                value = recordingSeconds.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { seconds ->
                        if (seconds >= 0 && seconds < 60) {
                            onSecondsChanged(seconds)
                        }
                    }
                },
                label = { Text("秒钟") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }

        Text(
            text = "总时长：${recordingMinutes}分${recordingSeconds}秒",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 保存位置选择器
 */
@Composable
private fun SaveLocationSelector(
    selectedSaveLocation: SaveLocation,
    onSaveLocationChanged: (SaveLocation) -> Unit,
    customPath: String,
    onCustomPathChanged: (String) -> Unit,
    isPhoto: Boolean
) {
    val context = LocalContext.current

    // 目录选择器启动器
    val directoryLauncher = FilePickerUtil.rememberDirectoryPickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getDirectoryRealPath(context, it)
            val pathToUse = realPath ?: it.toString()
            onCustomPathChanged(pathToUse)
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = if (isPhoto) "照片保存位置" else "视频保存位置",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        val locationOptions = if (isPhoto) {
            listOf(
                SaveLocation.DCIM_CAMERA to "相机文件夹",
                SaveLocation.PICTURES to "图片文件夹",
                SaveLocation.CUSTOM to "自定义路径"
            )
        } else {
            listOf(
                SaveLocation.MOVIES to "视频文件夹",
                SaveLocation.DCIM_CAMERA to "相机文件夹",
                SaveLocation.CUSTOM to "自定义路径"
            )
        }

        locationOptions.forEach { (location, label) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedSaveLocation == location,
                        onClick = { onSaveLocationChanged(location) }
                    )
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedSaveLocation == location,
                    onClick = { onSaveLocationChanged(location) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 自定义路径选择按钮
        if (selectedSaveLocation == SaveLocation.CUSTOM) {
            Button(
                onClick = {
                    FilePickerUtil.launchDirectoryPicker(directoryLauncher)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    if (customPath.isEmpty()) {
                        if (isPhoto) "选择照片保存目录" else "选择视频保存目录"
                    } else {
                        "已选择: $customPath"
                    }
                )
            }
        }
    }
}

/**
 * 截屏配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ScreenshotConfigContent(
    operation: CameraOperation,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "截屏设置",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "截取当前屏幕内容并保存为图片文件。截屏功能通过系统API实现，需要相应的存储权限。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Text(
            text = "注意：截屏图片将保存到系统默认的截屏文件夹中。",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = CameraTask(
                    operation = operation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 相机任务配置内容组件
 *
 * 统一的相机任务配置入口，根据操作类型调用相应的配置组件
 */
@Composable
fun CameraTaskConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 默认使用拍照配置
    TakePhotoConfigContent(CameraOperation.TAKE_PHOTO, onComplete)
}

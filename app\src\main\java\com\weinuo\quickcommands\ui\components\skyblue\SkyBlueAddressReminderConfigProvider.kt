package com.weinuo.quickcommands.ui.components.skyblue

import android.app.Activity
import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SimpleAppInfo
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.service.QuickCommandsService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands.ui.activities.AppSelectionActivity
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.storage.UIStateStorageManager
import kotlinx.coroutines.launch

/**
 * 地址提醒配置提供器
 *
 * 提供地址提醒功能的配置项和配置内容组件。
 * 复用音乐应用提醒的配置模式，提供一致的用户体验。
 *
 * 配置内容包括：
 * - 延迟提醒时间设置
 * - 提醒间隔配置
 * - 自动消失设置
 * - 按钮位置配置
 * - 按钮样式配置
 */
object SkyBlueAddressReminderConfigProvider {

    /**
     * 获取地址提醒配置项
     */
    fun getConfigurationItem(context: android.content.Context): ConfigurationCardItem<SmartReminderType> {
        return ConfigurationCardItem(
            id = "address_reminder",
            title = context.getString(R.string.address_reminder_title),
            description = context.getString(R.string.address_reminder_description),
            operationType = SmartReminderType.ADDRESS_REMINDER,
            permissionRequired = true, // 地址提醒需要悬浮窗权限
            content = { reminderType, onComplete ->
                AddressReminderConfigContent(reminderType, onComplete)
            },
            editableContent = { reminderType, initialConfig, onComplete ->
                AddressReminderConfigContent(reminderType, onComplete, initialConfig)
            }
        )
    }

    /**
     * 获取配置内容组件（支持外部保存请求）
     *
     * 提供地址提醒的配置内容组件，用于在智慧提醒详细配置界面中显示。
     * 支持外部保存请求机制，用于右上角保存按钮。
     *
     * @param reminderType 智慧提醒类型
     * @param onComplete 配置完成回调
     * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
     * @param navController 导航控制器，用于应用选择
     */
    @Composable
    fun getConfigContent(
        reminderType: SmartReminderType,
        onComplete: (Any) -> Unit,
        onSaveRequested: ((suspend () -> Unit) -> Unit)? = null,
        navController: androidx.navigation.NavController? = null
    ) {
        AddressReminderConfigContent(
            reminderType = reminderType,
            onComplete = onComplete,
            onSaveRequested = onSaveRequested,
            navController = navController
        )
    }
}

/**
 * 地址提醒配置内容组件
 *
 * 提供完整的地址提醒配置选项：
 * - 延迟提醒时间设置（1-60秒）
 * - 提醒间隔配置（30-600秒）
 * - 自动消失设置（1-60秒）
 * - 按钮位置配置（四个角落）
 * - 按钮样式配置（尺寸、边距）
 *
 * @param reminderType 智慧提醒类型
 * @param onComplete 配置完成回调
 * @param initialConfig 初始配置（编辑模式使用）
 * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
 */
@Composable
private fun AddressReminderConfigContent(
    reminderType: SmartReminderType,
    onComplete: (Any) -> Unit,
    initialConfig: Any? = null,
    onSaveRequested: ((suspend () -> Unit) -> Unit)? = null,
    navController: androidx.navigation.NavController? = null
) {
    val context = LocalContext.current
    val configAdapter = remember { SmartReminderConfigAdapter(context) }
    val coroutineScope = rememberCoroutineScope()

    // 配置状态
    var selectedMapApps by remember { mutableStateOf(listOf<SmartReminderConfigAdapter.SelectedMapApp>()) }
    var buttonConfigs by remember { mutableStateOf(listOf<SmartReminderConfigAdapter.AddressReminderButtonConfig>()) }
    var delayTime by rememberSaveable { mutableStateOf("2") }
    var cooldownTime by rememberSaveable { mutableStateOf("60") }
    var autoDismissEnabled by rememberSaveable { mutableStateOf(true) }
    var autoDismissSeconds by rememberSaveable { mutableStateOf("10") }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val selectedPackageNames = result.data?.getStringArrayListExtra("selected_apps") ?: emptyList()

            // 获取应用信息
            val packageManager = context.packageManager
            val newSelectedApps = selectedPackageNames.mapNotNull { packageName ->
                try {
                    val appInfo = packageManager.getApplicationInfo(packageName, 0)
                    val appName = packageManager.getApplicationLabel(appInfo).toString()
                    SmartReminderConfigAdapter.SelectedMapApp(
                        packageName = packageName,
                        appName = appName,
                        isEnabled = true
                    )
                } catch (e: Exception) {
                    null
                }
            }
            selectedMapApps = newSelectedApps

            // 为新选择的应用生成按钮配置
            buttonConfigs = newSelectedApps.mapIndexed { index, app ->
                val (position, marginX, marginY) = calculateButtonPosition(index)
                SmartReminderConfigAdapter.AddressReminderButtonConfig(
                    appPackageName = app.packageName,
                    buttonPosition = position,
                    buttonSize = 56,
                    buttonMarginX = marginX,
                    buttonMarginY = marginY,
                    isEnabled = true
                )
            }
        }
    }



    // 初始化配置
    LaunchedEffect(initialConfig) {
        if (initialConfig is SmartReminderConfigAdapter.AddressReminderConfig) {
            selectedMapApps = initialConfig.selectedMapApps
            buttonConfigs = initialConfig.buttonConfigs
            delayTime = initialConfig.delayTime.toString()
            cooldownTime = initialConfig.cooldownTime.toString()
            autoDismissEnabled = initialConfig.autoDismissEnabled
            autoDismissSeconds = initialConfig.autoDismissSeconds.toString()
        } else {
            // 尝试加载现有配置
            try {
                val existingConfig = configAdapter.loadAddressReminderConfig(reminderType.id)
                selectedMapApps = existingConfig.selectedMapApps
                buttonConfigs = existingConfig.buttonConfigs
                delayTime = existingConfig.delayTime.toString()
                cooldownTime = existingConfig.cooldownTime.toString()
                autoDismissEnabled = existingConfig.autoDismissEnabled
                autoDismissSeconds = existingConfig.autoDismissSeconds.toString()
            } catch (e: Exception) {
                // 使用默认值
            }
        }
    }

    // 保存配置函数
    val saveConfig: suspend () -> Unit = {
        try {
            val config = SmartReminderConfigAdapter.AddressReminderConfig(
                selectedMapApps = selectedMapApps,
                buttonConfigs = buttonConfigs,
                delayTime = delayTime.toIntOrNull()?.coerceIn(1, 60) ?: 2,
                cooldownTime = cooldownTime.toIntOrNull()?.coerceIn(30, 600) ?: 60,
                autoDismissEnabled = autoDismissEnabled,
                autoDismissSeconds = autoDismissSeconds.toIntOrNull()?.coerceIn(1, 60) ?: 10
            )

            // 保存配置
            configAdapter.saveAddressReminderConfig(reminderType.id, config)

            // 标记为已配置
            configAdapter.updateConfiguredState(reminderType.id, true)

            // 通知服务重新加载配置
            QuickCommandsService.recheckSmartReminderMonitoring(context)

            // 注意：这里不调用onComplete，避免自动跳转
            // onComplete(config)
        } catch (e: Exception) {
            // 处理保存错误
        }
    }

    // 暴露保存函数给外部调用
    LaunchedEffect(onSaveRequested) {
        onSaveRequested?.invoke(saveConfig)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // 应用选择配置 - 与其他配置项保持一致的样式
        ConfigSection(
            title = if (selectedMapApps.isEmpty()) "选择地图应用" else "更换地图应用",
            description = if (selectedMapApps.isEmpty()) {
                "选择要监控的地图应用，当这些应用启动时将显示提醒按钮"
            } else {
                "已选择 ${selectedMapApps.size} 个应用，点击按钮可重新选择"
            }
        ) {
            Button(
                onClick = {
                    // 启动应用选择界面
                    val currentSelectedApps = selectedMapApps.map { it.packageName }
                    val intent = Intent(context, AppSelectionActivity::class.java).apply {
                        putExtra("selection_mode", "MULTI")
                        putStringArrayListExtra("initial_selected_apps", ArrayList(currentSelectedApps))
                        putExtra("result_key", "smart_reminder_${reminderType.id}_apps")
                    }
                    appSelectionLauncher.launch(intent)
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (selectedMapApps.isEmpty()) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    },
                    contentColor = if (selectedMapApps.isEmpty()) {
                        MaterialTheme.colorScheme.onPrimary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            ) {
                Text(
                    text = if (selectedMapApps.isEmpty()) "选择应用" else "重新选择应用"
                )
            }

            // 显示已选择的应用列表
            if (selectedMapApps.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                    Text(
                        text = "已选择的应用：",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    selectedMapApps.forEach { app ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "• ${app.appName}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }
        }

        // 按钮位置配置
        ConfigSection(
            title = "按钮位置",
            description = "设置提醒按钮在屏幕上的显示位置"
        ) {
            val positionOptions = listOf(
                "bottom_left" to "左下角",
                "bottom_right" to "右下角",
                "top_left" to "左上角",
                "top_right" to "右上角"
            )

            // 为每个应用显示按钮位置配置
            selectedMapApps.forEachIndexed { index, app ->
                if (app.isEnabled) {
                    val appButtonConfig = buttonConfigs.find { it.appPackageName == app.packageName }
                        ?: run {
                            val (position, marginX, marginY) = calculateButtonPosition(index)
                            SmartReminderConfigAdapter.AddressReminderButtonConfig(
                                appPackageName = app.packageName,
                                buttonPosition = position,
                                buttonSize = 56,
                                buttonMarginX = marginX,
                                buttonMarginY = marginY,
                                isEnabled = true
                            ).also { newConfig ->
                                buttonConfigs = buttonConfigs + newConfig
                            }
                        }

                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Text(
                                text = app.appName,
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                positionOptions.forEach { (value, label) ->
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        RadioButton(
                                            selected = appButtonConfig.buttonPosition == value,
                                            onClick = {
                                                buttonConfigs = buttonConfigs.map { config ->
                                                    if (config.appPackageName == app.packageName) {
                                                        config.copy(buttonPosition = value)
                                                    } else config
                                                }
                                            }
                                        )
                                        Text(
                                            text = label,
                                            modifier = Modifier.padding(start = 8.dp),
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    }
                                }
                            }
                        }
                    }

                    if (index < selectedMapApps.filter { it.isEnabled }.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }

        // 按钮样式配置
        ConfigSection(
            title = "按钮样式",
            description = "设置提醒按钮的尺寸和边距"
        ) {
            selectedMapApps.forEachIndexed { index, app ->
                if (app.isEnabled) {
                    val appButtonConfig = buttonConfigs.find { it.appPackageName == app.packageName }
                        ?: run {
                            val (position, marginX, marginY) = calculateButtonPosition(index)
                            SmartReminderConfigAdapter.AddressReminderButtonConfig(
                                appPackageName = app.packageName,
                                buttonPosition = position,
                                buttonSize = 56,
                                buttonMarginX = marginX,
                                buttonMarginY = marginY,
                                isEnabled = true
                            ).also { newConfig ->
                                buttonConfigs = buttonConfigs + newConfig
                            }
                        }

                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "${app.appName} 按钮样式",
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            // 按钮尺寸
                            OutlinedTextField(
                                value = appButtonConfig.buttonSize.toString(),
                                onValueChange = { newValue ->
                                    newValue.toIntOrNull()?.let { size ->
                                        buttonConfigs = buttonConfigs.map { config ->
                                            if (config.appPackageName == app.packageName) {
                                                config.copy(buttonSize = size.coerceIn(40, 80))
                                            } else config
                                        }
                                    }
                                },
                                label = { Text("按钮尺寸（dp）") },
                                placeholder = { Text("56") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true,
                                modifier = Modifier.fillMaxWidth()
                            )

                            // 水平边距
                            OutlinedTextField(
                                value = appButtonConfig.buttonMarginX.toString(),
                                onValueChange = { newValue ->
                                    newValue.toIntOrNull()?.let { margin ->
                                        buttonConfigs = buttonConfigs.map { config ->
                                            if (config.appPackageName == app.packageName) {
                                                config.copy(buttonMarginX = margin.coerceIn(8, 100))
                                            } else config
                                        }
                                    }
                                },
                                label = { Text("水平边距（dp）") },
                                placeholder = { Text("24") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true,
                                modifier = Modifier.fillMaxWidth()
                            )

                            // 垂直边距
                            OutlinedTextField(
                                value = appButtonConfig.buttonMarginY.toString(),
                                onValueChange = { newValue ->
                                    newValue.toIntOrNull()?.let { margin ->
                                        buttonConfigs = buttonConfigs.map { config ->
                                            if (config.appPackageName == app.packageName) {
                                                config.copy(buttonMarginY = margin.coerceIn(8, 200))
                                            } else config
                                        }
                                    }
                                },
                                label = { Text("垂直边距（dp）") },
                                placeholder = { Text("24") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }

                    if (index < selectedMapApps.filter { it.isEnabled }.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }

        // 延迟时间设置
        ConfigSection(
            title = "延迟提醒时间",
            description = "检测到地址后延迟多久提醒，范围：1-60秒"
        ) {
            OutlinedTextField(
                value = delayTime,
                onValueChange = { delayTime = it },
                label = { Text("延迟时间（秒）") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 冷却时间设置
        ConfigSection(
            title = "提醒间隔",
            description = "两次提醒之间的最小间隔，范围：30-600秒"
        ) {
            OutlinedTextField(
                value = cooldownTime,
                onValueChange = { cooldownTime = it },
                label = { Text("间隔时间（秒）") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 自动消失设置
        ConfigSection(
            title = "自动消失",
            description = "设置提醒按钮是否自动消失，避免长时间停留在屏幕上"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                // 自动消失开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用自动消失",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "提醒按钮将在指定时间后自动关闭",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = autoDismissEnabled,
                        onCheckedChange = { autoDismissEnabled = it }
                    )
                }

                // 自动消失时间设置
                if (autoDismissEnabled) {
                    OutlinedTextField(
                        value = autoDismissSeconds,
                        onValueChange = { newValue ->
                            // 只允许输入数字
                            if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                autoDismissSeconds = newValue
                            }
                        },
                        label = { Text("自动消失时间（秒）") },
                        placeholder = { Text("10") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        supportingText = {
                            val value = autoDismissSeconds.toIntOrNull()
                            when {
                                value == null && autoDismissSeconds.isNotEmpty() -> {
                                    Text(
                                        text = "请输入有效数字",
                                        color = MaterialTheme.colorScheme.error
                                    )
                                }
                                value != null && (value < 1 || value > 60) -> {
                                    Text(
                                        text = "建议范围：1-60秒",
                                        color = MaterialTheme.colorScheme.error
                                    )
                                }
                                value != null -> {
                                    Text(
                                        text = when {
                                            value <= 5 -> "很快消失"
                                            value <= 15 -> "适中时间"
                                            else -> "较长停留"
                                        },
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    )
                }
            }
        }





        // 保存按钮（仅在没有外部保存请求时显示）
        if (onSaveRequested == null) {
            Button(
                onClick = {
                    coroutineScope.launch {
                        saveConfig()
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("保存配置")
            }
        }
    }
}



/**
 * 配置区域组件
 * 复用其他提醒配置界面的ConfigSection组件
 */
@Composable
private fun ConfigSection(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            content()
        }
    }
}

/**
 * 智能计算按钮位置，避免重叠
 * 支持无限数量的应用，通过增加边距和调整位置来避免重叠
 *
 * @param index 应用索引（从0开始）
 * @return Triple(位置, 水平边距, 垂直边距)
 */
private fun calculateButtonPosition(index: Int): Triple<String, Int, Int> {
    // 基础位置循环：右下角 -> 左下角 -> 右上角 -> 左上角
    val basePositions = listOf("bottom_right", "bottom_left", "top_right", "top_left")
    val position = basePositions[index % 4]

    // 计算层级（每4个应用为一层）
    val layer = index / 4

    // 基础边距
    val baseMarginX = 24
    val baseMarginY = 24

    // 根据层级和位置计算边距，确保不重叠且不超出屏幕
    val (marginX, marginY) = when (position) {
        "bottom_right" -> {
            // 右下角：向左和向上偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200) // 最大不超过200dp
            val y = baseMarginY + (layer * 80).coerceAtMost(300) // 最大不超过300dp
            Pair(x, y)
        }
        "bottom_left" -> {
            // 左下角：向右和向上偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        "top_right" -> {
            // 右上角：向左和向下偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        "top_left" -> {
            // 左上角：向右和向下偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        else -> Pair(baseMarginX, baseMarginY)
    }

    return Triple(position, marginX, marginY)
}



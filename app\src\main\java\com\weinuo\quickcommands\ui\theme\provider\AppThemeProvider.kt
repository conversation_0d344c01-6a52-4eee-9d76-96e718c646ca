package com.weinuo.quickcommands.ui.theme.provider

import android.util.Log
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.manager.ThemeContext
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.system.AppTheme
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueThemeProvider
import com.weinuo.quickcommands.data.SettingsRepository

/**
 * 应用主题提供者
 *
 * 为整个应用提供主题上下文，使所有组件都能感知当前主题
 * 这是连接新主题系统和现有UI的核心桥梁
 */
@Composable
fun AppThemeProvider(
    content: @Composable () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentTheme by themeManager.currentTheme

    // 添加主题调试信息
    LaunchedEffect(currentTheme) {
        Log.d("AppThemeProvider", "=== 当前主题调试信息 ===")
        Log.d("AppThemeProvider", "主题ID: ${currentTheme.id}")
        Log.d("AppThemeProvider", "主题名称: ${currentTheme.displayName}")
        Log.d("AppThemeProvider", "设计方法: ${currentTheme.designApproach}")
        Log.d("AppThemeProvider", "支持模糊: ${currentTheme.supportsBlur}")
        Log.d("AppThemeProvider", "支持阴影: ${currentTheme.supportsShadows}")
        Log.d("AppThemeProvider", "支持透明: ${currentTheme.supportsTransparency}")
        Log.d("AppThemeProvider", "========================")
    }

    // 创建主题上下文，支持天空蓝主题的动态颜色配置
    val themeContext = if (currentTheme.id == "sky_blue") {
        // 天空蓝主题使用动态颜色配置
        createSkyBlueDynamicThemeContext(currentTheme, context)
    } else {
        // 其他主题使用静态配置
        remember(currentTheme) {
            ThemeContext.fromTheme(currentTheme)
        }
    }
    
    // 提供主题上下文给子组件
    CompositionLocalProvider(
        LocalThemeContext provides themeContext
    ) {
        content()
    }
}

/**
 * 创建天空蓝主题的动态主题上下文
 *
 * 支持从SettingsRepository中读取用户自定义的颜色配置
 */
@Composable
private fun createSkyBlueDynamicThemeContext(theme: AppTheme, context: android.content.Context): ThemeContext {
    val settingsRepository = remember { SettingsRepository(context) }
    val provider = theme.themeProvider as SkyBlueThemeProvider

    // 获取动态颜色方案
    val dynamicColorScheme = provider.getDynamicColorScheme(settingsRepository)

    // 添加调试日志
    LaunchedEffect(dynamicColorScheme) {
        Log.d("SkyBlueDynamicTheme", "=== 天空蓝动态颜色配置 ===")
        Log.d("SkyBlueDynamicTheme", "Primary: ${dynamicColorScheme.primary}")
        Log.d("SkyBlueDynamicTheme", "Background: ${dynamicColorScheme.background}")
        Log.d("SkyBlueDynamicTheme", "Surface: ${dynamicColorScheme.surface}")
        Log.d("SkyBlueDynamicTheme", "========================")
    }

    return ThemeContext(
        theme = theme,
        colorScheme = dynamicColorScheme,
        componentFactory = provider.getComponentFactory(),
        styleConfiguration = provider.getStyleConfiguration(),
        interactionConfiguration = provider.getInteractionConfiguration(),
        animationConfiguration = provider.getAnimationConfiguration(),
        blurConfiguration = provider.getBlurConfiguration(),
        designApproach = theme.designApproach
    )
}

/**
 * 获取当前主题上下文的便捷函数
 */
@Composable
fun currentThemeContext(): ThemeContext {
    return LocalThemeContext.current
}

/**
 * 获取当前主题的便捷函数
 */
@Composable
fun currentTheme(): AppTheme {
    return LocalThemeContext.current.theme
}

/**
 * 检查当前主题是否支持特定功能的便捷函数
 */
@Composable
fun isThemeFeatureSupported(feature: com.weinuo.quickcommands.ui.theme.system.ThemeFeature): Boolean {
    return LocalThemeContext.current.theme.supportsFeature(feature)
}

/**
 * 主题切换函数
 */
@Composable
fun rememberThemeSwitcher(): (AppTheme) -> Unit {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    
    return remember {
        { newTheme: AppTheme ->
            themeManager.setTheme(newTheme)
        }
    }
}

/**
 * 主题感知的组件创建器
 *
 * 提供便捷的方式来创建主题感知的组件
 */
object ThemedComponentCreator {
    
    /**
     * 创建主题感知的底部导航栏
     */
    @Composable
    fun BottomNavigation(
        config: com.weinuo.quickcommands.ui.theme.config.BottomNavigationConfig
    ) {
        val themeContext = LocalThemeContext.current
        themeContext.componentFactory.createBottomNavigation()(config)
    }
    

    
    /**
     * 创建主题感知的卡片
     */
    @Composable
    fun Card(
        config: com.weinuo.quickcommands.ui.theme.config.CardConfig
    ) {
        val themeContext = LocalThemeContext.current
        themeContext.componentFactory.createCard()(config)
    }
    
    /**
     * 创建主题感知的按钮
     */
    @Composable
    fun Button(
        config: com.weinuo.quickcommands.ui.theme.config.ButtonConfig
    ) {
        val themeContext = LocalThemeContext.current
        themeContext.componentFactory.createButton()(config)
    }
    
    /**
     * 创建主题感知的文本输入框
     */
    @Composable
    fun TextField(
        config: com.weinuo.quickcommands.ui.theme.config.TextFieldConfig
    ) {
        val themeContext = LocalThemeContext.current
        themeContext.componentFactory.createTextField()(config)
    }
    
    /**
     * 创建主题感知的浮动操作按钮
     */
    @Composable
    fun FloatingActionButton(
        config: com.weinuo.quickcommands.ui.theme.config.FloatingActionButtonConfig
    ) {
        val themeContext = LocalThemeContext.current
        themeContext.componentFactory.createFloatingActionButton()(config)
    }
    
    /**
     * 创建主题感知的对话框
     */
    @Composable
    fun Dialog(
        config: com.weinuo.quickcommands.ui.theme.config.DialogConfig
    ) {
        val themeContext = LocalThemeContext.current
        themeContext.componentFactory.createDialog()(config)
    }
    
    /**
     * 创建主题感知的底部弹窗
     */
    @Composable
    fun BottomSheet(
        config: com.weinuo.quickcommands.ui.theme.config.BottomSheetConfig
    ) {
        val themeContext = LocalThemeContext.current
        themeContext.componentFactory.createBottomSheet()(config)
    }
}

/**
 * 主题感知的样式获取器
 *
 * 提供便捷的方式来获取当前主题的样式配置
 */
object ThemedStyleProvider {
    
    /**
     * 获取当前主题的圆角配置
     */
    @Composable
    fun cornerRadius(): com.weinuo.quickcommands.ui.theme.config.CornerRadiusConfig {
        return LocalThemeContext.current.styleConfiguration.cornerRadius
    }
    
    /**
     * 获取当前主题的阴影配置
     */
    @Composable
    fun elevation(): com.weinuo.quickcommands.ui.theme.config.ElevationConfig {
        return LocalThemeContext.current.styleConfiguration.elevation
    }
    
    /**
     * 获取当前主题的间距配置
     */
    @Composable
    fun spacing(): com.weinuo.quickcommands.ui.theme.config.SpacingConfig {
        return LocalThemeContext.current.styleConfiguration.spacing
    }
    
    /**
     * 获取当前主题的边框配置
     */
    @Composable
    fun borders(): com.weinuo.quickcommands.ui.theme.config.BorderConfig {
        return LocalThemeContext.current.styleConfiguration.borders
    }
    
    /**
     * 获取当前主题的阴影配置
     */
    @Composable
    fun shadows(): com.weinuo.quickcommands.ui.theme.config.ShadowConfig {
        return LocalThemeContext.current.styleConfiguration.shadows
    }
    
    /**
     * 获取当前主题的效果配置
     */
    @Composable
    fun effects(): com.weinuo.quickcommands.ui.theme.config.EffectsConfig {
        return LocalThemeContext.current.styleConfiguration.effects
    }
    
    /**
     * 获取当前主题的交互配置
     */
    @Composable
    fun interactions(): com.weinuo.quickcommands.ui.theme.system.InteractionConfiguration {
        return LocalThemeContext.current.interactionConfiguration
    }
    
    /**
     * 获取当前主题的动画配置
     */
    @Composable
    fun animations(): com.weinuo.quickcommands.ui.theme.system.AnimationConfiguration {
        return LocalThemeContext.current.animationConfiguration
    }

    /**
     * 获取当前主题的样式配置
     */
    @Composable
    fun styles(): com.weinuo.quickcommands.ui.theme.system.StyleConfiguration {
        return LocalThemeContext.current.styleConfiguration
    }
}

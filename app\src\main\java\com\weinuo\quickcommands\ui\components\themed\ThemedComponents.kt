package com.weinuo.quickcommands.ui.components.themed

/**
 * 主题感知组件系统统一入口
 *
 * 这个文件提供了所有主题感知组件的统一导入入口，
 * 方便在其他文件中使用主题感知组件。
 *
 * 主题感知组件的核心特性：
 * 1. 自动适配：根据当前主题自动选择合适的实现
 * 2. 统一接口：所有主题的同类组件都使用相同的API
 * 3. 可扩展性：新主题可以提供自己独有的组件实现
 * 4. 性能优化：组件创建使用工厂模式，支持缓存和优化
 * 5. 类型安全：使用强类型配置，避免运行时错误
 *
 * 使用示例：
 * ```kotlin
 * import com.weinuo.quickcommands.ui.components.themed.*
 *
 * @Composable
 * fun MyScreen() {
 *     ThemedScaffold(
 *         topBar = {
 *             // 直接使用主题专用组件
 *             IntegratedTopAppBar(config = topBarConfig, hazeState = hazeState) // 天空蓝主题
 *             // 或 LayeredTopAppBar(config = topBarConfig) // 海洋蓝主题
 *         },
 *         bottomBar = {
 *             ThemedBottomNavigation(
 *                 tabs = tabs,
 *                 selectedIndex = selectedIndex,
 *                 onTabSelected = onTabSelected
 *             )
 *         }
 *     ) { paddingValues ->
 *         LazyColumn(
 *             modifier = Modifier.padding(paddingValues)
 *         ) {
 *             items(items) { item ->
 *                 ThemedCard {
 *                     // 卡片内容
 *                 }
 *             }
 *         }
 *     }
 * }
 * ```
 *
 * 支持的主题感知组件：
 * - ThemedScaffold: 主题感知的脚手架
 * - ThemedBottomNavigation: 主题感知的底部导航
 * - ThemedCard: 主题感知的卡片
 * - ThemedButton: 主题感知的按钮
 * - ThemedTextField: 主题感知的文本输入框
 * - ThemedFloatingActionButton: 主题感知的浮动操作按钮
 * - ThemedDialog: 主题感知的对话框
 * - ThemedBottomSheet: 主题感知的底部弹窗
 *
 * 注意：TopAppBar组件已删除，各主题直接使用专用组件：
 * - 天空蓝主题：IntegratedTopAppBar
 * - 海洋蓝主题：LayeredTopAppBar
 *
 * 设计原则：
 * 1. 接口优先：所有组件都基于配置接口，而不是具体实现
 * 2. 组合优于继承：使用组合模式而不是继承来实现主题差异
 * 3. 最小惊讶原则：API设计符合开发者的直觉和期望
 * 4. 渐进增强：基础功能在所有主题中都可用，高级功能可选
 * 5. 向后兼容：新版本的组件API保持向后兼容
 */

// 重新导出所有主题感知组件，方便统一导入
// 这样其他文件只需要 import com.weinuo.quickcommands.ui.components.themed.*
// 就可以使用所有主题感知组件

// 核心布局组件
// ThemedScaffold 已在 ThemedScaffold.kt 中定义

// 导航组件
// 注意：TopAppBar组件已删除，各主题直接使用专用组件（IntegratedTopAppBar/LayeredTopAppBar）
// ThemedBottomNavigation 已在 ThemedBottomNavigation.kt 中定义

// 内容组件
// ThemedCard 已在 ThemedCard.kt 中定义
// ThemedButton 已在 ThemedButton.kt 中定义
// ThemedTextField 已在 ThemedTextField.kt 中定义

// 交互组件
// ThemedFloatingActionButton 已在 ThemedFloatingActionButton.kt 中定义
// ThemedDialog 已在 ThemedDialog.kt 中定义
// ThemedBottomSheet 已在 ThemedBottomSheet.kt 中定义

/**
 * 主题感知组件使用指南
 *
 * 1. 基础使用：
 *    直接使用 Themed* 组件替换原有的 Material 组件
 *
 * 2. 配置使用：
 *    对于复杂场景，使用 *Config 对象进行详细配置
 *
 * 3. 主题特性检查：
 *    使用 LocalThemeContext.current 检查当前主题支持的特性
 *
 * 4. 性能优化：
 *    组件会自动使用主题系统的缓存机制，无需手动优化
 *
 * 5. 扩展开发：
 *    新主题只需实现 ComponentFactory 接口即可支持所有组件
 */

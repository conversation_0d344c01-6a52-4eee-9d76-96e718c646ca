package com.weinuo.quickcommands.ui.theme.manager

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.weinuo.quickcommands.ui.theme.system.AppTheme
import com.weinuo.quickcommands.ui.theme.system.ThemeProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import android.util.Log

/**
 * 主题管理器
 *
 * 负责管理应用的主题状态，包括主题切换、持久化存储等
 * 采用单例模式确保全局唯一性
 *
 * 性能优化特性：
 * - 生命周期感知：应用进入后台时自动清理非必要缓存
 * - 智能缓存：只缓存当前主题和最近使用的主题
 * - 零后台开销：后台运行时不执行任何主题相关操作
 */
class ThemeManager private constructor(private val context: Context) : DefaultLifecycleObserver {
    
    companion object {
        private const val PREFS_NAME = "theme_preferences"
        private const val KEY_CURRENT_THEME = "current_theme"
        private const val DEFAULT_THEME_ID = "ocean_blue"
        private const val TAG = "ThemeManager"

        @Volatile
        private var INSTANCE: ThemeManager? = null

        /**
         * 获取主题管理器实例
         */
        fun getInstance(context: Context): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ThemeManager(context.applicationContext).also {
                    INSTANCE = it
                    // 注册生命周期观察者
                    ProcessLifecycleOwner.get().lifecycle.addObserver(it)
                }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val themeCache = ThemeCache()

    // 性能监控
    private var isAppInForeground = true
    private var backgroundCleanupJob: Job? = null
    private val performanceScope = CoroutineScope(Dispatchers.Default)

    // 当前主题状态
    private val _currentTheme = mutableStateOf(loadSavedTheme())
    val currentTheme: State<AppTheme> = _currentTheme

    // 当前主题提供者状态
    private val _currentThemeProvider = mutableStateOf(_currentTheme.value.themeProvider)
    val currentThemeProvider: State<ThemeProvider> = _currentThemeProvider
    
    init {
        // 启动性能监控
        ThemePerformanceMonitor.startMonitoring(context)

        // 初始化当前主题
        initializeTheme(_currentTheme.value)
    }
    
    /**
     * 从持久化存储加载保存的主题
     */
    private fun loadSavedTheme(): AppTheme {
        val savedThemeId = prefs.getString(KEY_CURRENT_THEME, DEFAULT_THEME_ID)
        return AppTheme.findById(savedThemeId ?: DEFAULT_THEME_ID) ?: AppTheme.getDefault()
    }
    
    /**
     * 初始化主题
     */
    private fun initializeTheme(theme: AppTheme) {
        try {
            theme.themeProvider.initialize()
            themeCache.cacheTheme(theme)
        } catch (e: Exception) {
            // 记录错误但不崩溃
            Log.e(TAG, "Failed to initialize theme: ${theme.id}", e)
        }
    }
    
    /**
     * 设置当前主题
     * 性能优化：只在前台时执行完整的主题切换
     */
    fun setTheme(theme: AppTheme) {
        if (_currentTheme.value == theme) {
            return // 主题相同，无需切换
        }

        val startTime = System.currentTimeMillis()
        val previousTheme = _currentTheme.value

        try {
            Log.d(TAG, "开始切换主题: ${previousTheme.id} -> ${theme.id}")

            // 如果应用在后台，只更新状态，不执行耗时操作
            if (!shouldPerformExpensiveOperation()) {
                Log.d(TAG, "应用在后台，延迟主题初始化")
                _currentTheme.value = theme
                _currentThemeProvider.value = theme.themeProvider
                saveThemeAsync(theme)
                return
            }

            // 清理前一个主题
            previousTheme.themeProvider.cleanup()

            // 初始化新主题
            initializeTheme(theme)

            // 更新状态
            _currentTheme.value = theme
            _currentThemeProvider.value = theme.themeProvider

            // 异步保存主题设置
            saveThemeAsync(theme)

            val endTime = System.currentTimeMillis()
            val switchTime = endTime - startTime
            Log.d(TAG, "主题切换完成，耗时: ${switchTime}ms")

            // 记录性能数据
            ThemePerformanceMonitor.recordThemeSwitch(switchTime)

        } catch (e: Exception) {
            // 如果切换失败，回滚到之前的主题
            Log.e(TAG, "主题切换失败: ${theme.id}", e)
            _currentTheme.value = previousTheme
            _currentThemeProvider.value = previousTheme.themeProvider
        }
    }
    
    /**
     * 异步保存主题设置
     */
    private fun saveThemeAsync(theme: AppTheme) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                prefs.edit()
                    .putString(KEY_CURRENT_THEME, theme.id)
                    .apply()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save theme: ${theme.id}", e)
            }
        }
    }
    
    /**
     * 获取当前主题ID
     */
    fun getCurrentThemeId(): String = _currentTheme.value.id
    
    /**
     * 获取当前主题显示名称
     */
    fun getCurrentThemeDisplayName(): String = _currentTheme.value.displayName
    
    /**
     * 检查当前主题是否支持特定功能
     */
    fun currentThemeSupportsFeature(feature: com.weinuo.quickcommands.ui.theme.system.ThemeFeature): Boolean {
        return _currentTheme.value.supportsFeature(feature)
    }
    
    /**
     * 获取所有可用主题
     */
    fun getAvailableThemes(): List<AppTheme> = AppTheme.values().toList()
    
    /**
     * 根据ID切换主题
     */
    fun setThemeById(themeId: String): Boolean {
        val theme = AppTheme.findById(themeId)
        return if (theme != null) {
            setTheme(theme)
            true
        } else {
            Log.w(TAG, "Theme not found: $themeId")
            false
        }
    }
    
    /**
     * 重置为默认主题
     */
    fun resetToDefault() {
        setTheme(AppTheme.getDefault())
    }
    
    /**
     * 智能预加载主题
     * 只在前台且有必要时预加载，避免后台资源消耗
     */
    fun preloadThemes() {
        if (!shouldPerformExpensiveOperation()) {
            Log.d(TAG, "应用在后台，跳过主题预加载")
            return
        }

        performanceScope.launch {
            Log.d(TAG, "开始智能预加载主题")
            val startTime = System.currentTimeMillis()

            // 只预加载当前主题和默认主题
            val themesToPreload = setOf(_currentTheme.value, AppTheme.getDefault())

            themesToPreload.forEach { theme ->
                try {
                    if (!themeCache.isThemeCached(theme)) {
                        themeCache.cacheTheme(theme)
                        Log.d(TAG, "预加载主题: ${theme.id}")
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "预加载主题失败: ${theme.id}", e)
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "主题预加载完成，耗时: ${endTime - startTime}ms")
        }
    }
    
    // ========== 生命周期回调方法 ==========

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        Log.d(TAG, "应用进入前台")
        isAppInForeground = true

        // 取消后台清理任务
        backgroundCleanupJob?.cancel()
        backgroundCleanupJob = null

        // 恢复当前主题的缓存（如果被清理了）
        if (!themeCache.isThemeCached(_currentTheme.value)) {
            performanceScope.launch {
                try {
                    themeCache.cacheTheme(_currentTheme.value)
                    Log.d(TAG, "恢复当前主题缓存: ${_currentTheme.value.id}")
                } catch (e: Exception) {
                    Log.e(TAG, "恢复主题缓存失败", e)
                }
            }
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Log.d(TAG, "应用进入后台")
        isAppInForeground = false

        // 启动后台清理任务（延迟执行，避免频繁前后台切换时的性能损失）
        backgroundCleanupJob = performanceScope.launch {
            kotlinx.coroutines.delay(30000) // 30秒后执行清理

            if (!isAppInForeground) {
                performBackgroundCleanup()
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        Log.d(TAG, "应用销毁")
        cleanup()
    }

    // ========== 性能优化方法 ==========

    /**
     * 执行后台清理
     * 只保留当前主题的缓存，清理其他主题缓存以节省内存
     */
    private fun performBackgroundCleanup() {
        try {
            Log.d(TAG, "执行后台清理")
            val currentThemeId = _currentTheme.value.id

            // 清理除当前主题外的所有缓存
            AppTheme.values().forEach { theme ->
                if (theme.id != currentThemeId) {
                    themeCache.clearThemeCache(theme.id)
                }
            }

            // 执行垃圾回收建议
            System.gc()

            Log.d(TAG, "后台清理完成，保留主题: $currentThemeId")
        } catch (e: Exception) {
            Log.e(TAG, "后台清理失败", e)
        }
    }

    /**
     * 检查是否应该执行性能敏感操作
     */
    private fun shouldPerformExpensiveOperation(): Boolean {
        return isAppInForeground
    }

    /**
     * 清理资源
     * 在应用销毁时调用
     */
    fun cleanup() {
        try {
            Log.d(TAG, "清理ThemeManager资源")

            // 取消所有后台任务
            backgroundCleanupJob?.cancel()
            performanceScope.cancel()

            // 清理主题提供者
            _currentTheme.value.themeProvider.cleanup()

            // 清理缓存
            themeCache.clearCache()

            // 停止性能监控
            ThemePerformanceMonitor.stopMonitoring()

            // 移除生命周期观察者
            ProcessLifecycleOwner.get().lifecycle.removeObserver(this)

        } catch (e: Exception) {
            Log.e(TAG, "清理ThemeManager失败", e)
        }
    }
    
    /**
     * 获取主题统计信息
     */
    fun getThemeStats(): ThemeStats {
        return ThemeStats(
            totalThemes = AppTheme.getCount(),
            currentTheme = _currentTheme.value,
            supportedFeatures = _currentTheme.value.features,
            cacheSize = themeCache.getCacheSize()
        )
    }
    
    /**
     * 验证主题系统状态
     */
    fun validateThemeSystem(): ThemeValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        try {
            // 验证当前主题
            val currentTheme = _currentTheme.value
            if (!currentTheme.themeProvider.validate()) {
                errors.add("Current theme validation failed: ${currentTheme.id}")
            }
            
            // 验证所有主题的基本信息
            AppTheme.values().forEach { theme ->
                if (theme.id.isBlank()) {
                    errors.add("Theme has empty ID: ${theme.displayName}")
                }
                if (theme.displayName.isBlank()) {
                    errors.add("Theme has empty display name: ${theme.id}")
                }
            }
            
            // 检查重复ID
            val themeIds = AppTheme.values().map { it.id }
            val duplicateIds = themeIds.groupBy { it }.filter { it.value.size > 1 }.keys
            if (duplicateIds.isNotEmpty()) {
                errors.add("Duplicate theme IDs found: $duplicateIds")
            }
            
        } catch (e: Exception) {
            errors.add("Theme system validation error: ${e.message}")
        }
        
        return ThemeValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }
}

/**
 * 主题统计信息
 */
data class ThemeStats(
    val totalThemes: Int,
    val currentTheme: AppTheme,
    val supportedFeatures: List<String>,
    val cacheSize: Int
)

/**
 * 主题验证结果
 */
data class ThemeValidationResult(
    val isValid: Boolean,
    val errors: List<String>,
    val warnings: List<String>
)

/**
 * ThemeProvider扩展函数，用于验证
 */
private fun ThemeProvider.validate(): Boolean {
    return try {
        // 基本验证：确保能获取到必要的配置
        getColorScheme()
        getComponentFactory()
        getStyleConfiguration()
        getInteractionConfiguration()
        getAnimationConfiguration()
        getBlurConfiguration()
        true
    } catch (e: Exception) {
        false
    }
}

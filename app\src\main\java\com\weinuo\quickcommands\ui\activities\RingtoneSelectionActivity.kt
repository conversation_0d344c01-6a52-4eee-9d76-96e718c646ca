package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.utils.RingtoneHelper

/**
 * 铃声选择独立Activity
 *
 * 提供全屏的铃声选择界面，支持单选模式
 * 使用ActivityResult机制返回选中的铃声信息
 * 
 * 启动方式：
 * - startForSelection: 启动铃声选择界面
 * 
 * 返回结果：
 * - RESULT_OK: 选择成功，通过Intent extras返回铃声信息
 * - RESULT_CANCELED: 用户取消选择
 */
class RingtoneSelectionActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_RINGTONE_TYPE = "ringtone_type"
        private const val EXTRA_INITIAL_SELECTED_URI = "initial_selected_uri"
        
        // 结果数据键
        const val RESULT_RINGTONE_URI = "result_ringtone_uri"
        const val RESULT_RINGTONE_NAME = "result_ringtone_name"
        const val RESULT_RINGTONE_TYPE = "result_ringtone_type"
        
        /**
         * 启动铃声选择界面
         * 
         * @param context 上下文
         * @param ringtoneType 铃声类型
         * @param initialSelectedUri 初始选中的铃声URI（可选）
         */
        fun startForSelection(
            context: Context,
            ringtoneType: RingtoneHelper.RingtoneType,
            initialSelectedUri: String = ""
        ) {
            val intent = Intent(context, RingtoneSelectionActivity::class.java).apply {
                putExtra(EXTRA_RINGTONE_TYPE, ringtoneType.name)
                putExtra(EXTRA_INITIAL_SELECTED_URI, initialSelectedUri)
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传入的参数
        val ringtoneTypeString = intent.getStringExtra(EXTRA_RINGTONE_TYPE) ?: "RINGTONE"
        val initialSelectedUri = intent.getStringExtra(EXTRA_INITIAL_SELECTED_URI) ?: ""
        
        val ringtoneType = try {
            RingtoneHelper.RingtoneType.valueOf(ringtoneTypeString)
        } catch (e: IllegalArgumentException) {
            RingtoneHelper.RingtoneType.RINGTONE
        }
        
        setContent {
            QuickCommandsTheme {
                RingtoneSelectionActivityContent(
                    ringtoneType = ringtoneType,
                    initialSelectedUri = initialSelectedUri,
                    onRingtoneSelected = { selectedRingtone ->
                        finishWithResult(selectedRingtone)
                    },
                    onFinish = { finish() }
                )
            }
        }
    }
    
    /**
     * 完成选择并返回结果
     */
    private fun finishWithResult(selectedRingtone: RingtoneHelper.RingtoneInfo) {
        val intent = Intent().apply {
            putExtra(RESULT_RINGTONE_URI, selectedRingtone.uriString)
            putExtra(RESULT_RINGTONE_NAME, selectedRingtone.title)
            putExtra(RESULT_RINGTONE_TYPE, selectedRingtone.type.name)
        }
        setResult(Activity.RESULT_OK, intent)
        finish()
    }
}

/**
 * 铃声选择Activity内容组件
 * 
 * 不依赖NavController的可复用组件，使用回调函数处理导航
 * 
 * @param ringtoneType 铃声类型
 * @param initialSelectedUri 初始选中的铃声URI
 * @param onRingtoneSelected 铃声选择完成回调
 * @param onFinish 完成回调（返回/取消）
 */
@Composable
fun RingtoneSelectionActivityContent(
    ringtoneType: RingtoneHelper.RingtoneType,
    initialSelectedUri: String,
    onRingtoneSelected: (RingtoneHelper.RingtoneInfo) -> Unit,
    onFinish: () -> Unit
) {
    // 使用原有的RingtoneSelectionScreen组件，但使用回调函数处理导航
    com.weinuo.quickcommands.ui.screens.RingtoneSelectionScreen(
        ringtoneType = ringtoneType,
        initialSelectedRingtoneUri = initialSelectedUri,
        onRingtoneSelected = onRingtoneSelected,
        onDismiss = onFinish
    )
}

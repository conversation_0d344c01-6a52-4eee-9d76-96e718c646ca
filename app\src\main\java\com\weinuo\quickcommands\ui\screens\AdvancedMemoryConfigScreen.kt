package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.loadAdvancedConfig
import com.weinuo.quickcommands.storage.saveAdvancedConfig
import com.weinuo.quickcommands.ui.components.PreferenceCategory
import com.weinuo.quickcommands.ui.components.SwitchPreference
import com.weinuo.quickcommands.ui.components.ClickablePreference

/**
 * 高级内存配置界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdvancedMemoryConfigScreen(
    mode: MemoryCheckMode,
    conditionId: String,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current

    // 加载配置
    var config by remember {
        mutableStateOf(loadAdvancedConfig(context, mode, conditionId))
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text("高级内存配置 - ${mode.displayName}")
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            saveAdvancedConfig(context, mode, conditionId, config)
                            onNavigateBack()
                        }
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                when (mode) {
                    MemoryCheckMode.EVENT_DRIVEN -> {
                        EventDrivenAdvancedConfig(
                            config = config as EventDrivenConfig,
                            onConfigChange = { config = it }
                        )
                    }
                    MemoryCheckMode.ADAPTIVE -> {
                        AdaptiveAdvancedConfig(
                            config = config as AdaptiveConfig,
                            onConfigChange = { config = it }
                        )
                    }
                    MemoryCheckMode.INTELLIGENT -> {
                        IntelligentAdvancedConfig(
                            config = config as IntelligentConfig,
                            onConfigChange = { config = it }
                        )
                    }
                    MemoryCheckMode.HYBRID -> {
                        HybridAdvancedConfig(
                            config = config as HybridConfig,
                            onConfigChange = { config = it }
                        )
                    }
                    else -> {
                        Text(
                            text = "该模式暂无高级配置选项",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 事件驱动模式高级配置
 */
@Composable
private fun EventDrivenAdvancedConfig(
    config: EventDrivenConfig,
    onConfigChange: (EventDrivenConfig) -> Unit
) {
    PreferenceCategory("触发事件配置") {
        SwitchPreference(
            title = "应用进入前台时触发",
            description = "当应用从后台切换到前台时触发检测",
            checked = config.triggerOnAppForeground,
            onCheckedChange = { onConfigChange(config.copy(triggerOnAppForeground = it)) }
        )

        SwitchPreference(
            title = "应用启动时触发",
            description = "当应用首次启动时触发检测",
            checked = config.triggerOnAppLaunch,
            onCheckedChange = { onConfigChange(config.copy(triggerOnAppLaunch = it)) }
        )

        SwitchPreference(
            title = "系统内存压力时触发",
            description = "当系统发出内存压力信号时触发检测",
            checked = config.triggerOnMemoryPressure,
            onCheckedChange = { onConfigChange(config.copy(triggerOnMemoryPressure = it)) }
        )
    }

    PreferenceCategory("延迟检测配置") {
        NumberInputPreference(
            title = "应用启动延迟时间",
            description = "应用启动后等待多少秒再检测内存",
            value = config.appLaunchDelaySeconds,
            onValueChange = { onConfigChange(config.copy(appLaunchDelaySeconds = it)) },
            suffix = "秒",
            range = 3..60
        )

        NumberInputPreference(
            title = "前台切换延迟时间",
            description = "应用切换到前台后等待多少秒再检测",
            value = config.foregroundDelaySeconds,
            onValueChange = { onConfigChange(config.copy(foregroundDelaySeconds = it)) },
            suffix = "秒",
            range = 1..30
        )

        SwitchPreference(
            title = "启用智能延迟",
            description = "根据应用类型自动调整延迟时间",
            checked = config.enableSmartDelay,
            onCheckedChange = { onConfigChange(config.copy(enableSmartDelay = it)) }
        )
    }

    PreferenceCategory("监控配置") {
        NumberInputPreference(
            title = "连续监控时长",
            description = "触发后持续监控多少秒",
            value = config.monitorDurationSeconds,
            onValueChange = { onConfigChange(config.copy(monitorDurationSeconds = it)) },
            suffix = "秒",
            range = 10..300
        )

        NumberInputPreference(
            title = "监控检测间隔",
            description = "监控期间每隔多少秒检测一次",
            value = config.monitorIntervalSeconds,
            onValueChange = { onConfigChange(config.copy(monitorIntervalSeconds = it)) },
            suffix = "秒",
            range = 1..10
        )

        NumberInputPreference(
            title = "冷却期时长",
            description = "检测完成后多少秒内不再触发",
            value = config.cooldownSeconds,
            onValueChange = { onConfigChange(config.copy(cooldownSeconds = it)) },
            suffix = "秒",
            range = 30..600
        )
    }
}

/**
 * 数字输入偏好设置组件
 */
@Composable
private fun NumberInputPreference(
    title: String,
    description: String,
    value: Int,
    onValueChange: (Int) -> Unit,
    suffix: String = "",
    range: IntRange = 1..Int.MAX_VALUE
) {
    var textValue by remember { mutableStateOf(value.toString()) }
    var isError by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium
        )

        if (description.isNotEmpty()) {
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 2.dp)
            )
        }

        OutlinedTextField(
            value = textValue,
            onValueChange = { newValue ->
                textValue = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in range) {
                    onValueChange(intValue)
                    isError = false
                } else {
                    isError = true
                }
            },
            suffix = if (suffix.isNotEmpty()) { { Text(suffix) } } else null,
            isError = isError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        )

        if (isError) {
            Text(
                text = "请输入 ${range.first} 到 ${range.last} 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

/**
 * 浮点数输入偏好设置组件
 */
@Composable
private fun FloatInputPreference(
    title: String,
    description: String,
    value: Float,
    onValueChange: (Float) -> Unit,
    suffix: String = "",
    range: ClosedFloatingPointRange<Float> = 0f..Float.MAX_VALUE
) {
    var textValue by remember {
        mutableStateOf(if (suffix == "%") "${(value * 100).toInt()}" else value.toString())
    }
    var isError by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium
        )

        if (description.isNotEmpty()) {
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 2.dp)
            )
        }

        OutlinedTextField(
            value = textValue,
            onValueChange = { newValue ->
                textValue = newValue
                val floatValue = if (suffix == "%") {
                    newValue.toIntOrNull()?.let { it / 100f }
                } else {
                    newValue.toFloatOrNull()
                }

                if (floatValue != null && floatValue in range) {
                    onValueChange(floatValue)
                    isError = false
                } else {
                    isError = true
                }
            },
            suffix = if (suffix.isNotEmpty()) { { Text(suffix) } } else null,
            isError = isError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        )

        if (isError) {
            val rangeText = if (suffix == "%") {
                "${(range.start * 100).toInt()} 到 ${(range.endInclusive * 100).toInt()}"
            } else {
                "${range.start} 到 ${range.endInclusive}"
            }
            Text(
                text = "请输入 $rangeText 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

/**
 * 下拉选择偏好设置组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun <T> DropdownPreference(
    title: String,
    description: String,
    selectedValue: T,
    options: List<T>,
    onValueChange: (T) -> Unit,
    optionLabel: (T) -> String
) {
    // 使用remember而不是rememberSaveable，遵循原生存储方式，避免序列化问题
    var expanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium
        )

        if (description.isNotEmpty()) {
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 2.dp)
            )
        }

        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded },
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        ) {
            OutlinedTextField(
                value = optionLabel(selectedValue),
                onValueChange = { },
                readOnly = true,
                trailingIcon = {
                    ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                options.forEach { option ->
                    DropdownMenuItem(
                        text = { Text(optionLabel(option)) },
                        onClick = {
                            onValueChange(option)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * 自适应模式高级配置
 */
@Composable
private fun AdaptiveAdvancedConfig(
    config: AdaptiveConfig,
    onConfigChange: (AdaptiveConfig) -> Unit
) {
    PreferenceCategory("自适应策略") {
        DropdownPreference(
            title = "自适应策略",
            description = "选择自适应检测的策略",
            selectedValue = config.adaptiveStrategy,
            options = AdaptiveStrategy.values().toList(),
            onValueChange = { onConfigChange(config.copy(adaptiveStrategy = it)) },
            optionLabel = { it.displayName }
        )

        SwitchPreference(
            title = "内存压力感知",
            description = "根据系统内存压力自动调整检测频率",
            checked = config.enableMemoryPressureAdaptation,
            onCheckedChange = { onConfigChange(config.copy(enableMemoryPressureAdaptation = it)) }
        )

        SwitchPreference(
            title = "应用活动感知",
            description = "根据应用活动频率调整检测策略",
            checked = config.enableAppActivityAdaptation,
            onCheckedChange = { onConfigChange(config.copy(enableAppActivityAdaptation = it)) }
        )
    }

    PreferenceCategory("频率调整范围") {
        NumberInputPreference(
            title = "内存充足时频率",
            description = "内存充足时的检测间隔",
            value = config.memoryAbundantFrequency,
            onValueChange = { onConfigChange(config.copy(memoryAbundantFrequency = it)) },
            suffix = "秒",
            range = 30..300
        )

        NumberInputPreference(
            title = "内存紧张时频率",
            description = "内存紧张时的检测间隔",
            value = config.memoryTightFrequency,
            onValueChange = { onConfigChange(config.copy(memoryTightFrequency = it)) },
            suffix = "秒",
            range = 1..30
        )
    }
}

/**
 * 智能学习模式高级配置
 */
@Composable
private fun IntelligentAdvancedConfig(
    config: IntelligentConfig,
    onConfigChange: (IntelligentConfig) -> Unit
) {
    PreferenceCategory("学习功能") {
        SwitchPreference(
            title = "启用智能学习",
            description = "自动学习应用内存使用模式",
            checked = config.enableLearning,
            onCheckedChange = { onConfigChange(config.copy(enableLearning = it)) }
        )

        if (config.enableLearning) {
            NumberInputPreference(
                title = "最小学习样本数",
                description = "开始预测前需要收集的最少样本数",
                value = config.minSamplesForPrediction,
                onValueChange = { onConfigChange(config.copy(minSamplesForPrediction = it)) },
                suffix = "次",
                range = 2..20
            )

            FloatInputPreference(
                title = "置信度阈值",
                description = "使用学习结果的最低置信度要求",
                value = config.confidenceThreshold,
                onValueChange = { onConfigChange(config.copy(confidenceThreshold = it)) },
                suffix = "%",
                range = 0.3f..0.95f
            )
        }
    }

    PreferenceCategory("内存稳定性检测") {
        NumberInputPreference(
            title = "稳定性检测间隔",
            description = "检测内存是否稳定的间隔时间",
            value = config.stabilityCheckInterval,
            onValueChange = { onConfigChange(config.copy(stabilityCheckInterval = it)) },
            suffix = "秒",
            range = 1..10
        )

        NumberInputPreference(
            title = "稳定性阈值",
            description = "判断内存稳定的变化阈值",
            value = config.stabilityThresholdMB,
            onValueChange = { onConfigChange(config.copy(stabilityThresholdMB = it)) },
            suffix = "MB",
            range = 10..200
        )

        NumberInputPreference(
            title = "连续稳定次数",
            description = "需要连续多少次检测稳定才确认",
            value = config.requiredStableChecks,
            onValueChange = { onConfigChange(config.copy(requiredStableChecks = it)) },
            suffix = "次",
            range = 2..10
        )
    }

    PreferenceCategory("数据管理") {
        NumberInputPreference(
            title = "最大历史记录数",
            description = "每个应用保留的最大学习记录数",
            value = config.maxHistoryRecords,
            onValueChange = { onConfigChange(config.copy(maxHistoryRecords = it)) },
            suffix = "条",
            range = 10..100
        )

        NumberInputPreference(
            title = "数据保留天数",
            description = "学习数据的保留时间",
            value = config.dataRetentionDays,
            onValueChange = { onConfigChange(config.copy(dataRetentionDays = it)) },
            suffix = "天",
            range = 7..90
        )

        ClickablePreference(
            title = "查看学习数据",
            description = "查看已学习的应用内存使用数据",
            onClick = {
                // TODO: 导航到学习数据界面
                // navController.navigate("memory_learning_data")
            }
        )

        ClickablePreference(
            title = "重置学习数据",
            description = "清除所有学习数据，重新开始学习",
            onClick = {
                // TODO: 显示重置确认对话框
            }
        )
    }
}

/**
 * 混合模式高级配置
 */
@Composable
private fun HybridAdvancedConfig(
    config: HybridConfig,
    onConfigChange: (HybridConfig) -> Unit
) {
    PreferenceCategory("混合策略配置") {
        SwitchPreference(
            title = "启用事件驱动",
            description = "结合事件驱动检测策略",
            checked = config.enableEventDriven,
            onCheckedChange = { onConfigChange(config.copy(enableEventDriven = it)) }
        )

        SwitchPreference(
            title = "启用自适应检测",
            description = "结合自适应检测策略",
            checked = config.enableAdaptive,
            onCheckedChange = { onConfigChange(config.copy(enableAdaptive = it)) }
        )

        SwitchPreference(
            title = "启用智能学习",
            description = "结合智能学习策略",
            checked = config.enableIntelligent,
            onCheckedChange = { onConfigChange(config.copy(enableIntelligent = it)) }
        )
    }

    PreferenceCategory("策略权重") {
        FloatInputPreference(
            title = "事件驱动权重",
            description = "事件驱动策略在混合模式中的权重",
            value = config.eventDrivenWeight,
            onValueChange = { onConfigChange(config.copy(eventDrivenWeight = it)) },
            suffix = "%",
            range = 0.0f..1.0f
        )

        FloatInputPreference(
            title = "自适应权重",
            description = "自适应策略在混合模式中的权重",
            value = config.adaptiveWeight,
            onValueChange = { onConfigChange(config.copy(adaptiveWeight = it)) },
            suffix = "%",
            range = 0.0f..1.0f
        )

        FloatInputPreference(
            title = "智能学习权重",
            description = "智能学习策略在混合模式中的权重",
            value = config.intelligentWeight,
            onValueChange = { onConfigChange(config.copy(intelligentWeight = it)) },
            suffix = "%",
            range = 0.0f..1.0f
        )
    }
}

/**
 * 高级内存配置界面内容组件
 *
 * 不依赖NavController的可复用组件，用于Activity中显示高级内存配置界面。
 * 通过回调函数处理导航和配置完成事件。
 *
 * @param mode 内存检测模式
 * @param conditionId 条件ID
 * @param onNavigateBack 返回回调
 * @param onNavigateToMemoryLearningData 导航到内存学习数据界面回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdvancedMemoryConfigActivityContent(
    mode: MemoryCheckMode,
    conditionId: String,
    onNavigateBack: () -> Unit,
    onNavigateToMemoryLearningData: () -> Unit
) {
    val context = LocalContext.current

    // 加载配置
    var config by remember {
        mutableStateOf(loadAdvancedConfig(context, mode, conditionId))
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text("高级内存配置 - ${mode.displayName}")
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            saveAdvancedConfig(context, mode, conditionId, config)
                            onNavigateBack()
                        }
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                when (mode) {
                    MemoryCheckMode.TRADITIONAL -> {
                        // 传统模式不需要高级配置，显示说明信息
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = "传统定时检测模式",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "传统模式使用固定间隔进行内存检测，配置简单，适合基础使用场景。高级配置请在基础设置中调整检测频率。",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                    MemoryCheckMode.EVENT_DRIVEN -> {
                        EventDrivenAdvancedConfigContent(
                            config = config as EventDrivenConfig,
                            onConfigChange = { config = it }
                        )
                    }
                    MemoryCheckMode.ADAPTIVE -> {
                        AdaptiveAdvancedConfigContent(
                            config = config as AdaptiveConfig,
                            onConfigChange = { config = it }
                        )
                    }
                    MemoryCheckMode.INTELLIGENT -> {
                        IntelligentAdvancedConfigContent(
                            config = config as IntelligentConfig,
                            onConfigChange = { config = it },
                            onNavigateToMemoryLearningData = onNavigateToMemoryLearningData
                        )
                    }
                    MemoryCheckMode.HYBRID -> {
                        HybridAdvancedConfigContent(
                            config = config as HybridConfig,
                            onConfigChange = { config = it }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 事件驱动模式高级配置内容组件
 */
@Composable
private fun EventDrivenAdvancedConfigContent(
    config: EventDrivenConfig,
    onConfigChange: (EventDrivenConfig) -> Unit
) {
    EventDrivenAdvancedConfig(
        config = config,
        onConfigChange = onConfigChange
    )
}

/**
 * 自适应模式高级配置内容组件
 */
@Composable
private fun AdaptiveAdvancedConfigContent(
    config: AdaptiveConfig,
    onConfigChange: (AdaptiveConfig) -> Unit
) {
    AdaptiveAdvancedConfig(
        config = config,
        onConfigChange = onConfigChange
    )
}

/**
 * 智能学习模式高级配置内容组件
 */
@Composable
private fun IntelligentAdvancedConfigContent(
    config: IntelligentConfig,
    onConfigChange: (IntelligentConfig) -> Unit,
    onNavigateToMemoryLearningData: () -> Unit
) {
    IntelligentAdvancedConfigWithNavigation(
        config = config,
        onConfigChange = onConfigChange,
        onNavigateToMemoryLearningData = onNavigateToMemoryLearningData
    )
}

/**
 * 混合模式高级配置内容组件
 */
@Composable
private fun HybridAdvancedConfigContent(
    config: HybridConfig,
    onConfigChange: (HybridConfig) -> Unit
) {
    HybridAdvancedConfig(
        config = config,
        onConfigChange = onConfigChange
    )
}

/**
 * 智能学习模式高级配置（带导航功能）
 */
@Composable
private fun IntelligentAdvancedConfigWithNavigation(
    config: IntelligentConfig,
    onConfigChange: (IntelligentConfig) -> Unit,
    onNavigateToMemoryLearningData: () -> Unit
) {
    PreferenceCategory("学习配置") {
        SwitchPreference(
            title = "启用智能学习",
            description = "根据历史数据智能预测应用内存使用情况",
            checked = config.enableLearning,
            onCheckedChange = { onConfigChange(config.copy(enableLearning = it)) }
        )

        NumberInputPreference(
            title = "最小样本数",
            description = "进行预测所需的最小学习样本数",
            value = config.minSamplesForPrediction,
            onValueChange = { onConfigChange(config.copy(minSamplesForPrediction = it)) },
            suffix = "个",
            range = 3..20
        )

        FloatInputPreference(
            title = "置信度阈值",
            description = "预测结果的最低置信度要求",
            value = config.confidenceThreshold,
            onValueChange = { onConfigChange(config.copy(confidenceThreshold = it)) },
            suffix = "",
            range = 0.5f..0.95f
        )
    }

    PreferenceCategory("稳定性检测") {
        NumberInputPreference(
            title = "稳定性检测间隔",
            description = "检测内存稳定性的时间间隔",
            value = config.stabilityCheckInterval,
            onValueChange = { onConfigChange(config.copy(stabilityCheckInterval = it)) },
            suffix = "秒",
            range = 1..10
        )

        NumberInputPreference(
            title = "稳定性阈值",
            description = "判断内存稳定的变化阈值",
            value = config.stabilityThresholdMB,
            onValueChange = { onConfigChange(config.copy(stabilityThresholdMB = it)) },
            suffix = "MB",
            range = 10..200
        )

        NumberInputPreference(
            title = "稳定检测次数",
            description = "连续稳定检测的最少次数",
            value = config.requiredStableChecks,
            onValueChange = { onConfigChange(config.copy(requiredStableChecks = it)) },
            suffix = "次",
            range = 2..10
        )
    }

    PreferenceCategory("数据管理") {
        NumberInputPreference(
            title = "最大历史记录数",
            description = "每个应用保留的最大学习记录数",
            value = config.maxHistoryRecords,
            onValueChange = { onConfigChange(config.copy(maxHistoryRecords = it)) },
            suffix = "条",
            range = 10..100
        )

        NumberInputPreference(
            title = "数据保留天数",
            description = "学习数据的保留时间",
            value = config.dataRetentionDays,
            onValueChange = { onConfigChange(config.copy(dataRetentionDays = it)) },
            suffix = "天",
            range = 7..90
        )

        ClickablePreference(
            title = "查看学习数据",
            description = "查看已学习的应用内存使用数据",
            onClick = onNavigateToMemoryLearningData
        )

        ClickablePreference(
            title = "重置学习数据",
            description = "清除所有学习数据，重新开始学习",
            onClick = {
                // TODO: 显示重置确认对话框
            }
        )
    }
}
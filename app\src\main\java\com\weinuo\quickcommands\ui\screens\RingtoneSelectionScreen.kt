package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.utils.RingtoneHelper
import com.weinuo.quickcommands.utils.RingtonesCacheManager
import kotlinx.coroutines.launch

/**
 * 铃声选择模式
 */
enum class RingtoneSelectionMode {
    SINGLE  // 单选模式
}

/**
 * 可复用的铃声选择界面
 *
 * 提供铃声列表选择界面，支持预览功能和单选模式
 * 使用全屏界面替代对话框，提供更好的用户体验
 * 可以被多个配置提供器复用，避免代码重复
 * 使用按需加载策略和智能缓存，避免不必要的电量消耗
 *
 * @param ringtoneType 铃声类型（电话铃声/通知铃声/闹钟铃声）
 * @param selectionMode 选择模式（目前只支持单选）
 * @param initialSelectedRingtoneUri 初始选中的铃声URI
 * @param onRingtoneSelected 铃声选择完成回调，返回选中的铃声信息
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RingtoneSelectionScreen(
    ringtoneType: RingtoneHelper.RingtoneType,
    selectionMode: RingtoneSelectionMode = RingtoneSelectionMode.SINGLE,
    initialSelectedRingtoneUri: String = "",
    onRingtoneSelected: (RingtoneHelper.RingtoneInfo) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // 状态管理
    var ringtones by remember { mutableStateOf<List<RingtoneHelper.RingtoneInfo>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var selectedRingtoneUri by remember { mutableStateOf(initialSelectedRingtoneUri) }
    var playingRingtoneId by remember { mutableStateOf<String?>(null) }

    // 加载铃声列表 - 使用按需加载和智能缓存
    LaunchedEffect(ringtoneType) {
        coroutineScope.launch {
            try {
                isLoading = true
                errorMessage = null

                val loadedRingtones = RingtonesCacheManager.getRingtones(context, ringtoneType)
                val defaultRingtone = RingtoneHelper.getDefaultRingtone(context, ringtoneType)

                // 将默认铃声添加到列表开头
                ringtones = if (defaultRingtone != null) {
                    listOf(defaultRingtone) + loadedRingtones
                } else {
                    loadedRingtones
                }

                // 如果没有初始选中的铃声，选择默认铃声
                if (selectedRingtoneUri.isEmpty() && defaultRingtone != null) {
                    selectedRingtoneUri = defaultRingtone.uriString
                }

            } catch (e: Exception) {
                errorMessage = "加载铃声列表失败: ${e.message}"
            } finally {
                isLoading = false
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("选择${ringtoneType.displayName}") },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 确认按钮
                    TextButton(
                        onClick = {
                            val selectedRingtone = ringtones.find { it.uriString == selectedRingtoneUri }
                            if (selectedRingtone != null) {
                                onRingtoneSelected(selectedRingtone)
                            }
                        },
                        enabled = selectedRingtoneUri.isNotEmpty()
                    ) {
                        Text("确认")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text(
                                text = "正在加载铃声列表...",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                errorMessage != null -> {
                    // 错误状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                            Button(
                                onClick = {
                                    // 重新加载 - 强制刷新缓存
                                    coroutineScope.launch {
                                        try {
                                            isLoading = true
                                            errorMessage = null
                                            // 清除缓存并重新加载
                                            RingtonesCacheManager.clearCache()
                                            val loadedRingtones = RingtonesCacheManager.getRingtones(context, ringtoneType)
                                            val defaultRingtone = RingtoneHelper.getDefaultRingtone(context, ringtoneType)

                                            ringtones = if (defaultRingtone != null) {
                                                listOf(defaultRingtone) + loadedRingtones
                                            } else {
                                                loadedRingtones
                                            }
                                        } catch (e: Exception) {
                                            errorMessage = "加载铃声列表失败: ${e.message}"
                                        } finally {
                                            isLoading = false
                                        }
                                    }
                                }
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }

                ringtones.isEmpty() -> {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "没有找到${ringtoneType.displayName}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                else -> {
                    // 铃声列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(ringtones) { ringtone ->
                            RingtoneItem(
                                ringtone = ringtone,
                                isSelected = ringtone.uriString == selectedRingtoneUri,
                                isPlaying = playingRingtoneId == ringtone.id,
                                onRingtoneClick = { clickedRingtone ->
                                    selectedRingtoneUri = clickedRingtone.uriString
                                },
                                onPlayClick = { clickedRingtone ->
                                    if (playingRingtoneId == clickedRingtone.id) {
                                        // 停止播放
                                        RingtoneHelper.stopAllRingtones()
                                        playingRingtoneId = null
                                    } else {
                                        // 开始播放
                                        RingtoneHelper.stopAllRingtones()
                                        if (RingtoneHelper.playRingtone(context, clickedRingtone)) {
                                            playingRingtoneId = clickedRingtone.id
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            RingtoneHelper.stopAllRingtones()
        }
    }
}

/**
 * 铃声项组件
 */
@Composable
private fun RingtoneItem(
    ringtone: RingtoneHelper.RingtoneInfo,
    isSelected: Boolean,
    isPlaying: Boolean,
    onRingtoneClick: (RingtoneHelper.RingtoneInfo) -> Unit,
    onPlayClick: (RingtoneHelper.RingtoneInfo) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onRingtoneClick(ringtone) },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceContainerLow
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 铃声标题
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = ringtone.title,
                    style = MaterialTheme.typography.bodyLarge,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                if (ringtone.id == "default") {
                    Text(
                        text = "系统默认",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
            }

            // 播放/停止按钮
            IconButton(
                onClick = { onPlayClick(ringtone) }
            ) {
                Icon(
                    imageVector = if (isPlaying) Icons.Default.Stop else Icons.Default.PlayArrow,
                    contentDescription = if (isPlaying) "停止播放" else "播放预览",
                    tint = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.primary
                    }
                )
            }

            // 选中状态指示器
            if (isSelected) {
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Check,
                        contentDescription = "已选中",
                        tint = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 连接任务存储适配器
 *
 * 负责ConnectivityTask的原生数据类型存储和重建。
 * 将复杂的连接任务对象拆分为独立的原生字段进行存储，
 * 所有字段都使用原生数据类型存储，包括复杂的集合类型。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - Intent相关：intentAction、intentTargetType、intentPackageName、intentClassName、intentData、intentMimeType、intentFlags
 * - 同步账号相关：accountType、accountName、authority、syncExtras
 * - 检查连接相关：checkUrl、checkTimeout、waitForCompletion
 * - 开关控制相关：hotspotOperation、mobileDataOperation、autoSyncOperation、airplaneModeOperation、wifiOperation
 * - 复杂字段：intentParams（原生数据类型存储）
 *
 * 存储格式示例：
 * task_{id}_type = "connectivity"
 * task_{id}_operation = "WIFI_CONTROL"
 * task_{id}_intent_action = "android.intent.action.VIEW"
 * task_{id}_intent_target_type = "ACTIVITY"
 * task_{id}_wifi_operation = "TOGGLE"
 * task_{id}_intent_params_count = 2
 * task_{id}_intent_param_0_name = "key1"
 * task_{id}_intent_param_0_value = "value1"
 * task_{id}_intent_param_0_type = "STRING"
 *
 * @param storageManager 原生类型存储管理器
 */
class ConnectivityTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<ConnectivityTask>(storageManager) {

    companion object {
        private const val TAG = "ConnectivityTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_INTENT_ACTION = "intent_action"
        private const val FIELD_INTENT_TARGET_TYPE = "intent_target_type"
        private const val FIELD_INTENT_PACKAGE_NAME = "intent_package_name"
        private const val FIELD_INTENT_CLASS_NAME = "intent_class_name"
        private const val FIELD_INTENT_DATA = "intent_data"
        private const val FIELD_INTENT_MIME_TYPE = "intent_mime_type"
        private const val FIELD_INTENT_FLAGS = "intent_flags"

        private const val FIELD_ACCOUNT_TYPE = "account_type"
        private const val FIELD_ACCOUNT_NAME = "account_name"
        private const val FIELD_AUTHORITY = "authority"
        private const val FIELD_SYNC_EXTRAS = "sync_extras"
        private const val FIELD_CHECK_URL = "check_url"
        private const val FIELD_CHECK_TIMEOUT = "check_timeout"
        private const val FIELD_WAIT_FOR_COMPLETION = "wait_for_completion"
        private const val FIELD_HOTSPOT_OPERATION = "hotspot_operation"
        private const val FIELD_MOBILE_DATA_OPERATION = "mobile_data_operation"
        private const val FIELD_AUTO_SYNC_OPERATION = "auto_sync_operation"
        private const val FIELD_AIRPLANE_MODE_OPERATION = "airplane_mode_operation"
        private const val FIELD_WIFI_OPERATION = "wifi_operation"

        // 自定义网络状态恢复命令字段
        private const val FIELD_USE_CUSTOM_RESTORE_COMMANDS = "use_custom_restore_commands"
        private const val FIELD_CUSTOM_WIFI_ENABLE_COMMAND = "custom_wifi_enable_command"
        private const val FIELD_CUSTOM_WIFI_DISABLE_COMMAND = "custom_wifi_disable_command"
        private const val FIELD_CUSTOM_MOBILE_DATA_ENABLE_COMMAND = "custom_mobile_data_enable_command"
        private const val FIELD_CUSTOM_MOBILE_DATA_DISABLE_COMMAND = "custom_mobile_data_disable_command"
        private const val FIELD_EFFECTIVE_WIFI_ENABLE_COMMAND = "effective_wifi_enable_command"
        private const val FIELD_EFFECTIVE_WIFI_DISABLE_COMMAND = "effective_wifi_disable_command"
        private const val FIELD_EFFECTIVE_MOBILE_DATA_ENABLE_COMMAND = "effective_mobile_data_enable_command"
        private const val FIELD_EFFECTIVE_MOBILE_DATA_DISABLE_COMMAND = "effective_mobile_data_disable_command"
    }

    override fun getTaskType() = "connectivity"

    /**
     * 保存连接任务
     * 将ConnectivityTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的连接任务
     * @return 操作是否成功
     */
    override fun save(task: ConnectivityTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存连接任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存连接任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),

                // Intent相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_INTENT_ACTION),
                    task.intentAction
                ),
                saveEnum(generateKey(task.id, FIELD_INTENT_TARGET_TYPE), task.intentTargetType),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_INTENT_PACKAGE_NAME),
                    task.intentPackageName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_INTENT_CLASS_NAME),
                    task.intentClassName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_INTENT_DATA),
                    task.intentDataUrl
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_INTENT_MIME_TYPE),
                    task.intentMimeType
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_INTENT_FLAGS),
                    task.intentFlags
                ),

                // 同步账号相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ACCOUNT_TYPE),
                    task.accountType
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ACCOUNT_NAME),
                    task.accountType
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_AUTHORITY),
                    ""
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SYNC_EXTRAS),
                    ""
                ),

                // 检查连接相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_URL),
                    task.checkUrl
                ),
                StorageOperation.createLongOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TIMEOUT),
                    task.checkTimeout.toLong()
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_WAIT_FOR_COMPLETION),
                    task.waitForCompletion
                ),

                // 开关控制相关字段
                saveEnum(generateKey(task.id, FIELD_HOTSPOT_OPERATION), task.hotspotOperation),
                saveEnum(generateKey(task.id, FIELD_MOBILE_DATA_OPERATION), task.mobileDataOperation),
                saveEnum(generateKey(task.id, FIELD_AUTO_SYNC_OPERATION), task.autoSyncOperation),
                saveEnum(generateKey(task.id, FIELD_AIRPLANE_MODE_OPERATION), task.airplaneModeOperation),
                saveEnum(generateKey(task.id, FIELD_WIFI_OPERATION), task.wifiOperation),

                // 自定义网络状态恢复命令字段
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_USE_CUSTOM_RESTORE_COMMANDS),
                    task.useCustomRestoreCommands
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_WIFI_ENABLE_COMMAND),
                    task.customWifiEnableCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_WIFI_DISABLE_COMMAND),
                    task.customWifiDisableCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_MOBILE_DATA_ENABLE_COMMAND),
                    task.customMobileDataEnableCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_MOBILE_DATA_DISABLE_COMMAND),
                    task.customMobileDataDisableCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EFFECTIVE_WIFI_ENABLE_COMMAND),
                    task.effectiveWifiEnableCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EFFECTIVE_WIFI_DISABLE_COMMAND),
                    task.effectiveWifiDisableCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EFFECTIVE_MOBILE_DATA_ENABLE_COMMAND),
                    task.effectiveMobileDataEnableCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EFFECTIVE_MOBILE_DATA_DISABLE_COMMAND),
                    task.effectiveMobileDataDisableCommand
                )
            ))

            // 保存复杂字段（Intent参数列表）
            operations.addAll(saveComplexFields(task.id, task))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "连接任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 保存复杂字段（Intent参数列表）- 使用原生数据类型存储
     */
    private fun saveComplexFields(taskId: String, task: ConnectivityTask): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        try {
            // 保存Intent参数数量
            operations.add(StorageOperation.createIntOperation(
                StorageDomain.TASKS,
                generateKey(taskId, "intent_params_count"),
                task.intentParams.size
            ))

            // 分别保存每个Intent参数的字段
            task.intentParams.forEachIndexed { index, param ->
                val prefix = "intent_param_${index}_"
                operations.add(StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(taskId, "${prefix}name"),
                    param.name
                ))
                operations.add(StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(taskId, "${prefix}value"),
                    param.value
                ))
                operations.add(StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(taskId, "${prefix}type"),
                    param.type.name
                ))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error saving complex fields for task: $taskId", e)
        }

        return operations
    }

    /**
     * 加载连接任务
     * 从原生数据类型重建ConnectivityTask对象
     *
     * @param taskId 任务ID
     * @return 加载的连接任务，失败时返回null
     */
    override fun load(taskId: String): ConnectivityTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载连接任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "连接任务不存在: $taskId")
                return null
            }

            // 加载复杂字段
            val complexFields = loadComplexFields(taskId)

            ConnectivityTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { ConnectivityOperation.valueOf(it) }
                    ?: ConnectivityOperation.WIFI_CONTROL,

                // Intent相关字段
                intentAction = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_INTENT_ACTION),
                    ""
                ),
                intentTargetType = loadEnum(generateKey(taskId, FIELD_INTENT_TARGET_TYPE)) { IntentTargetType.valueOf(it) }
                    ?: IntentTargetType.ACTIVITY,
                intentPackageName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_INTENT_PACKAGE_NAME),
                    ""
                ),
                intentClassName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_INTENT_CLASS_NAME),
                    ""
                ),
                intentDataUrl = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_INTENT_DATA),
                    ""
                ),
                intentMimeType = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_INTENT_MIME_TYPE),
                    ""
                ),
                intentFlags = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_INTENT_FLAGS),
                    0
                ),
                intentParams = complexFields.intentParams,

                // 同步账号相关字段
                accountType = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ACCOUNT_TYPE),
                    ""
                ),

                // 检查连接相关字段
                checkUrl = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_URL),
                    ""
                ),
                checkTimeout = storageManager.loadLong(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TIMEOUT),
                    5000L
                ).toInt(),
                waitForCompletion = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_WAIT_FOR_COMPLETION),
                    false
                ),

                // 开关控制相关字段
                hotspotOperation = loadEnum(generateKey(taskId, FIELD_HOTSPOT_OPERATION)) { SwitchOperation.valueOf(it) }
                    ?: SwitchOperation.TOGGLE,
                mobileDataOperation = loadEnum(generateKey(taskId, FIELD_MOBILE_DATA_OPERATION)) { SwitchOperation.valueOf(it) }
                    ?: SwitchOperation.TOGGLE,
                autoSyncOperation = loadEnum(generateKey(taskId, FIELD_AUTO_SYNC_OPERATION)) { SwitchOperation.valueOf(it) }
                    ?: SwitchOperation.TOGGLE,
                airplaneModeOperation = loadEnum(generateKey(taskId, FIELD_AIRPLANE_MODE_OPERATION)) { SwitchOperation.valueOf(it) }
                    ?: SwitchOperation.TOGGLE,
                wifiOperation = loadEnum(generateKey(taskId, FIELD_WIFI_OPERATION)) { SwitchOperation.valueOf(it) }
                    ?: SwitchOperation.TOGGLE,

                // 自定义网络状态恢复命令字段
                useCustomRestoreCommands = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_USE_CUSTOM_RESTORE_COMMANDS),
                    false
                ),
                customWifiEnableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_WIFI_ENABLE_COMMAND)
                ),
                customWifiDisableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_WIFI_DISABLE_COMMAND)
                ),
                customMobileDataEnableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_MOBILE_DATA_ENABLE_COMMAND)
                ),
                customMobileDataDisableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_MOBILE_DATA_DISABLE_COMMAND)
                ),
                effectiveWifiEnableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EFFECTIVE_WIFI_ENABLE_COMMAND)
                ).ifEmpty { "svc wifi enable" },
                effectiveWifiDisableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EFFECTIVE_WIFI_DISABLE_COMMAND)
                ).ifEmpty { "svc wifi disable" },
                effectiveMobileDataEnableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EFFECTIVE_MOBILE_DATA_ENABLE_COMMAND)
                ).ifEmpty { "svc data enable" },
                effectiveMobileDataDisableCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EFFECTIVE_MOBILE_DATA_DISABLE_COMMAND)
                ).ifEmpty { "svc data disable" }
            ).also {
                Log.d(TAG, "连接任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }

    /**
     * 加载复杂字段（Intent参数列表）- 使用原生数据类型存储
     */
    private fun loadComplexFields(taskId: String): ComplexFields {
        val intentParams = mutableListOf<IntentParam>()

        try {
            // 加载Intent参数数量
            val paramsCount = storageManager.loadInt(
                StorageDomain.TASKS,
                generateKey(taskId, "intent_params_count"),
                0
            )

            // 分别加载每个Intent参数的字段
            for (index in 0 until paramsCount) {
                val prefix = "intent_param_${index}_"
                val name = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, "${prefix}name"),
                    ""
                )
                val value = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, "${prefix}value"),
                    ""
                )
                val typeString = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, "${prefix}type"),
                    IntentParamType.STRING.name
                )

                if (name.isNotEmpty()) {
                    val type = try {
                        IntentParamType.valueOf(typeString)
                    } catch (e: IllegalArgumentException) {
                        IntentParamType.STRING
                    }

                    intentParams.add(IntentParam(
                        name = name,
                        value = value,
                        type = type
                    ))
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error loading complex fields for task: $taskId", e)
        }

        return ComplexFields(intentParams)
    }

    /**
     * 复杂字段数据类
     */
    private data class ComplexFields(
        val intentParams: List<IntentParam>
    )
}

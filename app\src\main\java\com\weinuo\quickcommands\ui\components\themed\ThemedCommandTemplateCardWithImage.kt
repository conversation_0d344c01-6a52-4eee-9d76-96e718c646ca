package com.weinuo.quickcommands.ui.components.themed

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.model.CommandTemplate
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.ui.components.themed.ThemedCommandTemplateCard

/**
 * 主题感知的指令模板大图片卡片组件
 *
 * 特点：
 * - 使用大尺寸背景图片
 * - 竖向布局，适配490×580图片比例
 * - 文字叠加在图片上，带渐变遮罩确保可读性
 * - 保持与现有卡片系统的主题一致性
 * - 支持动态样式配置
 *
 * @param template 模板数据（必须包含imageResId）
 * @param onClick 点击回调
 * @param modifier 修饰符
 */
@Composable
fun ThemedCommandTemplateCardWithImage(
    template: CommandTemplate,
    onClick: (CommandTemplate) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取全局设置以使用动态字体大小
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 根据主题获取样式配置
    val cornerRadius = cardStyle.defaultCornerRadius
    val elevation = cardStyle.defaultElevation

    // 所有模板都有图片资源，无需检查

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick(template) },
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent // 使用透明背景，让图片作为背景
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = elevation
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(490f / 580f) // 使用图片的原始宽高比
        ) {
            // 背景图片
            Image(
                painter = painterResource(id = template.imageResId!!),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(cornerRadius)),
                contentScale = ContentScale.Crop
            )



            // 文字内容区域 - 底部长条形遮罩
            Column(
                modifier = Modifier
                    .fillMaxSize(),
                verticalArrangement = Arrangement.Bottom
            ) {
                // 文字背景遮罩 - 长条形半透明背景
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color.Black.copy(alpha = 0.12f), // 非常透明的黑色背景，类似图片中的效果
                            shape = RoundedCornerShape(
                                topStart = 0.dp,
                                topEnd = 0.dp,
                                bottomStart = cornerRadius,
                                bottomEnd = cornerRadius
                            )
                        )
                        .padding(cardStyle.getPaddingValues())
                ) {
                    // 模板标题
                    Text(
                        text = template.getLocalizedTitle(context),
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontSize = globalSettings.cardTitleFontSize.sp, // 使用与其他卡片一致的字体大小
                            fontWeight = when (globalSettings.cardTitleFontWeight) {
                                "normal" -> FontWeight.Normal
                                "medium" -> FontWeight.Medium
                                "bold" -> FontWeight.Bold
                                else -> FontWeight.Medium // 使用与其他卡片一致的默认字重
                            }
                        ),
                        color = MaterialTheme.colorScheme.onSurface, // 使用与其他卡片一致的标题颜色
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.height(cardStyle.contentVerticalSpacing))

                    // 模板描述
                    Text(
                        text = template.getLocalizedDescription(context),
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontSize = globalSettings.cardContentFontSize.sp
                        ),
                        color = MaterialTheme.colorScheme.onSurfaceVariant, // 使用与其他卡片一致的内容颜色
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

/**
 * 主题感知的指令模板卡片组件 - 智能选择版本
 *
 * 根据模板是否有图片自动选择使用大图片卡片还是普通文字卡片
 *
 * @param template 模板数据
 * @param onClick 点击回调
 * @param modifier 修饰符
 */
@Composable
fun ThemedCommandTemplateCardAuto(
    template: CommandTemplate,
    onClick: (CommandTemplate) -> Unit,
    modifier: Modifier = Modifier
) {
    if (template.hasImage && template.imageResId != null) {
        // 使用大图片卡片
        ThemedCommandTemplateCardWithImage(
            template = template,
            onClick = onClick,
            modifier = modifier
        )
    } else {
        // 使用普通文字卡片
        ThemedCommandTemplateCard(
            template = template,
            onClick = onClick,
            modifier = modifier
        )
    }
}

package com.weinuo.quickcommands.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import android.graphics.drawable.GradientDrawable
import android.util.TypedValue
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.QuickCommandRepository
import com.weinuo.quickcommands.execution.SharedExecutionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 悬浮按钮服务
 * 提供可配置的白色圆点悬浮按钮，支持拖动、大小和透明度设置
 */
class FloatingButtonService : Service() {

    companion object {
        private const val TAG = "FloatingButtonService"
        private const val EXTRA_COMMAND_ID = "command_id"
        private const val EXTRA_BUTTON_SIZE = "button_size"
        private const val EXTRA_BUTTON_ALPHA = "button_alpha"
        private const val EXTRA_SHOW_TEXT = "show_text"
        private const val EXTRA_BUTTON_TEXT = "button_text"

        /**
         * 启动悬浮按钮服务
         */
        fun startService(context: Context, commandId: String, buttonSize: Int, buttonAlpha: Float, showText: Boolean = false, buttonText: String = "") {
            val intent = Intent(context, FloatingButtonService::class.java).apply {
                putExtra(EXTRA_COMMAND_ID, commandId)
                putExtra(EXTRA_BUTTON_SIZE, buttonSize)
                putExtra(EXTRA_BUTTON_ALPHA, buttonAlpha)
                putExtra(EXTRA_SHOW_TEXT, showText)
                putExtra(EXTRA_BUTTON_TEXT, buttonText)
            }
            context.startService(intent)
        }

        /**
         * 停止悬浮按钮服务
         */
        fun stopService(context: Context, commandId: String) {
            val intent = Intent(context, FloatingButtonService::class.java).apply {
                putExtra(EXTRA_COMMAND_ID, commandId)
                action = "STOP"
            }
            context.startService(intent)
        }
    }

    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var commandId: String? = null
    private var buttonSize: Int = 60
    private var buttonAlpha: Float = 0.8f
    private var showText: Boolean = false
    private var buttonText: String = ""

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private lateinit var quickCommandRepository: QuickCommandRepository
    private lateinit var executionHandler: SharedExecutionHandler

    // 拖动相关变量
    private var initialX: Int = 0
    private var initialY: Int = 0
    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "FloatingButtonService created")

        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        quickCommandRepository = QuickCommandRepository.getInstance(applicationContext)
        executionHandler = SharedExecutionHandler(applicationContext)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let { handleIntent(it) }
        return START_STICKY
    }

    private fun handleIntent(intent: Intent) {
        val action = intent.action
        val newCommandId = intent.getStringExtra(EXTRA_COMMAND_ID)

        if (action == "STOP") {
            if (newCommandId == commandId) {
                removeFloatingButton()
                stopSelf()
            }
            return
        }

        // 注意：调用此服务前应确保已有悬浮窗权限

        // 获取参数
        commandId = newCommandId
        buttonSize = intent.getIntExtra(EXTRA_BUTTON_SIZE, 60)
        buttonAlpha = intent.getFloatExtra(EXTRA_BUTTON_ALPHA, 0.8f)
        showText = intent.getBooleanExtra(EXTRA_SHOW_TEXT, false)
        buttonText = intent.getStringExtra(EXTRA_BUTTON_TEXT) ?: ""

        // 验证参数范围
        buttonSize = buttonSize.coerceIn(20, 200)
        buttonAlpha = buttonAlpha.coerceIn(0.1f, 1.0f)

        Log.d(TAG, "Creating floating button for command: $commandId, size: $buttonSize, alpha: $buttonAlpha")

        // 创建悬浮按钮
        createFloatingButton()
    }

    private fun createFloatingButton() {
        removeFloatingButton() // 先移除现有的悬浮按钮

        // 创建悬浮按钮视图
        val floatingButton = if (showText && buttonText.isNotEmpty()) {
            // 创建带文字的悬浮按钮
            createTextFloatingButton()
        } else {
            // 创建普通的圆形悬浮按钮
            createCircleFloatingButton()
        }

        // 计算按钮大小（dp转px）
        val density = resources.displayMetrics.density
        val sizeInPx = (buttonSize * density).toInt()

        // 统一使用固定圆形尺寸，确保所有悬浮按钮都保持完美圆形
        val params = WindowManager.LayoutParams(
            sizeInPx,
            sizeInPx,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 100
            y = 100
        }

        try {
            windowManager?.addView(floatingButton, params)
            floatingView = floatingButton
            Log.d(TAG, "Floating button created successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create floating button", e)
            stopSelf()
        }
    }

    /**
     * 创建圆形悬浮按钮
     */
    private fun createCircleFloatingButton(): View {
        return ImageView(this).apply {
            // 设置白色圆形背景
            setImageResource(R.drawable.ic_circle_white)
            scaleType = ImageView.ScaleType.CENTER_CROP
            alpha = buttonAlpha

            // 设置点击监听器
            setOnClickListener {
                executeCommand()
            }

            // 设置触摸监听器（用于拖动）
            setOnTouchListener(createTouchListener())
        }
    }

    /**
     * 创建带文字的悬浮按钮
     * 使用固定圆形尺寸，通过动态字体缩放和文字截断确保文字在圆形内正确显示
     */
    private fun createTextFloatingButton(): View {
        return TextView(this).apply {
            text = buttonText
            alpha = buttonAlpha

            // 设置文字样式
            setTextColor(ContextCompat.getColor(this@FloatingButtonService, android.R.color.black))
            gravity = Gravity.CENTER

            // 根据文字长度动态调整字体大小
            val textSize = when {
                buttonText.length <= 2 -> 14f
                buttonText.length <= 4 -> 12f
                buttonText.length <= 6 -> 10f
                else -> 8f
            }
            setTextSize(TypedValue.COMPLEX_UNIT_SP, textSize)

            // 设置文字截断，确保超长文字不会溢出
            setSingleLine(true)
            ellipsize = android.text.TextUtils.TruncateAt.END

            // 设置圆形背景，确保cornerRadius计算正确
            val drawable = GradientDrawable().apply {
                setColor(ContextCompat.getColor(this@FloatingButtonService, android.R.color.white))
                cornerRadius = (buttonSize / 2 * resources.displayMetrics.density)
                setStroke(2, ContextCompat.getColor(this@FloatingButtonService, android.R.color.darker_gray))
            }
            background = drawable

            // 设置内边距，确保文字在圆形内居中
            val padding = (buttonSize * 0.15 * resources.displayMetrics.density).toInt()
            setPadding(padding, padding, padding, padding)

            // 设置点击监听器
            setOnClickListener {
                executeCommand()
            }

            // 设置触摸监听器（用于拖动）
            setOnTouchListener(createTouchListener())
        }
    }

    private fun createTouchListener(): View.OnTouchListener {
        return View.OnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 记录初始位置
                    val params = view.layoutParams as WindowManager.LayoutParams
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    // 计算新位置
                    val params = view.layoutParams as WindowManager.LayoutParams
                    params.x = initialX + (event.rawX - initialTouchX).toInt()
                    params.y = initialY + (event.rawY - initialTouchY).toInt()

                    // 更新悬浮窗位置
                    windowManager?.updateViewLayout(view, params)
                    true
                }
                MotionEvent.ACTION_UP -> {
                    // 判断是点击还是拖动
                    val deltaX = Math.abs(event.rawX - initialTouchX)
                    val deltaY = Math.abs(event.rawY - initialTouchY)

                    if (deltaX < 10 && deltaY < 10) {
                        // 小范围移动，视为点击
                        view.performClick()
                    }
                    true
                }
                else -> false
            }
        }
    }

    private fun executeCommand() {
        val currentCommandId = commandId ?: return

        serviceScope.launch {
            try {
                // 从内存中的数据流获取快捷指令
                val quickCommand = quickCommandRepository.quickCommands.value.find { it.id == currentCommandId }
                if (quickCommand != null) {
                    // 使用手动执行方法（跳过触发条件检查）
                    executionHandler.executeQuickCommandManually(
                        command = quickCommand,
                        onExecutionCompleted = {
                        },
                        onExecutionAborted = { abortConditions ->
                        }
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing command", e)
            }
        }
    }

    private fun removeFloatingButton() {
        floatingView?.let { view ->
            try {
                windowManager?.removeView(view)
                Log.d(TAG, "Floating button removed")
            } catch (e: Exception) {
                Log.e(TAG, "Error removing floating button", e)
            }
        }
        floatingView = null
    }

    override fun onDestroy() {
        super.onDestroy()
        removeFloatingButton()
        Log.d(TAG, "FloatingButtonService destroyed")
    }

    override fun onBind(intent: Intent?): IBinder? = null
}

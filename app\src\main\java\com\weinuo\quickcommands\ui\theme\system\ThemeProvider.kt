package com.weinuo.quickcommands.ui.theme.system

import androidx.compose.material3.ColorScheme
import com.weinuo.quickcommands.ui.theme.config.BlurConfiguration
import com.weinuo.quickcommands.ui.screens.ScreenFactory

/**
 * 主题提供者接口
 *
 * 每个主题必须实现此接口，提供完整的主题配置
 * 这是主题系统的核心抽象，确保所有主题都能提供一致的配置接口
 */
interface ThemeProvider {
    /**
     * 获取颜色方案
     *
     * @return Material Design 3 ColorScheme
     */
    fun getColorScheme(): ColorScheme

    /**
     * 获取组件工厂
     *
     * @return 用于创建主题特定UI组件的工厂
     */
    fun getComponentFactory(): ComponentFactory

    /**
     * 获取样式配置
     *
     * @return 包含圆角、间距、高度等样式配置
     */
    fun getStyleConfiguration(): StyleConfiguration

    /**
     * 获取交互配置
     *
     * @return 包含点击、悬停、焦点等交互状态配置
     */
    fun getInteractionConfiguration(): InteractionConfiguration

    /**
     * 获取动画配置
     *
     * @return 包含过渡动画、持续时间等动画配置
     */
    fun getAnimationConfiguration(): AnimationConfiguration

    /**
     * 获取模糊配置
     *
     * @return 包含模糊效果的支持和默认设置
     */
    fun getBlurConfiguration(): BlurConfiguration

    /**
     * 获取界面工厂
     *
     * @return 用于创建主题特定界面实现的工厂
     */
    fun getScreenFactory(): ScreenFactory

    /**
     * 获取主题的设计方法
     *
     * @return 此主题采用的设计理念
     */
    fun getDesignApproach(): DesignApproach

    /**
     * 获取主题的唯一标识符
     *
     * @return 主题ID，用于持久化存储
     */
    fun getThemeId(): String

    /**
     * 获取主题的显示名称
     *
     * @return 用户界面中显示的主题名称
     */
    fun getDisplayName(): String

    /**
     * 检查主题是否支持特定功能
     *
     * @param feature 要检查的功能
     * @return 是否支持该功能
     */
    fun supportsFeature(feature: ThemeFeature): Boolean

    /**
     * 获取主题的版本信息
     *
     * @return 主题版本，用于兼容性检查
     */
    fun getVersion(): String {
        return "1.0.0"
    }

    /**
     * 初始化主题
     *
     * 在主题被激活时调用，用于执行必要的初始化操作
     */
    fun initialize() {
        // 默认实现为空，子类可以重写
    }

    /**
     * 清理主题资源
     *
     * 在主题被切换时调用，用于清理资源
     */
    fun cleanup() {
        // 默认实现为空，子类可以重写
    }
}

/**
 * 主题功能枚举
 *
 * 定义主题可能支持的各种功能特性
 */
enum class ThemeFeature {
    /**
     * 模糊效果支持
     */
    BLUR_EFFECTS,

    /**
     * 阴影效果支持
     */
    SHADOW_EFFECTS,

    /**
     * 透明度支持
     */
    TRANSPARENCY,

    /**
     * 动态颜色支持
     */
    DYNAMIC_COLORS,

    /**
     * 自定义动画支持
     */
    CUSTOM_ANIMATIONS,

    /**
     * 渐变效果支持
     */
    GRADIENT_EFFECTS,

    /**
     * 自适应布局支持
     */
    ADAPTIVE_LAYOUT,

    /**
     * 深色模式支持
     */
    DARK_MODE,

    /**
     * 高对比度支持
     */
    HIGH_CONTRAST,

    /**
     * 无障碍功能增强
     */
    ACCESSIBILITY_ENHANCED
}

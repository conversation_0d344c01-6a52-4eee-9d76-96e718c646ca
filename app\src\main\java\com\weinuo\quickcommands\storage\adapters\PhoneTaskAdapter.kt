package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.PhoneTask
import com.weinuo.quickcommands.model.PhoneOperation
import com.weinuo.quickcommands.model.MakeCallType
import com.weinuo.quickcommands.model.SimCardSelection
import com.weinuo.quickcommands.model.AnswerCallDelayType
import com.weinuo.quickcommands.model.ClearCallLogType
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 电话任务存储适配器
 *
 * 负责PhoneTask的原生数据类型存储和重建。
 * 将复杂的电话任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 拨打电话相关：makeCallType、phoneNumber、contactName、simCardSelection
 * - 联系人列表：contactIds（通过字符串列表存储）
 * - 接听延迟相关：answerDelayType、answerDelaySeconds
 * - 清除记录相关：clearLogType
 * - 铃声相关：selectedRingtoneUri、selectedRingtoneName
 *
 * 存储格式示例：
 * task_{id}_type = "phone"
 * task_{id}_operation = "MAKE_CALL"
 * task_{id}_make_call_type = "MANUAL_INPUT"
 * task_{id}_phone_number = "13800138000"
 * task_{id}_contact_name = "张三"
 * task_{id}_sim_card_selection = "SIM1"
 * task_{id}_contact_ids_count = 2
 * task_{id}_contact_ids_0 = "contact_id_1"
 * task_{id}_contact_ids_1 = "contact_id_2"
 *
 * @param storageManager 原生类型存储管理器
 */
class PhoneTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<PhoneTask>(storageManager) {

    companion object {
        private const val TAG = "PhoneTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_MAKE_CALL_TYPE = "make_call_type"
        private const val FIELD_PHONE_NUMBER = "phone_number"
        private const val FIELD_CONTACT_NAME = "contact_name"
        private const val FIELD_CONTACT_IDS = "contact_ids"
        private const val FIELD_SIM_CARD_SELECTION = "sim_card_selection"
        private const val FIELD_ANSWER_DELAY_TYPE = "answer_delay_type"
        private const val FIELD_ANSWER_DELAY_SECONDS = "answer_delay_seconds"
        private const val FIELD_CLEAR_LOG_TYPE = "clear_log_type"
        private const val FIELD_SELECTED_RINGTONE_URI = "selected_ringtone_uri"
        private const val FIELD_SELECTED_RINGTONE_NAME = "selected_ringtone_name"
    }

    override fun getTaskType() = "phone"

    /**
     * 保存电话任务
     * 将PhoneTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的电话任务
     * @return 操作是否成功
     */
    override fun save(task: PhoneTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存电话任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存电话任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),
                saveEnum(generateKey(task.id, FIELD_MAKE_CALL_TYPE), task.makeCallType),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_PHONE_NUMBER),
                    task.phoneNumber
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CONTACT_NAME),
                    task.contactName
                ),
                saveEnum(generateKey(task.id, FIELD_SIM_CARD_SELECTION), task.simCardSelection),
                saveEnum(generateKey(task.id, FIELD_ANSWER_DELAY_TYPE), task.answerDelayType),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ANSWER_DELAY_SECONDS),
                    task.answerDelaySeconds
                ),
                saveEnum(generateKey(task.id, FIELD_CLEAR_LOG_TYPE), task.clearLogType),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_URI),
                    task.selectedRingtoneUri
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_NAME),
                    task.selectedRingtoneName
                )
            ))

            // 保存联系人ID列表
            operations.addAll(saveStringList(generateKey(task.id, FIELD_CONTACT_IDS), task.contactIds))

            // 执行批量存储操作
            val success = storageManager.executeBatch(operations)

            if (success) {
                logSaveSuccess(task.id)
            } else {
                logSaveError(task.id, "Failed to execute batch operations")
            }

            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception: ${e.message}")
            Log.e(TAG, "Exception while saving PhoneTask: ${task.id}", e)
            false
        }
    }

    /**
     * 加载电话任务
     * 从原生数据类型重建PhoneTask对象
     *
     * @param taskId 任务ID
     * @return 加载的电话任务，失败时返回null
     */
    override fun load(taskId: String): PhoneTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载电话任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "电话任务不存在: $taskId")
                return null
            }

            PhoneTask(
                id = taskId,
                operation = loadEnum(
                    generateKey(taskId, FIELD_OPERATION),
                    PhoneOperation::class.java,
                    PhoneOperation.OPEN_CALL_LOG
                ),
                makeCallType = loadEnum(
                    generateKey(taskId, FIELD_MAKE_CALL_TYPE),
                    MakeCallType::class.java,
                    MakeCallType.MANUAL_INPUT
                ),
                phoneNumber = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_PHONE_NUMBER)
                ),
                contactName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CONTACT_NAME)
                ),
                contactIds = loadStringList(generateKey(taskId, FIELD_CONTACT_IDS)),
                simCardSelection = loadEnum(
                    generateKey(taskId, FIELD_SIM_CARD_SELECTION),
                    SimCardSelection::class.java,
                    SimCardSelection.ASK_EACH_TIME
                ),
                answerDelayType = loadEnum(
                    generateKey(taskId, FIELD_ANSWER_DELAY_TYPE),
                    AnswerCallDelayType::class.java,
                    AnswerCallDelayType.NO_DELAY
                ),
                answerDelaySeconds = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ANSWER_DELAY_SECONDS),
                    0
                ),
                clearLogType = loadEnum(
                    generateKey(taskId, FIELD_CLEAR_LOG_TYPE),
                    ClearCallLogType::class.java,
                    ClearCallLogType.ALL
                ),
                selectedRingtoneUri = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_URI)
                ),
                selectedRingtoneName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_NAME)
                )
            ).also {
                Log.d(TAG, "电话任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception: ${e.message}")
            Log.e(TAG, "Exception while loading PhoneTask: $taskId", e)
            null
        }
    }
}

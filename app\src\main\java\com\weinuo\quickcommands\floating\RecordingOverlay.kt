package com.weinuo.quickcommands.floating

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.WindowManager
import android.widget.FrameLayout
import com.weinuo.quickcommands.model.TouchEvent
import com.weinuo.quickcommands.model.TouchEventType
import com.weinuo.quickcommands.model.TouchPosition
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 透明录制覆盖层
 *
 * 提供全屏透明覆盖，捕获用户手势并转发到下层应用，
 * 同时记录手势用于后续回放。仅在录制时显示。
 * 避免拦截悬浮按钮区域的触摸事件。
 */
class RecordingOverlay(
    private val context: Context,
    private val onTouchEvent: (TouchEvent) -> Unit,
    private val gestureForwarder: GestureForwarder?
) {
    companion object {
        private const val TAG = "RecordingOverlay"
        private const val BUTTON_SIZE = 56 // dp，与悬浮按钮大小一致
        private const val BUTTON_MARGIN = 0 // dp，与悬浮按钮边距一致
        private const val EXCLUSION_PADDING = 8 // dp，排除区域的额外边距
    }

    private var windowManager: WindowManager? = null
    private var overlayView: OverlayView? = null
    private var isShowing = false

    // 手势状态跟踪
    private var gestureStartPoint: Pair<Float, Float>? = null
    private var isCurrentlyDragging = false
    private var gestureStartTime = 0L

    /**
     * 显示透明覆盖层
     */
    fun show(): Boolean {
        if (isShowing) {
            Log.w(TAG, "透明录制覆盖层已显示")
            return true
        }

        try {
            windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            overlayView = OverlayView(context)

            val params = createLayoutParams()
            windowManager?.addView(overlayView, params)

            isShowing = true
            Log.d(TAG, "透明录制覆盖层已显示")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "显示透明录制覆盖层失败", e)
            return false
        }
    }

    /**
     * 隐藏透明覆盖层
     */
    fun hide() {
        if (!isShowing) return

        try {
            overlayView?.let { view ->
                windowManager?.removeView(view)
            }
            isShowing = false
            Log.d(TAG, "透明录制覆盖层已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏透明录制覆盖层失败", e)
        }
    }

    /**
     * 创建窗口布局参数
     */
    private fun createLayoutParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
        }
    }

    /**
     * 透明覆盖视图
     */
    @SuppressLint("ViewConstructor")
    private inner class OverlayView(context: Context) : FrameLayout(context) {

        init {
            // 设置完全透明背景
            setBackgroundColor(0x00000000)
        }

        override fun onTouchEvent(event: MotionEvent): Boolean {
            handleTouchEvent(event)
            // 不拦截事件，让底层应用继续处理
            return false
        }

        override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
            handleTouchEvent(ev)
            // 不拦截事件
            return false
        }
    }

    /**
     * 检查触摸点是否在悬浮按钮区域内
     */
    private fun isInButtonArea(x: Float, y: Float): Boolean {
        val buttonSize = dpToPx(BUTTON_SIZE + EXCLUSION_PADDING * 2)
        val buttonMargin = dpToPx(BUTTON_MARGIN)

        return x >= buttonMargin && x <= buttonMargin + buttonSize &&
               y >= buttonMargin && y <= buttonMargin + buttonSize
    }

    /**
     * 处理触摸事件
     */
    private fun handleTouchEvent(event: MotionEvent) {
        // 如果触摸在悬浮按钮区域内，不处理该事件
        if (isInButtonArea(event.x, event.y)) {
            return
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                gestureStartPoint = Pair(event.x, event.y)
                gestureStartTime = System.currentTimeMillis()
                isCurrentlyDragging = false
            }

            MotionEvent.ACTION_MOVE -> {
                gestureStartPoint?.let { startPoint ->
                    val distance = kotlin.math.sqrt(
                        (event.x - startPoint.first).let { it * it } +
                        (event.y - startPoint.second).let { it * it }
                    )

                    if (distance > 20f && !isCurrentlyDragging) {
                        isCurrentlyDragging = true
                    }
                }
            }

            MotionEvent.ACTION_UP -> {
                gestureStartPoint?.let { startPoint ->
                    val distance = kotlin.math.sqrt(
                        (event.x - startPoint.first).let { it * it } +
                        (event.y - startPoint.second).let { it * it }
                    )

                    val screenWidth = context.resources.displayMetrics.widthPixels
                    val screenHeight = context.resources.displayMetrics.heightPixels

                    if (distance <= 20f) {
                        // 点击事件
                        val position = TouchPosition(
                            startX = startPoint.first / screenWidth.toFloat(),
                            startY = startPoint.second / screenHeight.toFloat()
                        )

                        val touchEvent = TouchEvent(
                            type = TouchEventType.TAP,
                            position = position
                        )

                        onTouchEvent(touchEvent)

                        // 转发手势
                        gestureForwarder?.let { forwarder ->
                            CoroutineScope(Dispatchers.Main).launch {
                                forwarder.forwardGesture(touchEvent)
                            }
                        }
                    } else {
                        // 滑动事件
                        val position = TouchPosition(
                            startX = startPoint.first / screenWidth.toFloat(),
                            startY = startPoint.second / screenHeight.toFloat(),
                            endX = event.x / screenWidth.toFloat(),
                            endY = event.y / screenHeight.toFloat()
                        )

                        val touchEvent = TouchEvent(
                            type = TouchEventType.SWIPE,
                            position = position,
                            duration = 300L
                        )

                        onTouchEvent(touchEvent)

                        // 转发手势
                        gestureForwarder?.let { forwarder ->
                            CoroutineScope(Dispatchers.Main).launch {
                                forwarder.forwardGesture(touchEvent)
                            }
                        }
                    }
                }

                // 重置状态
                gestureStartPoint = null
                isCurrentlyDragging = false
            }
        }
    }

    /**
     * dp转px
     */
    private fun dpToPx(dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }
}

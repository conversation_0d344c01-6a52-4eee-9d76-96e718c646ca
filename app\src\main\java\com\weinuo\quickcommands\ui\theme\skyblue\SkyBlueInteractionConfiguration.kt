package com.weinuo.quickcommands.ui.theme.skyblue

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.system.InteractionConfiguration
import com.weinuo.quickcommands.ui.theme.config.InteractionStateConfig

/**
 * 天空蓝主题交互配置
 *
 * 定义整合设计风格的交互行为和视觉反馈
 * 特点：统一的交互体验，支持多种交互状态
 */
class SkyBlueInteractionConfiguration : InteractionConfiguration {

    /**
     * 悬停状态配置
     */
    override val hover: InteractionStateConfig = InteractionStateConfig(
        backgroundColor = Color(0x0C000000), // interactive_hover - 通用悬停交互式颜色
        contentColor = null,
        borderColor = null,
        elevation = 0.dp, // 整合设计不使用阴影
        scale = 1.0f,
        alpha = 1.0f,
        animationDuration = 150
    )

    /**
     * 按压状态配置
     */
    override val pressed: InteractionStateConfig = InteractionStateConfig(
        backgroundColor = Color(0x19000000), // interactive_pressed - 通用按压交互式颜色
        contentColor = null,
        borderColor = null,
        elevation = 0.dp, // 整合设计不使用阴影
        scale = 0.98f, // 轻微缩放效果
        alpha = 1.0f,
        animationDuration = 150
    )

    /**
     * 焦点状态配置
     */
    override val focused: InteractionStateConfig = InteractionStateConfig(
        backgroundColor = null,
        contentColor = null,
        borderColor = Color(0xFF0A59F7), // interactive_focus - 通用获焦交互式颜色
        elevation = 0.dp,
        scale = 1.0f,
        alpha = 1.0f,
        animationDuration = 150
    )

    /**
     * 激活状态配置
     */
    override val active: InteractionStateConfig = InteractionStateConfig(
        backgroundColor = Color(0xFF0A59F7), // interactive_active - 通用激活交互式颜色
        contentColor = Color(0xFFFFFFFF),
        borderColor = null,
        elevation = 0.dp, // 整合设计不使用阴影
        scale = 1.0f,
        alpha = 1.0f,
        animationDuration = 150
    )

    /**
     * 选择状态配置
     */
    override val selected: InteractionStateConfig = InteractionStateConfig(
        backgroundColor = Color(0x33000000), // interactive_select - 通用选择交互式颜色
        contentColor = Color(0xFF0A59F7), // 品牌色作为选择指示器
        borderColor = null,
        elevation = 0.dp,
        scale = 1.0f,
        alpha = 1.0f,
        animationDuration = 150
    )

    /**
     * 禁用状态配置
     */
    override val disabled: InteractionStateConfig = InteractionStateConfig(
        backgroundColor = null,
        contentColor = null,
        borderColor = null,
        elevation = 0.dp,
        scale = 1.0f,
        alpha = 0.38f, // 禁用透明度
        animationDuration = 150
    )

    /**
     * 拖拽状态配置
     */
    override val dragging: InteractionStateConfig = InteractionStateConfig(
        backgroundColor = null,
        contentColor = null,
        borderColor = null,
        elevation = 0.dp, // 整合设计不使用阴影
        scale = 1.05f, // 轻微放大效果
        alpha = 0.9f,
        animationDuration = 150
    )

    /**
     * 获取特定组件的交互配置
     */
    fun getComponentInteraction(component: ComponentInteractionType): ComponentInteractionConfig {
        return when (component) {
            ComponentInteractionType.BUTTON -> ComponentInteractionConfig(
                rippleColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                hoverColor = hover.backgroundColor ?: androidx.compose.ui.graphics.Color(0x0C000000),
                pressedColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                focusColor = focused.borderColor ?: androidx.compose.ui.graphics.Color(0xFF0A59F7),
                pressedScale = pressed.scale,
                animationDuration = pressed.animationDuration.toLong()
            )
            ComponentInteractionType.CARD -> ComponentInteractionConfig(
                rippleColor = (pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000)).copy(alpha = 0.5f),
                hoverColor = hover.backgroundColor ?: androidx.compose.ui.graphics.Color(0x0C000000),
                pressedColor = (pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000)).copy(alpha = 0.5f),
                focusColor = focused.borderColor ?: androidx.compose.ui.graphics.Color(0xFF0A59F7),
                pressedScale = 0.99f, // 卡片使用更轻微的缩放
                animationDuration = pressed.animationDuration.toLong()
            )
            ComponentInteractionType.LIST_ITEM -> ComponentInteractionConfig(
                rippleColor = (pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000)).copy(alpha = 0.3f),
                hoverColor = hover.backgroundColor ?: androidx.compose.ui.graphics.Color(0x0C000000),
                pressedColor = (pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000)).copy(alpha = 0.3f),
                focusColor = focused.borderColor ?: androidx.compose.ui.graphics.Color(0xFF0A59F7),
                pressedScale = 1.0f, // 列表项不使用缩放
                animationDuration = pressed.animationDuration.toLong()
            )
            ComponentInteractionType.TAB -> ComponentInteractionConfig(
                rippleColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                hoverColor = hover.backgroundColor ?: androidx.compose.ui.graphics.Color(0x0C000000),
                pressedColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                focusColor = focused.borderColor ?: androidx.compose.ui.graphics.Color(0xFF0A59F7),
                pressedScale = 1.0f, // 标签页不使用缩放
                animationDuration = pressed.animationDuration.toLong()
            )
            ComponentInteractionType.CHIP -> ComponentInteractionConfig(
                rippleColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                hoverColor = hover.backgroundColor ?: androidx.compose.ui.graphics.Color(0x0C000000),
                pressedColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                focusColor = focused.borderColor ?: androidx.compose.ui.graphics.Color(0xFF0A59F7),
                pressedScale = 0.95f, // 芯片使用明显的缩放
                animationDuration = pressed.animationDuration.toLong()
            )
            ComponentInteractionType.SWITCH -> ComponentInteractionConfig(
                rippleColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                hoverColor = hover.backgroundColor ?: androidx.compose.ui.graphics.Color(0x0C000000),
                pressedColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                focusColor = focused.borderColor ?: androidx.compose.ui.graphics.Color(0xFF0A59F7),
                pressedScale = 1.0f, // 开关不使用缩放
                animationDuration = 200L // 开关使用稍长的动画
            )
            ComponentInteractionType.SLIDER -> ComponentInteractionConfig(
                rippleColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                hoverColor = hover.backgroundColor ?: androidx.compose.ui.graphics.Color(0x0C000000),
                pressedColor = pressed.backgroundColor ?: androidx.compose.ui.graphics.Color(0x19000000),
                focusColor = focused.borderColor ?: androidx.compose.ui.graphics.Color(0xFF0A59F7),
                pressedScale = 1.1f, // 滑块手柄使用放大效果
                animationDuration = 100L // 滑块使用快速响应
            )
        }
    }
}

/**
 * 组件交互类型枚举
 */
enum class ComponentInteractionType {
    BUTTON,
    CARD,
    LIST_ITEM,
    TAB,
    CHIP,
    SWITCH,
    SLIDER
}

/**
 * 组件交互配置数据类
 */
data class ComponentInteractionConfig(
    val rippleColor: Color,
    val hoverColor: Color,
    val pressedColor: Color,
    val focusColor: Color,
    val pressedScale: Float,
    val animationDuration: Long
)

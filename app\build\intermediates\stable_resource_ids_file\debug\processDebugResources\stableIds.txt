com.weinuo.quickcommands:xml/one_click_command_widget_3_info = 0x7f0d000a
com.weinuo.quickcommands:xml/interface_interaction_accessibility_service_config = 0x7f0d0007
com.weinuo.quickcommands:xml/gesture_recognition_accessibility_service_config = 0x7f0d0006
com.weinuo.quickcommands:xml/device_admin = 0x7f0d0004
com.weinuo.quickcommands:xml/data_extraction_rules = 0x7f0d0003
com.weinuo.quickcommands:xml/backup_rules = 0x7f0d0002
com.weinuo.quickcommands:xml/auto_clicker_accessibility_service_config = 0x7f0d0001
com.weinuo.quickcommands:styleable/Navigator = 0x7f0c000d
com.weinuo.quickcommands:styleable/NavHost = 0x7f0c000b
com.weinuo.quickcommands:styleable/NavDeepLink = 0x7f0c0009
com.weinuo.quickcommands:styleable/NavArgument = 0x7f0c0008
com.weinuo.quickcommands:styleable/NavAction = 0x7f0c0007
com.weinuo.quickcommands:styleable/GradientColor = 0x7f0c0005
com.weinuo.quickcommands:styleable/FontFamily = 0x7f0c0003
com.weinuo.quickcommands:styleable/ColorStateListItem = 0x7f0c0002
com.weinuo.quickcommands:style/Widget.Compat.NotificationActionText = 0x7f0b000d
com.weinuo.quickcommands:style/Widget.Compat.NotificationActionContainer = 0x7f0b000c
com.weinuo.quickcommands:style/Theme.QuickCommands = 0x7f0b000a
com.weinuo.quickcommands:style/TextAppearance.Compat.Notification.Title = 0x7f0b0009
com.weinuo.quickcommands:style/TextAppearance.Compat.Notification.Time = 0x7f0b0008
com.weinuo.quickcommands:style/TextAppearance.Compat.Notification = 0x7f0b0005
com.weinuo.quickcommands:style/EdgeToEdgeFloatingDialogWindowTheme = 0x7f0b0002
com.weinuo.quickcommands:string/widget_update_settings = 0x7f0a02ce
com.weinuo.quickcommands:string/widget_update_interval_error = 0x7f0a02cc
com.weinuo.quickcommands:string/widget_update_interval = 0x7f0a02cb
com.weinuo.quickcommands:string/widget_update_completed = 0x7f0a02c8
com.weinuo.quickcommands:string/widget_3_label = 0x7f0a02c6
com.weinuo.quickcommands:string/widget_2_label = 0x7f0a02c5
com.weinuo.quickcommands:string/widget_1_label = 0x7f0a02c4
com.weinuo.quickcommands:string/volume_volume_change_description = 0x7f0a02c1
com.weinuo.quickcommands:string/volume_volume_change = 0x7f0a02c0
com.weinuo.quickcommands:string/volume_volume_adjust = 0x7f0a02be
com.weinuo.quickcommands:string/volume_vibration_mode_description = 0x7f0a02bd
com.weinuo.quickcommands:string/volume_task = 0x7f0a02ba
com.weinuo.quickcommands:string/volume_speakerphone_control_description = 0x7f0a02b9
com.weinuo.quickcommands:string/volume_do_not_disturb_description = 0x7f0a02b7
com.weinuo.quickcommands:string/volume_changed = 0x7f0a02b4
com.weinuo.quickcommands:style/FloatingDialogWindowTheme = 0x7f0b0004
com.weinuo.quickcommands:string/usage_stats_permission_description = 0x7f0a02b2
com.weinuo.quickcommands:string/trigger_mode = 0x7f0a02af
com.weinuo.quickcommands:string/tooltip_label = 0x7f0a02ae
com.weinuo.quickcommands:string/time_condition_time_period_description = 0x7f0a02ab
com.weinuo.quickcommands:string/time_condition_time_period = 0x7f0a02aa
com.weinuo.quickcommands:string/time_condition_sun_event_description = 0x7f0a02a9
com.weinuo.quickcommands:string/time_condition_sun_event = 0x7f0a02a8
com.weinuo.quickcommands:string/time_condition_periodic_time_description = 0x7f0a02a2
com.weinuo.quickcommands:string/time_condition_periodic_time = 0x7f0a02a1
com.weinuo.quickcommands:string/time_condition_delayed_trigger = 0x7f0a029f
com.weinuo.quickcommands:string/time_based_description = 0x7f0a029e
com.weinuo.quickcommands:string/time_based = 0x7f0a029d
com.weinuo.quickcommands:string/text_content_placeholder = 0x7f0a029c
com.weinuo.quickcommands:string/text_content_label = 0x7f0a029b
com.weinuo.quickcommands:string/template_screen_on_network_title = 0x7f0a029a
com.weinuo.quickcommands:string/template_screen_on_network_desc = 0x7f0a0299
com.weinuo.quickcommands:string/template_do_not_disturb_desc = 0x7f0a0294
com.weinuo.quickcommands:string/template_category_network = 0x7f0a0291
com.weinuo.quickcommands:string/volume_changed_description = 0x7f0a02b5
com.weinuo.quickcommands:string/template_category_display = 0x7f0a0290
com.weinuo.quickcommands:string/template_category_automation = 0x7f0a028f
com.weinuo.quickcommands:string/template_battery_saver_title = 0x7f0a028e
com.weinuo.quickcommands:string/template_battery_saver_desc = 0x7f0a028d
com.weinuo.quickcommands:string/template_auto_brightness_desc = 0x7f0a028b
com.weinuo.quickcommands:string/volume_vibration_mode = 0x7f0a02bc
com.weinuo.quickcommands:string/template_percent = 0x7f0a0296
com.weinuo.quickcommands:string/template_anti_embarrassment_mode_title = 0x7f0a028a
com.weinuo.quickcommands:string/tasker_locale_plugin = 0x7f0a0286
com.weinuo.quickcommands:string/task_screen_control_description = 0x7f0a0283
com.weinuo.quickcommands:string/task_screen_control = 0x7f0a0282
com.weinuo.quickcommands:string/task_phone_task_description = 0x7f0a0281
com.weinuo.quickcommands:string/task_phone_task = 0x7f0a0280
com.weinuo.quickcommands:string/task_network_description = 0x7f0a027d
com.weinuo.quickcommands:string/task_media_task = 0x7f0a027a
com.weinuo.quickcommands:string/task_log_task_description = 0x7f0a0279
com.weinuo.quickcommands:string/template_screen_off_network_title = 0x7f0a0298
com.weinuo.quickcommands:string/task_log_task = 0x7f0a0278
com.weinuo.quickcommands:string/task_log_description = 0x7f0a0277
com.weinuo.quickcommands:string/task_location = 0x7f0a0274
com.weinuo.quickcommands:string/task_file_operation_description = 0x7f0a0273
com.weinuo.quickcommands:string/task_app_management_description = 0x7f0a026f
com.weinuo.quickcommands:string/task_alarm_reminder_description = 0x7f0a026d
com.weinuo.quickcommands:string/task_alarm_reminder = 0x7f0a026c
com.weinuo.quickcommands:string/tap_to_select_music_apps = 0x7f0a026b
com.weinuo.quickcommands:string/tap_to_select_map_apps = 0x7f0a026a
com.weinuo.quickcommands:string/system_settings_description = 0x7f0a0268
com.weinuo.quickcommands:string/system_setting_changed_description = 0x7f0a0266
com.weinuo.quickcommands:string/system_setting_changed = 0x7f0a0265
com.weinuo.quickcommands:string/system_operation_accessibility_service_name = 0x7f0a0264
com.weinuo.quickcommands:string/switch_operation_toggle = 0x7f0a0261
com.weinuo.quickcommands:string/switch_operation_on = 0x7f0a0260
com.weinuo.quickcommands:string/sun_event_sunset = 0x7f0a025e
com.weinuo.quickcommands:string/storage_permission_required = 0x7f0a025c
com.weinuo.quickcommands:string/storage_permission_description = 0x7f0a025b
com.weinuo.quickcommands:string/state_on = 0x7f0a0258
com.weinuo.quickcommands:string/state_off = 0x7f0a0257
com.weinuo.quickcommands:string/state_change_description = 0x7f0a0255
com.weinuo.quickcommands:string/state_change = 0x7f0a0254
com.weinuo.quickcommands:string/snackbar_pane_title = 0x7f0a0253
com.weinuo.quickcommands:string/smart_reminders_title = 0x7f0a0252
com.weinuo.quickcommands:string/smart_reminders_description = 0x7f0a0251
com.weinuo.quickcommands:string/smart_reminder_tap_to_setup = 0x7f0a024f
com.weinuo.quickcommands:string/task_screen_control_task_description = 0x7f0a0285
com.weinuo.quickcommands:string/smart_reminder_status_label = 0x7f0a024e
com.weinuo.quickcommands:string/smart_reminder_selected_apps_count = 0x7f0a024d
com.weinuo.quickcommands:string/smart_reminder_select_app = 0x7f0a024c
com.weinuo.quickcommands:string/smart_reminder_ready_to_use = 0x7f0a024b
com.weinuo.quickcommands:string/smart_reminder_needs_configuration = 0x7f0a024a
com.weinuo.quickcommands:string/smart_reminder_detail_settings = 0x7f0a0249
com.weinuo.quickcommands:string/smart_reminder_configure = 0x7f0a0246
com.weinuo.quickcommands:string/smart_reminder_config_error = 0x7f0a0244
com.weinuo.quickcommands:string/sim_card_state_description = 0x7f0a0242
com.weinuo.quickcommands:string/usage_stats_permission_required = 0x7f0a02b3
com.weinuo.quickcommands:string/sim_card_state = 0x7f0a0241
com.weinuo.quickcommands:string/shortcut_not_configured = 0x7f0a0240
com.weinuo.quickcommands:string/shortcut_4_short_label = 0x7f0a023f
com.weinuo.quickcommands:string/shortcut_4_long_label = 0x7f0a023e
com.weinuo.quickcommands:string/shortcut_2_short_label = 0x7f0a023b
com.weinuo.quickcommands:string/shortcut_2_long_label = 0x7f0a023a
com.weinuo.quickcommands:string/shortcut_1_short_label = 0x7f0a0239
com.weinuo.quickcommands:string/shortcut_1_long_label = 0x7f0a0238
com.weinuo.quickcommands:string/shopping_app_reminder_status_enabled = 0x7f0a0235
com.weinuo.quickcommands:string/shopping_app_reminder_description = 0x7f0a0233
com.weinuo.quickcommands:string/shizuku_permission_required = 0x7f0a0232
com.weinuo.quickcommands:string/shizuku_not_running = 0x7f0a0231
com.weinuo.quickcommands:string/shizuku_not_installed = 0x7f0a0230
com.weinuo.quickcommands:string/shizuku_category_network = 0x7f0a022c
com.weinuo.quickcommands:string/shizuku_category_app_management = 0x7f0a022a
com.weinuo.quickcommands:string/shell_script_settings = 0x7f0a0229
com.weinuo.quickcommands:string/shell_script_placeholder = 0x7f0a0227
com.weinuo.quickcommands:string/shell_script = 0x7f0a0226
com.weinuo.quickcommands:string/share_url_reminder_title = 0x7f0a0225
com.weinuo.quickcommands:string/share_url_reminder_status_enabled = 0x7f0a0223
com.weinuo.quickcommands:string/share_url_reminder_description = 0x7f0a0221
com.weinuo.quickcommands:string/share_text_description = 0x7f0a0220
com.weinuo.quickcommands:string/sensor_sleep_sensor_description = 0x7f0a021b
com.weinuo.quickcommands:string/sensor_sleep_sensor = 0x7f0a021a
com.weinuo.quickcommands:string/no_search_results = 0x7f0a01b2
com.weinuo.quickcommands:string/sensor_shake_sensor_description = 0x7f0a0219
com.weinuo.quickcommands:string/volume_volume_popup = 0x7f0a02c2
com.weinuo.quickcommands:string/sensor_shake_sensor = 0x7f0a0218
com.weinuo.quickcommands:string/datetime_voice_time_announcement_description = 0x7f0a00aa
com.weinuo.quickcommands:string/sensor_proximity_sensor_description = 0x7f0a0217
com.weinuo.quickcommands:string/search_apps = 0x7f0a0202
com.weinuo.quickcommands:string/sensor_proximity_sensor = 0x7f0a0216
com.weinuo.quickcommands:styleable/GradientColorItem = 0x7f0c0006
com.weinuo.quickcommands:string/task_phone = 0x7f0a027e
com.weinuo.quickcommands:string/sensor_orientation_sensor_description = 0x7f0a0215
com.weinuo.quickcommands:string/sensor_orientation_sensor = 0x7f0a0214
com.weinuo.quickcommands:style/DialogWindowTheme = 0x7f0b0000
com.weinuo.quickcommands:string/sensor_flip_sensor = 0x7f0a0210
com.weinuo.quickcommands:string/tasks_count = 0x7f0a0288
com.weinuo.quickcommands:string/sensor_light_sensor = 0x7f0a0212
com.weinuo.quickcommands:string/sensor_activity_recognition_description = 0x7f0a020f
com.weinuo.quickcommands:string/search_templates = 0x7f0a020a
com.weinuo.quickcommands:string/search_field_settings = 0x7f0a0207
com.weinuo.quickcommands:string/location_task = 0x7f0a0143
com.weinuo.quickcommands:string/screen_text_check_failed = 0x7f0a0201
com.weinuo.quickcommands:drawable/ic_swipe = 0x7f04002a
com.weinuo.quickcommands:string/screen_state_description = 0x7f0a0200
com.weinuo.quickcommands:string/screen_rotation_reminder_title = 0x7f0a01fe
com.weinuo.quickcommands:string/screen_keep_device_awake_description = 0x7f0a01f9
com.weinuo.quickcommands:string/share_url_reminder_status_unconfigured = 0x7f0a0224
com.weinuo.quickcommands:drawable/ic_save = 0x7f04001d
com.weinuo.quickcommands:string/screen_event_off = 0x7f0a01f5
com.weinuo.quickcommands:string/address_reminder_status_enabled = 0x7f0a000e
com.weinuo.quickcommands:string/app_link_reminder_title = 0x7f0a002e
com.weinuo.quickcommands:string/screen_event_auto_rotate_enabled = 0x7f0a01f4
com.weinuo.quickcommands:string/screen_content_text_label = 0x7f0a01ef
com.weinuo.quickcommands:string/m3c_dropdown_menu_expanded = 0x7f0a0171
com.weinuo.quickcommands:string/datetime_voice_time_announcement = 0x7f0a00a9
com.weinuo.quickcommands:string/screen_rotation_reminder_status_disabled = 0x7f0a01fb
com.weinuo.quickcommands:string/file_write_file = 0x7f0a00f8
com.weinuo.quickcommands:string/icon_weight_medium = 0x7f0a010f
com.weinuo.quickcommands:string/screen_brightness_control_description = 0x7f0a01eb
com.weinuo.quickcommands:attr/dataPattern = 0x7f010004
com.weinuo.quickcommands:id/pooling_container_listener_holder_tag = 0x7f050044
com.weinuo.quickcommands:string/lifecycle_description = 0x7f0a013a
com.weinuo.quickcommands:string/save = 0x7f0a01e9
com.weinuo.quickcommands:string/range_start = 0x7f0a01e4
com.weinuo.quickcommands:string/quick_commands_title = 0x7f0a01e2
com.weinuo.quickcommands:id/tag_on_receive_content_mime_types = 0x7f050050
com.weinuo.quickcommands:string/quick_command_new = 0x7f0a01df
com.weinuo.quickcommands:string/time_condition_scheduled_time_description = 0x7f0a02a5
com.weinuo.quickcommands:string/datetime_stopwatch = 0x7f0a00a5
com.weinuo.quickcommands:string/quick_command_edit = 0x7f0a01dd
com.weinuo.quickcommands:string/phone_task_description = 0x7f0a01d8
com.weinuo.quickcommands:string/widget_update_interval_hint = 0x7f0a02cd
com.weinuo.quickcommands:string/dialog_title = 0x7f0a00dc
com.weinuo.quickcommands:string/phone_task = 0x7f0a01d7
com.weinuo.quickcommands:string/interface_click_description = 0x7f0a0127
com.weinuo.quickcommands:string/app_link_reminder_status_enabled = 0x7f0a002c
com.weinuo.quickcommands:string/phone_status_excellent = 0x7f0a01d4
com.weinuo.quickcommands:string/phone_ringtone_settings_description = 0x7f0a01d3
com.weinuo.quickcommands:string/phone_ringtone_settings = 0x7f0a01d2
com.weinuo.quickcommands:string/optimization_complete = 0x7f0a01c0
com.weinuo.quickcommands:string/phone_reject_call = 0x7f0a01d0
com.weinuo.quickcommands:string/phone_open_call_log_description = 0x7f0a01cf
com.weinuo.quickcommands:string/phone_open_call_log = 0x7f0a01ce
com.weinuo.quickcommands:xml/one_click_command_widget_1_info = 0x7f0d0008
com.weinuo.quickcommands:drawable/ic_sky_blue_check_disabled = 0x7f040027
com.weinuo.quickcommands:string/new_app_reminder_description = 0x7f0a01ac
com.weinuo.quickcommands:string/phone_clear_call_log = 0x7f0a01ca
com.weinuo.quickcommands:string/optimizing = 0x7f0a01c3
com.weinuo.quickcommands:string/optimization_failed = 0x7f0a01c1
com.weinuo.quickcommands:string/notification_task = 0x7f0a01be
com.weinuo.quickcommands:string/notification_listener_service_name = 0x7f0a01bb
com.weinuo.quickcommands:attr/data = 0x7f010003
com.weinuo.quickcommands:string/search_quick_commands = 0x7f0a0208
com.weinuo.quickcommands:string/notification_event_description = 0x7f0a01b9
com.weinuo.quickcommands:id/accessibility_custom_action_8 = 0x7f05001f
com.weinuo.quickcommands:string/notification_event = 0x7f0a01b8
com.weinuo.quickcommands:string/new_app_reminder_status_enabled = 0x7f0a01ae
com.weinuo.quickcommands:string/new_app_reminder_status_disabled = 0x7f0a01ad
com.weinuo.quickcommands:string/navigation_menu = 0x7f0a01ab
com.weinuo.quickcommands:string/volume_volume_adjust_description = 0x7f0a02bf
com.weinuo.quickcommands:string/app_link_reminder_status_disabled = 0x7f0a002b
com.weinuo.quickcommands:string/nav_smart_reminders = 0x7f0a01aa
com.weinuo.quickcommands:attr/popEnterAnim = 0x7f01001b
com.weinuo.quickcommands:string/nav_quick_commands = 0x7f0a01a9
com.weinuo.quickcommands:string/music_playback_state_description = 0x7f0a01a5
com.weinuo.quickcommands:string/app_open_website_description = 0x7f0a0033
com.weinuo.quickcommands:string/quick_command_form = 0x7f0a01de
com.weinuo.quickcommands:string/music_app_reminder_status_unconfigured = 0x7f0a01a2
com.weinuo.quickcommands:string/add_cleanup_rule_description = 0x7f0a000a
com.weinuo.quickcommands:attr/uri = 0x7f010027
com.weinuo.quickcommands:string/music_app_reminder_status_configured = 0x7f0a019f
com.weinuo.quickcommands:drawable/ic_search_sky_blue_regular = 0x7f040021
com.weinuo.quickcommands:string/music_app_reminder_description = 0x7f0a019e
com.weinuo.quickcommands:string/memory_learning_data = 0x7f0a019d
com.weinuo.quickcommands:string/media_task_description = 0x7f0a019c
com.weinuo.quickcommands:attr/targetPackage = 0x7f010025
com.weinuo.quickcommands:string/media_task = 0x7f0a019b
com.weinuo.quickcommands:string/media_play_stop_sound_description = 0x7f0a019a
com.weinuo.quickcommands:styleable/FontFamilyFont = 0x7f0c0004
com.weinuo.quickcommands:string/app_execute_shell_script = 0x7f0a001c
com.weinuo.quickcommands:string/media_play_stop_sound = 0x7f0a0199
com.weinuo.quickcommands:string/media_multimedia_control_description = 0x7f0a0198
com.weinuo.quickcommands:string/media_multimedia_control = 0x7f0a0197
com.weinuo.quickcommands:string/m3c_snackbar_dismiss = 0x7f0a0174
com.weinuo.quickcommands:string/media_microphone_recording = 0x7f0a0195
com.weinuo.quickcommands:xml/one_click_command_widget_2_info = 0x7f0d0009
com.weinuo.quickcommands:string/unified_configuration = 0x7f0a02b0
com.weinuo.quickcommands:id/is_pooling_container_tag = 0x7f05003a
com.weinuo.quickcommands:string/match_options = 0x7f0a0194
com.weinuo.quickcommands:string/running_apps_count = 0x7f0a01e8
com.weinuo.quickcommands:string/manual_widget_update = 0x7f0a0192
com.weinuo.quickcommands:string/manual_volume_key_press_description = 0x7f0a0191
com.weinuo.quickcommands:string/manual_media_key_press = 0x7f0a018a
com.weinuo.quickcommands:id/notification_main_column = 0x7f050042
com.weinuo.quickcommands:string/manual_home_button_long_press_description = 0x7f0a0189
com.weinuo.quickcommands:style/TextAppearance.Compat.Notification.Line2 = 0x7f0b0007
com.weinuo.quickcommands:string/manual_home_button_long_press = 0x7f0a0188
com.weinuo.quickcommands:string/location_toggle_location_service = 0x7f0a0145
com.weinuo.quickcommands:string/manual_fingerprint_gesture_description = 0x7f0a0187
com.weinuo.quickcommands:string/intent_received = 0x7f0a0124
com.weinuo.quickcommands:string/m3c_time_picker_minute_suffix = 0x7f0a017e
com.weinuo.quickcommands:string/m3c_time_picker_minute = 0x7f0a017c
com.weinuo.quickcommands:string/screen_event_unlocked = 0x7f0a01f7
com.weinuo.quickcommands:string/m3c_time_picker_hour_text_field = 0x7f0a017b
com.weinuo.quickcommands:string/m3c_time_picker_hour_suffix = 0x7f0a017a
com.weinuo.quickcommands:string/m3c_suggestions_available = 0x7f0a0175
com.weinuo.quickcommands:string/m3c_search_bar_search = 0x7f0a0173
com.weinuo.quickcommands:string/phone_make_call_description = 0x7f0a01cd
com.weinuo.quickcommands:string/m3c_dropdown_menu_toggle = 0x7f0a0172
com.weinuo.quickcommands:string/m3c_dialog = 0x7f0a016f
com.weinuo.quickcommands:string/m3c_date_range_picker_title = 0x7f0a016e
com.weinuo.quickcommands:string/m3c_date_range_picker_scroll_to_previous_month = 0x7f0a016c
com.weinuo.quickcommands:string/screen_state = 0x7f0a01ff
com.weinuo.quickcommands:string/m3c_date_range_picker_scroll_to_next_month = 0x7f0a016b
com.weinuo.quickcommands:string/m3c_date_picker_today_description = 0x7f0a0165
com.weinuo.quickcommands:string/screen_control_task_description = 0x7f0a01f2
com.weinuo.quickcommands:string/m3c_date_picker_switch_to_next_month = 0x7f0a0161
com.weinuo.quickcommands:string/m3c_date_picker_switch_to_input_mode = 0x7f0a0160
com.weinuo.quickcommands:drawable/ic_call_answer_video = 0x7f040009
com.weinuo.quickcommands:string/m3c_date_picker_switch_to_day_selection = 0x7f0a015f
com.weinuo.quickcommands:drawable/notification_bg_normal_pressed = 0x7f040034
com.weinuo.quickcommands:string/m3c_date_picker_switch_to_calendar_mode = 0x7f0a015e
com.weinuo.quickcommands:string/template_anti_embarrassment_mode_desc = 0x7f0a0289
com.weinuo.quickcommands:string/m3c_date_picker_navigate_to_year_description = 0x7f0a015a
com.weinuo.quickcommands:string/m3c_date_input_label = 0x7f0a0155
com.weinuo.quickcommands:string/m3c_date_input_invalid_not_allowed = 0x7f0a0153
com.weinuo.quickcommands:string/app_unfreeze_app_description = 0x7f0a0042
com.weinuo.quickcommands:string/manual_static_shortcut_description = 0x7f0a018d
com.weinuo.quickcommands:styleable/Capability = 0x7f0c0001
com.weinuo.quickcommands:string/m3c_date_input_headline = 0x7f0a0150
com.weinuo.quickcommands:attr/popExitAnim = 0x7f01001c
com.weinuo.quickcommands:string/m3c_bottom_sheet_pane_title = 0x7f0a014f
com.weinuo.quickcommands:string/m3c_bottom_sheet_dismiss_description = 0x7f0a014c
com.weinuo.quickcommands:string/location_get_location = 0x7f0a013d
com.weinuo.quickcommands:string/m3c_bottom_sheet_collapse_description = 0x7f0a014b
com.weinuo.quickcommands:string/smart_reminder_change_app = 0x7f0a0243
com.weinuo.quickcommands:string/login_attempt_failed_description = 0x7f0a014a
com.weinuo.quickcommands:string/logcat_message = 0x7f0a0147
com.weinuo.quickcommands:string/location_toggle_location_service_description = 0x7f0a0146
com.weinuo.quickcommands:string/app_freeze_app = 0x7f0a0020
com.weinuo.quickcommands:string/m3c_time_picker_hour_selection = 0x7f0a0179
com.weinuo.quickcommands:string/location_share_location = 0x7f0a0141
com.weinuo.quickcommands:string/m3c_date_range_input_title = 0x7f0a0168
com.weinuo.quickcommands:string/phone_clear_call_log_description = 0x7f0a01cb
com.weinuo.quickcommands:string/m3c_time_picker_pm = 0x7f0a0181
com.weinuo.quickcommands:string/location_set_location_update_frequency_description = 0x7f0a0140
com.weinuo.quickcommands:string/notification_show_notification_description = 0x7f0a01bd
com.weinuo.quickcommands:string/state_empty = 0x7f0a0256
com.weinuo.quickcommands:string/location_get_location_description = 0x7f0a013e
com.weinuo.quickcommands:string/smart_reminder_type_not_found = 0x7f0a0250
com.weinuo.quickcommands:color/purple_200 = 0x7f020007
com.weinuo.quickcommands:string/location_force_location_update = 0x7f0a013b
com.weinuo.quickcommands:string/language_settings = 0x7f0a0136
com.weinuo.quickcommands:string/task_media_task_description = 0x7f0a027b
com.weinuo.quickcommands:string/language_selection_title = 0x7f0a0135
com.weinuo.quickcommands:string/language_english = 0x7f0a0134
com.weinuo.quickcommands:string/invert_colors_operation = 0x7f0a0130
com.weinuo.quickcommands:string/default_keyboard_description = 0x7f0a00ad
com.weinuo.quickcommands:string/search_smart_reminders = 0x7f0a0209
com.weinuo.quickcommands:string/task_app_management = 0x7f0a026e
com.weinuo.quickcommands:string/invert_colors_on = 0x7f0a012f
com.weinuo.quickcommands:string/shortcut_3_short_label = 0x7f0a023d
com.weinuo.quickcommands:string/interface_click = 0x7f0a0126
com.weinuo.quickcommands:string/intent_received_description = 0x7f0a0125
com.weinuo.quickcommands:string/information_task_description = 0x7f0a011f
com.weinuo.quickcommands:string/volume_do_not_disturb = 0x7f0a02b6
com.weinuo.quickcommands:drawable/ic_sky_blue_back_arrow = 0x7f040026
com.weinuo.quickcommands:drawable/floating_button_background = 0x7f040003
com.weinuo.quickcommands:string/m3c_time_picker_hour = 0x7f0a0177
com.weinuo.quickcommands:string/information_task = 0x7f0a011e
com.weinuo.quickcommands:string/info_show_toast_description = 0x7f0a011d
com.weinuo.quickcommands:id/action_save = 0x7f050025
com.weinuo.quickcommands:string/info_show_dialog = 0x7f0a011a
com.weinuo.quickcommands:id/accessibility_custom_action_20 = 0x7f05000e
com.weinuo.quickcommands:string/lifecycle = 0x7f0a0139
com.weinuo.quickcommands:drawable/circular_reminder_background = 0x7f040002
com.weinuo.quickcommands:string/app_lifecycle_description = 0x7f0a0029
com.weinuo.quickcommands:string/language_chinese = 0x7f0a0133
com.weinuo.quickcommands:string/manual_trigger_description = 0x7f0a018f
com.weinuo.quickcommands:string/info_send_email_description = 0x7f0a0117
com.weinuo.quickcommands:id/tag_unhandled_key_event_manager = 0x7f050055
com.weinuo.quickcommands:string/info_send_email = 0x7f0a0116
com.weinuo.quickcommands:id/view_tree_lifecycle_owner = 0x7f05005d
com.weinuo.quickcommands:string/info_message_ringtone_description = 0x7f0a0115
com.weinuo.quickcommands:attr/route = 0x7f010022
com.weinuo.quickcommands:string/info_message_ringtone = 0x7f0a0114
com.weinuo.quickcommands:string/app_detection_selected_apps = 0x7f0a0019
com.weinuo.quickcommands:string/indeterminate = 0x7f0a0113
com.weinuo.quickcommands:string/notification_listener_service_description = 0x7f0a01ba
com.weinuo.quickcommands:string/m3c_time_picker_hour_24h_suffix = 0x7f0a0178
com.weinuo.quickcommands:string/status_bar_notification_info_overflow = 0x7f0a0259
com.weinuo.quickcommands:string/shizuku_category_screen_operation = 0x7f0a022d
com.weinuo.quickcommands:string/icon_weight_regular = 0x7f0a0110
com.weinuo.quickcommands:id/forever = 0x7f050032
com.weinuo.quickcommands:string/screen_brightness_control = 0x7f0a01ea
com.weinuo.quickcommands:string/go_to_settings = 0x7f0a010b
com.weinuo.quickcommands:string/dark_theme_changed_description = 0x7f0a00a2
com.weinuo.quickcommands:string/gesture_recording_edit_description = 0x7f0a010a
com.weinuo.quickcommands:string/gesture_recording_edit = 0x7f0a0109
com.weinuo.quickcommands:string/widget_4_label = 0x7f0a02c7
com.weinuo.quickcommands:string/shopping_app_reminder_title = 0x7f0a0237
com.weinuo.quickcommands:string/font_weight_selection_title = 0x7f0a0106
com.weinuo.quickcommands:string/font_weight_bold = 0x7f0a0103
com.weinuo.quickcommands:string/package_name = 0x7f0a01c6
com.weinuo.quickcommands:string/font_size_settings = 0x7f0a0102
com.weinuo.quickcommands:string/invert_colors = 0x7f0a012c
com.weinuo.quickcommands:string/font_size_percentage = 0x7f0a0101
com.weinuo.quickcommands:string/task_screen_control_task = 0x7f0a0284
com.weinuo.quickcommands:string/search_field_icon_weight = 0x7f0a0203
com.weinuo.quickcommands:string/datetime_alarm_description = 0x7f0a00a4
com.weinuo.quickcommands:string/font_size = 0x7f0a00ff
com.weinuo.quickcommands:string/configure_item = 0x7f0a0080
com.weinuo.quickcommands:id/accessibility_custom_action_22 = 0x7f050010
com.weinuo.quickcommands:string/flashlight_reminder_title = 0x7f0a00fe
com.weinuo.quickcommands:string/flashlight_reminder_status_unconfigured = 0x7f0a00fd
com.weinuo.quickcommands:string/flashlight_reminder_description = 0x7f0a00fa
com.weinuo.quickcommands:string/tasker_locale_plugin_description = 0x7f0a0287
com.weinuo.quickcommands:string/communication_state_description = 0x7f0a007f
com.weinuo.quickcommands:string/m3c_time_picker_period_toggle_description = 0x7f0a0180
com.weinuo.quickcommands:string/file_write_file_description = 0x7f0a00f9
com.weinuo.quickcommands:string/application_task = 0x7f0a0043
com.weinuo.quickcommands:string/file_operation_task_description = 0x7f0a00f7
com.weinuo.quickcommands:string/conn_wifi_state = 0x7f0a0088
com.weinuo.quickcommands:string/file_open_file_description = 0x7f0a00f5
com.weinuo.quickcommands:string/experimental_features_description = 0x7f0a00f0
com.weinuo.quickcommands:string/experimental_features_enabled = 0x7f0a00f1
com.weinuo.quickcommands:string/enable = 0x7f0a00ec
com.weinuo.quickcommands:string/connectivity_task = 0x7f0a009a
com.weinuo.quickcommands:string/edit_task = 0x7f0a00ea
com.weinuo.quickcommands:string/edit_condition = 0x7f0a00e8
com.weinuo.quickcommands:string/template_category_system = 0x7f0a0293
com.weinuo.quickcommands:string/keyboard_hint_description = 0x7f0a0132
com.weinuo.quickcommands:string/edit_abort_condition_description = 0x7f0a00e7
com.weinuo.quickcommands:string/edit_abort_condition = 0x7f0a00e6
com.weinuo.quickcommands:string/m3c_time_picker_minute_text_field = 0x7f0a017f
com.weinuo.quickcommands:string/driving_mode_description = 0x7f0a00e4
com.weinuo.quickcommands:string/share_url_reminder_status_disabled = 0x7f0a0222
com.weinuo.quickcommands:string/screen_rotation_reminder_status_enabled = 0x7f0a01fc
com.weinuo.quickcommands:string/driving_mode = 0x7f0a00e3
com.weinuo.quickcommands:string/package_management = 0x7f0a01c4
com.weinuo.quickcommands:string/interface_interaction_service_not_enabled = 0x7f0a012a
com.weinuo.quickcommands:string/m3c_date_picker_headline_description = 0x7f0a0159
com.weinuo.quickcommands:string/dock_state_description = 0x7f0a00e2
com.weinuo.quickcommands:string/dock_state = 0x7f0a00e1
com.weinuo.quickcommands:dimen/notification_top_pad_large_text = 0x7f030015
com.weinuo.quickcommands:string/disable = 0x7f0a00e0
com.weinuo.quickcommands:string/digital_assistant = 0x7f0a00de
com.weinuo.quickcommands:string/dialog_title_placeholder = 0x7f0a00dd
com.weinuo.quickcommands:string/dialog_settings = 0x7f0a00db
com.weinuo.quickcommands:string/device_settings_keyboard_hint_description = 0x7f0a00d8
com.weinuo.quickcommands:id/action_settings = 0x7f050026
com.weinuo.quickcommands:string/app_tasker_locale_plugin = 0x7f0a003d
com.weinuo.quickcommands:string/device_settings_keyboard_hint = 0x7f0a00d7
com.weinuo.quickcommands:id/action_image = 0x7f050024
com.weinuo.quickcommands:string/default_popup_window_title = 0x7f0a00ae
com.weinuo.quickcommands:string/device_settings_invert_colors = 0x7f0a00d5
com.weinuo.quickcommands:string/device_settings_font_size_description = 0x7f0a00d2
com.weinuo.quickcommands:id/accessibility_custom_action_29 = 0x7f050017
com.weinuo.quickcommands:id/consume_window_insets_tag = 0x7f05002f
com.weinuo.quickcommands:string/device_settings_font_size = 0x7f0a00d1
com.weinuo.quickcommands:dimen/notification_big_circle_margin = 0x7f030009
com.weinuo.quickcommands:string/notification_show_notification = 0x7f0a01bc
com.weinuo.quickcommands:string/device_settings_enter_screensaver_description = 0x7f0a00d0
com.weinuo.quickcommands:string/device_settings_enter_screensaver = 0x7f0a00cf
com.weinuo.quickcommands:string/device_settings_driving_mode_description = 0x7f0a00ce
com.weinuo.quickcommands:style/Theme.QuickCommands.NoActionBar = 0x7f0b000b
com.weinuo.quickcommands:string/device_settings_driving_mode = 0x7f0a00cd
com.weinuo.quickcommands:string/phone_status_poor = 0x7f0a01d6
com.weinuo.quickcommands:string/device_settings_description = 0x7f0a00ca
com.weinuo.quickcommands:string/range_end = 0x7f0a01e3
com.weinuo.quickcommands:string/camera_task = 0x7f0a0067
com.weinuo.quickcommands:string/file_file_operation = 0x7f0a00f2
com.weinuo.quickcommands:id/line1 = 0x7f05003d
com.weinuo.quickcommands:string/device_settings_auto_rotate_description = 0x7f0a00c9
com.weinuo.quickcommands:string/smart_reminder_detail_config = 0x7f0a0248
com.weinuo.quickcommands:string/device_settings_auto_rotate = 0x7f0a00c8
com.weinuo.quickcommands:string/template_screen_off_network_desc = 0x7f0a0297
com.weinuo.quickcommands:string/not_selected = 0x7f0a01b5
com.weinuo.quickcommands:string/device_settings_accessibility_service = 0x7f0a00c6
com.weinuo.quickcommands:string/device_logcat_message_description = 0x7f0a00c4
com.weinuo.quickcommands:string/device_gps_state_description = 0x7f0a00c2
com.weinuo.quickcommands:string/device_event_description = 0x7f0a00c0
com.weinuo.quickcommands:string/no_template_search_results = 0x7f0a01b3
com.weinuo.quickcommands:string/info_show_dialog_description = 0x7f0a011b
com.weinuo.quickcommands:string/device_event = 0x7f0a00bf
com.weinuo.quickcommands:string/location_set_location_update_frequency = 0x7f0a013f
com.weinuo.quickcommands:color/androidx_core_secondary_text_default_material_light = 0x7f020001
com.weinuo.quickcommands:string/device_clipboard_changed = 0x7f0a00bd
com.weinuo.quickcommands:string/device_boot_completed_description = 0x7f0a00bc
com.weinuo.quickcommands:string/device_boot_completed = 0x7f0a00bb
com.weinuo.quickcommands:string/device_action_task_description = 0x7f0a00ba
com.weinuo.quickcommands:string/file_open_file = 0x7f0a00f4
com.weinuo.quickcommands:color/notification_icon_bg_color = 0x7f020006
com.weinuo.quickcommands:string/device_action_share_text_description = 0x7f0a00b8
com.weinuo.quickcommands:string/m3c_date_picker_title = 0x7f0a0164
com.weinuo.quickcommands:string/device_action_share_text = 0x7f0a00b7
com.weinuo.quickcommands:string/logcat_message_description = 0x7f0a0148
com.weinuo.quickcommands:string/detailed_configuration = 0x7f0a00b6
com.weinuo.quickcommands:string/demo_mode_description = 0x7f0a00b5
com.weinuo.quickcommands:string/delete_quick_command_confirm = 0x7f0a00b2
com.weinuo.quickcommands:string/shortcut_3_long_label = 0x7f0a023c
com.weinuo.quickcommands:string/share_target_selection = 0x7f0a021e
com.weinuo.quickcommands:string/checkup_button = 0x7f0a006d
com.weinuo.quickcommands:string/device_gps_state = 0x7f0a00c1
com.weinuo.quickcommands:string/delete_quick_command = 0x7f0a00b1
com.weinuo.quickcommands:string/clipboard_changed_description = 0x7f0a006f
com.weinuo.quickcommands:string/location_share_location_description = 0x7f0a0142
com.weinuo.quickcommands:string/volume_volume_popup_description = 0x7f0a02c3
com.weinuo.quickcommands:string/shizuku_category_key_simulation = 0x7f0a022b
com.weinuo.quickcommands:string/delete_operation_irreversible = 0x7f0a00b0
com.weinuo.quickcommands:string/manual_trigger = 0x7f0a018e
com.weinuo.quickcommands:string/task_device_settings_description = 0x7f0a0271
com.weinuo.quickcommands:string/font_size_description = 0x7f0a0100
com.weinuo.quickcommands:string/comm_incoming_call_description = 0x7f0a0077
com.weinuo.quickcommands:string/datetime_task_description = 0x7f0a00a8
com.weinuo.quickcommands:string/file_file_operation_description = 0x7f0a00f3
com.weinuo.quickcommands:string/datetime_alarm = 0x7f0a00a3
com.weinuo.quickcommands:drawable/ic_clear = 0x7f04000e
com.weinuo.quickcommands:id/icon = 0x7f050036
com.weinuo.quickcommands:string/dark_theme_changed = 0x7f0a00a1
com.weinuo.quickcommands:string/accessibility_service_disable = 0x7f0a0001
com.weinuo.quickcommands:string/m3c_date_range_picker_day_in_range = 0x7f0a0169
com.weinuo.quickcommands:string/custom_app_platform_config = 0x7f0a009f
com.weinuo.quickcommands:string/select_map_apps = 0x7f0a020b
com.weinuo.quickcommands:string/screen_content_text_placeholder = 0x7f0a01f0
com.weinuo.quickcommands:string/camera_open_last_photo = 0x7f0a005f
com.weinuo.quickcommands:drawable/menu_item_background = 0x7f04002d
com.weinuo.quickcommands:string/language_settings_description = 0x7f0a0137
com.weinuo.quickcommands:attr/alpha = 0x7f010001
com.weinuo.quickcommands:attr/mimeType = 0x7f010017
com.weinuo.quickcommands:string/contact_selection = 0x7f0a009e
com.weinuo.quickcommands:string/task_network = 0x7f0a027c
com.weinuo.quickcommands:string/connectivity_task_description = 0x7f0a009b
com.weinuo.quickcommands:string/search_field_placeholder_font_weight_description = 0x7f0a0206
com.weinuo.quickcommands:string/connectivity_send_intent = 0x7f0a0098
com.weinuo.quickcommands:string/connectivity_nfc_control = 0x7f0a0096
com.weinuo.quickcommands:string/connectivity_network_check = 0x7f0a0094
com.weinuo.quickcommands:string/device_settings_immersive_mode = 0x7f0a00d3
com.weinuo.quickcommands:string/edit_task_description = 0x7f0a00eb
com.weinuo.quickcommands:string/connectivity_hotspot_control_description = 0x7f0a0091
com.weinuo.quickcommands:string/connectivity_hotspot_control = 0x7f0a0090
com.weinuo.quickcommands:string/connectivity_bluetooth_control_description = 0x7f0a008f
com.weinuo.quickcommands:string/connectivity_bluetooth_control = 0x7f0a008e
com.weinuo.quickcommands:string/connection_state_description = 0x7f0a008b
com.weinuo.quickcommands:xml/accessibility_service_config = 0x7f0d0000
com.weinuo.quickcommands:string/m3c_date_picker_scroll_to_later_years = 0x7f0a015d
com.weinuo.quickcommands:string/conn_wifi_state_description = 0x7f0a0089
com.weinuo.quickcommands:string/delete_quick_command_warning = 0x7f0a00b3
com.weinuo.quickcommands:id/action_clear = 0x7f050021
com.weinuo.quickcommands:string/communication_state = 0x7f0a007e
com.weinuo.quickcommands:string/music_app_reminder_status_enabled = 0x7f0a01a1
com.weinuo.quickcommands:string/m3c_tooltip_long_press_label = 0x7f0a0182
com.weinuo.quickcommands:string/comm_sms_received = 0x7f0a007a
com.weinuo.quickcommands:dimen/compat_button_inset_vertical_material = 0x7f030001
com.weinuo.quickcommands:string/comm_sms_sent_description = 0x7f0a007d
com.weinuo.quickcommands:string/intelligent_link_recognition_description = 0x7f0a0120
com.weinuo.quickcommands:string/search_field_icon_weight_description = 0x7f0a0204
com.weinuo.quickcommands:string/sensor_state = 0x7f0a021c
com.weinuo.quickcommands:string/comm_incoming_call = 0x7f0a0076
com.weinuo.quickcommands:id/tag_screen_reader_focusable = 0x7f050051
com.weinuo.quickcommands:string/notification_cancel_notification = 0x7f0a01b6
com.weinuo.quickcommands:string/location_task_description = 0x7f0a0144
com.weinuo.quickcommands:string/sensor_state_description = 0x7f0a021d
com.weinuo.quickcommands:string/connection_state = 0x7f0a008a
com.weinuo.quickcommands:string/font_weight_medium = 0x7f0a0104
com.weinuo.quickcommands:string/comm_call_ended_description = 0x7f0a0075
com.weinuo.quickcommands:string/comm_sms_received_description = 0x7f0a007b
com.weinuo.quickcommands:string/phone_make_call = 0x7f0a01cc
com.weinuo.quickcommands:dimen/notification_small_icon_background_padding = 0x7f030011
com.weinuo.quickcommands:layout/notification_template_part_chronometer = 0x7f070007
com.weinuo.quickcommands:string/close_drawer = 0x7f0a0070
com.weinuo.quickcommands:attr/fontVariationSettings = 0x7f010012
com.weinuo.quickcommands:dimen/notification_large_icon_width = 0x7f03000c
com.weinuo.quickcommands:string/datetime_task = 0x7f0a00a7
com.weinuo.quickcommands:dimen/compat_button_padding_horizontal_material = 0x7f030002
com.weinuo.quickcommands:string/clipboard_changed = 0x7f0a006e
com.weinuo.quickcommands:xml/shortcuts = 0x7f0d000c
com.weinuo.quickcommands:string/connectivity_wifi_control = 0x7f0a009c
com.weinuo.quickcommands:string/change_music_apps = 0x7f0a006b
com.weinuo.quickcommands:string/tab = 0x7f0a0269
com.weinuo.quickcommands:string/cancel = 0x7f0a0069
com.weinuo.quickcommands:string/manual_volume_key_press = 0x7f0a0190
com.weinuo.quickcommands:string/m3c_time_picker_am = 0x7f0a0176
com.weinuo.quickcommands:attr/graph = 0x7f010014
com.weinuo.quickcommands:drawable/ic_share_24 = 0x7f040023
com.weinuo.quickcommands:string/device_action_task = 0x7f0a00b9
com.weinuo.quickcommands:string/invalid_command = 0x7f0a012b
com.weinuo.quickcommands:string/digital_assistant_description = 0x7f0a00df
com.weinuo.quickcommands:string/conn_wifi_network = 0x7f0a0086
com.weinuo.quickcommands:id/circular_reminder_button = 0x7f05002d
com.weinuo.quickcommands:dimen/notification_media_narrow_margin = 0x7f03000e
com.weinuo.quickcommands:string/camera_take_photo_description = 0x7f0a0066
com.weinuo.quickcommands:string/camera_screenshot = 0x7f0a0063
com.weinuo.quickcommands:string/camera_record_video = 0x7f0a0061
com.weinuo.quickcommands:string/device_settings_accessibility_service_description = 0x7f0a00c7
com.weinuo.quickcommands:string/call_notification_hang_up_action = 0x7f0a0059
com.weinuo.quickcommands:id/compose_view_saveable_id_tag = 0x7f05002e
com.weinuo.quickcommands:style/TextAppearance.Compat.Notification.Info = 0x7f0b0006
com.weinuo.quickcommands:string/call_notification_answer_video_action = 0x7f0a0057
com.weinuo.quickcommands:string/battery_state = 0x7f0a0054
com.weinuo.quickcommands:string/m3c_date_range_picker_start_headline = 0x7f0a016d
com.weinuo.quickcommands:string/executing_quick_command = 0x7f0a00ed
com.weinuo.quickcommands:string/batch_delete_quick_commands = 0x7f0a004e
com.weinuo.quickcommands:id/accessibility_custom_action_11 = 0x7f050004
com.weinuo.quickcommands:string/ringer_mode_changed = 0x7f0a01e5
com.weinuo.quickcommands:string/quick_command_not_found = 0x7f0a01e0
com.weinuo.quickcommands:string/batch_delete_confirm = 0x7f0a004d
com.weinuo.quickcommands:string/back = 0x7f0a004c
com.weinuo.quickcommands:id/accessibility_custom_action_16 = 0x7f050009
com.weinuo.quickcommands:string/connectivity_wifi_control_description = 0x7f0a009d
com.weinuo.quickcommands:string/auto_rotate_disable = 0x7f0a0047
com.weinuo.quickcommands:string/auto_sync_state = 0x7f0a0049
com.weinuo.quickcommands:string/call_notification_ongoing_text = 0x7f0a005b
com.weinuo.quickcommands:string/application_task_description = 0x7f0a0044
com.weinuo.quickcommands:color/vector_tint_theme_color = 0x7f02000d
com.weinuo.quickcommands:string/app_state_description = 0x7f0a003c
com.weinuo.quickcommands:string/app_state_change = 0x7f0a003a
com.weinuo.quickcommands:string/batch_delete_warning = 0x7f0a004f
com.weinuo.quickcommands:drawable/notification_bg_low = 0x7f040030
com.weinuo.quickcommands:string/app_unfreeze_app = 0x7f0a0041
com.weinuo.quickcommands:id/accessibility_custom_action_31 = 0x7f05001a
com.weinuo.quickcommands:string/app_screen_content = 0x7f0a0036
com.weinuo.quickcommands:string/camera_record_video_description = 0x7f0a0062
com.weinuo.quickcommands:string/share_text = 0x7f0a021f
com.weinuo.quickcommands:string/address_reminder_title = 0x7f0a000f
com.weinuo.quickcommands:string/app_package_management = 0x7f0a0034
com.weinuo.quickcommands:string/gesture_recognition_accessibility_service_description = 0x7f0a0107
com.weinuo.quickcommands:dimen/notification_top_pad = 0x7f030014
com.weinuo.quickcommands:string/shell_script_placeholder_command = 0x7f0a0228
com.weinuo.quickcommands:drawable/ic_circle_white = 0x7f04000d
com.weinuo.quickcommands:string/app_link_reminder_description = 0x7f0a002a
com.weinuo.quickcommands:string/camera_take_photo = 0x7f0a0065
com.weinuo.quickcommands:string/app_execute_javascript_description = 0x7f0a001b
com.weinuo.quickcommands:id/tag_compat_insets_dispatch = 0x7f05004d
com.weinuo.quickcommands:id/accessibility_custom_action_12 = 0x7f050005
com.weinuo.quickcommands:string/camera_screenshot_description = 0x7f0a0064
com.weinuo.quickcommands:string/system_operation_accessibility_service_description = 0x7f0a0263
com.weinuo.quickcommands:id/text = 0x7f050058
com.weinuo.quickcommands:string/nav_global_settings = 0x7f0a01a7
com.weinuo.quickcommands:string/app_interface_click_description = 0x7f0a0025
com.weinuo.quickcommands:string/switch_role = 0x7f0a0262
com.weinuo.quickcommands:string/connectivity_mobile_data_control_description = 0x7f0a0093
com.weinuo.quickcommands:string/airplane_mode_changed_description = 0x7f0a0014
com.weinuo.quickcommands:string/app_interface_click = 0x7f0a0024
com.weinuo.quickcommands:string/shopping_app_reminder_status_unconfigured = 0x7f0a0236
com.weinuo.quickcommands:string/app_importance_management_description = 0x7f0a0023
com.weinuo.quickcommands:string/app_importance_management = 0x7f0a0022
com.weinuo.quickcommands:string/app_trigger_any_app = 0x7f0a0040
com.weinuo.quickcommands:string/dialog_content = 0x7f0a00d9
com.weinuo.quickcommands:string/flashlight_reminder_status_enabled = 0x7f0a00fc
com.weinuo.quickcommands:string/app_freeze_app_description = 0x7f0a0021
com.weinuo.quickcommands:string/dialog_content_placeholder = 0x7f0a00da
com.weinuo.quickcommands:xml/one_click_command_widget_4_info = 0x7f0d000b
com.weinuo.quickcommands:string/app_force_stop_app_description = 0x7f0a001f
com.weinuo.quickcommands:string/app_state = 0x7f0a0039
com.weinuo.quickcommands:string/phone_answer_call_description = 0x7f0a01c8
com.weinuo.quickcommands:string/m3c_date_picker_no_selection_description = 0x7f0a015b
com.weinuo.quickcommands:string/app_force_stop_app = 0x7f0a001e
com.weinuo.quickcommands:string/app_execute_shell_script_description = 0x7f0a001d
com.weinuo.quickcommands:string/m3c_date_picker_switch_to_year_selection = 0x7f0a0163
com.weinuo.quickcommands:id/dialog_button = 0x7f050030
com.weinuo.quickcommands:string/keyboard_hint = 0x7f0a0131
com.weinuo.quickcommands:string/app_execute_javascript = 0x7f0a001a
com.weinuo.quickcommands:string/custom_shopping_platform_config = 0x7f0a00a0
com.weinuo.quickcommands:string/app_package_management_description = 0x7f0a0035
com.weinuo.quickcommands:string/app_detection_any_app = 0x7f0a0018
com.weinuo.quickcommands:string/manual_media_key_press_description = 0x7f0a018b
com.weinuo.quickcommands:id/accessibility_custom_action_23 = 0x7f050011
com.weinuo.quickcommands:string/ambient_display = 0x7f0a0015
com.weinuo.quickcommands:id/accessibility_custom_action_19 = 0x7f05000c
com.weinuo.quickcommands:string/airplane_mode_changed = 0x7f0a0013
com.weinuo.quickcommands:dimen/notification_small_icon_size_as_large = 0x7f030012
com.weinuo.quickcommands:string/advanced_memory_config = 0x7f0a0012
com.weinuo.quickcommands:string/time_condition_scheduled_time = 0x7f0a02a4
com.weinuo.quickcommands:string/info_show_toast = 0x7f0a011c
com.weinuo.quickcommands:string/dropdown_menu = 0x7f0a00e5
com.weinuo.quickcommands:string/advanced_cleanup_strategy_description = 0x7f0a0011
com.weinuo.quickcommands:string/smart_reminder_config_not_found = 0x7f0a0245
com.weinuo.quickcommands:string/file_operation_task = 0x7f0a00f6
com.weinuo.quickcommands:string/screen_content_description = 0x7f0a01ed
com.weinuo.quickcommands:string/in_progress = 0x7f0a0112
com.weinuo.quickcommands:drawable/ic_record_voice_over = 0x7f04001c
com.weinuo.quickcommands:string/address_reminder_status_disabled = 0x7f0a000d
com.weinuo.quickcommands:string/selected = 0x7f0a020d
com.weinuo.quickcommands:string/screen_rotation_reminder_status_unconfigured = 0x7f0a01fd
com.weinuo.quickcommands:string/address_reminder_description = 0x7f0a000b
com.weinuo.quickcommands:string/m3c_date_picker_year_picker_pane_title = 0x7f0a0166
com.weinuo.quickcommands:string/auto_sync_state_description = 0x7f0a004a
com.weinuo.quickcommands:id/accessibility_custom_action_25 = 0x7f050013
com.weinuo.quickcommands:string/account_selection = 0x7f0a0008
com.weinuo.quickcommands:string/accessibility_service_title = 0x7f0a0007
com.weinuo.quickcommands:string/accessibility_service_name = 0x7f0a0005
com.weinuo.quickcommands:string/system_settings = 0x7f0a0267
com.weinuo.quickcommands:string/accessibility_service_enable = 0x7f0a0002
com.weinuo.quickcommands:string/update_now = 0x7f0a02b1
com.weinuo.quickcommands:string/shizuku_category_system = 0x7f0a022e
com.weinuo.quickcommands:color/teal_700 = 0x7f02000b
com.weinuo.quickcommands:string/screen_keep_device_awake = 0x7f0a01f8
com.weinuo.quickcommands:string/connectivity_airplane_mode_control_description = 0x7f0a008d
com.weinuo.quickcommands:dimen/compat_button_padding_vertical_material = 0x7f030003
com.weinuo.quickcommands:mipmap/ic_launcher = 0x7f090000
com.weinuo.quickcommands:drawable/notification_action_background = 0x7f04002e
com.weinuo.quickcommands:string/gesture_recognition_accessibility_service_name = 0x7f0a0108
com.weinuo.quickcommands:id/info = 0x7f050038
com.weinuo.quickcommands:menu/advanced_recording_menu = 0x7f080000
com.weinuo.quickcommands:layout/widget_one_click_command = 0x7f07000a
com.weinuo.quickcommands:string/m3c_date_input_no_input_description = 0x7f0a0156
com.weinuo.quickcommands:id/right_icon = 0x7f050047
com.weinuo.quickcommands:layout/overlay_smart_reminder = 0x7f070009
com.weinuo.quickcommands:string/default_keyboard = 0x7f0a00ac
com.weinuo.quickcommands:string/confirm_configuration = 0x7f0a0081
com.weinuo.quickcommands:string/task_phone_description = 0x7f0a027f
com.weinuo.quickcommands:id/accessibility_custom_action_9 = 0x7f050020
com.weinuo.quickcommands:layout/notification_template_custom_big = 0x7f070005
com.weinuo.quickcommands:attr/enterAnim = 0x7f010006
com.weinuo.quickcommands:string/gps_state_description = 0x7f0a010d
com.weinuo.quickcommands:layout/notification_action_tombstone = 0x7f070004
com.weinuo.quickcommands:id/androidx_compose_ui_view_composition_context = 0x7f050029
com.weinuo.quickcommands:string/smart_reminder_configured = 0x7f0a0247
com.weinuo.quickcommands:color/call_notification_answer_color = 0x7f020003
com.weinuo.quickcommands:layout/ime_secondary_split_test_activity = 0x7f070002
com.weinuo.quickcommands:drawable/overlay_background_md3 = 0x7f04003c
com.weinuo.quickcommands:string/battery_charging_state = 0x7f0a0052
com.weinuo.quickcommands:layout/ime_base_split_test_activity = 0x7f070001
com.weinuo.quickcommands:string/task_log = 0x7f0a0276
com.weinuo.quickcommands:string/intelligent_link_recognition_enabled = 0x7f0a0121
com.weinuo.quickcommands:string/template_category_power = 0x7f0a0292
com.weinuo.quickcommands:id/wrapped_composition_tag = 0x7f050063
com.weinuo.quickcommands:string/invert_colors_description = 0x7f0a012d
com.weinuo.quickcommands:id/accessibility_custom_action_27 = 0x7f050015
com.weinuo.quickcommands:string/interface_interaction_accessibility_service_name = 0x7f0a0129
com.weinuo.quickcommands:string/conn_mobile_data = 0x7f0a0084
com.weinuo.quickcommands:id/widget_text_line1 = 0x7f050061
com.weinuo.quickcommands:id/accessibility_custom_action_2 = 0x7f05000d
com.weinuo.quickcommands:id/view_tree_view_model_store_owner = 0x7f050060
com.weinuo.quickcommands:string/advanced_cleanup_strategy = 0x7f0a0010
com.weinuo.quickcommands:string/volume_task_description = 0x7f0a02bb
com.weinuo.quickcommands:string/toggle = 0x7f0a02ac
com.weinuo.quickcommands:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f05005e
com.weinuo.quickcommands:id/time = 0x7f05005a
com.weinuo.quickcommands:id/text2 = 0x7f050059
com.weinuo.quickcommands:string/quick_command_not_found_simple = 0x7f0a01e1
com.weinuo.quickcommands:string/call_notification_answer_action = 0x7f0a0056
com.weinuo.quickcommands:drawable/ic_call_decline_low = 0x7f04000c
com.weinuo.quickcommands:id/tag_unhandled_key_listeners = 0x7f050056
com.weinuo.quickcommands:string/app_tasker_locale_plugin_description = 0x7f0a003e
com.weinuo.quickcommands:id/tag_transition_group = 0x7f050054
com.weinuo.quickcommands:string/intelligent_link_recognition_title = 0x7f0a0123
com.weinuo.quickcommands:dimen/notification_right_icon_size = 0x7f03000f
com.weinuo.quickcommands:string/accessibility_service_description = 0x7f0a0000
com.weinuo.quickcommands:string/phone_status_good = 0x7f0a01d5
com.weinuo.quickcommands:string/language_system_default = 0x7f0a0138
com.weinuo.quickcommands:id/tag_state_description = 0x7f050052
com.weinuo.quickcommands:id/right_side = 0x7f050048
com.weinuo.quickcommands:attr/ttcIndex = 0x7f010026
com.weinuo.quickcommands:id/tag_on_receive_content_listener = 0x7f05004f
com.weinuo.quickcommands:id/tag_accessibility_pane_title = 0x7f05004c
com.weinuo.quickcommands:string/music_app_reminder_status_disabled = 0x7f0a01a0
com.weinuo.quickcommands:drawable/notification_oversize_large_icon_bg = 0x7f040036
com.weinuo.quickcommands:drawable/ic_music_note_24 = 0x7f040016
com.weinuo.quickcommands:id/tag_accessibility_heading = 0x7f05004b
com.weinuo.quickcommands:string/info_send_sms_description = 0x7f0a0119
com.weinuo.quickcommands:id/report_drawn = 0x7f050046
com.weinuo.quickcommands:id/notification_main_column_container = 0x7f050043
com.weinuo.quickcommands:string/screen_control_task = 0x7f0a01f1
com.weinuo.quickcommands:string/comm_call_ended = 0x7f0a0074
com.weinuo.quickcommands:id/notification_background = 0x7f050041
com.weinuo.quickcommands:drawable/ic_sky_blue_check_enabled = 0x7f040028
com.weinuo.quickcommands:string/accessibility_service_system_description = 0x7f0a0006
com.weinuo.quickcommands:string/camera_open_last_photo_description = 0x7f0a0060
com.weinuo.quickcommands:string/delete = 0x7f0a00af
com.weinuo.quickcommands:id/normal = 0x7f050040
com.weinuo.quickcommands:string/m3c_time_picker_minute_selection = 0x7f0a017d
com.weinuo.quickcommands:string/flashlight_reminder_status_disabled = 0x7f0a00fb
com.weinuo.quickcommands:string/accessibility_service_go_to_settings = 0x7f0a0003
com.weinuo.quickcommands:id/nav_controller_view_tag = 0x7f05003f
com.weinuo.quickcommands:id/iv_reminder_icon = 0x7f05003c
com.weinuo.quickcommands:id/italic = 0x7f05003b
com.weinuo.quickcommands:attr/nullable = 0x7f01001a
com.weinuo.quickcommands:id/hide_graphics_layer_in_inspector_tag = 0x7f050033
com.weinuo.quickcommands:string/m3c_date_range_input_invalid_range_input = 0x7f0a0167
com.weinuo.quickcommands:string/shizuku_install_guide = 0x7f0a022f
com.weinuo.quickcommands:id/edit_text_id = 0x7f050031
com.weinuo.quickcommands:string/music_app_reminder_title = 0x7f0a01a3
com.weinuo.quickcommands:attr/navGraph = 0x7f010018
com.weinuo.quickcommands:id/chronometer = 0x7f05002c
com.weinuo.quickcommands:string/demo_mode = 0x7f0a00b4
com.weinuo.quickcommands:string/auto_rotate_enable = 0x7f0a0048
com.weinuo.quickcommands:id/view_tree_saved_state_registry_owner = 0x7f05005f
com.weinuo.quickcommands:attr/fontProviderQuery = 0x7f01000f
com.weinuo.quickcommands:id/async = 0x7f05002a
com.weinuo.quickcommands:mipmap/ic_launcher_round = 0x7f090001
com.weinuo.quickcommands:id/tag_on_apply_window_listener = 0x7f05004e
com.weinuo.quickcommands:string/device_logcat_message = 0x7f0a00c3
com.weinuo.quickcommands:id/action_container = 0x7f050022
com.weinuo.quickcommands:drawable/template_anti_embarrassment_mode = 0x7f04003d
com.weinuo.quickcommands:drawable/ic_shopping_cart_24 = 0x7f040024
com.weinuo.quickcommands:string/conn_bluetooth_state_description = 0x7f0a0083
com.weinuo.quickcommands:string/power_save_mode = 0x7f0a01d9
com.weinuo.quickcommands:string/app_link_reminder_status_unconfigured = 0x7f0a002d
com.weinuo.quickcommands:string/battery_battery_level = 0x7f0a0050
com.weinuo.quickcommands:styleable/NavInclude = 0x7f0c000c
com.weinuo.quickcommands:attr/action = 0x7f010000
com.weinuo.quickcommands:id/accessibility_custom_action_18 = 0x7f05000b
com.weinuo.quickcommands:string/default_error_message = 0x7f0a00ab
com.weinuo.quickcommands:color/purple_500 = 0x7f020008
com.weinuo.quickcommands:string/time_condition_delayed_trigger_description = 0x7f0a02a0
com.weinuo.quickcommands:string/no_templates = 0x7f0a01b4
com.weinuo.quickcommands:id/accessibility_custom_action_7 = 0x7f05001e
com.weinuo.quickcommands:id/accessibility_custom_action_6 = 0x7f05001d
com.weinuo.quickcommands:string/conn_mobile_data_description = 0x7f0a0085
com.weinuo.quickcommands:string/sensor_flip_sensor_description = 0x7f0a0211
com.weinuo.quickcommands:layout/notification_action = 0x7f070003
com.weinuo.quickcommands:string/m3c_date_picker_scroll_to_earlier_years = 0x7f0a015c
com.weinuo.quickcommands:id/accessibility_custom_action_5 = 0x7f05001c
com.weinuo.quickcommands:style/EdgeToEdgeFloatingDialogTheme = 0x7f0b0001
com.weinuo.quickcommands:attr/popUpToSaveState = 0x7f01001f
com.weinuo.quickcommands:string/no_quick_commands = 0x7f0a01b1
com.weinuo.quickcommands:drawable/ic_radio_button_unchecked_sky_blue = 0x7f04001b
com.weinuo.quickcommands:string/conn_bluetooth_state = 0x7f0a0082
com.weinuo.quickcommands:string/device_settings_invert_colors_description = 0x7f0a00d6
com.weinuo.quickcommands:id/blocking = 0x7f05002b
com.weinuo.quickcommands:string/add_cleanup_rule = 0x7f0a0009
com.weinuo.quickcommands:id/accessibility_custom_action_4 = 0x7f05001b
com.weinuo.quickcommands:styleable/ActivityNavigator = 0x7f0c0000
com.weinuo.quickcommands:attr/fontProviderFetchTimeout = 0x7f01000d
com.weinuo.quickcommands:string/quick_command_completed = 0x7f0a01dc
com.weinuo.quickcommands:id/accessibility_custom_action_3 = 0x7f050018
com.weinuo.quickcommands:string/comm_outgoing_call = 0x7f0a0078
com.weinuo.quickcommands:drawable/ic_launcher_background = 0x7f040011
com.weinuo.quickcommands:string/gps_state = 0x7f0a010c
com.weinuo.quickcommands:drawable/ic_map_24 = 0x7f040015
com.weinuo.quickcommands:dimen/notification_subtext_size = 0x7f030013
com.weinuo.quickcommands:string/call_notification_screening_text = 0x7f0a005c
com.weinuo.quickcommands:id/accessibility_custom_action_28 = 0x7f050016
com.weinuo.quickcommands:drawable/notification_template_icon_low_bg = 0x7f040038
com.weinuo.quickcommands:id/accessibility_custom_action_26 = 0x7f050014
com.weinuo.quickcommands:string/app_lifecycle = 0x7f0a0028
com.weinuo.quickcommands:id/inspection_slot_table_set = 0x7f050039
com.weinuo.quickcommands:layout/notification_template_part_time = 0x7f070008
com.weinuo.quickcommands:string/connectivity_airplane_mode_control = 0x7f0a008c
com.weinuo.quickcommands:drawable/ic_call_answer_low = 0x7f040008
com.weinuo.quickcommands:id/accessibility_custom_action_21 = 0x7f05000f
com.weinuo.quickcommands:string/connectivity_network_check_description = 0x7f0a0095
com.weinuo.quickcommands:string/info_send_sms = 0x7f0a0118
com.weinuo.quickcommands:id/icon_group = 0x7f050037
com.weinuo.quickcommands:drawable/notification_icon_background = 0x7f040035
com.weinuo.quickcommands:id/accessibility_custom_action_17 = 0x7f05000a
com.weinuo.quickcommands:id/accessibility_custom_action_10 = 0x7f050003
com.weinuo.quickcommands:string/task_location_description = 0x7f0a0275
com.weinuo.quickcommands:string/phone_reject_call_description = 0x7f0a01d1
com.weinuo.quickcommands:id/accessibility_action_clickable_span = 0x7f050000
com.weinuo.quickcommands:string/notification_cancel_notification_description = 0x7f0a01b7
com.weinuo.quickcommands:string/app_management = 0x7f0a002f
com.weinuo.quickcommands:drawable/template_screen_on_network = 0x7f04003f
com.weinuo.quickcommands:string/m3c_date_input_invalid_for_pattern = 0x7f0a0152
com.weinuo.quickcommands:string/accessibility_service_manual_title = 0x7f0a0004
com.weinuo.quickcommands:dimen/compat_control_corner_material = 0x7f030004
com.weinuo.quickcommands:string/connectivity_send_intent_description = 0x7f0a0099
com.weinuo.quickcommands:string/device_clipboard_changed_description = 0x7f0a00be
com.weinuo.quickcommands:id/tag_accessibility_clickable_spans = 0x7f05004a
com.weinuo.quickcommands:id/hide_ime_id = 0x7f050034
com.weinuo.quickcommands:string/conn_wifi_network_description = 0x7f0a0087
com.weinuo.quickcommands:string/manual_widget_update_description = 0x7f0a0193
com.weinuo.quickcommands:string/experimental_features = 0x7f0a00ef
com.weinuo.quickcommands:string/time_condition_stopwatch = 0x7f0a02a6
com.weinuo.quickcommands:attr/fontProviderAuthority = 0x7f010009
com.weinuo.quickcommands:drawable/template_screen_off_network = 0x7f04003e
com.weinuo.quickcommands:string/camera_task_description = 0x7f0a0068
com.weinuo.quickcommands:id/tag_accessibility_actions = 0x7f050049
com.weinuo.quickcommands:drawable/notification_tile_bg = 0x7f040039
com.weinuo.quickcommands:drawable/notification_bg_normal = 0x7f040033
com.weinuo.quickcommands:string/intelligent_link_recognition_help = 0x7f0a0122
com.weinuo.quickcommands:string/call_notification_incoming_text = 0x7f0a005a
com.weinuo.quickcommands:string/battery_battery_level_description = 0x7f0a0051
com.weinuo.quickcommands:string/edit_condition_description = 0x7f0a00e9
com.weinuo.quickcommands:drawable/notification_bg_low_normal = 0x7f040031
com.weinuo.quickcommands:string/new_app_reminder_status_unconfigured = 0x7f0a01af
com.weinuo.quickcommands:drawable/notification_bg = 0x7f04002f
com.weinuo.quickcommands:id/accessibility_custom_action_30 = 0x7f050019
com.weinuo.quickcommands:drawable/menu_background = 0x7f04002c
com.weinuo.quickcommands:drawable/ic_touch_app = 0x7f04002b
com.weinuo.quickcommands:id/accessibility_custom_action_1 = 0x7f050002
com.weinuo.quickcommands:drawable/ic_apps_24 = 0x7f040006
com.weinuo.quickcommands:dimen/notification_action_icon_size = 0x7f030007
com.weinuo.quickcommands:string/m3c_tooltip_pane_description = 0x7f0a0183
com.weinuo.quickcommands:drawable/notification_template_icon_bg = 0x7f040037
com.weinuo.quickcommands:id/tag_window_insets_animation_callback = 0x7f050057
com.weinuo.quickcommands:string/battery_charging_state_description = 0x7f0a0053
com.weinuo.quickcommands:drawable/ic_shortcut_command = 0x7f040025
com.weinuo.quickcommands:drawable/ic_close = 0x7f04000f
com.weinuo.quickcommands:string/widget_update_enabled_description = 0x7f0a02ca
com.weinuo.quickcommands:string/template_auto_brightness_title = 0x7f0a028c
com.weinuo.quickcommands:dimen/compat_notification_large_icon_max_height = 0x7f030005
com.weinuo.quickcommands:drawable/ic_settings = 0x7f040022
com.weinuo.quickcommands:string/task_device_settings = 0x7f0a0270
com.weinuo.quickcommands:string/power_save_mode_description = 0x7f0a01da
com.weinuo.quickcommands:drawable/ic_search_sky_blue_medium = 0x7f040020
com.weinuo.quickcommands:id/action_divider = 0x7f050023
com.weinuo.quickcommands:style/FloatingDialogTheme = 0x7f0b0003
com.weinuo.quickcommands:string/nav_phone_checkup = 0x7f0a01a8
com.weinuo.quickcommands:drawable/ic_search_sky_blue_bold = 0x7f04001f
com.weinuo.quickcommands:string/comm_call_active_description = 0x7f0a0073
com.weinuo.quickcommands:attr/restoreState = 0x7f010021
com.weinuo.quickcommands:id/accessibility_custom_action_0 = 0x7f050001
com.weinuo.quickcommands:drawable/ic_radio_button_checked_sky_blue = 0x7f04001a
com.weinuo.quickcommands:color/call_notification_decline_color = 0x7f020004
com.weinuo.quickcommands:id/accessibility_custom_action_15 = 0x7f050008
com.weinuo.quickcommands:string/manual_dynamic_shortcut_description = 0x7f0a0185
com.weinuo.quickcommands:string/location_force_location_update_description = 0x7f0a013c
com.weinuo.quickcommands:string/time_condition_periodic_trigger = 0x7f0a02a3
com.weinuo.quickcommands:drawable/ic_close_24 = 0x7f040010
com.weinuo.quickcommands:string/m3c_date_picker_switch_to_previous_month = 0x7f0a0162
com.weinuo.quickcommands:string/battery_state_description = 0x7f0a0055
com.weinuo.quickcommands:integer/m3c_window_layout_in_display_cutout_mode = 0x7f060000
com.weinuo.quickcommands:drawable/ic_screen_rotation_24 = 0x7f04001e
com.weinuo.quickcommands:drawable/ic_call_decline = 0x7f04000b
com.weinuo.quickcommands:attr/fontProviderFallbackQuery = 0x7f01000b
com.weinuo.quickcommands:string/m3c_date_input_invalid_year_range = 0x7f0a0154
com.weinuo.quickcommands:drawable/ic_call_answer_video_low = 0x7f04000a
com.weinuo.quickcommands:string/phone_answer_call = 0x7f0a01c7
com.weinuo.quickcommands:attr/font = 0x7f010008
com.weinuo.quickcommands:string/quick_command_aborted = 0x7f0a01db
com.weinuo.quickcommands:string/comm_sms_sent = 0x7f0a007c
com.weinuo.quickcommands:id/accessibility_custom_action_24 = 0x7f050012
com.weinuo.quickcommands:string/comm_call_active = 0x7f0a0072
com.weinuo.quickcommands:string/sensor_activity_recognition = 0x7f0a020e
com.weinuo.quickcommands:color/androidx_core_ripple_material_light = 0x7f020000
com.weinuo.quickcommands:xml/system_operation_accessibility_service_config = 0x7f0d000d
com.weinuo.quickcommands:id/view_tree_disjoint_parent = 0x7f05005c
com.weinuo.quickcommands:string/manual_fingerprint_gesture = 0x7f0a0186
com.weinuo.quickcommands:string/m3c_date_range_picker_end_headline = 0x7f0a016a
com.weinuo.quickcommands:drawable/ic_add_skyblue = 0x7f040005
com.weinuo.quickcommands:string/login_attempt_failed = 0x7f0a0149
com.weinuo.quickcommands:string/execution_mode = 0x7f0a00ee
com.weinuo.quickcommands:drawable/ic_stop = 0x7f040029
com.weinuo.quickcommands:drawable/circular_progress_drawable = 0x7f040001
com.weinuo.quickcommands:string/nav_command_templates = 0x7f0a01a6
com.weinuo.quickcommands:string/new_app_reminder_title = 0x7f0a01b0
com.weinuo.quickcommands:string/sun_event_sunrise = 0x7f0a025d
com.weinuo.quickcommands:string/screen_content_text = 0x7f0a01ee
com.weinuo.quickcommands:string/notification_task_description = 0x7f0a01bf
com.weinuo.quickcommands:string/switch_operation_off = 0x7f0a025f
com.weinuo.quickcommands:id/widget_text_line2 = 0x7f050062
com.weinuo.quickcommands:drawable/widget_background = 0x7f040040
com.weinuo.quickcommands:string/tooltip_description = 0x7f0a02ad
com.weinuo.quickcommands:drawable/overlay_background = 0x7f04003b
com.weinuo.quickcommands:string/package_management_description = 0x7f0a01c5
com.weinuo.quickcommands:drawable/$ic_launcher_foreground__0 = 0x7f040000
com.weinuo.quickcommands:attr/destination = 0x7f010005
com.weinuo.quickcommands:attr/launchSingleTop = 0x7f010016
com.weinuo.quickcommands:string/call_notification_decline_action = 0x7f0a0058
com.weinuo.quickcommands:color/white = 0x7f02000e
com.weinuo.quickcommands:attr/queryPatterns = 0x7f010020
com.weinuo.quickcommands:layout/notification_template_icon_group = 0x7f070006
com.weinuo.quickcommands:dimen/notification_large_icon_height = 0x7f03000b
com.weinuo.quickcommands:string/template_do_not_disturb_title = 0x7f0a0295
com.weinuo.quickcommands:dimen/notification_right_side_padding_top = 0x7f030010
com.weinuo.quickcommands:dimen/notification_main_column_padding_top = 0x7f03000d
com.weinuo.quickcommands:string/task_file_operation = 0x7f0a0272
com.weinuo.quickcommands:drawable/ic_play_arrow = 0x7f040019
com.weinuo.quickcommands:drawable/ic_long_press = 0x7f040014
com.weinuo.quickcommands:dimen/compat_button_inset_horizontal_material = 0x7f030000
com.weinuo.quickcommands:string/manual_static_shortcut = 0x7f0a018c
com.weinuo.quickcommands:drawable/ic_pause = 0x7f040018
com.weinuo.quickcommands:string/time_condition_stopwatch_description = 0x7f0a02a7
com.weinuo.quickcommands:string/shopping_app_reminder_status_disabled = 0x7f0a0234
com.weinuo.quickcommands:string/screen_event_on = 0x7f0a01f6
com.weinuo.quickcommands:id/title = 0x7f05005b
com.weinuo.quickcommands:string/ambient_display_description = 0x7f0a0016
com.weinuo.quickcommands:dimen/compat_notification_large_icon_max_width = 0x7f030006
com.weinuo.quickcommands:string/stopwatch_selection = 0x7f0a025a
com.weinuo.quickcommands:string/ringer_mode_changed_description = 0x7f0a01e6
com.weinuo.quickcommands:attr/exitAnim = 0x7f010007
com.weinuo.quickcommands:id/tag_system_bar_state_monitor = 0x7f050053
com.weinuo.quickcommands:attr/fontStyle = 0x7f010011
com.weinuo.quickcommands:string/screen_event_auto_rotate_disabled = 0x7f0a01f3
com.weinuo.quickcommands:id/action_text = 0x7f050027
com.weinuo.quickcommands:color/black = 0x7f020002
com.weinuo.quickcommands:string/app_launch_app = 0x7f0a0026
com.weinuo.quickcommands:drawable/ic_lightbulb_24 = 0x7f040013
com.weinuo.quickcommands:string/font_weight_regular = 0x7f0a0105
com.weinuo.quickcommands:string/close_sheet = 0x7f0a0071
com.weinuo.quickcommands:id/hide_in_inspector_tag = 0x7f050035
com.weinuo.quickcommands:string/datetime_stopwatch_description = 0x7f0a00a6
com.weinuo.quickcommands:attr/shortcutMatchRequired = 0x7f010023
com.weinuo.quickcommands:styleable/NavGraphNavigator = 0x7f0c000a
com.weinuo.quickcommands:color/vector_tint_color = 0x7f02000c
com.weinuo.quickcommands:string/app_open_website = 0x7f0a0032
com.weinuo.quickcommands:string/connectivity_nfc_control_description = 0x7f0a0097
com.weinuo.quickcommands:drawable/ic_call_answer = 0x7f040007
com.weinuo.quickcommands:string/device_settings_display_density_description = 0x7f0a00cc
com.weinuo.quickcommands:attr/fontWeight = 0x7f010013
com.weinuo.quickcommands:attr/argType = 0x7f010002
com.weinuo.quickcommands:string/m3c_date_input_title = 0x7f0a0157
com.weinuo.quickcommands:attr/popUpToInclusive = 0x7f01001e
com.weinuo.quickcommands:string/optimize_button = 0x7f0a01c2
com.weinuo.quickcommands:dimen/notification_content_margin_start = 0x7f03000a
com.weinuo.quickcommands:id/actions = 0x7f050028
com.weinuo.quickcommands:color/teal_200 = 0x7f02000a
com.weinuo.quickcommands:drawable/notification_bg_low_pressed = 0x7f040032
com.weinuo.quickcommands:string/comm_outgoing_call_description = 0x7f0a0079
com.weinuo.quickcommands:string/m3c_bottom_sheet_drag_handle_description = 0x7f0a014d
com.weinuo.quickcommands:xml/file_provider_paths = 0x7f0d0005
com.weinuo.quickcommands:string/camera_flashlight_control = 0x7f0a005d
com.weinuo.quickcommands:string/m3c_dropdown_menu_collapsed = 0x7f0a0170
com.weinuo.quickcommands:id/line3 = 0x7f05003e
com.weinuo.quickcommands:color/purple_700 = 0x7f020009
com.weinuo.quickcommands:string/search_field_placeholder_font_weight = 0x7f0a0205
com.weinuo.quickcommands:string/manual_dynamic_shortcut = 0x7f0a0184
com.weinuo.quickcommands:string/app_selection = 0x7f0a0038
com.weinuo.quickcommands:string/camera_flashlight_control_description = 0x7f0a005e
com.weinuo.quickcommands:string/m3c_bottom_sheet_expand_description = 0x7f0a014e
com.weinuo.quickcommands:string/icon_weight_bold = 0x7f0a010e
com.weinuo.quickcommands:integer/status_bar_notification_info_maxnum = 0x7f060001
com.weinuo.quickcommands:string/app_name = 0x7f0a0031
com.weinuo.quickcommands:string/invert_colors_off = 0x7f0a012e
com.weinuo.quickcommands:string/address_reminder_message = 0x7f0a000c
com.weinuo.quickcommands:drawable/notify_panel_notification_icon_bg = 0x7f04003a
com.weinuo.quickcommands:string/connectivity_mobile_data_control = 0x7f0a0092
com.weinuo.quickcommands:color/notification_action_color_filter = 0x7f020005
com.weinuo.quickcommands:attr/lStar = 0x7f010015
com.weinuo.quickcommands:id/progress_auto_dismiss = 0x7f050045
com.weinuo.quickcommands:string/check_text_content = 0x7f0a006c
com.weinuo.quickcommands:string/app_launch_app_description = 0x7f0a0027
com.weinuo.quickcommands:string/volume_speakerphone_control = 0x7f0a02b8
com.weinuo.quickcommands:string/device_settings_immersive_mode_description = 0x7f0a00d4
com.weinuo.quickcommands:string/androidx_startup = 0x7f0a0017
com.weinuo.quickcommands:string/phone_checkup_title = 0x7f0a01c9
com.weinuo.quickcommands:string/widget_update_enabled = 0x7f0a02c9
com.weinuo.quickcommands:string/music_playback_state = 0x7f0a01a4
com.weinuo.quickcommands:string/device_settings = 0x7f0a00c5
com.weinuo.quickcommands:string/auto_clicker_accessibility_service_name = 0x7f0a0046
com.weinuo.quickcommands:string/app_trigger_all_apps = 0x7f0a003f
com.weinuo.quickcommands:string/sensor_light_sensor_description = 0x7f0a0213
com.weinuo.quickcommands:dimen/notification_action_text_size = 0x7f030008
com.weinuo.quickcommands:attr/fontProviderFetchStrategy = 0x7f01000c
com.weinuo.quickcommands:string/m3c_date_picker_headline = 0x7f0a0158
com.weinuo.quickcommands:string/app_state_change_description = 0x7f0a003b
com.weinuo.quickcommands:string/app_screen_content_description = 0x7f0a0037
com.weinuo.quickcommands:string/m3c_date_input_headline_description = 0x7f0a0151
com.weinuo.quickcommands:string/autofill = 0x7f0a004b
com.weinuo.quickcommands:attr/popUpTo = 0x7f01001d
com.weinuo.quickcommands:string/auto_clicker_accessibility_service_description = 0x7f0a0045
com.weinuo.quickcommands:string/screen_content = 0x7f0a01ec
com.weinuo.quickcommands:drawable/haze_noise = 0x7f040004
com.weinuo.quickcommands:string/media_microphone_recording_description = 0x7f0a0196
com.weinuo.quickcommands:attr/fontProviderSystemFontFamily = 0x7f010010
com.weinuo.quickcommands:string/device_settings_display_density = 0x7f0a00cb
com.weinuo.quickcommands:attr/nestedScrollViewStyle = 0x7f010019
com.weinuo.quickcommands:drawable/ic_launcher_foreground = 0x7f040012
com.weinuo.quickcommands:string/interface_interaction_accessibility_service_description = 0x7f0a0128
com.weinuo.quickcommands:string/icon_weight_selection_title = 0x7f0a0111
com.weinuo.quickcommands:string/change_map_apps = 0x7f0a006a
com.weinuo.quickcommands:layout/custom_dialog = 0x7f070000
com.weinuo.quickcommands:attr/fontProviderPackage = 0x7f01000e
com.weinuo.quickcommands:attr/startDestination = 0x7f010024
com.weinuo.quickcommands:id/accessibility_custom_action_14 = 0x7f050007
com.weinuo.quickcommands:drawable/ic_notification = 0x7f040017
com.weinuo.quickcommands:attr/fontProviderCerts = 0x7f01000a
com.weinuo.quickcommands:string/app_management_description = 0x7f0a0030
com.weinuo.quickcommands:string/ringtone_selection = 0x7f0a01e7
com.weinuo.quickcommands:string/screen_rotation_reminder_description = 0x7f0a01fa
com.weinuo.quickcommands:string/select_music_apps = 0x7f0a020c
com.weinuo.quickcommands:id/accessibility_custom_action_13 = 0x7f050006

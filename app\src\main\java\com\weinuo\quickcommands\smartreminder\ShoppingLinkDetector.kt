package com.weinuo.quickcommands.smartreminder

import android.util.Log

/**
 * 购物链接检测器
 *
 * 负责检测剪贴板中的购物链接，识别对应的购物平台。
 * 支持主流购物平台的链接检测，包括：
 * - 淘宝/天猫
 * - 京东
 * - 拼多多
 * - 苏宁易购
 * - 唯品会
 *
 * 设计特点：
 * - 高准确性：使用正则表达式精确匹配
 * - 高性能：优化的检测算法
 * - 易扩展：便于添加新的购物平台
 * - 容错性：处理各种链接格式变体
 */
object ShoppingLinkDetector {

    private const val TAG = "ShoppingLinkDetector"

    /**
     * 购物平台信息
     */
    data class ShoppingPlatform(
        val name: String,
        val packageName: String,
        val urlPatterns: List<String>
    )

    /**
     * 检测结果
     */
    data class DetectionResult(
        val isShoppingLink: Boolean,
        val platform: ShoppingPlatform? = null,
        val originalUrl: String = ""
    )

    /**
     * 支持的购物平台配置
     */
    private val shoppingPlatforms = listOf(
        ShoppingPlatform(
            name = "淘宝",
            packageName = "com.taobao.taobao",
            urlPatterns = listOf(
                ".*taobao\\.com.*",
                ".*tmall\\.com.*",
                ".*tb\\.cn.*",
                ".*m\\.tb\\.cn.*",
                ".*item\\.taobao\\.com.*",
                ".*detail\\.tmall\\.com.*"
            )
        ),
        ShoppingPlatform(
            name = "京东",
            packageName = "com.jingdong.app.mall",
            urlPatterns = listOf(
                ".*jd\\.com.*",
                ".*3\\.cn.*",
                ".*item\\.jd\\.com.*",
                ".*m\\.jd\\.com.*",
                ".*wq\\.jd\\.com.*"
            )
        ),
        ShoppingPlatform(
            name = "拼多多",
            packageName = "com.xunmeng.pinduoduo",
            urlPatterns = listOf(
                ".*pinduoduo\\.com.*",
                ".*pdd\\.cn.*",
                ".*yangkeduo\\.com.*",
                ".*mobile\\.yangkeduo\\.com.*"
            )
        ),
        ShoppingPlatform(
            name = "天猫",
            packageName = "com.tmall.wireless",
            urlPatterns = listOf(
                ".*tmall\\.com.*",
                ".*detail\\.tmall\\.com.*",
                ".*m\\.tmall\\.com.*"
            )
        ),
        ShoppingPlatform(
            name = "苏宁易购",
            packageName = "com.suning.mobile.ebuy",
            urlPatterns = listOf(
                ".*suning\\.com.*",
                ".*m\\.suning\\.com.*",
                ".*product\\.suning\\.com.*"
            )
        ),
        ShoppingPlatform(
            name = "唯品会",
            packageName = "com.achievo.vipshop",
            urlPatterns = listOf(
                ".*vip\\.com.*",
                ".*m\\.vip\\.com.*",
                ".*detail\\.vip\\.com.*"
            )
        )
    )

    /**
     * 检测文本中是否包含购物链接
     *
     * @param text 要检测的文本内容
     * @param enabledBuiltInPlatforms 启用的内置平台
     * @param customPlatforms 自定义平台列表
     * @return 检测结果，包含是否为购物链接和对应平台信息
     */
    fun detectShoppingLink(
        text: String,
        enabledBuiltInPlatforms: Set<String> = setOf("淘宝", "京东", "天猫", "拼多多"),
        customPlatforms: List<com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.CustomShoppingPlatform> = emptyList()
    ): DetectionResult {
        if (text.isBlank()) {
            return DetectionResult(false)
        }

        try {
            Log.d(TAG, "Detecting shopping link in text: ${text.take(100)}...")

            // 预处理文本，移除多余的空白字符
            val cleanText = text.trim()

            // 检查是否包含URL
            if (!containsUrl(cleanText)) {
                Log.d(TAG, "No URL found in text")
                return DetectionResult(false)
            }

            // 首先检查启用的内置平台
            for (platform in shoppingPlatforms) {
                if (platform.name in enabledBuiltInPlatforms && matchesPlatform(cleanText, platform)) {
                    Log.d(TAG, "Detected ${platform.name} shopping link (built-in)")
                    return DetectionResult(
                        isShoppingLink = true,
                        platform = platform,
                        originalUrl = cleanText
                    )
                }
            }

            // 然后检查自定义平台
            for (customPlatform in customPlatforms) {
                if (customPlatform.enabled && matchesCustomPlatform(cleanText, customPlatform)) {
                    Log.d(TAG, "Detected ${customPlatform.name} shopping link (custom)")
                    // 转换为标准平台格式
                    val platform = ShoppingPlatform(
                        name = customPlatform.name,
                        packageName = customPlatform.packageName,
                        urlPatterns = customPlatform.urlPatterns
                    )
                    return DetectionResult(
                        isShoppingLink = true,
                        platform = platform,
                        originalUrl = cleanText
                    )
                }
            }

            Log.d(TAG, "No shopping platform matched")
            return DetectionResult(false, originalUrl = cleanText)

        } catch (e: Exception) {
            Log.e(TAG, "Error detecting shopping link", e)
            return DetectionResult(false)
        }
    }

    /**
     * 检查文本是否包含URL
     */
    private fun containsUrl(text: String): Boolean {
        val urlPattern = "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+".toRegex(RegexOption.IGNORE_CASE)
        return urlPattern.containsMatchIn(text)
    }

    /**
     * 检查文本是否匹配指定购物平台
     */
    private fun matchesPlatform(text: String, platform: ShoppingPlatform): Boolean {
        return platform.urlPatterns.any { pattern ->
            try {
                val regex = pattern.toRegex(RegexOption.IGNORE_CASE)
                regex.containsMatchIn(text)
            } catch (e: Exception) {
                Log.w(TAG, "Invalid regex pattern: $pattern", e)
                false
            }
        }
    }

    /**
     * 检查文本是否匹配自定义购物平台
     */
    private fun matchesCustomPlatform(
        text: String,
        platform: com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.CustomShoppingPlatform
    ): Boolean {
        return platform.urlPatterns.any { pattern ->
            try {
                if (platform.useRegex) {
                    // 使用正则表达式匹配
                    val regexOptions = if (platform.caseSensitive) {
                        emptySet()
                    } else {
                        setOf(RegexOption.IGNORE_CASE)
                    }
                    val regex = pattern.toRegex(regexOptions)
                    regex.containsMatchIn(text)
                } else {
                    // 使用简单文本匹配
                    if (platform.caseSensitive) {
                        text.contains(pattern)
                    } else {
                        text.contains(pattern, ignoreCase = true)
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "Invalid pattern: $pattern", e)
                false
            }
        }
    }

    /**
     * 获取所有支持的购物平台
     */
    fun getSupportedPlatforms(): List<ShoppingPlatform> {
        return shoppingPlatforms.toList()
    }

    /**
     * 根据包名获取购物平台信息
     */
    fun getPlatformByPackageName(packageName: String): ShoppingPlatform? {
        return shoppingPlatforms.find { it.packageName == packageName }
    }

    /**
     * 根据平台名称获取购物平台信息
     */
    fun getPlatformByName(name: String): ShoppingPlatform? {
        return shoppingPlatforms.find { it.name == name }
    }
}

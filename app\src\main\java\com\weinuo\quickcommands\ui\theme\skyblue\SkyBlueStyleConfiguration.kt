package com.weinuo.quickcommands.ui.theme.skyblue

import android.content.Context
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.system.StyleConfiguration
import com.weinuo.quickcommands.ui.theme.config.*
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.PageLayoutConfigurationManager

/**
 * 天空蓝主题样式配置
 *
 * 实现整合设计风格的样式配置
 * 特点：更大的圆角、无阴影、支持模糊效果、更大的间距
 *
 * 支持动态卡片样式配置，从GlobalSettings中读取用户自定义设置
 */
class SkyBlueStyleConfiguration : StyleConfiguration {

    companion object {
        /**
         * 获取动态卡片样式配置
         *
         * 从GlobalSettings中读取用户自定义的卡片样式配置
         */
        @Composable
        fun getDynamicCardStyleConfig(settingsRepository: SettingsRepository): CardStyleConfig {
            val globalSettings by settingsRepository.globalSettings.collectAsState()

            return CardStyleConfig(
                // 基础卡片样式 - 从设置中读取
                defaultCornerRadius = globalSettings.cardCornerRadius.dp,
                defaultElevation = 0.dp,      // 天空蓝主题始终无阴影
                defaultHorizontalPadding = globalSettings.cardDefaultHorizontalPadding.dp,
                defaultVerticalPadding = globalSettings.cardDefaultVerticalPadding.dp,
                settingsVerticalPadding = globalSettings.cardSettingsVerticalPadding.dp,

                // 间距配置 - 从设置中读取
                itemSpacing = globalSettings.cardItemSpacing.dp,
                sectionSpacing = globalSettings.cardSectionSpacing.dp,

                // 内容间距配置 - 从设置中读取
                contentVerticalSpacing = globalSettings.cardContentVerticalSpacing.dp,
                contentHorizontalSpacing = globalSettings.cardContentHorizontalSpacing.dp,

                // 选择状态样式 - 从设置中读取
                selectedElevation = globalSettings.cardSelectedElevation.dp,
                selectedBorderWidth = globalSettings.cardSelectedBorderWidth.dp
            )
        }

        /**
         * 获取动态页面布局配置
         *
         * 从全局设置中读取用户自定义的页面布局配置
         * 用于天空蓝主题的页面布局自定义功能
         */
        fun getDynamicPageLayoutConfig(settingsRepository: SettingsRepository): PageLayoutConfig {
            val globalSettings = settingsRepository.globalSettings.value

            return PageLayoutConfig(
                // 页面内容边距配置 - 从设置中读取
                contentHorizontalPadding = globalSettings.pageContentHorizontalPadding.dp,

                // 搜索框配置 - 从设置中读取
                searchFieldMargin = globalSettings.pageSearchFieldMargin.dp,

                // 页面间距配置 - 从设置中读取
                headerSpacing = globalSettings.pageHeaderSpacing.dp,
                bottomPadding = globalSettings.pageBottomPadding.dp,
                scrollContentSpacing = globalSettings.pageScrollContentSpacing.dp
            )
        }
    }

    /**
     * 圆角配置 - 整合设计使用更大的圆角
     */
    override val cornerRadius = CornerRadiusConfig(
        extraSmall = 6.dp,  // 超小圆角
        small = 8.dp,       // 小圆角 - 比分层设计更大
        medium = 12.dp,     // 中等圆角
        large = 16.dp,      // 大圆角
        extraLarge = 24.dp  // 超大圆角
    )

    /**
     * 阴影配置 - 整合设计不使用阴影
     */
    override val elevation = ElevationConfig(
        none = 0.dp,        // 无阴影
        low = 0.dp,         // 低阴影 - 不使用
        medium = 0.dp,      // 中等阴影 - 不使用
        high = 0.dp,        // 高阴影 - 不使用
        extraHigh = 0.dp    // 超高阴影 - 不使用
    )

    /**
     * 间距配置 - 整合设计使用更大的间距
     */
    override val spacing = SpacingConfig(
        extraSmall = 6.dp,  // 超小间距
        small = 12.dp,      // 小间距 - 比分层设计更大
        medium = 20.dp,     // 中等间距
        large = 28.dp,      // 大间距
        extraLarge = 36.dp  // 超大间距
    )

    /**
     * 字体配置 - 整合设计的字体设置
     */
    override val typography = TypographyConfig(
        typography = Typography(), // 使用Material 3默认字体
        lineHeightMultiplier = 1.2f, // 行高倍数
        letterSpacingMultiplier = 1.0f // 字间距倍数
    )

    /**
     * 视觉效果配置 - 整合设计支持多种效果
     */
    override val effects = EffectsConfig(
        blurEnabled = true,           // 支持模糊效果
        blurRadius = 20.dp,          // 默认模糊半径
        transparencyEnabled = true,   // 支持透明度
        shadowEnabled = false,        // 不使用阴影
        gradientEnabled = true,       // 支持渐变
        animationsEnabled = true      // 支持动画
    )

    /**
     * 边框配置 - 整合设计无边框
     */
    override val borders = BorderConfig(
        width = 0.dp,                 // 无边框
        color = Color.Transparent,    // 透明边框色
        enabled = false               // 不启用边框
    )

    /**
     * 阴影配置 - 整合设计不使用阴影
     */
    override val shadows = ShadowConfig(
        enabled = false,              // 不启用阴影
        color = Color.Transparent,    // 透明阴影色
        offsetX = 0.dp,              // X轴偏移
        offsetY = 0.dp,              // Y轴偏移
        blurRadius = 0.dp,           // 模糊半径
        spreadRadius = 0.dp          // 扩散半径
    )

    /**
     * 动画配置 - 整合设计的动画设置
     */
    override val animations = AnimationConfig(
        enabled = true,               // 启用动画
        duration = 300,              // 默认动画时长（毫秒）
        easing = "ease_in_out"       // 缓动函数
    )

    /**
     * 颜色透明度配置
     */
    override val opacity = OpacityConfig(
        disabled = 0.38f,     // 禁用状态透明度
        inactive = 0.6f,      // 非活跃状态透明度
        active = 1.0f,        // 活跃状态透明度
        hover = 0.08f,        // 悬停状态透明度
        pressed = 0.12f,      // 按压状态透明度
        focused = 0.12f,      // 焦点状态透明度
        selected = 0.16f,     // 选中状态透明度
        dragging = 0.16f      // 拖拽状态透明度
    )

    /**
     * 尺寸配置
     */
    override val dimensions = DimensionConfig(
        minTouchTarget = 48.dp,   // 最小触摸目标尺寸
        iconSize = 24.dp,         // 图标尺寸
        avatarSize = 40.dp,       // 头像尺寸
        buttonHeight = 40.dp,     // 按钮高度
        textFieldHeight = 56.dp   // 文本框高度
    )

    /**
     * 获取组件特定的样式配置
     */
    fun getComponentStyle(component: ComponentType): ComponentStyleConfig {
        return when (component) {
            ComponentType.BUTTON -> ComponentStyleConfig(
                cornerRadius = cornerRadius.medium,
                elevation = elevation.none,
                padding = spacing.medium,
                borderWidth = borders.width,
                borderColor = borders.color
            )
            ComponentType.CARD -> ComponentStyleConfig(
                cornerRadius = cornerRadius.large,
                elevation = elevation.none,
                padding = spacing.medium,
                borderWidth = borders.width,
                borderColor = borders.color
            )
            ComponentType.DIALOG -> ComponentStyleConfig(
                cornerRadius = cornerRadius.extraLarge,
                elevation = elevation.none,
                padding = spacing.large,
                borderWidth = borders.width,
                borderColor = borders.color
            )
            ComponentType.BOTTOM_SHEET -> ComponentStyleConfig(
                cornerRadius = cornerRadius.extraLarge,
                elevation = elevation.none,
                padding = spacing.medium,
                borderWidth = borders.width,
                borderColor = borders.color
            )
            ComponentType.TEXT_FIELD -> ComponentStyleConfig(
                cornerRadius = cornerRadius.medium,
                elevation = elevation.none,
                padding = spacing.medium,
                borderWidth = borders.width,
                borderColor = borders.color
            )
            ComponentType.TOP_APP_BAR -> ComponentStyleConfig(
                cornerRadius = 0.dp, // 顶部栏不使用圆角
                elevation = elevation.none,
                padding = spacing.medium,
                borderWidth = borders.width,
                borderColor = borders.color
            )
            ComponentType.BOTTOM_NAVIGATION -> ComponentStyleConfig(
                cornerRadius = 0.dp, // 底部导航不使用圆角
                elevation = elevation.none,
                padding = spacing.small,
                borderWidth = borders.width,
                borderColor = borders.color
            )
        }
    }

    /**
     * 卡片样式配置 - 天空蓝主题（整合设计）
     *
     * 特点：
     * - 使用默认值作为后备配置
     * - 实际使用中应优先使用 getDynamicCardStyleConfig() 获取动态配置
     * - 无阴影设计，依靠模糊效果和颜色层次
     * - 紧凑的内边距，提供更大的自由度
     *
     * 注意：此配置为静态默认值，实际应用中会被动态配置覆盖
     */
    override val cardStyle = CardStyleConfig(
        // 基础卡片样式 - 默认值
        defaultCornerRadius = 20.dp,  // 整合设计使用更大的圆角
        defaultElevation = 0.dp,      // 无阴影设计
        defaultHorizontalPadding = 13.dp,
        defaultVerticalPadding = 18.dp,
        settingsVerticalPadding = 6.dp,  // 设置界面卡片垂直边距默认值

        // 间距配置 - 默认值
        itemSpacing = 8.dp,
        sectionSpacing = 20.dp,

        // 内容间距配置 - 默认值
        contentVerticalSpacing = 4.dp,
        contentHorizontalSpacing = 12.dp,

        // 选择状态样式 - 默认值
        selectedElevation = 0.dp,     // 选中时不使用阴影
        selectedBorderWidth = 3.dp    // 使用更粗的边框表示选中
    )
}

/**
 * 组件类型枚举
 */
enum class ComponentType {
    BUTTON,
    CARD,
    DIALOG,
    BOTTOM_SHEET,
    TEXT_FIELD,
    TOP_APP_BAR,
    BOTTOM_NAVIGATION
}

/**
 * 组件样式配置数据类
 */
data class ComponentStyleConfig(
    val cornerRadius: androidx.compose.ui.unit.Dp,
    val elevation: androidx.compose.ui.unit.Dp,
    val padding: androidx.compose.ui.unit.Dp,
    val borderWidth: androidx.compose.ui.unit.Dp,
    val borderColor: Color
)

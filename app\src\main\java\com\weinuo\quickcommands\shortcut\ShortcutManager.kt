package com.weinuo.quickcommands.shortcut

import android.content.Context
import android.content.Intent
import android.content.pm.ShortcutManager
import android.os.Build
import android.util.Log
import android.widget.Toast
import androidx.core.content.pm.ShortcutInfoCompat
import androidx.core.content.pm.ShortcutManagerCompat
import androidx.core.graphics.drawable.IconCompat
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.QuickCommand

/**
 * 快捷方式管理器，负责创建和管理桌面快捷方式
 */
class ShortcutManager(private val context: Context) {



    /**
     * 创建快捷指令的桌面快捷方式
     *
     * @param command 要创建快捷方式的快捷指令
     * @return 是否成功创建快捷方式
     */
    fun createQuickCommandShortcut(command: QuickCommand): Boolean {
        return try {
            // 创建启动快捷指令的Intent
            val intent = Intent(context, QuickCommandExecutorActivity::class.java).apply {
                action = Intent.ACTION_VIEW
                putExtra(EXTRA_COMMAND_ID, command.id)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }

            // 创建快捷方式
            val shortcutId = "quick_command_${command.id}"
            val shortcutIcon = IconCompat.createWithResource(context, R.drawable.ic_shortcut_command)

            val shortcutInfo = ShortcutInfoCompat.Builder(context, shortcutId)
                .setShortLabel(command.name)
                .setLongLabel(command.name)
                .setIcon(shortcutIcon)
                .setIntent(intent)
                .build()

            // 请求创建快捷方式
            ShortcutManagerCompat.requestPinShortcut(context, shortcutInfo, null)

            Log.d(TAG, "Created shortcut for quick command: ${command.name}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error creating quick command shortcut", e)
            false
        }
    }



    /**
     * 尝试删除快捷指令的桌面快捷方式
     *
     * 注意：
     * 1. Android 8.0以上系统不允许应用程序删除已固定的快捷方式，只能删除动态快捷方式
     * 2. 对于Android 7.1以下的系统，此方法不执行任何操作
     *
     * @param commandId 要删除快捷方式的快捷指令ID
     * @return 是否尝试了删除操作（不代表实际删除成功）
     */
    fun removeQuickCommandShortcut(commandId: String): Boolean {
        // 检查API级别
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N_MR1) {
            Log.d(TAG, "Shortcut API not available on this device (API level ${Build.VERSION.SDK_INT})")
            return false
        }

        return try {
            val shortcutId = "quick_command_$commandId"

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                // 使用系统ShortcutManager（Android 7.1+）
                val shortcutManager = context.getSystemService(ShortcutManager::class.java)
                shortcutManager?.let {
                    // 尝试删除动态快捷方式
                    it.removeDynamicShortcuts(listOf(shortcutId))

                    // 对于Android 10+，尝试更新快捷方式为无效状态
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        val shortcuts = it.pinnedShortcuts.filter { s -> s.id == shortcutId }
                        if (shortcuts.isNotEmpty()) {
                            Log.d(TAG, "Attempting to disable pinned quick command shortcut: $shortcutId")
                            it.disableShortcuts(listOf(shortcutId), "Quick command has been deleted")
                        }
                    }
                }
            } else {
                // 使用兼容性API
                ShortcutManagerCompat.removeDynamicShortcuts(context, listOf(shortcutId))
            }

            Log.d(TAG, "Removed shortcut for quick command: $commandId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error removing quick command shortcut", e)
            false
        }
    }



    /**
     * 更新快捷指令的桌面快捷方式
     *
     * 由于Android系统限制，无法直接更新已固定的快捷方式，因此此方法的实现策略是：
     * 1. 先尝试删除旧的快捷方式（如果是动态快捷方式）
     * 2. 创建新的快捷方式
     *
     * @param command 更新后的快捷指令
     * @return 是否成功更新快捷方式
     */
    fun updateQuickCommandShortcut(command: QuickCommand): Boolean {
        // 先尝试删除旧的快捷方式
        removeQuickCommandShortcut(command.id)

        // 创建新的快捷方式
        return createQuickCommandShortcut(command)
    }





    /**
     * 将快捷指令与静态快捷方式槽位关联
     *
     * 注意：此方法仅保存关联关系，实际的静态快捷方式执行逻辑将在后续实现
     *
     * @param command 要关联的快捷指令
     * @param slotIndex 静态快捷方式槽位索引（0-3）
     * @return 是否成功关联
     */
    fun associateWithStaticShortcut(command: QuickCommand, slotIndex: Int): Boolean {
        if (slotIndex < 0 || slotIndex > 3) {
            Log.e(TAG, "Invalid static shortcut slot index: $slotIndex")
            return false
        }

        try {
            // 保存关联关系到SharedPreferences
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putString(PREF_STATIC_SHORTCUT_PREFIX + slotIndex, command.id).apply()

            // 显示提示信息
            Toast.makeText(
                context,
                "已将快捷指令\"" + command.name + "\"关联到静态快捷方式" + (slotIndex + 1),
                Toast.LENGTH_SHORT
            ).show()

            Log.d(TAG, "Associated quick command ${command.id} with static shortcut slot $slotIndex")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error associating quick command with static shortcut", e)
            return false
        }
    }

    /**
     * 将快捷指令与桌面小组件槽位关联
     *
     * 注意：此方法仅保存关联关系，实际的桌面小组件执行逻辑将在后续实现
     *
     * @param command 要关联的快捷指令
     * @param slotIndex 桌面小组件槽位索引（0-3）
     * @return 是否成功关联
     */
    fun associateWithWidget(command: QuickCommand, slotIndex: Int): Boolean {
        if (slotIndex < 0 || slotIndex > 3) {
            Log.e(TAG, "Invalid widget slot index: $slotIndex")
            return false
        }

        try {
            // 保存关联关系到SharedPreferences
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putString(PREF_WIDGET_PREFIX + slotIndex, command.id).apply()

            // 显示提示信息
            Toast.makeText(
                context,
                "已将快捷指令\"" + command.name + "\"关联到桌面小组件" + (slotIndex + 1),
                Toast.LENGTH_SHORT
            ).show()

            Log.d(TAG, "Associated quick command ${command.id} with widget slot $slotIndex")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error associating quick command with widget", e)
            return false
        }
    }

    companion object {
        private const val TAG = "ShortcutManager"
        const val EXTRA_COMMAND_ID = "command_id"

        // SharedPreferences相关常量
        const val PREFS_NAME = "shortcut_manager_prefs"
        const val PREF_STATIC_SHORTCUT_PREFIX = "static_shortcut_"
        const val PREF_WIDGET_PREFIX = "widget_"
    }
}

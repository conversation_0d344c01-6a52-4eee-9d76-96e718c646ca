package com.weinuo.quickcommands.model

/**
 * 预设清理策略工具类
 * 提供常用的清理策略模板
 */
object PresetCleanupStrategies {

    /**
     * 保护重要应用策略
     * 优先清理不重要的应用，保护通讯、工作类重要应用
     */
    fun getProtectImportantStrategy() = CleanupStrategy(
        id = "preset_protect_important",
        name = "保护重要应用",
        description = "优先清理不重要的应用，保护通讯、工作类重要应用",
        rules = listOf(
            CleanupRule(
                type = CleanupRuleType.LOW_PRIORITY_APPS,
                order = 1,
                parameters = mapOf("description" to "最先清理用户标记为不重要的应用")
            ),
            CleanupRule(
                type = CleanupRuleType.LONG_UNUSED_APPS,
                order = 2,
                parameters = mapOf(
                    "hours" to 6,
                    "description" to "然后清理超过6小时未使用的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.NORMAL_APPS,
                order = 3,
                parameters = mapOf("description" to "接着清理普通应用")
            ),
            CleanupRule(
                type = CleanupRuleType.HIGH_PRIORITY_APPS,
                order = 4,
                parameters = mapOf("description" to "最后清理用户标记为重要的应用")
            )
        ),
        isPreset = true
    )

    /**
     * 优先清理久未使用策略
     * 按后台时间排序，最久未使用的应用优先清理
     */
    fun getUnusedFirstStrategy() = CleanupStrategy(
        id = "preset_unused_first",
        name = "优先清理久未使用",
        description = "按后台时间排序，最久未使用的应用优先清理",
        rules = listOf(
            CleanupRule(
                type = CleanupRuleType.LONG_UNUSED_APPS,
                order = 1,
                parameters = mapOf(
                    "hours" to 12,
                    "description" to "最先清理超过12小时未使用的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.LONG_UNUSED_APPS,
                order = 2,
                parameters = mapOf(
                    "hours" to 6,
                    "description" to "然后清理超过6小时未使用的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.LONG_UNUSED_APPS,
                order = 3,
                parameters = mapOf(
                    "hours" to 1,
                    "description" to "接着清理超过1小时未使用的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.NORMAL_APPS,
                order = 4,
                parameters = mapOf("description" to "最后清理其他应用")
            )
        ),
        isPreset = true
    )

    /**
     * 内存优先清理策略
     * 优先清理占用内存较大的应用
     */
    fun getMemoryFirstStrategy() = CleanupStrategy(
        id = "preset_memory_first",
        name = "内存优先清理",
        description = "优先清理占用内存较大的应用",
        rules = listOf(
            CleanupRule(
                type = CleanupRuleType.MEMORY_HEAVY_APPS,
                order = 1,
                parameters = mapOf(
                    "memoryThresholdMB" to 200,
                    "description" to "最先清理占用内存超过200MB的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.LOW_PRIORITY_APPS,
                order = 2,
                parameters = mapOf("description" to "然后清理不重要的应用")
            ),
            CleanupRule(
                type = CleanupRuleType.NORMAL_APPS,
                order = 3,
                parameters = mapOf("description" to "接着清理普通应用")
            ),
            CleanupRule(
                type = CleanupRuleType.HIGH_PRIORITY_APPS,
                order = 4,
                parameters = mapOf("description" to "最后清理重要应用")
            )
        ),
        isPreset = true
    )

    /**
     * 平衡策略
     * 综合考虑重要性和使用时间
     */
    fun getBalancedStrategy() = CleanupStrategy(
        id = "preset_balanced",
        name = "平衡策略",
        description = "综合考虑重要性和使用时间",
        rules = listOf(
            CleanupRule(
                type = CleanupRuleType.LOW_PRIORITY_APPS,
                order = 1,
                parameters = mapOf("description" to "最先清理不重要的应用")
            ),
            CleanupRule(
                type = CleanupRuleType.LONG_UNUSED_APPS,
                order = 2,
                parameters = mapOf(
                    "hours" to 8,
                    "description" to "然后清理超过8小时未使用的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.NORMAL_APPS,
                order = 3,
                parameters = mapOf("description" to "接着清理普通应用")
            ),
            CleanupRule(
                type = CleanupRuleType.LONG_UNUSED_APPS,
                order = 4,
                parameters = mapOf(
                    "hours" to 2,
                    "description" to "然后清理超过2小时未使用的重要应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.HIGH_PRIORITY_APPS,
                order = 5,
                parameters = mapOf("description" to "最后清理重要应用")
            )
        ),
        isPreset = true
    )

    /**
     * 获取所有预设策略
     */
    fun getAllPresetStrategies(): List<CleanupStrategy> {
        return listOf(
            getProtectImportantStrategy(),
            getUnusedFirstStrategy(),
            getMemoryFirstStrategy(),
            getBalancedStrategy(),
            getSmartUsageFrequencyStrategy()
        )
    }

    /**
     * 智能使用频率排序策略
     * 按应用使用频率智能排序，保护常用应用，优先清理少用应用
     */
    fun getSmartUsageFrequencyStrategy() = CleanupStrategy(
        id = "preset_smart_usage_frequency",
        name = "智能使用频率排序",
        description = "按应用使用频率智能排序，保护常用应用，优先清理少用应用",
        rules = listOf(
            CleanupRule(
                type = CleanupRuleType.LEAST_USED_APPS,
                order = 1,
                parameters = mapOf(
                    "timeRange" to "ONE_WEEK",
                    "sortMode" to "BY_SMART_SCORE",
                    "limit" to 20,
                    "description" to "最先清理最少使用的前20个应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.RARE_APPS,
                order = 2,
                parameters = mapOf(
                    "timeRange" to "ONE_WEEK",
                    "launchCountThreshold" to 3,
                    "description" to "然后清理一周内启动少于3次的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.LONG_UNUSED_APPS,
                order = 3,
                parameters = mapOf(
                    "hours" to 12,
                    "description" to "接着清理超过12小时未使用的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.NORMAL_APPS,
                order = 4,
                parameters = mapOf("description" to "然后清理普通应用")
            ),
            CleanupRule(
                type = CleanupRuleType.FREQUENT_APPS,
                order = 5,
                parameters = mapOf(
                    "timeRange" to "ONE_WEEK",
                    "launchCountThreshold" to 10,
                    "description" to "接着清理一周内启动超过10次的应用"
                )
            ),
            CleanupRule(
                type = CleanupRuleType.MOST_USED_APPS,
                order = 6,
                parameters = mapOf(
                    "timeRange" to "ONE_WEEK",
                    "sortMode" to "BY_SMART_SCORE",
                    "limit" to 10,
                    "description" to "最后清理最常使用的前10个应用"
                )
            )
        ),
        isPreset = true
    )

    /**
     * 根据简单策略类型获取对应的预设策略
     */
    fun getStrategyBySimpleType(type: SimpleStrategyType): CleanupStrategy {
        return when (type) {
            SimpleStrategyType.PROTECT_IMPORTANT -> getProtectImportantStrategy()
            SimpleStrategyType.UNUSED_FIRST -> getUnusedFirstStrategy()
            SimpleStrategyType.MEMORY_FIRST -> getMemoryFirstStrategy()
            SimpleStrategyType.BALANCED -> getBalancedStrategy()
            SimpleStrategyType.SMART_USAGE_FREQUENCY -> getSmartUsageFrequencyStrategy()
        }
    }

    /**
     * 获取策略的预览描述
     */
    fun getStrategyPreview(strategy: CleanupStrategy): List<String> {
        return strategy.rules.sortedBy { it.order }.mapNotNull { rule ->
            rule.parameters["description"] as? String
        }
    }
}

package com.weinuo.quickcommands.ui.theme.manager

import android.content.Context
import androidx.compose.runtime.*
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.config.PageLayoutConfig
import com.weinuo.quickcommands.data.SettingsRepository

/**
 * 页面布局配置管理器
 *
 * 负责管理应用中所有页面布局的配置，包括：
 * - 从全局设置中获取用户自定义配置
 * - 页面布局配置的实时更新
 * - 配置的状态管理
 * - 配置的验证和同步
 *
 * 与CardStyleConfigurationManager保持一致的实现方式，
 * 确保与其他设置项保持一致的存储和同步机制。
 */
class PageLayoutConfigurationManager private constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {

    companion object {
        @Volatile
        private var INSTANCE: PageLayoutConfigurationManager? = null

        /**
         * 获取页面布局配置管理器实例
         */
        fun getInstance(context: Context, settingsRepository: SettingsRepository): PageLayoutConfigurationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PageLayoutConfigurationManager(context.applicationContext, settingsRepository).also { INSTANCE = it }
            }
        }

        /**
         * 获取页面布局配置管理器实例（兼容旧版本）
         */
        fun getInstance(context: Context): PageLayoutConfigurationManager {
            return getInstance(context, SettingsRepository(context))
        }
    }

    /**
     * 获取当前页面布局配置
     *
     * 从GlobalSettings中读取用户自定义的页面布局配置
     */
    @Composable
    fun getPageLayoutConfiguration(): PageLayoutConfig {
        val globalSettings by settingsRepository.globalSettings.collectAsState()
        
        return PageLayoutConfig(
            // 页面内容边距配置
            contentHorizontalPadding = globalSettings.pageContentHorizontalPadding.dp,

            // 搜索框配置
            searchFieldMargin = globalSettings.pageSearchFieldMargin.dp,

            // 页面间距配置
            headerSpacing = globalSettings.pageHeaderSpacing.dp,
            bottomPadding = globalSettings.pageBottomPadding.dp,
            scrollContentSpacing = globalSettings.pageScrollContentSpacing.dp
        )
    }

    /**
     * 更新页面内容水平边距
     */
    fun updateContentHorizontalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(pageContentHorizontalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }



    /**
     * 更新搜索框外边距
     */
    fun updateSearchFieldMargin(margin: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(pageSearchFieldMargin = margin)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新页面标题间距
     */
    fun updateHeaderSpacing(spacing: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(pageHeaderSpacing = spacing)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新页面底部边距
     */
    fun updateBottomPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(pageBottomPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新滚动内容间距
     */
    fun updateScrollContentSpacing(spacing: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(pageScrollContentSpacing = spacing)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 批量更新页面布局配置
     * 
     * 用于一次性更新多个配置项，避免多次触发设置保存
     */
    fun updatePageLayoutConfiguration(
        contentHorizontalPadding: Int? = null,
        searchFieldMargin: Int? = null,
        headerSpacing: Int? = null,
        bottomPadding: Int? = null,
        scrollContentSpacing: Int? = null
    ) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            pageContentHorizontalPadding = contentHorizontalPadding ?: currentSettings.pageContentHorizontalPadding,
            pageSearchFieldMargin = searchFieldMargin ?: currentSettings.pageSearchFieldMargin,
            pageHeaderSpacing = headerSpacing ?: currentSettings.pageHeaderSpacing,
            pageBottomPadding = bottomPadding ?: currentSettings.pageBottomPadding,
            pageScrollContentSpacing = scrollContentSpacing ?: currentSettings.pageScrollContentSpacing
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 重置页面布局配置为默认值
     */
    fun resetToDefaults() {
        updatePageLayoutConfiguration(
            contentHorizontalPadding = 16,
            searchFieldMargin = 16,
            headerSpacing = 16,
            bottomPadding = 88,
            scrollContentSpacing = 8
        )
    }
}

package com.weinuo.quickcommands.ui.components.themed

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import com.weinuo.quickcommands.ui.theme.config.BottomSheetConfig
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的底部弹窗组件
 *
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用分层设计风格（LayeredBottomSheet）- 带阴影效果
 * - 天空蓝主题：使用整合设计风格（IntegratedBottomSheet）- 支持模糊效果
 * - 未来主题：可以使用各自独有的实现
 *
 * 底部弹窗是现代移动应用的重要交互模式，
 * 天空蓝主题的模糊效果可以提供更好的视觉体验。
 */
@Composable
fun ThemedBottomSheet(
    config: BottomSheetConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    // 使用当前主题的组件工厂创建底部弹窗
    themeContext.componentFactory.createBottomSheet()(
        config.copy(modifier = modifier)
    )
}

/**
 * 主题感知的底部弹窗组件 - 便捷版本
 *
 * 提供更简洁的API，自动处理常见的配置
 */
@Composable
fun ThemedBottomSheet(
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
    sheetState: Any? = null,
    shape: Shape? = null,
    containerColor: Color? = null,
    contentColor: Color? = null,
    tonalElevation: Dp? = null,
    scrimColor: Color? = null,
    dragHandle: (@Composable () -> Unit)? = null,
    blurEnabled: Boolean? = null,
    blurRadius: Dp? = null,
    content: @Composable () -> Unit
) {
    val themeContext = LocalThemeContext.current
    
    val config = BottomSheetConfig(
        onDismissRequest = onDismissRequest,
        modifier = modifier,
        sheetState = sheetState,
        shape = shape,
        containerColor = containerColor,
        contentColor = contentColor,
        tonalElevation = tonalElevation,
        scrimColor = scrimColor,
        dragHandle = dragHandle,
        blurEnabled = blurEnabled ?: false, // 弹窗模糊已移除，默认禁用
        blurRadius = blurRadius,
        content = content
    )
    
    // 使用主题感知组件
    ThemedBottomSheet(config = config)
}

/**
 * 主题感知的简单底部弹窗 - 最简版本
 *
 * 只需要关闭回调和内容的最简单版本
 */
@Composable
fun ThemedSimpleBottomSheet(
    onDismissRequest: () -> Unit,
    content: @Composable () -> Unit
) {
    ThemedBottomSheet(
        onDismissRequest = onDismissRequest,
        dragHandle = null, // 简化版本不使用拖拽手柄
        content = content
    )
}

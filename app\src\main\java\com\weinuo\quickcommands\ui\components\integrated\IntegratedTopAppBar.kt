package com.weinuo.quickcommands.ui.components.integrated

import android.util.Log
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.draw.alpha
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.unit.Velocity
import com.weinuo.quickcommands.ui.theme.config.BlurComponent
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.ui.theme.config.TopAppBarAction
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.manager.BlurConfigurationManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueColorScheme
import com.weinuo.quickcommands.ui.effects.HazeManager
import com.weinuo.quickcommands.ui.effects.backgroundBlurEffect
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.SkyBlueColorConfigurationManager
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeSource
import kotlin.math.abs

/**
 * 整合设计风格的顶部应用栏组件
 *
 * 特点：
 * - 支持iOS风格模糊效果（使用Haze库）
 * - 无阴影设计
 * - 统一的视觉体验
 * - 流畅的动画过渡
 * - 支持两种样式（标准、可折叠）
 */
@Composable
fun IntegratedTopAppBar(
    config: TopAppBarConfig,
    hazeState: HazeState,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val blurConfigManager = remember { BlurConfigurationManager.getInstance(context) }

    // 获取全局设置中的标题栏类型配置
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 根据全局设置决定使用哪种标题栏类型
    val shouldUseCollapsible = config.collapsible || globalSettings.topAppBarType == "collapsible"

    if (shouldUseCollapsible) {
        // 如果需要可折叠但没有提供scrollBehavior，则自动创建一个
        val finalConfig = if (config.scrollBehavior == null) {
            val scrollBehavior = rememberIntegratedTopAppBarScrollBehavior()
            config.copy(scrollBehavior = scrollBehavior)
        } else {
            config
        }

        IntegratedCollapsibleTopAppBar(
            config = finalConfig,
            hazeState = hazeState,
            blurConfigManager = blurConfigManager,
            modifier = modifier
        )
    } else {
        // 使用标准版本
        IntegratedStandardTopAppBar(
            config = config,
            hazeState = hazeState,
            blurConfigManager = blurConfigManager,
            modifier = modifier
        )
    }
}

/**
 * 标准高度的整合设计顶部应用栏
 *
 * 完全自定义实现，不依赖MD3组件，仿照可折叠标题栏的设计模式
 */
@Composable
private fun IntegratedStandardTopAppBar(
    config: TopAppBarConfig,
    hazeState: HazeState,
    blurConfigManager: BlurConfigurationManager,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    val blurConfig = blurConfigManager.getBlurConfiguration()
    Log.d("IntegratedTopAppBar", "标准顶部应用栏: topBarBlurEnabled=${blurConfig.topBarBlurEnabled}, supportedOnDevice=${blurConfig.supportedOnDevice}")

    val hazeStyle = blurConfigManager.getTopBarHazeMaterial()
    Log.d("IntegratedTopAppBar", "标准顶部应用栏: hazeStyle=${hazeStyle != null}, blurIntensity=${blurConfig.getComponentBlurIntensity(BlurComponent.TOP_BAR)}")

    // 获取天空蓝主题的颜色配置管理器
    val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context, settingsRepository) }
    val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()
    val backgroundColorValue = colorConfig.topBarBackground // 使用用户自定义的标题栏背景色

    // 处理WindowInsets和状态栏高度
    val windowInsets = config.windowInsets as? WindowInsets ?: WindowInsets.statusBars
    val density = LocalDensity.current
    val statusBarHeight = with(density) { windowInsets.getTop(density).toDp() }
    val topAppBarHeight = globalSettings.topAppBarHeight.dp // 使用全局设置的高度
    val totalHeight = topAppBarHeight + statusBarHeight // 标准TopAppBar高度 + 状态栏高度

    Log.d("IntegratedTopAppBar", "Surface配置: hazeStyle=${hazeStyle != null}, backgroundColor=$backgroundColorValue, statusBarHeight=$statusBarHeight, totalHeight=$totalHeight, customHeight=${topAppBarHeight}")

    // 使用单一Surface，同时设置hazeSource和hazeEffect，与底部导航栏保持一致的模糊实现模式
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(totalHeight)
            // 添加hazeSource，使标题栏既能模糊下方内容，也能被上层组件模糊
            .hazeSource(
                state = hazeState,
                zIndex = 1f // 标题栏层级，高于内容区域(0f)，低于底部导航栏(2f)
            )
            .backgroundBlurEffect(
                hazeState = hazeState,
                style = if (blurConfig.topBarBlurEnabled) hazeStyle else null,
                backgroundColor = backgroundColorValue, // 始终使用#fff1f3f5与模糊效果混合
                component = BlurComponent.TOP_BAR // 指定为顶部栏组件
            ),
        color = if (blurConfig.topBarBlurEnabled) {
            Color.Transparent // 模糊时完全透明，让backgroundBlurEffect处理，与底部导航栏保持一致
        } else {
            backgroundColorValue // 不模糊时使用用户自定义的标题栏背景色
        },
        tonalElevation = 0.dp, // 整合设计不使用阴影
        shadowElevation = 0.dp
    ) {
        IntegratedStandardTopAppBarContent(
            config = config,
            windowInsets = windowInsets,
            globalSettings = globalSettings
        )
    }
}

/**
 * 整合设计标准顶部应用栏的内容布局
 *
 * 仿照可折叠标题栏的布局模式，使用Box + 绝对定位
 */
@Composable
private fun IntegratedStandardTopAppBarContent(
    config: TopAppBarConfig,
    windowInsets: WindowInsets,
    globalSettings: com.weinuo.quickcommands.model.GlobalSettings
) {
    // 获取状态栏高度
    val density = LocalDensity.current
    val statusBarHeight = with(density) { windowInsets.getTop(density).toDp() }

    // 标准TopAppBar的标题位置：垂直居中，根据设置的高度和字体大小动态计算，并应用用户自定义偏移
    val topAppBarHeight = globalSettings.topAppBarHeight.dp
    val titleFontSize = globalSettings.topAppBarTitleFontSize.sp
    // 使用字体大小的一半来计算垂直居中位置，确保上下空间一致，然后应用用户自定义的垂直偏移
    val titleY = (topAppBarHeight / 2) - (titleFontSize.value / 2).dp + globalSettings.topAppBarTitleVerticalOffset.dp
    // 应用用户自定义的水平偏移
    val titleX = 16.dp + globalSettings.topAppBarTitleHorizontalOffset.dp

    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(top = statusBarHeight) // 为状态栏留出空间
    ) {
        // 顶部操作栏（导航图标和操作按钮）
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(topAppBarHeight)
                .align(Alignment.TopStart)
                .padding(horizontal = 16.dp), // 操作栏保持原有的水平边距
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 导航图标
            config.navigationIcon?.let { navigationIcon ->
                IconButton(
                    onClick = config.onNavigationClick ?: {},
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        imageVector = navigationIcon,
                        contentDescription = config.navigationContentDescription,
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
            }

            // 为标题留出空间
            Spacer(modifier = Modifier.weight(1f))

            // 操作按钮
            config.actions.forEach { action ->
                IconButton(
                    onClick = action.onClick,
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        imageVector = action.icon,
                        contentDescription = action.contentDescription,
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }

        // 使用绝对定位的标题，确保始终可见且居中
        Text(
            text = config.title,
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Bold
            },
            fontSize = globalSettings.topAppBarTitleFontSize.sp, // 使用全局设置的字体大小
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(x = titleX, y = titleY)
        )
    }
}

/**
 * 整合设计的可折叠顶部应用栏
 *
 * 特点：
 * - 完全自定义的滚动行为，不依赖MD3 TopAppBar
 * - 支持iOS风格模糊效果（使用Haze库）
 * - 可折叠功能，从大型应用栏折叠为标准应用栏
 * - 无阴影设计
 * - 统一的视觉体验
 * - 流畅的动画过渡
 * - 真正的模糊效果，模糊下方内容
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun IntegratedCollapsibleTopAppBar(
    config: TopAppBarConfig,
    hazeState: HazeState,
    blurConfigManager: BlurConfigurationManager,
    modifier: Modifier = Modifier
) {
    val blurConfig = blurConfigManager.getBlurConfiguration()
    val hazeStyle = blurConfigManager.getTopBarHazeMaterial()

    // 获取天空蓝主题的颜色配置管理器
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context, settingsRepository) }
    val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()
    val backgroundColorValue = colorConfig.topBarBackground // 使用用户自定义的标题栏背景色

    // 处理WindowInsets
    val windowInsets = config.windowInsets as? WindowInsets ?: WindowInsets.statusBars

    // 处理ScrollBehavior - 支持自定义的IntegratedTopAppBarScrollBehavior
    val scrollBehavior = when (val behavior = config.scrollBehavior) {
        is IntegratedTopAppBarScrollBehavior -> behavior
        is TopAppBarScrollBehavior -> behavior
        else -> null
    }

    // 获取折叠进度
    val collapseFraction = when (scrollBehavior) {
        is IntegratedTopAppBarScrollBehavior -> scrollBehavior.state.collapsedFraction
        is TopAppBarScrollBehavior -> scrollBehavior.state.collapsedFraction
        else -> 0f
    }

    // 获取状态栏高度
    val density = LocalDensity.current
    val statusBarHeight = with(density) { windowInsets.getTop(density).toDp() }

    // 计算动态高度：从152dp（展开）到64dp（折叠），加上状态栏高度
    val expandedContentHeight = 152.dp
    val collapsedContentHeight = 64.dp
    val currentContentHeight = expandedContentHeight - (expandedContentHeight - collapsedContentHeight) * collapseFraction
    val currentHeight by animateDpAsState(
        targetValue = currentContentHeight + statusBarHeight,
        animationSpec = tween(durationMillis = 0), // 跟随滚动，无延迟
        label = "topbar_height"
    )

    // 计算标题字体大小：从30sp（展开）到22sp（折叠）
    val expandedTitleSize = 30.sp
    val collapsedTitleSize = 22.sp
    val currentTitleSize by animateFloatAsState(
        targetValue = expandedTitleSize.value - (expandedTitleSize.value - collapsedTitleSize.value) * collapseFraction,
        animationSpec = tween(durationMillis = 0), // 跟随滚动，无延迟
        label = "title_size"
    )

    // 不再需要透明度动画，标题会无缝缩放

    Log.d("IntegratedTopAppBar", "可折叠顶部应用栏: topBarBlurEnabled=${blurConfig.topBarBlurEnabled}, collapseFraction=$collapseFraction, currentHeight=$currentHeight")

    // 使用单一Surface，同时设置hazeSource和hazeEffect，与底部导航栏保持一致的模糊实现模式
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(currentHeight)
            // 添加hazeSource，使标题栏既能模糊下方内容，也能被上层组件模糊
            .hazeSource(
                state = hazeState,
                zIndex = 1f // 标题栏层级，高于内容区域(0f)，低于底部导航栏(2f)
            )
            .backgroundBlurEffect(
                hazeState = hazeState,
                style = if (blurConfig.topBarBlurEnabled) hazeStyle else null,
                backgroundColor = backgroundColorValue, // 使用用户自定义的标题栏背景色
                component = BlurComponent.TOP_BAR // 指定为顶部栏组件
            ),
        color = if (blurConfig.topBarBlurEnabled) {
            Color.Transparent // 模糊时完全透明，让backgroundBlurEffect处理，与底部导航栏保持一致
        } else {
            backgroundColorValue // 不模糊时使用用户自定义的标题栏背景色
        },
        tonalElevation = 0.dp, // 整合设计不使用阴影
        shadowElevation = 0.dp
    ) {
        IntegratedCollapsibleTopAppBarContent(
            config = config,
            collapseFraction = collapseFraction,
            currentTitleSize = currentTitleSize.sp,
            windowInsets = windowInsets,
            globalSettings = globalSettings
        )
    }
}

/**
 * 整合设计可折叠顶部应用栏的内容布局
 */
@Composable
private fun IntegratedCollapsibleTopAppBarContent(
    config: TopAppBarConfig,
    collapseFraction: Float,
    currentTitleSize: TextUnit,
    windowInsets: WindowInsets,
    globalSettings: com.weinuo.quickcommands.model.GlobalSettings
) {
    // 获取状态栏高度
    val density = LocalDensity.current
    val statusBarHeight = with(density) { windowInsets.getTop(density).toDp() }

    // 计算标题的绝对位置：从底部（展开）到顶部操作栏中央（折叠）
    val expandedTitleY = 152.dp - 16.dp - 30.dp // 展开时：总高度 - 底部边距 - 字体高度
    val collapsedTitleY = 32.dp // 折叠时：顶部操作栏中央
    val currentTitleY = expandedTitleY * (1f - collapseFraction) + collapsedTitleY * collapseFraction

    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .padding(top = statusBarHeight) // 为状态栏留出空间
    ) {
        // 顶部操作栏（导航图标和操作按钮）
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(64.dp)
                .align(Alignment.TopStart),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 导航图标
            config.navigationIcon?.let { navigationIcon ->
                IconButton(
                    onClick = config.onNavigationClick ?: {},
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        imageVector = navigationIcon,
                        contentDescription = config.navigationContentDescription,
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
            }

            // 为标题留出空间
            Spacer(modifier = Modifier.weight(1f))

            // 操作按钮
            config.actions.forEach { action ->
                IconButton(
                    onClick = action.onClick,
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        imageVector = action.icon,
                        contentDescription = action.contentDescription,
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }

        // 使用绝对定位的标题，确保始终可见
        Text(
            text = config.title,
            fontWeight = FontWeight.Bold,
            fontSize = currentTitleSize,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(y = currentTitleY)
        )
    }
}

// TopAppBarStyle 枚举已在 ComponentConfigs.kt 中定义

// TopAppBarAction 数据类已在 ComponentConfigs.kt 中定义

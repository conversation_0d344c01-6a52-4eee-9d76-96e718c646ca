package com.weinuo.quickcommands.ui.recording

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.model.TouchEventType
import com.weinuo.quickcommands.model.TouchPosition

/**
 * 位置选择全屏界面
 *
 * 提供可视化的位置选择功能，包括：
 * - 模拟屏幕显示
 * - 点击选择位置
 * - 滑动路径设置
 * - 预设位置快捷选择
 * - 精确坐标调整
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PositionPickerScreen(
    currentPosition: TouchPosition,
    actionType: TouchEventType,
    onPositionSelected: (TouchPosition) -> Unit,
    onNavigateBack: () -> Unit
) {
    var selectedPosition by remember { mutableStateOf(currentPosition) }
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "选择触摸位置",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            onPositionSelected(selectedPosition)
                            // 不需要再调用onNavigateBack()，因为onPositionSelected内部已经处理了导航
                        }
                    ) {
                        Text("确定", fontWeight = FontWeight.Medium)
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 提示信息
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        Icons.Default.Info,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = when (actionType) {
                            TouchEventType.SWIPE -> "点击设置起始位置，再点击设置结束位置"
                            else -> "点击屏幕选择触摸位置"
                        },
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }

            // 模拟屏幕区域
            ScreenSimulator(
                position = selectedPosition,
                actionType = actionType,
                onPositionChanged = { selectedPosition = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            )

            // 坐标显示和调整
            CoordinateAdjustmentSection(
                position = selectedPosition,
                onPositionChanged = { selectedPosition = it },
                actionType = actionType
            )

            // 预设位置
            PresetPositionsSection(
                onPositionSelected = { selectedPosition = it },
                actionType = actionType
            )
        }
    }
}

/**
 * 屏幕模拟器组件
 */
@Composable
private fun ScreenSimulator(
    position: TouchPosition,
    actionType: TouchEventType,
    onPositionChanged: (TouchPosition) -> Unit,
    modifier: Modifier = Modifier
) {
    var isSelectingEnd by remember { mutableStateOf(false) }

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "模拟屏幕",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // 模拟屏幕
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(9f / 16f) // 模拟手机屏幕比例
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.Black)
                    .border(
                        2.dp,
                        MaterialTheme.colorScheme.outline,
                        RoundedCornerShape(12.dp)
                    )
                    .clickable { /* 处理点击 */ }
                    .pointerInput(Unit) {
                        detectTapGestures { offset ->
                            val relativeX = offset.x / size.width
                            val relativeY = offset.y / size.height

                            when (actionType) {
                                TouchEventType.SWIPE -> {
                                    if (!isSelectingEnd) {
                                        // 设置起始位置
                                        onPositionChanged(
                                            position.copy(
                                                startX = relativeX,
                                                startY = relativeY
                                            )
                                        )
                                        isSelectingEnd = true
                                    } else {
                                        // 设置结束位置
                                        onPositionChanged(
                                            position.copy(
                                                endX = relativeX,
                                                endY = relativeY
                                            )
                                        )
                                        isSelectingEnd = false
                                    }
                                }
                                else -> {
                                    // 设置单点位置
                                    onPositionChanged(
                                        position.copy(
                                            startX = relativeX,
                                            startY = relativeY,
                                            endX = relativeX,
                                            endY = relativeY
                                        )
                                    )
                                }
                            }
                        }
                    }
            ) {
                // 绘制触摸点和路径
                Canvas(modifier = Modifier.fillMaxSize()) {
                    drawTouchIndicators(position, actionType, isSelectingEnd)
                }
            }

            // 状态提示
            if (actionType == TouchEventType.SWIPE) {
                Text(
                    text = if (isSelectingEnd) "请点击设置结束位置" else "请点击设置起始位置",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * 绘制触摸指示器
 */
private fun DrawScope.drawTouchIndicators(
    position: TouchPosition,
    actionType: TouchEventType,
    isSelectingEnd: Boolean
) {
    val startX = position.startX * size.width
    val startY = position.startY * size.height
    val endX = position.endX * size.width
    val endY = position.endY * size.height

    when (actionType) {
        TouchEventType.SWIPE -> {
            // 绘制滑动路径
            if (position.startX != position.endX || position.startY != position.endY) {
                drawLine(
                    color = Color.Blue,
                    start = Offset(startX, startY),
                    end = Offset(endX, endY),
                    strokeWidth = 4.dp.toPx()
                )
            }

            // 绘制起始点
            drawCircle(
                color = Color.Green,
                radius = 12.dp.toPx(),
                center = Offset(startX, startY)
            )

            // 绘制结束点
            if (position.startX != position.endX || position.startY != position.endY) {
                drawCircle(
                    color = Color.Red,
                    radius = 12.dp.toPx(),
                    center = Offset(endX, endY)
                )
            }
        }
        else -> {
            // 绘制单点
            drawCircle(
                color = Color.Blue,
                radius = 16.dp.toPx(),
                center = Offset(startX, startY)
            )
        }
    }
}

/**
 * 坐标调整区域
 */
@Composable
private fun CoordinateAdjustmentSection(
    position: TouchPosition,
    onPositionChanged: (TouchPosition) -> Unit,
    actionType: TouchEventType
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "精确坐标",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // 起始位置或单点位置
            Text(
                text = if (actionType == TouchEventType.SWIPE) "起始位置" else "触摸位置",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val screenWidth = configuration.screenWidthDp * density.density
                val screenHeight = configuration.screenHeightDp * density.density

                Text(
                    text = "X: ${(position.startX * screenWidth).toInt()}px",
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = "Y: ${(position.startY * screenHeight).toInt()}px",
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.bodyLarge
                )
            }

            // 结束位置（仅滑动显示）
            if (actionType == TouchEventType.SWIPE) {
                Text(
                    text = "结束位置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    val screenWidth = configuration.screenWidthDp * density.density
                    val screenHeight = configuration.screenHeightDp * density.density

                    Text(
                        text = "X: ${(position.endX * screenWidth).toInt()}px",
                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "Y: ${(position.endY * screenHeight).toInt()}px",
                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    }
}

/**
 * 预设位置区域
 */
@Composable
private fun PresetPositionsSection(
    onPositionSelected: (TouchPosition) -> Unit,
    actionType: TouchEventType
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "预设位置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            val presetPositions = listOf(
                "屏幕中心" to TouchPosition(0.5f, 0.5f),
                "左上角" to TouchPosition(0.1f, 0.1f),
                "右上角" to TouchPosition(0.9f, 0.1f),
                "左下角" to TouchPosition(0.1f, 0.9f),
                "右下角" to TouchPosition(0.9f, 0.9f),
                "顶部中心" to TouchPosition(0.5f, 0.1f),
                "底部中心" to TouchPosition(0.5f, 0.9f)
            )

            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(presetPositions.size) { index ->
                    val (name, position) = presetPositions[index]
                    OutlinedButton(
                        onClick = { onPositionSelected(position) },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(name, fontSize = 12.sp)
                    }
                }
            }
        }
    }
}



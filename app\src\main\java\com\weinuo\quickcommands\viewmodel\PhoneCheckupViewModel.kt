package com.weinuo.quickcommands.viewmodel

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.weinuo.quickcommands.data.OptimizeResult
import com.weinuo.quickcommands.data.PhoneCheckupRepository
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.permission.GlobalPermissionManager
import com.weinuo.quickcommands.ui.components.ParticleAnimationState
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.launch

/**
 * 手机体检按钮状态枚举
 *
 * 定义体检界面按钮的各种状态，实现手动体检流程
 */
enum class CheckupButtonState {
    FIRST_TIME,         // 首次进入，无体检数据
    OPTIMIZE,           // 显示"一键优化"
    OPTIMIZING,         // 显示"正在优化..."
    COMPLETED,          // 显示"完成"（优化后状态）
    EXCELLENT,          // 显示"状态良好"（天然良好状态）
    READY_TO_CHECKUP,   // 显示"立即体检"（已有体检数据的状态）
    MANUAL_CHECKING,    // 手动体检中（点击"开始体检"/"立即体检"后）
    PERMISSION_NEEDED   // 显示"开启权限"
}

/**
 * 手机体检界面的ViewModel
 *
 * 负责管理手机体检界面的状态和业务逻辑，包括：
 * - 健康分数管理
 * - 运行应用数量统计
 * - 一键优化功能
 * - 权限状态检查
 * - Shizuku提示卡片管理
 * - 界面状态管理
 * - 按钮状态机管理
 */
class PhoneCheckupViewModel(
    private val repository: PhoneCheckupRepository,
    private val context: Context
) : ViewModel() {

    // 设置仓库，用于读取体检完成展示时长设置
    private val settingsRepository = SettingsRepository(context)

    companion object {
        private const val TAG = "PhoneCheckupViewModel"
        private const val PREFS_NAME = "phone_checkup_prefs"
        private const val KEY_SHIZUKU_TIP_SHOWN = "shizuku_tip_shown"
        private const val KEY_LAST_CHECKUP_TIME = "last_checkup_time"
        private const val KEY_LAST_CHECKUP_SCORE = "last_checkup_score"
    }

    // 按钮状态管理
    private val _buttonState = MutableStateFlow(CheckupButtonState.FIRST_TIME)
    val buttonState: StateFlow<CheckupButtonState> = _buttonState.asStateFlow()

    // 健康分数状态
    private val _healthScore = MutableStateFlow(0)
    val healthScore: StateFlow<Int> = _healthScore.asStateFlow()

    // 运行应用数量状态
    private val _runningAppsCount = MutableStateFlow(0)
    val runningAppsCount: StateFlow<Int> = _runningAppsCount.asStateFlow()

    // 是否正在优化状态
    private val _isOptimizing = MutableStateFlow(false)
    val isOptimizing: StateFlow<Boolean> = _isOptimizing.asStateFlow()

    // 是否正在加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 权限状态
    private val _hasUsageStatsPermission = MutableStateFlow(false)
    val hasUsageStatsPermission: StateFlow<Boolean> = _hasUsageStatsPermission.asStateFlow()

    private val _hasShizukuPermission = MutableStateFlow(false)
    val hasShizukuPermission: StateFlow<Boolean> = _hasShizukuPermission.asStateFlow()

    private val _hasKillBackgroundProcessesPermission = MutableStateFlow(false)
    val hasKillBackgroundProcessesPermission: StateFlow<Boolean> = _hasKillBackgroundProcessesPermission.asStateFlow()

    // 优化结果状态
    private val _lastOptimizeResult = MutableStateFlow<OptimizeResult?>(null)
    val lastOptimizeResult: StateFlow<OptimizeResult?> = _lastOptimizeResult.asStateFlow()



    // 错误消息状态
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 粒子动画状态
    private val _particleAnimationState = MutableStateFlow(ParticleAnimationState.NORMAL)
    val particleAnimationState: StateFlow<ParticleAnimationState> = _particleAnimationState.asStateFlow()

    // Shizuku提示卡片状态
    private val _showShizukuTipCard = MutableStateFlow(false)
    val showShizukuTipCard: StateFlow<Boolean> = _showShizukuTipCard.asStateFlow()

    // 响应式状态文字
    val healthStatusDescription: StateFlow<String> = combine(
        _buttonState,
        _healthScore,
        _lastOptimizeResult,
        _hasUsageStatsPermission
    ) { buttonState, healthScore, optimizeResult, hasPermission ->
        getHealthStatusDescriptionInternal(buttonState, healthScore, optimizeResult, hasPermission)
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), "")

    val secondaryStatusText: StateFlow<String> = combine(
        _buttonState,
        _runningAppsCount,
        _hasUsageStatsPermission,
        _healthScore
    ) { buttonState, runningAppsCount, hasPermission, healthScore ->
        getSecondaryStatusTextInternal(buttonState, runningAppsCount, hasPermission, healthScore)
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), "")

    // 全局权限管理器
    private val globalPermissionManager = GlobalPermissionManager.getInstance(context)

    init {
        // 初始化时检查权限和初始化状态
        checkPermissions()
        checkShizukuTipCard()
        initializeCheckupState()

        // 监听权限状态变化
        startPermissionStateMonitoring()
    }

    /**
     * 初始化体检状态
     *
     * 根据权限和历史数据确定初始状态
     */
    private fun initializeCheckupState() {
        if (!repository.hasUsageStatsPermission()) {
            _buttonState.value = CheckupButtonState.PERMISSION_NEEDED
            return
        }

        // 检查是否有上一次体检数据
        val lastCheckupTime = getLastCheckupTime()
        if (lastCheckupTime == 0L) {
            // 首次进入，无体检数据
            _buttonState.value = CheckupButtonState.FIRST_TIME
            // 移除硬编码100分，首次进入时不设置分数，由StaticWaterBallComponent独立处理显示
        } else {
            // 已有体检数据，显示上次结果
            _buttonState.value = CheckupButtonState.READY_TO_CHECKUP
            // 模拟加载过程以触发灌水动画
            val lastScore = getLastCheckupScore()

            viewModelScope.launch {
                // 模拟加载状态
                _isLoading.value = true
                _healthScore.value = 0 // 重置分数

                // 短暂延迟模拟加载过程
                delay(200)

                // 设置目标分数并结束加载状态，触发灌水动画
                _healthScore.value = lastScore
                _isLoading.value = false

                Log.d(TAG, "Loaded last checkup data with animation - Score: $lastScore, Time: $lastCheckupTime")
            }
        }
    }

    /**
     * 刷新手机体检数据
     *
     * 获取最新的运行应用数量和健康分数
     */
    fun refreshHealthCheck() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null

                Log.d(TAG, "Refreshing health check data")

                // 刷新应用运行状态
                repository.refreshAppRunningStatus()

                // 获取运行应用数量
                val runningCount = repository.getRunningAppsCount()
                _runningAppsCount.value = runningCount

                // 计算健康分数
                val score = if (settingsRepository.globalSettings.value.fixedCheckupScoreEnabled) {
                    // 如果启用了固定体检分数，使用固定分数
                    settingsRepository.globalSettings.value.fixedCheckupScore
                } else {
                    // 否则使用正常的计算逻辑
                    repository.calculateHealthScore(runningCount)
                }

                Log.d(TAG, "Health check refreshed - Score: $score, Running apps: $runningCount")

                // 手动体检状态现在在 startManualCheckup() 中处理，这里只处理非手动体检状态
                if (_buttonState.value != CheckupButtonState.MANUAL_CHECKING) {
                    // 非体检状态直接显示结果
                    _healthScore.value = score
                    onCheckupComplete()
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing health check", e)
                _errorMessage.value = "刷新数据失败: ${e.message}"
                // 出错时回到初始状态
                _buttonState.value = if (!repository.hasUsageStatsPermission()) {
                    CheckupButtonState.PERMISSION_NEEDED
                } else {
                    CheckupButtonState.READY_TO_CHECKUP
                }
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 统一的处理完成流程（体检和优化共用）
     * 包含等待展示->膨胀的流程（收缩和处理状态已在调用方处理）
     */
    private suspend fun handleProcessingComplete(
        finalScore: Int,
        finalButtonState: CheckupButtonState
    ) {
        // 进入等待展示状态
        _particleAnimationState.value = ParticleAnimationState.WAITING_DISPLAY
        Log.d(TAG, "Entering waiting display state, continuing to show rotating arc")

        // 获取用户设置的体检完成展示时长
        val displayDurationSeconds = settingsRepository.globalSettings.value.checkupDisplayDurationSeconds

        // 等待体检完成展示时长
        if (displayDurationSeconds > 0) {
            Log.d(TAG, "Waiting for $displayDurationSeconds seconds before showing results and starting expansion")
            delay(displayDurationSeconds * 1000L)
        }

        // 等待结束后，同时显示结果、设置按钮状态和开始膨胀动画
        _healthScore.value = finalScore
        _buttonState.value = finalButtonState
        _particleAnimationState.value = ParticleAnimationState.EXPANDING_STATIC
        Log.d(TAG, "Display duration completed, showing results, setting button state and starting expansion")

        // 保存结果
        saveCheckupTime()
        saveCheckupScore(finalScore)

        // 等待膨胀动画完成时间（1.5s），但用户可以通过点击按钮打断
        val animationTime = 1.5f // 膨胀动画时长
        if (animationTime > 0) {
            Log.d(TAG, "Waiting for $animationTime seconds for expansion animation to complete (can be interrupted by user)")
            delay((animationTime * 1000L).toLong())
        }

        // 动画完成后重置粒子动画状态到正常（如果没有被用户打断）
        if (_particleAnimationState.value == ParticleAnimationState.EXPANDING_STATIC) {
            _particleAnimationState.value = ParticleAnimationState.NORMAL
            Log.d(TAG, "Expansion animation completed, reset to normal state")
        }

        Log.d(TAG, "Processing complete, final button state: $finalButtonState")
    }

    /**
     * 体检完成后的状态处理
     * 包含等待展示时长的逻辑（用于非手动体检状态）
     */
    private fun onCheckupComplete() {
        viewModelScope.launch {
            // 保存体检时间和分数
            saveCheckupTime()
            saveCheckupScore(_healthScore.value)

            // 如果不是体检状态，启动膨胀动画（体检状态已经在refreshHealthCheck中启动了）
            if (_buttonState.value != CheckupButtonState.MANUAL_CHECKING) {
                Log.d(TAG, "Setting particle animation state to EXPANDING")
                _particleAnimationState.value = ParticleAnimationState.EXPANDING_STATIC
            }

            // 获取用户设置的体检完成展示时长
            val displayDurationSeconds = settingsRepository.globalSettings.value.checkupDisplayDurationSeconds

            // 等待指定的时长，让用户欣赏动画效果
            // 需要等待膨胀动画完成时间（1.5s）
            val animationTime = 1.5f // 膨胀动画时长
            val totalWaitTime = maxOf(displayDurationSeconds.toFloat(), animationTime)

            if (totalWaitTime > 0) {
                Log.d(TAG, "Waiting for $totalWaitTime seconds to display checkup results and complete animations")
                delay((totalWaitTime * 1000L).toLong())
            }

            // 等待结束后更新按钮状态
            _buttonState.value = when {
                !repository.hasUsageStatsPermission() -> CheckupButtonState.PERMISSION_NEEDED
                _healthScore.value >= 100 -> CheckupButtonState.EXCELLENT // 天然良好状态
                else -> CheckupButtonState.OPTIMIZE // 需要优化
            }

            // 重置粒子动画状态到正常
            _particleAnimationState.value = ParticleAnimationState.NORMAL

            Log.d(TAG, "Checkup complete, button state: ${_buttonState.value}")
        }
    }



    /**
     * 执行一键优化
     *
     * 停止所有后台运行的应用（排除前台应用等）
     */
    fun optimizeSystem() {
        viewModelScope.launch {
            try {
                _buttonState.value = CheckupButtonState.OPTIMIZING
                _isOptimizing.value = true
                _errorMessage.value = null

                // 开始粒子收缩动画
                _particleAnimationState.value = ParticleAnimationState.CONTRACTING

                Log.d(TAG, "Starting system optimization")

                // 等待收缩动画完成
                val contractionDuration = 1200L + (3 - 1) * 200L // CONTRACTION_DURATION + (TOTAL_LAYERS - 1) * LAYER_DELAY
                delay(contractionDuration)
                Log.d(TAG, "Contraction completed")

                // 收缩完成后进入处理状态，此时显示旋转弧线
                _particleAnimationState.value = ParticleAnimationState.PROCESSING
                Log.d(TAG, "Entering processing state, showing rotating arc")

                // 执行优化
                val result = repository.optimizeSystem()
                _lastOptimizeResult.value = result

                if (result.success) {
                    Log.d(TAG, "Optimization successful: ${result.message}")
                    // 优化成功后设置为100分
                    _healthScore.value = 100
                    _runningAppsCount.value = 0

                    // 立即停止优化状态，让按钮变为可点击
                    _isOptimizing.value = false

                    // 使用统一的处理完成流程
                    handleProcessingComplete(
                        finalScore = 100,
                        finalButtonState = CheckupButtonState.COMPLETED
                    )
                } else {
                    Log.w(TAG, "Optimization failed: ${result.message}")
                    _errorMessage.value = result.message
                    // 优化失败，回到优化状态
                    _buttonState.value = CheckupButtonState.OPTIMIZE
                    _isOptimizing.value = false
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error during optimization", e)
                _errorMessage.value = "优化失败: ${e.message}"
                // 出错时回到优化状态
                _buttonState.value = CheckupButtonState.OPTIMIZE
                _isOptimizing.value = false
            }
        }
    }

    /**
     * 检查所需权限
     *
     * 检查使用情况访问权限、Shizuku权限和后台进程管理权限
     */
    fun checkPermissions() {
        viewModelScope.launch {
            try {
                val hasUsageStats = repository.hasUsageStatsPermission()
                val hasShizuku = repository.hasShizukuPermission()
                val hasKillBgProcesses = repository.hasKillBackgroundProcessesPermission()

                _hasUsageStatsPermission.value = hasUsageStats
                _hasShizukuPermission.value = hasShizuku
                _hasKillBackgroundProcessesPermission.value = hasKillBgProcesses

                // 重新检查提示卡片状态
                checkShizukuTipCard()

                Log.d(TAG, "Permissions checked - UsageStats: $hasUsageStats, Shizuku: $hasShizuku, KillBgProcesses: $hasKillBgProcesses")

            } catch (e: Exception) {
                Log.e(TAG, "Error checking permissions", e)
            }
        }
    }

    /**
     * 检查是否需要显示Shizuku提示卡片
     */
    private fun checkShizukuTipCard() {
        val hasUsageStats = repository.hasUsageStatsPermission()
        val hasShizuku = repository.hasShizukuPermission()
        val hasShownTip = getShizukuTipShownFlag()

        // 只有在有使用情况权限、没有Shizuku权限、且从未显示过提示时才显示
        _showShizukuTipCard.value = hasUsageStats && !hasShizuku && !hasShownTip
    }

    /**
     * 关闭Shizuku提示卡片
     */
    fun dismissShizukuTipCard() {
        _showShizukuTipCard.value = false
        saveShizukuTipShownFlag()
    }

    /**
     * 获取Shizuku提示已显示标志
     */
    private fun getShizukuTipShownFlag(): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(KEY_SHIZUKU_TIP_SHOWN, false)
    }

    /**
     * 保存Shizuku提示已显示标志
     */
    private fun saveShizukuTipShownFlag() {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(KEY_SHIZUKU_TIP_SHOWN, true).apply()
    }

    /**
     * 开始监听权限状态变化
     */
    private fun startPermissionStateMonitoring() {
        viewModelScope.launch {
            globalPermissionManager.permissionStates.collect { permissionStates ->
                // 监听使用情况统计权限和Shizuku权限状态变化
                val usageStatsState = permissionStates[GlobalPermissionManager.PermissionType.USAGE_STATS]
                val shizukuState = permissionStates[GlobalPermissionManager.PermissionType.SHIZUKU]

                // 更新本地权限状态
                val hasUsageStats = repository.hasUsageStatsPermission()
                val hasShizuku = repository.hasShizukuPermission()
                val hasKillBgProcesses = repository.hasKillBackgroundProcessesPermission()

                if (_hasUsageStatsPermission.value != hasUsageStats) {
                    _hasUsageStatsPermission.value = hasUsageStats
                    Log.d(TAG, "Usage stats permission changed: $hasUsageStats")

                    // 权限状态变化时重新初始化状态
                    initializeCheckupState()
                }

                if (_hasShizukuPermission.value != hasShizuku) {
                    _hasShizukuPermission.value = hasShizuku
                    Log.d(TAG, "Shizuku permission changed: $hasShizuku")

                    // 重新检查Shizuku提示卡片状态
                    checkShizukuTipCard()
                }

                if (_hasKillBackgroundProcessesPermission.value != hasKillBgProcesses) {
                    _hasKillBackgroundProcessesPermission.value = hasKillBgProcesses
                    Log.d(TAG, "Kill background processes permission changed: $hasKillBgProcesses")
                }
            }
        }
    }

    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * 清除优化结果
     */
    fun clearOptimizeResult() {
        _lastOptimizeResult.value = null
    }

    /**
     * 获取健康状态描述（内部方法）
     */
    private fun getHealthStatusDescriptionInternal(
        buttonState: CheckupButtonState,
        healthScore: Int,
        optimizeResult: OptimizeResult?,
        hasPermission: Boolean
    ): String {
        return if (!hasPermission) {
            "需要开启使用情况访问权限才能检测手机状态"
        } else {
            when (buttonState) {
                CheckupButtonState.FIRST_TIME -> {
                    "手机状态待检测"
                }
                CheckupButtonState.MANUAL_CHECKING -> {
                    "正在体检……"
                }
                CheckupButtonState.OPTIMIZING -> {
                    "正在优化……"
                }
                CheckupButtonState.COMPLETED -> {
                    // 优化完成状态
                    if (optimizeResult != null && optimizeResult.success) {
                        "优化完成！手机性能已提升"
                    } else {
                        "优化完成"
                    }
                }
                CheckupButtonState.EXCELLENT -> {
                    // 天然完美状态（100分）
                    "手机状态完美"
                }
                CheckupButtonState.READY_TO_CHECKUP -> {
                    // 非首次体检状态，基于历史数据显示状态
                    when (healthScore) {
                        100 -> "手机状态完美"
                        in 80..99 -> "手机状态良好"
                        in 60..79 -> "手机有些卡顿"
                        else -> "手机运行缓慢"
                    }
                }
                else -> {
                    // 正常状态显示
                    when (healthScore) {
                        100 -> "手机状态完美"
                        in 80..99 -> "手机状态良好"
                        in 60..79 -> "手机有些卡顿"
                        else -> "手机运行缓慢"
                    }
                }
            }
        }
    }

    /**
     * 获取健康状态描述（兼容性方法）
     *
     * @return 根据分数返回相应的状态描述
     */
    fun getHealthStatusDescription(): String {
        return getHealthStatusDescriptionInternal(
            _buttonState.value,
            _healthScore.value,
            _lastOptimizeResult.value,
            _hasUsageStatsPermission.value
        )
    }

    /**
     * 获取健康状态颜色类型
     *
     * @return 颜色类型：excellent, good, poor
     */
    fun getHealthStatusColorType(): String {
        return when (val score = _healthScore.value) {
            in 80..100 -> "excellent"
            in 60..79 -> "good"
            else -> "poor"
        }
    }

    /**
     * 获取次要状态文字（内部方法）
     */
    private fun getSecondaryStatusTextInternal(
        buttonState: CheckupButtonState,
        runningAppsCount: Int,
        hasPermission: Boolean,
        healthScore: Int = 0
    ): String {
        return if (!hasPermission) {
            "需要开启使用情况访问权限"
        } else {
            when (buttonState) {
                CheckupButtonState.FIRST_TIME -> {
                    "点击下方按钮开始首次体检"
                }
                CheckupButtonState.MANUAL_CHECKING -> {
                    "正在检测后台应用项目"
                }
                CheckupButtonState.OPTIMIZING -> {
                    "正在停止后台应用，释放内存空间"
                }
                CheckupButtonState.COMPLETED -> {
                    "系统已优化，内存使用更加高效"
                }
                CheckupButtonState.EXCELLENT -> {
                    // 天然完美状态（100分）
                    if (runningAppsCount <= 2) {
                        "系统运行完美，后台应用极少"
                    } else {
                        "已检测到后台运行应用 $runningAppsCount 个"
                    }
                }
                CheckupButtonState.READY_TO_CHECKUP -> {
                    // 非首次体检状态，显示上次体检时间
                    val lastCheckupTime = getLastCheckupTime()
                    if (lastCheckupTime > 0) {
                        "上次体检：${formatCheckupTime(lastCheckupTime)}"
                    } else {
                        "点击下方按钮开始体检"
                    }
                }
                else -> {
                    // 根据分数和应用数量提供针对性信息
                    when {
                        healthScore == 100 && runningAppsCount <= 2 -> "系统运行完美，后台应用极少"
                        healthScore >= 80 && runningAppsCount <= 2 -> "系统运行流畅，后台应用较少"
                        healthScore >= 80 -> "检测到 $runningAppsCount 个后台应用，可进一步优化"
                        healthScore >= 60 -> "检测到较多后台应用，建议清理"
                        else -> "后台应用过多，影响手机性能"
                    }
                }
            }
        }
    }

    /**
     * 获取次要状态文字（兼容性方法）
     *
     * @return 根据当前状态返回相应的次要状态描述
     */
    fun getSecondaryStatusText(): String {
        return getSecondaryStatusTextInternal(
            _buttonState.value,
            _runningAppsCount.value,
            _hasUsageStatsPermission.value,
            _healthScore.value
        )
    }

    /**
     * 是否应该显示按钮
     *
     * @return 体检时隐藏按钮，其他时候显示按钮
     */
    fun shouldShowButton(): Boolean {
        return when (_buttonState.value) {
            CheckupButtonState.MANUAL_CHECKING -> false
            else -> true
        }
    }

    /**
     * 是否可以执行优化
     * 
     * @return 是否可以优化（有权限且不在优化中且分数小于100）
     */
    fun canOptimize(): Boolean {
        return repository.hasAllRequiredPermissions() && 
               !_isOptimizing.value && 
               _healthScore.value < 100
    }

    /**
     * 是否需要权限设置
     *
     * @return 是否缺少必要权限
     */
    fun needsPermissionSetup(): Boolean {
        return false // 始终显示按钮，不显示权限设置卡片
    }

    /**
     * 获取缺失的权限列表
     *
     * @return 缺失权限的描述列表
     */
    fun getMissingPermissions(): List<String> {
        val missing = mutableListOf<String>()

        if (!_hasUsageStatsPermission.value) {
            missing.add("使用情况访问权限")
        }

        if (!_hasKillBackgroundProcessesPermission.value) {
            missing.add("后台进程管理权限")
        }

        return missing
    }

    /**
     * 获取按钮文本
     *
     * @return 根据当前状态返回相应的按钮文本
     */
    fun getButtonText(): String {
        return when (_buttonState.value) {
            CheckupButtonState.FIRST_TIME -> "开始体检"
            CheckupButtonState.PERMISSION_NEEDED -> "开启权限"
            CheckupButtonState.OPTIMIZE -> "一键优化"
            CheckupButtonState.OPTIMIZING -> "正在优化..."
            CheckupButtonState.COMPLETED -> "完成"
            CheckupButtonState.EXCELLENT -> "重新体检"
            CheckupButtonState.READY_TO_CHECKUP -> "立即体检"
            CheckupButtonState.MANUAL_CHECKING -> "正在体检..."
        }
    }

    /**
     * 获取按钮是否可点击
     *
     * @return 按钮是否可以点击
     */
    fun canClick(): Boolean {
        return when (_buttonState.value) {
            CheckupButtonState.OPTIMIZING -> false         // 优化中不可点击
            CheckupButtonState.MANUAL_CHECKING -> false    // 手动体检中不可点击
            else -> true
        }
    }

    /**
     * 是否显示优化按钮
     *
     * @return 是否应该显示优化按钮
     */
    fun shouldShowOptimizeButton(): Boolean {
        return repository.hasAllRequiredPermissions()
    }

    /**
     * 处理按钮点击事件
     *
     * 根据当前按钮状态执行相应的操作
     */
    fun handleButtonClick() {
        // 如果当前在膨胀动画状态，用户点击按钮可以打断动画
        if (_particleAnimationState.value == ParticleAnimationState.EXPANDING_STATIC) {
            Log.d(TAG, "User interrupted expansion animation by clicking button")
            _particleAnimationState.value = ParticleAnimationState.NORMAL
        }

        when (_buttonState.value) {
            CheckupButtonState.FIRST_TIME -> startManualCheckup() // 点击开始体检
            CheckupButtonState.PERMISSION_NEEDED -> requestUsageStatsPermission()
            CheckupButtonState.OPTIMIZE -> optimizeSystem()
            CheckupButtonState.COMPLETED -> onCompletedClick() // 点击完成
            CheckupButtonState.EXCELLENT -> startManualCheckup() // 点击重新体检
            CheckupButtonState.READY_TO_CHECKUP -> startManualCheckup() // 点击立即体检
            else -> {
                // 其他状态不处理（如正在体检、正在优化等）
                Log.d(TAG, "Button click ignored in state: ${_buttonState.value}")
            }
        }
    }

    /**
     * 点击完成按钮的处理
     */
    private fun onCompletedClick() {
        // 点击完成后，进入标准的非首次体检状态
        _buttonState.value = CheckupButtonState.READY_TO_CHECKUP
        // 清除优化结果状态
        _lastOptimizeResult.value = null
        Log.d(TAG, "Completed clicked, button changed to 'Ready to Checkup', entering standard non-first-time checkup state")
    }

    /**
     * 开始手动体检
     */
    private fun startManualCheckup() {
        viewModelScope.launch {
            try {
                // 用户点击"立即体检"后才开始重新检测
                _buttonState.value = CheckupButtonState.MANUAL_CHECKING
                _errorMessage.value = null

                // 体检开始时先设置为100分，给用户良好的初始体验
                _healthScore.value = 100
                Log.d(TAG, "Manual checkup started with initial score of 100")

                // 开始粒子收缩动画
                Log.d(TAG, "Setting particle animation state to CONTRACTING")
                _particleAnimationState.value = ParticleAnimationState.CONTRACTING

                Log.d(TAG, "Starting manual checkup")

                // 等待收缩动画完成
                val contractionDuration = 1200L + (3 - 1) * 200L // CONTRACTION_DURATION + (TOTAL_LAYERS - 1) * LAYER_DELAY
                delay(contractionDuration)
                Log.d(TAG, "Contraction completed")

                // 收缩完成后进入处理状态，此时显示旋转弧线
                _particleAnimationState.value = ParticleAnimationState.PROCESSING
                Log.d(TAG, "Entering processing state, showing rotating arc")

                // 执行体检操作
                // 刷新应用运行状态
                repository.refreshAppRunningStatus()

                // 获取运行应用数量
                val runningCount = repository.getRunningAppsCount()
                _runningAppsCount.value = runningCount

                // 计算健康分数
                val score = if (settingsRepository.globalSettings.value.fixedCheckupScoreEnabled) {
                    // 如果启用了固定体检分数，使用固定分数
                    settingsRepository.globalSettings.value.fixedCheckupScore
                } else {
                    // 否则使用正常的计算逻辑
                    repository.calculateHealthScore(runningCount)
                }

                Log.d(TAG, "Health check refreshed - Score: $score, Running apps: $runningCount")

                // 使用统一的处理完成流程
                handleProcessingComplete(
                    finalScore = score,
                    finalButtonState = when {
                        !repository.hasUsageStatsPermission() -> CheckupButtonState.PERMISSION_NEEDED
                        score >= 100 -> CheckupButtonState.EXCELLENT
                        else -> CheckupButtonState.OPTIMIZE
                    }
                )

            } catch (e: Exception) {
                Log.e(TAG, "Error during manual checkup", e)
                _errorMessage.value = "体检失败: ${e.message}"
                // 出错时回到初始状态
                _buttonState.value = if (!repository.hasUsageStatsPermission()) {
                    CheckupButtonState.PERMISSION_NEEDED
                } else {
                    CheckupButtonState.READY_TO_CHECKUP
                }
            }
        }
    }

    /**
     * 更新粒子动画状态
     * 由WaterBallComponent回调调用，用于处理粒子动画状态的自动切换
     */
    fun updateParticleAnimationState(newState: ParticleAnimationState) {
        Log.d(TAG, "Updating particle animation state from ${_particleAnimationState.value} to $newState")
        _particleAnimationState.value = newState
    }

    /**
     * 请求使用情况统计权限
     */
    private fun requestUsageStatsPermission() {
        try {
            val intent = android.content.Intent(android.provider.Settings.ACTION_USAGE_ACCESS_SETTINGS)
            intent.flags = android.content.Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening usage stats settings", e)
            _errorMessage.value = "无法打开权限设置页面"
        }
    }

    /**
     * 获取上一次体检时间
     */
    private fun getLastCheckupTime(): Long {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getLong(KEY_LAST_CHECKUP_TIME, 0L)
    }

    /**
     * 保存体检时间
     */
    private fun saveCheckupTime() {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putLong(KEY_LAST_CHECKUP_TIME, System.currentTimeMillis()).apply()
        Log.d(TAG, "Checkup time saved: ${System.currentTimeMillis()}")
    }

    /**
     * 获取上一次体检分数
     */
    private fun getLastCheckupScore(): Int {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val score = prefs.getInt(KEY_LAST_CHECKUP_SCORE, 0)
        // 如果分数为0且有体检时间记录，说明可能是数据异常，返回一个合理的默认值
        return if (score == 0 && getLastCheckupTime() > 0) {
            Log.w(TAG, "Found checkup time but score is 0, using default score 60")
            60 // 返回一个中等分数作为默认值
        } else {
            score
        }
    }

    /**
     * 保存体检分数
     */
    private fun saveCheckupScore(score: Int) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putInt(KEY_LAST_CHECKUP_SCORE, score).apply()
        Log.d(TAG, "Checkup score saved: $score")
    }

    /**
     * 格式化体检时间显示
     */
    private fun formatCheckupTime(timestamp: Long): String {
        if (timestamp == 0L) return ""

        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < 60 * 1000 -> "刚刚"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
            else -> {
                val sdf = java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
                sdf.format(java.util.Date(timestamp))
            }
        }
    }

    /**
     * 在ViewModel销毁时清理资源
     */
    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "PhoneCheckupViewModel cleared")
    }
}

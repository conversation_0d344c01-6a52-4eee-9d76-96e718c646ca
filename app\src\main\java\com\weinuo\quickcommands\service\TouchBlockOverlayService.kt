package com.weinuo.quickcommands.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.graphics.Rect
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import kotlinx.coroutines.*

/**
 * 触摸屏蔽悬浮窗服务
 *
 * 负责显示透明悬浮窗来屏蔽屏幕触摸，参考闹钟悬浮窗的实现
 * 使用后台服务而非前台服务，避免崩溃问题
 */
class TouchBlockOverlayService : Service() {
    private val TAG = "TouchBlockOverlayService"

    private lateinit var windowManager: WindowManager
    private var overlayView: View? = null
    
    // 应急关闭参数
    private var emergencyCloseEnabled = true
    private var emergencyClickCount = 5
    private var emergencyTimeWindowSeconds = 3
    
    // 应急关闭检测
    private val clickTimes = mutableListOf<Long>()
    private val detectionArea = Rect(0, 0, 100, 100) // 左上角100x100像素
    
    // 协程相关
    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())

    // 悬浮窗参数
    private val overlayParams by lazy {
        WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
            type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_FULLSCREEN
            format = PixelFormat.TRANSLUCENT
            gravity = Gravity.TOP or Gravity.START
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        Log.d(TAG, "TouchBlockOverlayService created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "TouchBlockOverlayService started")

        // 获取参数
        emergencyCloseEnabled = intent?.getBooleanExtra("emergencyCloseEnabled", true) ?: true
        emergencyClickCount = intent?.getIntExtra("emergencyClickCount", 5) ?: 5
        emergencyTimeWindowSeconds = intent?.getIntExtra("emergencyTimeWindowSeconds", 3) ?: 3

        // 检查悬浮窗权限
        if (!Settings.canDrawOverlays(this)) {
            Log.e(TAG, "No overlay permission, stopping service")
            stopSelf()
            return START_NOT_STICKY
        }

        // 显示悬浮窗
        showOverlay()

        return START_STICKY
    }

    private fun showOverlay() {
        try {
            // 创建透明的全屏容器
            overlayView = FrameLayout(this).apply {
                setBackgroundColor(Color.TRANSPARENT)
                
                // 设置触摸监听器
                setOnTouchListener { _, event ->
                    handleTouch(event)
                    true // 拦截所有触摸事件
                }
            }

            // 添加到窗口管理器
            windowManager.addView(overlayView, overlayParams)

            Log.d(TAG, "Touch block overlay view added to window manager")
        } catch (e: Exception) {
            Log.e(TAG, "Error showing overlay", e)
            stopSelf()
        }
    }

    private fun handleTouch(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN && emergencyCloseEnabled) {
            val x = event.x.toInt()
            val y = event.y.toInt()
            
            // 检查是否在左上角检测区域
            if (detectionArea.contains(x, y)) {
                val currentTime = System.currentTimeMillis()
                clickTimes.add(currentTime)
                
                // 清理超时的点击记录
                val timeWindowMs = emergencyTimeWindowSeconds * 1000L
                clickTimes.removeAll { currentTime - it > timeWindowMs }
                
                Log.d(TAG, "Emergency click detected: ${clickTimes.size}/$emergencyClickCount")
                
                // 检查是否达到触发条件
                if (clickTimes.size >= emergencyClickCount) {
                    Log.d(TAG, "Emergency close triggered")
                    dismissTouchBlock()
                    return true
                }
            }
        }
        
        // 拦截所有其他触摸事件
        return true
    }

    private fun dismissTouchBlock() {
        Log.d(TAG, "Touch block dismissed via emergency close")
        
        // 移除悬浮窗
        removeOverlay()
        
        // 停止服务
        stopSelf()
    }

    private fun removeOverlay() {
        try {
            overlayView?.let {
                windowManager.removeView(it)
                overlayView = null
                Log.d(TAG, "Touch block overlay view removed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error removing overlay", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "TouchBlockOverlayService destroyed")
        
        // 清理资源
        removeOverlay()
        serviceScope.cancel()
    }

    companion object {
        /**
         * 启动触摸屏蔽服务
         */
        fun startTouchBlock(
            context: Context,
            emergencyCloseEnabled: Boolean = true,
            emergencyClickCount: Int = 5,
            emergencyTimeWindowSeconds: Int = 3
        ) {
            val intent = Intent(context, TouchBlockOverlayService::class.java).apply {
                putExtra("emergencyCloseEnabled", emergencyCloseEnabled)
                putExtra("emergencyClickCount", emergencyClickCount)
                putExtra("emergencyTimeWindowSeconds", emergencyTimeWindowSeconds)
            }
            context.startService(intent)
        }

        /**
         * 停止触摸屏蔽服务
         */
        fun stopTouchBlock(context: Context) {
            val intent = Intent(context, TouchBlockOverlayService::class.java)
            context.stopService(intent)
        }
    }
}

package com.weinuo.quickcommands.floating

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.R

/**
 * 悬浮录制按钮
 *
 * 独立的悬浮按钮，支持开始录制、暂停、继续、保存等功能。
 * 按钮固定在左上角，不可移动。
 */
class FloatingRecordingButton(
    private val context: Context,
    private val onStartRecording: () -> Unit,
    private val onPauseRecording: () -> Unit,
    private val onResumeRecording: () -> Unit,
    private val onSaveRecording: () -> Unit
) {
    companion object {
        private const val TAG = "FloatingRecordingButton"
        private const val BUTTON_SIZE = 56 // dp
        private const val BUTTON_MARGIN = 0 // dp，距离边缘0dp
    }

    private var windowManager: WindowManager? = null
    private var buttonContainer: LinearLayout? = null
    private var startButton: ImageView? = null
    private var pauseButton: ImageView? = null
    private var continueButton: ImageView? = null
    private var saveButton: ImageView? = null
    private var isShowing = false

    // 录制状态
    private var recordingState = RecordingState.IDLE

    enum class RecordingState {
        IDLE,       // 未录制
        RECORDING,  // 录制中
        PAUSED      // 已暂停
    }

    /**
     * 显示悬浮按钮
     */
    fun show(): Boolean {
        if (isShowing) {
            Log.w(TAG, "悬浮录制按钮已显示")
            return true
        }

        try {
            windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            createButtonContainer()

            val params = createLayoutParams()
            windowManager?.addView(buttonContainer, params)

            isShowing = true
            recordingState = RecordingState.IDLE
            updateButtonVisibility()
            Log.d(TAG, "悬浮录制按钮已显示")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "显示悬浮录制按钮失败", e)
            return false
        }
    }

    /**
     * 隐藏悬浮按钮
     */
    fun hide() {
        if (!isShowing) return

        try {
            buttonContainer?.let { view ->
                windowManager?.removeView(view)
            }
            isShowing = false
            Log.d(TAG, "悬浮录制按钮已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮录制按钮失败", e)
        }
    }

    /**
     * 设置录制状态
     */
    fun setRecordingState(state: RecordingState) {
        recordingState = state
        updateButtonVisibility()
    }

    /**
     * 创建窗口布局参数
     */
    private fun createLayoutParams(): WindowManager.LayoutParams {
        // 使用更高层级的窗口类型，确保在透明覆盖层之上
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = dpToPx(BUTTON_MARGIN)
            y = dpToPx(BUTTON_MARGIN)
        }
    }

    /**
     * 创建按钮容器
     */
    private fun createButtonContainer() {
        buttonContainer = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL

            // 创建开始录制按钮（未录制时显示）
            startButton = createButton(R.drawable.ic_record_voice_over) {
                onStartRecording()
            }

            // 创建暂停按钮（录制时显示）
            pauseButton = createButton(R.drawable.ic_pause) {
                onPauseRecording()
            }

            // 创建继续按钮（暂停时显示）
            continueButton = createButton(R.drawable.ic_play_arrow) {
                onResumeRecording()
            }

            // 创建保存按钮（暂停时显示）
            saveButton = createButton(R.drawable.ic_save) {
                onSaveRecording()
            }

            addView(startButton)
            addView(pauseButton)
            addView(continueButton)
            addView(saveButton)
        }
    }

    private fun createButton(iconRes: Int, onClick: () -> Unit): ImageView {
        return ImageView(context).apply {
            setImageResource(iconRes)
            setBackgroundResource(android.R.drawable.btn_default)
            background.setTint(ContextCompat.getColor(context, android.R.color.holo_blue_bright))
            scaleType = ImageView.ScaleType.CENTER_INSIDE
            setPadding(12, 12, 12, 12)

            val size = dpToPx(BUTTON_SIZE)
            layoutParams = LinearLayout.LayoutParams(size, size).apply {
                setMargins(0, 0, 8, 0) // 按钮间距
            }

            setOnClickListener { onClick() }
        }
    }

    /**
     * 更新按钮可见性
     */
    private fun updateButtonVisibility() {
        startButton?.visibility = if (recordingState == RecordingState.IDLE) View.VISIBLE else View.GONE
        pauseButton?.visibility = if (recordingState == RecordingState.RECORDING) View.VISIBLE else View.GONE
        continueButton?.visibility = if (recordingState == RecordingState.PAUSED) View.VISIBLE else View.GONE
        saveButton?.visibility = if (recordingState == RecordingState.PAUSED) View.VISIBLE else View.GONE
    }

    /**
     * dp转px
     */
    private fun dpToPx(dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }
}

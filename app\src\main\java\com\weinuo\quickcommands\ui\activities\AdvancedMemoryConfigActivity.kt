package com.weinuo.quickcommands.ui.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.weinuo.quickcommands.model.MemoryCheckMode
import com.weinuo.quickcommands.ui.screens.AdvancedMemoryConfigActivityContent
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme

/**
 * 高级内存配置界面Activity
 * 
 * 独立Activity，用于配置高级内存检测参数，包括事件驱动、自适应、智能学习、混合模式等。
 * 支持不同的内存检测模式配置，提供详细的参数调整选项。
 */
class AdvancedMemoryConfigActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_MODE = "mode"
        private const val EXTRA_CONDITION_ID = "condition_id"
        
        /**
         * 启动高级内存配置界面
         * 
         * @param context 上下文
         * @param mode 内存检测模式
         * @param conditionId 条件ID
         */
        fun startForConfiguration(
            context: Context, 
            mode: MemoryCheckMode, 
            conditionId: String
        ) {
            val intent = Intent(context, AdvancedMemoryConfigActivity::class.java).apply {
                putExtra(EXTRA_MODE, mode.name)
                putExtra(EXTRA_CONDITION_ID, conditionId)
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传递的参数
        val modeString = intent.getStringExtra(EXTRA_MODE) ?: MemoryCheckMode.EVENT_DRIVEN.name
        val conditionId = intent.getStringExtra(EXTRA_CONDITION_ID) ?: "temp"
        
        val mode = try {
            MemoryCheckMode.valueOf(modeString)
        } catch (e: IllegalArgumentException) {
            MemoryCheckMode.EVENT_DRIVEN
        }
        
        setContent {
            QuickCommandsTheme {
                AdvancedMemoryConfigActivityContent(
                    mode = mode,
                    conditionId = conditionId,
                    onNavigateBack = { finish() },
                    onNavigateToMemoryLearningData = {
                        // 启动内存学习数据界面
                        MemoryLearningDataActivity.startForView(this)
                    }
                )
            }
        }
    }
}

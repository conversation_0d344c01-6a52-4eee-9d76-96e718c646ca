package com.weinuo.quickcommands.storage

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.model.GestureRecording
import com.weinuo.quickcommands.storage.adapters.GestureRecordingStorageAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.UUID

/**
 * 手势录制原生存储管理器
 *
 * 负责管理手势录制数据的原生存储，替代原来基于JSON文件的GestureRecordingManager。
 * 使用GestureRecordingStorageAdapter进行原生数据类型存储，避免序列化问题。
 *
 * 功能特点：
 * - 原生数据类型存储：使用SharedPreferences存储，避免JSON序列化
 * - 自动ID管理：为每个录制生成唯一ID
 * - 异步操作：所有IO操作都在后台线程执行
 * - 完整的CRUD操作：支持保存、加载、删除、列表查询
 * - 错误处理：完善的异常处理和日志记录
 *
 * @param context Android上下文
 */
class GestureRecordingNativeManager(context: Context) {

    companion object {
        private const val TAG = "GestureRecordingNativeManager"
    }

    private val storageManager = NativeTypeStorageManager(context)
    private val gestureAdapter = GestureRecordingStorageAdapter(storageManager)

    /**
     * 保存录制数据
     *
     * @param recording 要保存的录制对象
     * @param recordingId 录制ID（可选，不提供则自动生成）
     * @return 保存成功返回录制ID，失败返回null
     */
    suspend fun saveRecording(recording: GestureRecording, recordingId: String? = null): String? {
        return withContext(Dispatchers.IO) {
            try {
                val id = recordingId ?: generateRecordingId()

                val success = gestureAdapter.saveGestureRecording(id, recording)
                if (success) {
                    Log.d(TAG, "录制数据保存成功: $id")
                    id
                } else {
                    Log.e(TAG, "录制数据保存失败: $id")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存录制数据时发生异常", e)
                null
            }
        }
    }

    /**
     * 加载录制数据
     *
     * @param recordingId 录制ID
     * @return 录制对象，失败返回null
     */
    suspend fun loadRecording(recordingId: String): GestureRecording? {
        return withContext(Dispatchers.IO) {
            try {
                val recording = gestureAdapter.loadGestureRecording(recordingId)
                if (recording != null) {
                    Log.d(TAG, "录制数据加载成功: $recordingId")
                } else {
                    Log.w(TAG, "录制数据不存在: $recordingId")
                }
                recording
            } catch (e: Exception) {
                Log.e(TAG, "加载录制数据时发生异常: $recordingId", e)
                null
            }
        }
    }

    /**
     * 删除录制数据
     *
     * @param recordingId 录制ID
     * @return 删除是否成功
     */
    suspend fun deleteRecording(recordingId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val success = gestureAdapter.deleteGestureRecording(recordingId)
                if (success) {
                    Log.d(TAG, "录制数据删除成功: $recordingId")
                } else {
                    Log.e(TAG, "录制数据删除失败: $recordingId")
                }
                success
            } catch (e: Exception) {
                Log.e(TAG, "删除录制数据时发生异常: $recordingId", e)
                false
            }
        }
    }

    /**
     * 检查录制是否存在
     *
     * @param recordingId 录制ID
     * @return 是否存在
     */
    suspend fun exists(recordingId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                gestureAdapter.existsGestureRecording(recordingId)
            } catch (e: Exception) {
                Log.e(TAG, "检查录制存在性时发生异常: $recordingId", e)
                false
            }
        }
    }

    /**
     * 获取所有录制数据的基本信息
     *
     * @return 录制信息列表
     */
    suspend fun getAllRecordings(): List<RecordingInfo> {
        return withContext(Dispatchers.IO) {
            try {
                val recordingIds = gestureAdapter.getAllGestureRecordingIds()
                val recordings = mutableListOf<RecordingInfo>()

                recordingIds.forEach { id ->
                    try {
                        val recording = gestureAdapter.loadGestureRecording(id)
                        if (recording != null) {
                            recordings.add(
                                RecordingInfo(
                                    id = id,
                                    name = recording.name,
                                    description = recording.description,
                                    createdTime = recording.createdTime,
                                    duration = recording.duration,
                                    eventCount = recording.getEventCount(),
                                    screenWidth = recording.screenWidth,
                                    screenHeight = recording.screenHeight,
                                    version = recording.version
                                )
                            )
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "加载录制信息失败: $id", e)
                    }
                }

                Log.d(TAG, "获取录制列表成功，共 ${recordings.size} 个录制")
                recordings.sortedByDescending { it.createdTime }

            } catch (e: Exception) {
                Log.e(TAG, "获取录制列表时发生异常", e)
                emptyList()
            }
        }
    }

    /**
     * 清除所有录制数据
     *
     * @return 清除是否成功
     */
    suspend fun clearAllRecordings(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val recordingIds = gestureAdapter.getAllGestureRecordingIds()
                var allSuccess = true

                recordingIds.forEach { id ->
                    val success = gestureAdapter.deleteGestureRecording(id)
                    if (!success) {
                        allSuccess = false
                        Log.w(TAG, "删除录制失败: $id")
                    }
                }

                if (allSuccess) {
                    Log.d(TAG, "清除所有录制数据成功，共清除 ${recordingIds.size} 个录制")
                } else {
                    Log.w(TAG, "清除录制数据时部分失败")
                }
                allSuccess

            } catch (e: Exception) {
                Log.e(TAG, "清除所有录制数据时发生异常", e)
                false
            }
        }
    }

    /**
     * 生成录制ID
     *
     * @return 唯一的录制ID
     */
    private fun generateRecordingId(): String {
        return "recording_${System.currentTimeMillis()}_${UUID.randomUUID().toString().take(8)}"
    }


}

/**
 * 录制信息数据类
 *
 * 包含录制的基本信息，用于列表显示和管理。
 *
 * @property id 录制ID
 * @property name 录制名称
 * @property description 录制描述
 * @property createdTime 创建时间戳
 * @property duration 总持续时间（毫秒）
 * @property eventCount 事件数量
 * @property screenWidth 录制时的屏幕宽度
 * @property screenHeight 录制时的屏幕高度
 * @property version 录制格式版本
 */
data class RecordingInfo(
    val id: String,
    val name: String,
    val description: String = "",
    val createdTime: Long = System.currentTimeMillis(),
    val duration: Long = 0L,
    val eventCount: Int = 0,
    val screenWidth: Int = 0,
    val screenHeight: Int = 0,
    val version: Int = 1
) {
    /**
     * 获取录制的显示名称
     */
    fun getDisplayName(): String {
        return if (name.isNotEmpty()) name else "录制_${id.takeLast(8)}"
    }

    /**
     * 获取录制的详细描述
     */
    fun getDetailedDescription(): String {
        val durationText = if (duration > 0) {
            "${duration}ms"
        } else {
            "未知时长"
        }
        val eventText = "${eventCount}个事件"
        val screenText = if (screenWidth > 0 && screenHeight > 0) {
            "${screenWidth}x${screenHeight}"
        } else {
            "未知分辨率"
        }

        return "$eventText，$durationText，$screenText"
    }
}



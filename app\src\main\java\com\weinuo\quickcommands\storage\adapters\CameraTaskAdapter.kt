package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 相机任务存储适配器
 *
 * 负责CameraTask的原生数据类型存储和重建。
 * 将复杂的相机任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 相机相关：cameraType
 * - 录像相关：videoOperation、recordingDurationMinutes、recordingDurationSeconds
 * - 保存位置相关：photoSaveLocation、videoSaveLocation、customPhotoPath、customVideoPath
 *
 * 存储格式示例：
 * task_{id}_type = "camera"
 * task_{id}_operation = "TAKE_PHOTO"
 * task_{id}_camera_type = "BACK"
 * task_{id}_video_operation = "START"
 * task_{id}_recording_duration_minutes = 0
 * task_{id}_recording_duration_seconds = 30
 * task_{id}_photo_save_location = "DCIM_CAMERA"
 * task_{id}_video_save_location = "MOVIES"
 * task_{id}_custom_photo_path = ""
 * task_{id}_custom_video_path = ""
 *
 * @param storageManager 原生类型存储管理器
 */
class CameraTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<CameraTask>(storageManager) {

    companion object {
        private const val TAG = "CameraTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_CAMERA_TYPE = "camera_type"
        private const val FIELD_VIDEO_OPERATION = "video_operation"
        private const val FIELD_RECORDING_DURATION_MINUTES = "recording_duration_minutes"
        private const val FIELD_RECORDING_DURATION_SECONDS = "recording_duration_seconds"
        private const val FIELD_PHOTO_SAVE_LOCATION = "photo_save_location"
        private const val FIELD_VIDEO_SAVE_LOCATION = "video_save_location"
        private const val FIELD_CUSTOM_PHOTO_PATH = "custom_photo_path"
        private const val FIELD_CUSTOM_VIDEO_PATH = "custom_video_path"
    }

    override fun getTaskType() = "camera"

    /**
     * 保存相机任务
     * 将CameraTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的相机任务
     * @return 操作是否成功
     */
    override fun save(task: CameraTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存相机任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存相机任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),
                saveEnum(generateKey(task.id, FIELD_CAMERA_TYPE), task.cameraType),
                saveEnum(generateKey(task.id, FIELD_VIDEO_OPERATION), task.videoOperation),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDING_DURATION_MINUTES),
                    task.recordingDurationMinutes
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDING_DURATION_SECONDS),
                    task.recordingDurationSeconds
                ),
                saveEnum(generateKey(task.id, FIELD_PHOTO_SAVE_LOCATION), task.photoSaveLocation),
                saveEnum(generateKey(task.id, FIELD_VIDEO_SAVE_LOCATION), task.videoSaveLocation),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_PHOTO_PATH),
                    task.customPhotoPath
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_VIDEO_PATH),
                    task.customVideoPath
                )
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "相机任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载相机任务
     * 从原生数据类型重建CameraTask对象
     *
     * @param taskId 任务ID
     * @return 加载的相机任务，失败时返回null
     */
    override fun load(taskId: String): CameraTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载相机任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "相机任务不存在: $taskId")
                return null
            }

            CameraTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { CameraOperation.valueOf(it) }
                    ?: CameraOperation.OPEN_LAST_PHOTO,
                cameraType = loadEnum(generateKey(taskId, FIELD_CAMERA_TYPE)) { CameraType.valueOf(it) }
                    ?: CameraType.BACK,
                videoOperation = loadEnum(generateKey(taskId, FIELD_VIDEO_OPERATION)) { VideoRecordingOperation.valueOf(it) }
                    ?: VideoRecordingOperation.START,
                recordingDurationMinutes = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDING_DURATION_MINUTES),
                    0
                ),
                recordingDurationSeconds = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDING_DURATION_SECONDS),
                    30
                ),
                photoSaveLocation = loadEnum(generateKey(taskId, FIELD_PHOTO_SAVE_LOCATION)) { SaveLocation.valueOf(it) }
                    ?: SaveLocation.DCIM_CAMERA,
                videoSaveLocation = loadEnum(generateKey(taskId, FIELD_VIDEO_SAVE_LOCATION)) { SaveLocation.valueOf(it) }
                    ?: SaveLocation.MOVIES,
                customPhotoPath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_PHOTO_PATH),
                    ""
                ),
                customVideoPath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_VIDEO_PATH),
                    ""
                )
            ).also {
                Log.d(TAG, "相机任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }
}

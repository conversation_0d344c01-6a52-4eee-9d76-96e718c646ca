package com.weinuo.quickcommands.ui.components.layered

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.weinuo.quickcommands.ui.theme.config.BottomNavigationConfig
import com.weinuo.quickcommands.ui.theme.config.NavigationTab

/**
 * 分层设计风格的底部导航栏
 *
 * 特点：
 * - 使用标准Material 3 NavigationBar设计
 * - 简洁清晰的视觉分离
 * - 遵循Material Design 3规范
 * - 保持与old项目一致的简洁风格
 */
@Composable
fun LayeredBottomNavigation(
    config: BottomNavigationConfig,
    modifier: Modifier = Modifier
) {
    NavigationBar(
        modifier = modifier
    ) {
        config.tabs.forEachIndexed { index, tab ->
            val selected = config.selectedIndex == index

            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = if (selected) (tab.selectedIcon ?: tab.icon) else tab.icon,
                        contentDescription = tab.label
                    )
                },
                label = { Text(tab.label) },
                selected = selected,
                onClick = { config.onTabSelected(index) }
            )
        }
    }
}





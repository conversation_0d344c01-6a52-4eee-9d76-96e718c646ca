package com.weinuo.quickcommands.service

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.*
import android.widget.FrameLayout
import androidx.annotation.RequiresApi

/**
 * 触摸事件录制器
 *
 * 通过透明悬浮窗捕获触摸事件，用于自动点击器的录制功能。
 * 采用最小化干扰设计，确保用户正常使用的同时能够录制操作。
 *
 * 技术特点：
 * - 使用透明悬浮窗覆盖整个屏幕
 * - 不拦截触摸事件，只监听和记录
 * - 支持多点触控和复杂手势
 * - 自动管理悬浮窗的生命周期
 */
class TouchEventRecorder(
    private val context: Context,
    private val callback: TouchEventCallback
) {

    companion object {
        private const val TAG = "TouchEventRecorder"
    }

    interface TouchEventCallback {
        fun onTouchEvent(event: MotionEvent): Boolean
    }

    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var isRecording = false

    /**
     * 开始录制触摸事件
     */
    fun startRecording(): Boolean {
        if (isRecording) {
            Log.w(TAG, "触摸事件录制已在进行中")
            return true
        }

        if (!hasOverlayPermission()) {
            Log.e(TAG, "没有悬浮窗权限，无法开始录制")
            return false
        }

        try {
            createOverlayWindow()
            isRecording = true
            Log.d(TAG, "触摸事件录制已开始")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "开始触摸事件录制失败", e)
            return false
        }
    }

    /**
     * 停止录制触摸事件
     */
    fun stopRecording() {
        if (!isRecording) {
            Log.w(TAG, "触摸事件录制未在进行中")
            return
        }

        try {
            removeOverlayWindow()
            isRecording = false
            Log.d(TAG, "触摸事件录制已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止触摸事件录制失败", e)
        }
    }

    /**
     * 检查是否有悬浮窗权限
     */
    private fun hasOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            android.provider.Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    /**
     * 创建悬浮窗
     */
    private fun createOverlayWindow() {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        // 创建透明的悬浮窗布局
        overlayView = object : FrameLayout(context) {
            override fun onTouchEvent(event: MotionEvent): Boolean {
                // 将触摸事件传递给回调
                val handled = callback.onTouchEvent(event)
                
                // 不拦截事件，让底层应用继续处理
                return false
            }

            override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
                // 监听但不拦截触摸事件
                callback.onTouchEvent(ev)
                return false
            }
        }

        // 配置悬浮窗参数
        val layoutParams = WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
            
            type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }
            
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            
            format = PixelFormat.TRANSLUCENT
            gravity = Gravity.TOP or Gravity.START
            x = 0
            y = 0
        }

        // 添加悬浮窗到窗口管理器
        windowManager?.addView(overlayView, layoutParams)
        Log.d(TAG, "触摸事件录制悬浮窗已创建")
    }

    /**
     * 移除悬浮窗
     */
    private fun removeOverlayWindow() {
        overlayView?.let { view ->
            windowManager?.removeView(view)
            overlayView = null
        }
        windowManager = null
        Log.d(TAG, "触摸事件录制悬浮窗已移除")
    }

    /**
     * 检查是否正在录制
     */
    fun isRecording(): Boolean = isRecording

    /**
     * 清理资源
     */
    fun cleanup() {
        if (isRecording) {
            stopRecording()
        }
    }
}

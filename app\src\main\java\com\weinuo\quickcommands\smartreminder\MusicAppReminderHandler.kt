package com.weinuo.quickcommands.smartreminder

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import android.os.Build
import android.util.Log
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.*

/**
 * 音乐应用提醒处理器
 *
 * 检测耳机连接状态，当检测到耳机连接时，发送智能提醒建议用户打开音乐应用。
 *
 * 检测逻辑：
 * - 监听有线耳机连接/断开事件
 * - 监听蓝牙耳机连接/断开事件
 * - 检查是否已配置音乐应用
 * - 在合适的时机发送提醒通知
 *
 * 设计特点：
 * - 智能防重复：避免频繁提醒
 * - 延迟检测：给耳机连接稳定时间
 * - 状态感知：只在需要时提醒
 * - 用户友好：提供直接的操作建议
 */
class MusicAppReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "MusicAppReminderHandler"
    }

    // 协程作用域
    private val handlerScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 广播接收器
    private var headsetReceiver: BroadcastReceiver? = null
    private var bluetoothReceiver: BroadcastReceiver? = null

    // 配置适配器
    private val configAdapter = SmartReminderConfigAdapter(context)

    // 最后提醒时间
    private var lastReminderTime = 0L

    /**
     * 开始监控耳机连接
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Already monitoring headphone connections")
            return
        }

        try {
            // 注册有线耳机监听器
            setupWiredHeadsetMonitoring()

            // 注册蓝牙耳机监听器
            setupBluetoothHeadsetMonitoring()

            isMonitoring = true
            Log.d(TAG, "Started monitoring headphone connections")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting headphone monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监控耳机连接
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Headphone monitoring not started")
            return
        }

        try {
            // 注销有线耳机接收器
            headsetReceiver?.let { receiver ->
                context.unregisterReceiver(receiver)
                headsetReceiver = null
            }

            // 注销蓝牙耳机接收器
            bluetoothReceiver?.let { receiver ->
                context.unregisterReceiver(receiver)
                bluetoothReceiver = null
            }

            isMonitoring = false
            Log.d(TAG, "Stopped monitoring headphone connections")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping headphone monitoring", e)
        }
    }

    /**
     * 设置有线耳机监听
     */
    private fun setupWiredHeadsetMonitoring() {
        headsetReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (intent.action == AudioManager.ACTION_HEADSET_PLUG) {
                    val state = intent.getIntExtra("state", -1)
                    when (state) {
                        1 -> {
                            // 耳机连接
                            Log.d(TAG, "Wired headset connected")
                            handleHeadsetConnected("wired")
                        }
                        0 -> {
                            // 耳机断开
                            Log.d(TAG, "Wired headset disconnected")
                        }
                    }
                }
            }
        }

        val filter = IntentFilter(AudioManager.ACTION_HEADSET_PLUG)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(headsetReceiver, filter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(headsetReceiver, filter)
        }
    }

    /**
     * 设置蓝牙耳机监听
     */
    private fun setupBluetoothHeadsetMonitoring() {
        bluetoothReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                when (intent.action) {
                    BluetoothDevice.ACTION_ACL_CONNECTED -> {
                        val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                        } else {
                            @Suppress("DEPRECATION")
                            intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                        }

                        device?.let {
                            // 检查是否为音频设备
                            if (isAudioDevice(it)) {
                                Log.d(TAG, "Bluetooth audio device connected: ${it.name}")
                                handleHeadsetConnected("bluetooth")
                            }
                        }
                    }
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(bluetoothReceiver, filter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(bluetoothReceiver, filter)
        }
    }

    /**
     * 检查蓝牙设备是否为音频设备
     */
    private fun isAudioDevice(device: BluetoothDevice): Boolean {
        return try {
            val deviceClass = device.bluetoothClass
            deviceClass?.majorDeviceClass == 1024 || // AUDIO_VIDEO
            deviceClass?.hasService(2097152) == true // AUDIO
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 处理耳机连接事件
     */
    private fun handleHeadsetConnected(type: String) {
        handlerScope.launch {
            try {
                // 加载配置
                val config = loadMusicAppReminderConfig()

                // 检查是否已配置音乐应用
                if (config.selectedMusicApps.isEmpty()) {
                    Log.d(TAG, "Music app not configured, skipping reminder")
                    return@launch
                }

                // 检查冷却时间
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastReminderTime < config.cooldownTime * 1000L) {
                    Log.d(TAG, "Reminder in cooldown period, skipping")
                    return@launch
                }

                // 延迟提醒，给耳机连接稳定时间
                delay(config.delayTime * 1000L)

                // 显示音乐应用提醒
                showMusicAppReminder(config, type)

                // 更新最后提醒时间
                lastReminderTime = currentTime

            } catch (e: Exception) {
                Log.e(TAG, "Error handling headset connected", e)
            }
        }
    }

    /**
     * 显示音乐应用提醒悬浮窗
     */
    private fun showMusicAppReminder(config: SmartReminderConfigAdapter.MusicAppReminderConfig, headsetType: String) {
        try {
            // 获取启用的音乐应用
            val enabledApps = config.selectedMusicApps.filter { it.isEnabled }
            if (enabledApps.isEmpty()) {
                Log.d(TAG, "No enabled music apps found")
                return
            }

            Log.d(TAG, "Attempting to show music app reminder for ${enabledApps.size} apps")

            val title = context.getString(R.string.music_app_reminder_title)

            // 根据应用数量生成不同的消息
            val message = when {
                enabledApps.size == 1 -> "检测到耳机连接，建议打开「${enabledApps.first().appName}」"
                enabledApps.size <= 3 -> "检测到耳机连接，建议打开：${enabledApps.joinToString("、") { "「${it.appName}」" }}"
                else -> "检测到耳机连接，建议打开音乐应用（${enabledApps.size}个可选）"
            }

            Log.d(TAG, "Calling SmartReminderOverlayService.showMultiMusicAppReminder")

            // 调用SmartReminderOverlayService显示多应用音乐提醒
            com.weinuo.quickcommands.service.SmartReminderOverlayService.showMultiMusicAppReminder(
                context = context,
                title = title,
                message = message,
                selectedApps = enabledApps,
                buttonConfigs = config.buttonConfigs.filter { buttonConfig ->
                    enabledApps.any { app -> app.packageName == buttonConfig.appPackageName } && buttonConfig.isEnabled
                }
            )

            Log.d(TAG, "SmartReminderOverlayService.showMultiMusicAppReminder called successfully")

            // 触发回调通知
            onReminderTriggered(mapOf(
                "type" to "music_app_reminder",
                "timestamp" to System.currentTimeMillis(),
                "selectedApps" to enabledApps.map { mapOf("packageName" to it.packageName, "appName" to it.appName) },
                "headsetType" to headsetType,
                "message" to message
            ))
        } catch (e: Exception) {
            Log.e(TAG, "Error showing music app reminder", e)
        }
    }

    /**
     * 加载音乐应用提醒配置
     */
    private fun loadMusicAppReminderConfig(): SmartReminderConfigAdapter.MusicAppReminderConfig {
        return try {
            val reminderTypeId = SmartReminderType.MUSIC_APP_REMINDER.id
            if (configAdapter.hasConfig(reminderTypeId)) {
                configAdapter.loadMusicAppReminderConfig(reminderTypeId)
            } else {
                // 返回默认配置
                SmartReminderConfigAdapter.MusicAppReminderConfig()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading music app reminder config", e)
            SmartReminderConfigAdapter.MusicAppReminderConfig()
        }
    }
}

# SimpleAppInfo Parcelable 序列化问题修复指南

## 问题描述

### 错误现象
```
java.lang.IllegalArgumentException: Parcel: unknown type for value SimpleAppInfo(packageName=com.qihoo.zhinao, appName=360智脑, isSystemApp=false)
	at android.os.Parcel.getValueType(Parcel.java:2507)
	at android.os.Parcel.writeValue(Parcel.java:2406)
	at android.os.Parcel.writeList(Parcel.java:1467)
	...
```

### 问题根源
- `SimpleAppInfo` 类没有实现 `Parcelable` 接口
- Compose 的状态保存机制（`rememberSaveable`、`savedStateHandle`）尝试序列化包含 `SimpleAppInfo` 的对象时失败
- 项目使用自定义序列化机制（Gson + ConfigurationSerializationUtils），不应该让数据类实现 Parcelable

## 解决方案概述

**核心策略**: 保持项目的自定义序列化策略，通过字段分离和可序列化 Map 格式来传递 `SimpleAppInfo` 数据。

## 具体修复步骤

### 1. 增强 SimpleAppInfo 类

在 `app/src/main/java/com/my/backgroundmanager/model/shared_trigger_condition_list.kt` 中：

```kotlin
data class SimpleAppInfo(
    val packageName: String,
    val appName: String,
    val isSystemApp: Boolean
) {
    companion object {
        /**
         * 将SimpleAppInfo列表转换为可序列化的Map格式
         */
        fun toSerializableList(apps: List<SimpleAppInfo>): List<Map<String, Any>> {
            return apps.map { app ->
                mapOf(
                    "packageName" to app.packageName,
                    "appName" to app.appName,
                    "isSystemApp" to app.isSystemApp
                )
            }
        }

        /**
         * 从可序列化的Map格式恢复SimpleAppInfo列表
         */
        fun fromSerializableList(data: List<Map<String, Any>>): List<SimpleAppInfo> {
            return data.mapNotNull { appMap ->
                try {
                    SimpleAppInfo(
                        packageName = appMap["packageName"] as? String ?: "",
                        appName = appMap["appName"] as? String ?: "",
                        isSystemApp = appMap["isSystemApp"] as? Boolean ?: false
                    )
                } catch (e: Exception) {
                    null
                }
            }
        }
    }
}
```

### 2. 修复导航结果传递机制

在 `MainActivity.kt` 的应用选择结果处理中：

```kotlin
onAppsSelected = { selectedApps ->
    val previousEntry = navController.previousBackStackEntry
    previousEntry?.savedStateHandle?.apply {
        if (resultKey == "selected_apps" && selectedApps.isNotEmpty()) {
            // 默认结果键：使用字段分离方式传递单个应用
            val app = selectedApps.first()
            set("selected_app_package_name", app.packageName)
            set("selected_app_name", app.appName)
            set("selected_app_is_system_app", app.isSystemApp)
        } else if (resultKey == "selected_vpn_apps") {
            // VPN应用选择：使用可序列化的Map格式传递应用列表
            val serializableApps = SimpleAppInfo.toSerializableList(selectedApps)
            set(resultKey, serializableApps)
        } else {
            // 其他自定义结果键：使用可序列化的Map格式
            val serializableApps = SimpleAppInfo.toSerializableList(selectedApps)
            set(resultKey, serializableApps)
        }
    }
    navController.popBackStack()
}
```

### 3. 修复配置组件的结果监听

#### 单选应用场景（字段分离方式）

适用于：插件应用选择、应用状态配置等单选场景

```kotlin
// 监听应用选择结果
LaunchedEffect(Unit) {
    val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
    // 使用字段分离的方式获取应用选择结果
    val selectedAppPackageNameResult = savedStateHandle?.get<String>("selected_app_package_name")
    val selectedAppNameResult = savedStateHandle?.get<String>("selected_app_name")
    val selectedAppIsSystemAppResult = savedStateHandle?.get<Boolean>("selected_app_is_system_app")

    if (!selectedAppPackageNameResult.isNullOrEmpty()) {
        // 更新选中的应用
        selectedAppPackageName = selectedAppPackageNameResult
        selectedAppName = selectedAppNameResult ?: ""
        selectedAppIsSystemApp = selectedAppIsSystemAppResult ?: false
        // 清除结果，避免重复处理
        savedStateHandle.remove<String>("selected_app_package_name")
        savedStateHandle.remove<String>("selected_app_name")
        savedStateHandle.remove<Boolean>("selected_app_is_system_app")
    }
}
```

#### 多选应用场景（可序列化Map格式）

适用于：强制停止应用选择、通知事件应用选择等多选场景

```kotlin
// 监听应用选择结果
LaunchedEffect(Unit) {
    val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle

    // 监听主要应用选择结果（使用可序列化的Map格式）
    val selectedAppsData = savedStateHandle?.get<List<Map<String, Any>>>("selected_apps")
    if (selectedAppsData != null && selectedAppsData.isNotEmpty()) {
        selectedApps = SimpleAppInfo.fromSerializableList(selectedAppsData)
        savedStateHandle.remove<List<Map<String, Any>>>("selected_apps")
    }

    // 监听VPN应用选择结果（使用可序列化的Map格式）
    val selectedVpnAppsData = savedStateHandle?.get<List<Map<String, Any>>>("selected_vpn_apps")
    if (selectedVpnAppsData != null && selectedVpnAppsData.isNotEmpty()) {
        selectedVpnApps = SimpleAppInfo.fromSerializableList(selectedVpnAppsData)
        savedStateHandle.remove<List<Map<String, Any>>>("selected_vpn_apps")
    }
}
```

## 需要修复的文件清单

### 已修复的文件：
1. ✅ `model/shared_trigger_condition_list.kt` - 增强 SimpleAppInfo 类
2. ✅ `MainActivity.kt` - 修复导航结果传递
3. ✅ `ui/configuration/ApplicationTaskConfigProvider.kt` - 修复强制停止应用配置
4. ✅ `ui/configuration/AppStateConfigProvider.kt` - 修复应用状态配置（3处）
5. ✅ `ui/configuration/DeviceEventConfigProvider.kt` - 修复通知事件配置
6. ✅ `ui/configuration/NotificationTaskConfigProvider.kt` - 修复通知任务配置（2处）

### 可能需要检查的文件：
- `ui/configuration/ConnectivityTaskConfigProvider.kt`
- `ui/configuration/TaskConfigProvider.kt`
- `ui/configuration/DeviceSettingsTaskConfigProvider.kt`
- `ui/configuration/FileOperationTaskConfigProvider.kt`

## 检查和修复流程

### 1. 识别问题
搜索使用 `savedStateHandle?.get<List<SimpleAppInfo>>("selected_apps")` 的代码：
```bash
findstr /s /i "savedStateHandle.*get.*List.*SimpleAppInfo" app\src\*
```

### 2. 确定修复方式
- **单选场景**: 使用字段分离方式（package_name, app_name, is_system_app）
- **多选场景**: 使用可序列化Map格式（List<Map<String, Any>>）

### 3. 验证修复
```bash
./gradlew assembleDebug
```

## 重要注意事项

1. **保持项目架构一致性**: 不要让 SimpleAppInfo 实现 Parcelable，保持自定义序列化策略
2. **类型安全**: 使用 `as?` 安全转换，提供默认值
3. **内存管理**: 及时清除 savedStateHandle 中的结果，避免重复处理
4. **向后兼容**: 修复时不要破坏现有的数据模型结构

## 测试验证

修复完成后，应用应该能够：
- ✅ 正常编译和安装
- ✅ 应用选择功能正常工作
- ✅ 状态保存和恢复正常
- ✅ 不再出现 Parcelable 序列化错误

## 故障排除

### 常见错误和解决方法

#### 1. 类型转换错误
```
ClassCastException: Cannot cast to List<Map<String, Any>>
```
**解决**: 检查 savedStateHandle 中存储的数据类型，确保使用正确的泛型参数。

#### 2. 空指针异常
```
NullPointerException in fromSerializableList
```
**解决**: 在 fromSerializableList 方法中添加 null 检查和异常处理。

#### 3. 数据丢失
应用选择后数据没有传递成功。
**解决**: 检查 resultKey 是否匹配，确保 savedStateHandle 的 set/get 操作使用相同的键。

### 调试技巧

1. **添加日志**:
```kotlin
Log.d("AppSelection", "Received data: $selectedAppsData")
Log.d("AppSelection", "Converted apps: ${SimpleAppInfo.fromSerializableList(selectedAppsData)}")
```

2. **检查数据格式**:
```kotlin
val data = savedStateHandle?.get<Any>("selected_apps")
Log.d("AppSelection", "Data type: ${data?.javaClass?.simpleName}")
```

3. **验证序列化**:
```kotlin
val testApps = listOf(SimpleAppInfo("test.pkg", "Test App", false))
val serialized = SimpleAppInfo.toSerializableList(testApps)
val deserialized = SimpleAppInfo.fromSerializableList(serialized)
Log.d("AppSelection", "Serialization test: $deserialized")
```

## 扩展指南

### 添加新的应用选择场景

1. **确定选择模式**: 单选还是多选
2. **选择传递方式**: 字段分离（单选）或 Map 格式（多选）
3. **实现结果监听**: 参考上述模板代码
4. **测试验证**: 确保状态保存/恢复正常工作

### 自定义结果键

如果需要添加新的自定义结果键（如 `"selected_music_apps"`），在 MainActivity.kt 中添加相应的处理逻辑：

```kotlin
else if (resultKey == "selected_music_apps") {
    // 音乐应用选择：使用可序列化的Map格式传递应用列表
    val serializableApps = SimpleAppInfo.toSerializableList(selectedApps)
    set(resultKey, serializableApps)
}
```

## 相关文档

- [项目架构文档](system_architecture_document.md)
- [实施计划](implementation_plan_and_todo.md)
- [反思日志](reflection_log.md)

---

**创建时间**: 2025-06-17
**适用版本**: BackgroundManagerMD3 项目
**维护状态**: 当前有效
**最后更新**: 解决 SimpleAppInfo Parcelable 序列化问题

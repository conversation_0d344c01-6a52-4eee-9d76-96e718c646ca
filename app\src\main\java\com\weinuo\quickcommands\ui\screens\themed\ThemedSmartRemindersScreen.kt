package com.weinuo.quickcommands.ui.screens.themed

import androidx.compose.runtime.Composable
import androidx.navigation.NavController
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的智能提醒界面
 *
 * 根据当前主题自动选择合适的界面实现：
 * - 海洋蓝主题：使用OceanBlueSmartRemindersScreen（分层设计风格）
 * - 天空蓝主题：使用SkyBlueSmartRemindersScreen（整合设计风格）
 * - 未来主题：可以使用各自独有的实现
 *
 * 这是主题系统的核心界面组件之一，提供了统一的接口，
 * 同时允许不同主题有完全不同的界面实现和功能特性。
 */
@Composable
fun ThemedSmartRemindersScreen(
    navController: NavController
) {
    val themeContext = LocalThemeContext.current
    val screenFactory = themeContext.theme.themeProvider.getScreenFactory()

    // 使用界面工厂创建主题特定的界面实现
    screenFactory.createSmartRemindersScreen(navController = navController)
}

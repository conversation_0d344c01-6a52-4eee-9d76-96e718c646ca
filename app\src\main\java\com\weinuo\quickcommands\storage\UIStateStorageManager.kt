package com.weinuo.quickcommands.storage

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands.model.SharedTriggerCondition
import com.weinuo.quickcommands.model.SharedTask
import com.weinuo.quickcommands.model.SimpleAppInfo
import com.weinuo.quickcommands.storage.adapters.ConditionAdapterManager
import com.weinuo.quickcommands.storage.adapters.TaskAdapterManager

/**
 * UI状态存储管理器
 *
 * 专门用于管理UI组件的状态数据，替代rememberSaveable中的JSON序列化。
 * 使用原生数据类型存储，确保状态保存和恢复的可靠性。
 *
 * 核心功能：
 * - 条件列表状态保存/恢复
 * - 任务列表状态保存/恢复
 * - UI组件状态管理
 * - 导航过程中的状态持久化
 *
 * @param context Android上下文
 */
class UIStateStorageManager(context: Context) {

    companion object {
        private const val TAG = "UIStateStorageManager"
    }

    private val storageManager = NativeTypeStorageManager(context)
    private val appListStorageEngine = AppListStorageEngine(storageManager)
    private val conditionAdapterManager = ConditionAdapterManager(storageManager, appListStorageEngine)
    private val taskAdapterManager = TaskAdapterManager(storageManager)

    /**
     * 保存条件列表状态
     *
     * @param stateKey 状态键名，用于标识不同的UI组件
     * @param conditions 条件列表
     * @return 保存是否成功
     */
    fun saveConditionListState(stateKey: String, conditions: List<SharedTriggerCondition>): Boolean {
        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存条件数量
            operations.add(StorageOperation.createIntOperation(
                StorageDomain.UI_STATE,
                "ui_state_${stateKey}_condition_count",
                conditions.size
            ))

            // 保存每个条件的ID
            conditions.forEachIndexed { index, condition ->
                operations.add(StorageOperation.createStringOperation(
                    StorageDomain.UI_STATE,
                    "ui_state_${stateKey}_condition_${index}_id",
                    condition.id
                ))

                // 保存条件到条件存储域
                conditionAdapterManager.saveCondition(condition)
            }

            storageManager.executeBatch(operations)
        } catch (e: Exception) {
            Log.e(TAG, "保存条件列表状态失败: $stateKey", e)
            false
        }
    }

    /**
     * 加载条件列表状态
     *
     * @param stateKey 状态键名
     * @return 条件列表，失败返回空列表
     */
    fun loadConditionListState(stateKey: String): List<SharedTriggerCondition> {
        return try {
            val count = storageManager.loadInt(StorageDomain.UI_STATE, "ui_state_${stateKey}_condition_count", 0)
            if (count == 0) return emptyList()

            val conditions = mutableListOf<SharedTriggerCondition>()

            for (index in 0 until count) {
                val conditionId = storageManager.loadString(
                    StorageDomain.UI_STATE,
                    "ui_state_${stateKey}_condition_${index}_id"
                )

                if (conditionId.isNotEmpty()) {
                    conditionAdapterManager.loadConditionByStoredType(conditionId)?.let { condition ->
                        conditions.add(condition)
                    }
                }
            }

            conditions
        } catch (e: Exception) {
            Log.e(TAG, "加载条件列表状态失败: $stateKey", e)
            emptyList()
        }
    }

    /**
     * 保存任务列表状态
     *
     * @param stateKey 状态键名，用于标识不同的UI组件
     * @param tasks 任务列表
     * @return 保存是否成功
     */
    fun saveTaskListState(stateKey: String, tasks: List<SharedTask>): Boolean {
        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存任务数量
            operations.add(StorageOperation.createIntOperation(
                StorageDomain.UI_STATE,
                "ui_state_${stateKey}_task_count",
                tasks.size
            ))

            // 保存每个任务的ID
            tasks.forEachIndexed { index, task ->
                operations.add(StorageOperation.createStringOperation(
                    StorageDomain.UI_STATE,
                    "ui_state_${stateKey}_task_${index}_id",
                    task.id
                ))

                // 保存任务到任务存储域
                taskAdapterManager.saveTask(task)
            }

            storageManager.executeBatch(operations)
        } catch (e: Exception) {
            Log.e(TAG, "保存任务列表状态失败: $stateKey", e)
            false
        }
    }

    /**
     * 加载任务列表状态
     *
     * @param stateKey 状态键名
     * @return 任务列表，失败返回空列表
     */
    fun loadTaskListState(stateKey: String): List<SharedTask> {
        return try {
            val count = storageManager.loadInt(StorageDomain.UI_STATE, "ui_state_${stateKey}_task_count", 0)
            if (count == 0) return emptyList()

            val tasks = mutableListOf<SharedTask>()

            for (index in 0 until count) {
                val taskId = storageManager.loadString(
                    StorageDomain.UI_STATE,
                    "ui_state_${stateKey}_task_${index}_id"
                )

                if (taskId.isNotEmpty()) {
                    taskAdapterManager.loadTaskByStoredType(taskId)?.let { task ->
                        tasks.add(task)
                    }
                }
            }

            tasks
        } catch (e: Exception) {
            Log.e(TAG, "加载任务列表状态失败: $stateKey", e)
            emptyList()
        }
    }

    /**
     * 清除UI状态
     *
     * @param stateKey 状态键名
     * @return 清除是否成功
     */
    fun clearUIState(stateKey: String): Boolean {
        return try {
            storageManager.deleteByPrefix(StorageDomain.UI_STATE, "ui_state_${stateKey}_")
        } catch (e: Exception) {
            Log.e(TAG, "清除UI状态失败: $stateKey", e)
            false
        }
    }

    /**
     * 保存简单状态值
     *
     * @param stateKey 状态键名
     * @param key 键名
     * @param value 值
     * @return 保存是否成功
     */
    fun saveSimpleState(stateKey: String, key: String, value: Any): Boolean {
        return try {
            val fullKey = "ui_state_${stateKey}_${key}"
            when (value) {
                is String -> storageManager.saveString(StorageDomain.UI_STATE, fullKey, value)
                is Int -> storageManager.saveInt(StorageDomain.UI_STATE, fullKey, value)
                is Boolean -> storageManager.saveBoolean(StorageDomain.UI_STATE, fullKey, value)
                is Float -> storageManager.saveFloat(StorageDomain.UI_STATE, fullKey, value)
                is Long -> storageManager.saveLong(StorageDomain.UI_STATE, fullKey, value)
                else -> {
                    Log.w(TAG, "不支持的状态值类型: ${value::class.java.simpleName}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存简单状态失败: $stateKey.$key", e)
            false
        }
    }

    /**
     * 加载字符串状态值
     */
    fun loadStringState(stateKey: String, key: String, defaultValue: String = ""): String {
        return storageManager.loadString(StorageDomain.UI_STATE, "ui_state_${stateKey}_${key}", defaultValue)
    }

    /**
     * 加载整数状态值
     */
    fun loadIntState(stateKey: String, key: String, defaultValue: Int = 0): Int {
        return storageManager.loadInt(StorageDomain.UI_STATE, "ui_state_${stateKey}_${key}", defaultValue)
    }

    /**
     * 加载布尔状态值
     */
    fun loadBooleanState(stateKey: String, key: String, defaultValue: Boolean = false): Boolean {
        return storageManager.loadBoolean(StorageDomain.UI_STATE, "ui_state_${stateKey}_${key}", defaultValue)
    }

    /**
     * 加载浮点数状态值
     */
    fun loadFloatState(stateKey: String, key: String, defaultValue: Float = 0f): Float {
        return storageManager.loadFloat(StorageDomain.UI_STATE, "ui_state_${stateKey}_${key}", defaultValue)
    }

    /**
     * 加载长整数状态值
     */
    fun loadLongState(stateKey: String, key: String, defaultValue: Long = 0L): Long {
        return storageManager.loadLong(StorageDomain.UI_STATE, "ui_state_${stateKey}_${key}", defaultValue)
    }

    /**
     * 保存应用列表状态
     *
     * @param stateKey 状态键名，用于标识不同的UI组件
     * @param listKey 应用列表的键名（如"selected_apps"、"selected_vpn_apps"）
     * @param apps 应用列表
     * @return 保存是否成功
     */
    fun saveAppListState(stateKey: String, listKey: String, apps: List<SimpleAppInfo>): Boolean {
        return try {
            val fullKey = "ui_state_${stateKey}_${listKey}"
            appListStorageEngine.saveAppList(fullKey, apps)
        } catch (e: Exception) {
            Log.e(TAG, "保存应用列表状态失败: $stateKey.$listKey", e)
            false
        }
    }

    /**
     * 加载应用列表状态
     *
     * @param stateKey 状态键名
     * @param listKey 应用列表的键名
     * @return 应用列表，失败返回空列表
     */
    fun loadAppListState(stateKey: String, listKey: String): List<SimpleAppInfo> {
        return try {
            val fullKey = "ui_state_${stateKey}_${listKey}"
            appListStorageEngine.loadAppList(fullKey)
        } catch (e: Exception) {
            Log.e(TAG, "加载应用列表状态失败: $stateKey.$listKey", e)
            emptyList()
        }
    }

    /**
     * 清除应用列表状态
     *
     * @param stateKey 状态键名
     * @param listKey 应用列表的键名
     * @return 清除是否成功
     */
    fun clearAppListState(stateKey: String, listKey: String): Boolean {
        return try {
            val fullKey = "ui_state_${stateKey}_${listKey}"
            appListStorageEngine.deleteAppList(fullKey)
        } catch (e: Exception) {
            Log.e(TAG, "清除应用列表状态失败: $stateKey.$listKey", e)
            false
        }
    }
}

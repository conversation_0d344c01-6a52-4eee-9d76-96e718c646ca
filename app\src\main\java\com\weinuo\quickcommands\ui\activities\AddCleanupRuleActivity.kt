package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.weinuo.quickcommands.model.CleanupRule
import com.weinuo.quickcommands.ui.screens.AddCleanupRuleScreen
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme

/**
 * 添加清理规则界面Activity
 * 
 * 独立Activity，用于添加新的清理规则。
 * 支持多种规则类型的配置，包括参数设置等。
 */
class AddCleanupRuleActivity : ComponentActivity() {
    
    companion object {
        /**
         * 启动添加清理规则界面
         * 
         * @param context 上下文
         */
        fun startForAdd(context: Context) {
            val intent = Intent(context, AddCleanupRuleActivity::class.java)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            QuickCommandsTheme {
                AddCleanupRuleScreen(
                    onRuleAdded = { rule ->
                        // 返回添加的规则
                        val intent = Intent().apply {
                            putExtra("added_rule_type", rule.type.name)
                            putExtra("added_rule_order", rule.order)
                            putExtra("added_rule_enabled", rule.enabled)
                            // 将参数转换为字符串传递
                            rule.parameters.forEach { (key, value) ->
                                putExtra("param_$key", value.toString())
                            }
                        }
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    },
                    onNavigateBack = { finish() }
                )
            }
        }
    }
}

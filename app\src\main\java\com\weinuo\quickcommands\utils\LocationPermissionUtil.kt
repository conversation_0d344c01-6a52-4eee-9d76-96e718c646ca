package com.weinuo.quickcommands.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 位置权限工具类
 * 管理位置相关权限的检查、申请和引导
 * 适用于WiFi SSID检测、基站连接检测、日出日落计算等功能
 */
object LocationPermissionUtil {
    
    /**
     * 位置权限列表
     */
    private val LOCATION_PERMISSIONS = arrayOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    
    /**
     * 检查是否拥有位置权限
     * @param context 上下文
     * @return 是否拥有位置权限
     */
    fun hasLocationPermission(context: Context): Boolean {
        return LOCATION_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查是否拥有精确位置权限
     * @param context 上下文
     * @return 是否拥有精确位置权限
     */
    fun hasFineLocationPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否拥有粗略位置权限
     * @param context 上下文
     * @return 是否拥有粗略位置权限
     */
    fun hasCoarseLocationPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 申请位置权限
     * @param launcher 权限申请启动器
     */
    fun requestLocationPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(LOCATION_PERMISSIONS)
    }
    
    /**
     * 打开位置设置页面
     * @param context 上下文
     */
    fun openLocationSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开位置设置，则打开应用详情页面
            openAppDetailsSettings(context)
        }
    }
    
    /**
     * 打开应用详情设置页面
     * @param context 上下文
     */
    fun openAppDetailsSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 忽略异常，用户可以手动到设置中授权
        }
    }
    
    /**
     * 位置权限说明对话框
     * 向用户解释为什么需要位置权限
     */
    @Composable
    fun LocationPermissionRationaleDialog(
        onConfirm: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "位置权限说明",
            message = """
                为了使用位置相关功能，应用需要位置权限：

                • WiFi网络切换检测：需要位置权限来获取WiFi网络名称(SSID)
                • 基站连接检测：需要位置权限来检测基站信息变化
                • 日出日落计算：需要位置权限来自动获取当前位置进行计算

                这些权限仅用于功能检测，不会收集或上传您的位置信息。
                您可以随时在系统设置中撤销这些权限。
            """.trimIndent(),
            confirmText = "申请权限",
            dismissText = "取消",
            onConfirm = onConfirm,
            onDismiss = onDismiss
        )
    }
    
    /**
     * 位置权限被拒绝后的说明对话框
     */
    @Composable
    fun LocationPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "位置权限被拒绝",
            message = """
                位置相关功能需要位置权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动授予位置权限：
                • WiFi网络切换检测
                • 基站连接检测  
                • 日出日落自动位置计算

                您可以点击"打开设置"直接跳转到位置权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }
    
    /**
     * 获取需要的位置权限列表
     * @return 位置权限数组
     */
    fun getRequiredLocationPermissions(): Array<String> {
        return LOCATION_PERMISSIONS
    }
}

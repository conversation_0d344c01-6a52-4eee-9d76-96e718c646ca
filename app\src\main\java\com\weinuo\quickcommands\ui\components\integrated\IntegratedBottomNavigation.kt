package com.weinuo.quickcommands.ui.components.integrated

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.ui.theme.config.BottomNavigationConfig
import com.weinuo.quickcommands.ui.theme.config.NavigationTab
import com.weinuo.quickcommands.ui.theme.config.BlurComponent
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.manager.BlurConfigurationManager
import com.weinuo.quickcommands.ui.theme.manager.SkyBlueColorConfigurationManager
import com.weinuo.quickcommands.ui.theme.manager.BottomNavigationStyleConfigurationManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueColorScheme
import com.weinuo.quickcommands.ui.effects.HazeManager
import com.weinuo.quickcommands.ui.effects.backgroundBlurEffect
import com.weinuo.quickcommands.data.SettingsRepository
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeSource

/**
 * 整合设计风格的底部导航组件
 *
 * 特点：
 * - 支持iOS风格模糊效果（使用Haze库）
 * - 无阴影设计
 * - 大圆角
 * - 统一的视觉体验
 * - 流畅的动画过渡
 */
@Composable
fun IntegratedBottomNavigation(
    config: BottomNavigationConfig,
    hazeState: HazeState,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val blurConfigManager = remember { BlurConfigurationManager.getInstance(context) }
    val blurConfig = blurConfigManager.getBlurConfiguration()

    // 根据当前主题获取颜色配置
    val themeContext = LocalThemeContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }

    // 获取底部导航栏背景颜色 - 根据主题动态选择
    val bottomNavBackgroundColor = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context) }
        val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()
        colorConfig.bottomNavBackground
    } else {
        // 其他主题使用MaterialTheme的surface颜色
        themeContext.colorScheme.surface
    }

    // 获取底部导航栏样式配置管理器
    val styleConfigManager = remember { BottomNavigationStyleConfigurationManager.getInstance(context, settingsRepository) }
    val styleConfig = styleConfigManager.getBottomNavigationStyleConfiguration()

    // 获取扩展颜色 - 根据主题动态选择
    val extendedColors = if (themeManager.getCurrentThemeId() == "sky_blue") {
        SkyBlueColorScheme.ExtendedColors
    } else {
        null // 其他主题暂时不使用扩展颜色
    }

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(styleConfig.height) // 使用动态配置的高度
            .hazeSource(
                state = hazeState,
                zIndex = 2f // 底部导航栏层级，高于标题栏(1f)和内容区域(0f)
            )
            .backgroundBlurEffect(
                hazeState = hazeState,
                style = blurConfigManager.getBottomBarHazeMaterial(),
                backgroundColor = bottomNavBackgroundColor, // 使用主题相关的底部导航栏背景色
                component = BlurComponent.BOTTOM_BAR // 指定为底部栏组件
            ),
        color = if (blurConfig.bottomBarBlurEnabled) {
            Color.Transparent // 模糊时完全透明，让backgroundBlurEffect处理
        } else {
            bottomNavBackgroundColor // 不模糊时使用主题相关的底部导航栏背景色
        },
        tonalElevation = 0.dp, // 整合设计不使用阴影
        shadowElevation = 0.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .selectableGroup()
                .padding(horizontal = styleConfig.horizontalPadding, vertical = styleConfig.verticalPadding), // 使用动态配置的内边距
            horizontalArrangement = styleConfig.itemArrangement, // 使用动态配置的排列方式
            verticalAlignment = Alignment.CenterVertically
        ) {
            config.tabs.forEachIndexed { index, tab ->
                IntegratedNavigationItem(
                    tab = tab,
                    selected = config.selectedIndex == index,
                    onClick = { config.onTabSelected(index) },
                    styleConfig = styleConfig, // 传递样式配置
                    selectedIconColor = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context) }
                        val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()
                        colorConfig.bottomNavSelectedIcon
                    } else {
                        themeContext.colorScheme.primary
                    },
                    unselectedIconColor = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context) }
                        val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()
                        colorConfig.bottomNavUnselectedIcon
                    } else {
                        themeContext.colorScheme.onSurfaceVariant
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 整合设计风格的导航项
 */
@Composable
private fun IntegratedNavigationItem(
    tab: NavigationTab,
    selected: Boolean,
    onClick: () -> Unit,
    styleConfig: com.weinuo.quickcommands.ui.theme.config.BottomNavigationStyleConfig,
    selectedIconColor: Color,
    unselectedIconColor: Color,
    modifier: Modifier = Modifier
) {
    // 动画状态
    val animatedColor by animateColorAsState(
        targetValue = if (selected) {
            selectedIconColor // 使用传入的选中图标颜色
        } else {
            unselectedIconColor // 使用传入的未选中图标颜色
        },
        animationSpec = tween(durationMillis = styleConfig.colorAnimationDuration), // 使用动态配置的动画时长
        label = "icon_color"
    )

    val animatedBackgroundColor by animateColorAsState(
        targetValue = Color.Transparent, // 去掉选中状态的绿色阴影背景
        animationSpec = tween(durationMillis = styleConfig.backgroundAnimationDuration), // 使用动态配置的动画时长
        label = "background_color"
    )

    Surface(
        onClick = onClick,
        modifier = modifier
            .clip(RoundedCornerShape(styleConfig.itemCornerRadius)) // 使用动态配置的圆角
            .padding(styleConfig.itemOuterPadding), // 使用动态配置的外边距
        color = animatedBackgroundColor,
        shape = RoundedCornerShape(styleConfig.itemCornerRadius) // 使用动态配置的圆角
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = styleConfig.itemVerticalPadding, horizontal = styleConfig.itemHorizontalPadding), // 使用动态配置的内边距
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 图标
            Icon(
                imageVector = if (selected) (tab.selectedIcon ?: tab.icon) else tab.icon,
                contentDescription = tab.label,
                tint = animatedColor,
                modifier = Modifier.size(styleConfig.iconSize) // 使用动态配置的图标大小
            )

            Spacer(modifier = Modifier.height(styleConfig.iconTextSpacing)) // 使用动态配置的图标文字间距

            // 标签文本
            Text(
                text = tab.label,
                style = MaterialTheme.typography.labelSmall.copy(
                    fontWeight = if (selected) styleConfig.selectedFontWeight else styleConfig.unselectedFontWeight, // 使用动态配置的字重
                    fontSize = styleConfig.textFontSize // 使用动态配置的字体大小
                ),
                color = animatedColor,
                maxLines = 1
            )
        }
    }
}



// NavigationTab 数据类已在 ComponentConfigs.kt 中定义

package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 信息任务存储适配器
 *
 * 负责InformationTask的原生数据类型存储和重建。
 * 将复杂的信息任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 短信相关：phoneNumber、messageText、simCardSelection、draftOnly
 * - 邮件相关：emailRecipient、emailSubject、smtpServer、smtpPort、senderEmail、senderPassword
 * - 邮件配置：useSSL、useAuthentication、supportStartTLS、notifyOnCompletion、notifyOnFailure、isHtmlEmail、username
 * - 铃声相关：selectedRingtoneUri、selectedRingtoneName
 *
 * 存储格式示例：
 * task_{id}_type = "information"
 * task_{id}_operation = "SEND_SMS"
 * task_{id}_phone_number = "13800138000"
 * task_{id}_message_text = "Hello World"
 * task_{id}_sim_card_selection = "SIM1"
 * task_{id}_draft_only = false
 * task_{id}_email_recipient = "<EMAIL>"
 * task_{id}_email_subject = "Test Email"
 *
 * @param storageManager 原生类型存储管理器
 */
class InformationTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<InformationTask>(storageManager) {

    companion object {
        private const val TAG = "InformationTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_PHONE_NUMBER = "phone_number"
        private const val FIELD_MESSAGE_TEXT = "message_text"
        private const val FIELD_SIM_CARD_SELECTION = "sim_card_selection"
        private const val FIELD_DRAFT_ONLY = "draft_only"
        private const val FIELD_EMAIL_RECIPIENT = "email_recipient"
        private const val FIELD_EMAIL_SUBJECT = "email_subject"
        private const val FIELD_SMTP_SERVER = "smtp_server"
        private const val FIELD_SMTP_PORT = "smtp_port"
        private const val FIELD_SENDER_EMAIL = "sender_email"
        private const val FIELD_SENDER_PASSWORD = "sender_password"
        private const val FIELD_USE_SSL = "use_ssl"
        private const val FIELD_USE_AUTHENTICATION = "use_authentication"
        private const val FIELD_SUPPORT_START_TLS = "support_start_tls"
        private const val FIELD_NOTIFY_ON_COMPLETION = "notify_on_completion"
        private const val FIELD_NOTIFY_ON_FAILURE = "notify_on_failure"
        private const val FIELD_IS_HTML_EMAIL = "is_html_email"
        private const val FIELD_USERNAME = "username"
        private const val FIELD_SELECTED_RINGTONE_URI = "selected_ringtone_uri"
        private const val FIELD_SELECTED_RINGTONE_NAME = "selected_ringtone_name"
    }

    override fun getTaskType() = "information"

    /**
     * 保存信息任务
     * 将InformationTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的信息任务
     * @return 操作是否成功
     */
    override fun save(task: InformationTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存信息任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存信息任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),

                // 短信相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_PHONE_NUMBER),
                    task.phoneNumber
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_MESSAGE_TEXT),
                    task.messageText
                ),
                saveEnum(generateKey(task.id, FIELD_SIM_CARD_SELECTION), task.simCardSelection),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DRAFT_ONLY),
                    task.draftOnly
                ),

                // 邮件相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EMAIL_RECIPIENT),
                    task.emailRecipient
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EMAIL_SUBJECT),
                    task.emailSubject
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SMTP_SERVER),
                    task.smtpServer
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SMTP_PORT),
                    task.smtpPort
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SENDER_EMAIL),
                    task.senderEmail
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SENDER_PASSWORD),
                    task.senderPassword
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_USE_SSL),
                    task.useSSL
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_USE_AUTHENTICATION),
                    task.useAuthentication
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SUPPORT_START_TLS),
                    task.supportStartTLS
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_NOTIFY_ON_COMPLETION),
                    task.notifyOnCompletion
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_NOTIFY_ON_FAILURE),
                    task.notifyOnFailure
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_IS_HTML_EMAIL),
                    task.isHtmlEmail
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_USERNAME),
                    task.username
                ),

                // 铃声相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_URI),
                    task.selectedRingtoneUri
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_NAME),
                    task.selectedRingtoneName
                )
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "信息任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载信息任务
     * 从原生数据类型重建InformationTask对象
     *
     * @param taskId 任务ID
     * @return 加载的信息任务，失败时返回null
     */
    override fun load(taskId: String): InformationTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载信息任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "信息任务不存在: $taskId")
                return null
            }

            InformationTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { InformationOperation.valueOf(it) }
                    ?: InformationOperation.SEND_SMS,

                // 短信相关字段
                phoneNumber = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_PHONE_NUMBER),
                    ""
                ),
                messageText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_MESSAGE_TEXT),
                    ""
                ),
                simCardSelection = loadEnum(generateKey(taskId, FIELD_SIM_CARD_SELECTION)) { SimCardSelection.valueOf(it) }
                    ?: SimCardSelection.ASK_EACH_TIME,
                draftOnly = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DRAFT_ONLY),
                    false
                ),

                // 邮件相关字段
                emailRecipient = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EMAIL_RECIPIENT),
                    ""
                ),
                emailSubject = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EMAIL_SUBJECT),
                    ""
                ),
                smtpServer = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SMTP_SERVER),
                    ""
                ),
                smtpPort = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SMTP_PORT),
                    587
                ),
                senderEmail = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SENDER_EMAIL),
                    ""
                ),
                senderPassword = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SENDER_PASSWORD),
                    ""
                ),
                useSSL = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_USE_SSL),
                    false
                ),
                useAuthentication = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_USE_AUTHENTICATION),
                    true
                ),
                supportStartTLS = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SUPPORT_START_TLS),
                    true
                ),
                notifyOnCompletion = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFY_ON_COMPLETION),
                    false
                ),
                notifyOnFailure = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NOTIFY_ON_FAILURE),
                    true
                ),
                isHtmlEmail = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_IS_HTML_EMAIL),
                    false
                ),
                username = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_USERNAME),
                    ""
                ),

                // 铃声相关字段
                selectedRingtoneUri = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_URI),
                    ""
                ),
                selectedRingtoneName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_NAME),
                    ""
                )
            ).also {
                Log.d(TAG, "信息任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }
}

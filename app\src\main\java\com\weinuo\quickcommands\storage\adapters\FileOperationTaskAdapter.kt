package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation

/**
 * 文件操作任务存储适配器
 *
 * 负责FileOperationTask的原生数据类型存储和重建。
 * 将复杂的文件操作任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 文件路径相关：folderPath、fileName、sourcePath、targetPath
 * - 写入文件相关：fileContent、writeMode
 * - 打开文件相关：appName
 * - 文件操作相关：fileOperationType、sourceSelectionMode、targetSelectionMode、specificFilePattern
 * - 压缩相关：compressionLocation、customCompressionPath、deleteOriginalAfterCompression、compressionFormat、compressionLevel
 * - 重命名相关：newFileName、newFileExtension、allowOverwrite
 * - 新建文件夹相关：newFolderName、skipIfFolderExists
 *
 * 存储格式示例：
 * task_{id}_type = "file_operation"
 * task_{id}_operation = "WRITE_FILE"
 * task_{id}_folder_path = "/storage/emulated/0/Documents"
 * task_{id}_file_name = "test.txt"
 * task_{id}_file_content = "Hello World"
 * task_{id}_write_mode = "APPEND"
 * task_{id}_file_operation_type = "COPY"
 * task_{id}_compression_format = "ZIP"
 * task_{id}_compression_level = "NORMAL"
 *
 * @param storageManager 原生类型存储管理器
 */
class FileOperationTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<FileOperationTask>(storageManager) {

    companion object {
        private const val TAG = "FileOperationTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_FOLDER_PATH = "folder_path"
        private const val FIELD_FILE_NAME = "file_name"
        private const val FIELD_FILE_CONTENT = "file_content"
        private const val FIELD_WRITE_MODE = "write_mode"
        private const val FIELD_APP_NAME = "app_name"
        private const val FIELD_FILE_OPERATION_TYPE = "file_operation_type"
        private const val FIELD_SOURCE_SELECTION_MODE = "source_selection_mode"
        private const val FIELD_TARGET_SELECTION_MODE = "target_selection_mode"
        private const val FIELD_SPECIFIC_FILE_PATTERN = "specific_file_pattern"
        private const val FIELD_SOURCE_PATH = "source_path"
        private const val FIELD_TARGET_PATH = "target_path"
        private const val FIELD_COMPRESSION_LOCATION = "compression_location"
        private const val FIELD_CUSTOM_COMPRESSION_PATH = "custom_compression_path"
        private const val FIELD_DELETE_ORIGINAL_AFTER_COMPRESSION = "delete_original_after_compression"
        private const val FIELD_NEW_FILE_NAME = "new_file_name"
        private const val FIELD_NEW_FILE_EXTENSION = "new_file_extension"
        private const val FIELD_ALLOW_OVERWRITE = "allow_overwrite"
        private const val FIELD_NEW_FOLDER_NAME = "new_folder_name"
        private const val FIELD_SKIP_IF_FOLDER_EXISTS = "skip_if_folder_exists"
        private const val FIELD_COMPRESSION_FORMAT = "compression_format"
        private const val FIELD_COMPRESSION_LEVEL = "compression_level"
    }

    override fun getTaskType() = "file_operation"

    /**
     * 保存文件操作任务
     * 将FileOperationTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的文件操作任务
     * @return 操作是否成功
     */
    override fun save(task: FileOperationTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存文件操作任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存文件操作任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),

                // 文件路径相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_FOLDER_PATH),
                    task.folderPath
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_FILE_NAME),
                    task.fileName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SOURCE_PATH),
                    task.sourcePath
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TARGET_PATH),
                    task.targetPath
                ),

                // 写入文件相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_FILE_CONTENT),
                    task.fileContent
                ),
                saveEnum(generateKey(task.id, FIELD_WRITE_MODE), task.writeMode),

                // 打开文件相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_APP_NAME),
                    task.appName
                ),

                // 文件操作相关字段
                saveEnum(generateKey(task.id, FIELD_FILE_OPERATION_TYPE), task.fileOperationType),
                saveEnum(generateKey(task.id, FIELD_SOURCE_SELECTION_MODE), task.sourceSelectionMode),
                saveEnum(generateKey(task.id, FIELD_TARGET_SELECTION_MODE), task.targetSelectionMode),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SPECIFIC_FILE_PATTERN),
                    task.specificFilePattern
                ),

                // 压缩相关字段
                saveEnum(generateKey(task.id, FIELD_COMPRESSION_LOCATION), task.compressionLocation),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_COMPRESSION_PATH),
                    task.customCompressionPath
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DELETE_ORIGINAL_AFTER_COMPRESSION),
                    task.deleteOriginalAfterCompression
                ),
                saveEnum(generateKey(task.id, FIELD_COMPRESSION_FORMAT), task.compressionFormat),
                saveEnum(generateKey(task.id, FIELD_COMPRESSION_LEVEL), task.compressionLevel),

                // 重命名相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_NEW_FILE_NAME),
                    task.newFileName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_NEW_FILE_EXTENSION),
                    task.newFileExtension
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALLOW_OVERWRITE),
                    task.allowOverwrite
                ),

                // 新建文件夹相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_NEW_FOLDER_NAME),
                    task.newFolderName
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SKIP_IF_FOLDER_EXISTS),
                    task.skipIfFolderExists
                )
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "文件操作任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载文件操作任务
     * 从原生数据类型重建FileOperationTask对象
     *
     * @param taskId 任务ID
     * @return 加载的文件操作任务，失败时返回null
     */
    override fun load(taskId: String): FileOperationTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载文件操作任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "文件操作任务不存在: $taskId")
                return null
            }

            FileOperationTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { FileOperation.valueOf(it) }
                    ?: FileOperation.WRITE_FILE,

                // 文件路径相关字段
                folderPath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_FOLDER_PATH),
                    ""
                ),
                fileName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_FILE_NAME),
                    ""
                ),
                sourcePath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SOURCE_PATH),
                    ""
                ),
                targetPath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TARGET_PATH),
                    ""
                ),

                // 写入文件相关字段
                fileContent = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_FILE_CONTENT),
                    ""
                ),
                writeMode = loadEnum(generateKey(taskId, FIELD_WRITE_MODE)) { FileWriteMode.valueOf(it) }
                    ?: FileWriteMode.APPEND,

                // 打开文件相关字段
                appName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_APP_NAME),
                    ""
                ),

                // 文件操作相关字段
                fileOperationType = loadEnum(generateKey(taskId, FIELD_FILE_OPERATION_TYPE)) { FileOperationType.valueOf(it) }
                    ?: FileOperationType.COPY,
                sourceSelectionMode = loadEnum(generateKey(taskId, FIELD_SOURCE_SELECTION_MODE)) { FileSelectionMode.valueOf(it) }
                    ?: FileSelectionMode.ALL_FILES,
                targetSelectionMode = loadEnum(generateKey(taskId, FIELD_TARGET_SELECTION_MODE)) { FileSelectionMode.valueOf(it) }
                    ?: FileSelectionMode.ALL_FILES,
                specificFilePattern = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SPECIFIC_FILE_PATTERN),
                    ""
                ),

                // 压缩相关字段
                compressionLocation = loadEnum(generateKey(taskId, FIELD_COMPRESSION_LOCATION)) { CompressionLocation.valueOf(it) }
                    ?: CompressionLocation.CURRENT_LOCATION,
                customCompressionPath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_COMPRESSION_PATH),
                    ""
                ),
                deleteOriginalAfterCompression = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DELETE_ORIGINAL_AFTER_COMPRESSION),
                    false
                ),
                compressionFormat = loadEnum(generateKey(taskId, FIELD_COMPRESSION_FORMAT)) { CompressionFormat.valueOf(it) }
                    ?: CompressionFormat.ZIP,
                compressionLevel = loadEnum(generateKey(taskId, FIELD_COMPRESSION_LEVEL)) { CompressionLevel.valueOf(it) }
                    ?: CompressionLevel.NORMAL,

                // 重命名相关字段
                newFileName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NEW_FILE_NAME),
                    ""
                ),
                newFileExtension = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NEW_FILE_EXTENSION),
                    ""
                ),
                allowOverwrite = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALLOW_OVERWRITE),
                    false
                ),

                // 新建文件夹相关字段
                newFolderName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_NEW_FOLDER_NAME),
                    ""
                ),
                skipIfFolderExists = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SKIP_IF_FOLDER_EXISTS),
                    false
                )
            ).also {
                Log.d(TAG, "文件操作任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }
}

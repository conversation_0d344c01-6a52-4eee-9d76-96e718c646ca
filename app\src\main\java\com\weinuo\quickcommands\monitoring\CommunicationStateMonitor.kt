package com.weinuo.quickcommands.monitoring

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.database.ContentObserver
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.CallLog
import android.provider.ContactsContract
import android.provider.Telephony
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.utils.ContactsHelper
import com.weinuo.quickcommands.utils.CommunicationPermissionUtil
import kotlinx.coroutines.*

/**
 * 通信状态监听器
 * 监听通话和短信状态变化，触发相应的条件
 *
 * 支持的监听功能：
 * - 通话状态变化（拨出、接听、结束、未接等）
 * - 短信发送和接收
 * - 联系人筛选和分组过滤
 */
class CommunicationStateMonitor(
    private val context: Context,
    private val onConditionTriggered: (CommunicationStateCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "CommunicationStateMonitor"
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<CommunicationStateCondition>()

    // 广播接收器
    private var smsReceiver: BroadcastReceiver? = null
    private var callReceiver: BroadcastReceiver? = null

    // 内容观察器
    private var callLogObserver: ContentObserver? = null
    private var smsObserver: ContentObserver? = null

    // 电话状态监听器
    private var phoneStateListener: PhoneStateListener? = null
    private var telephonyManager: TelephonyManager? = null

    // 通话状态跟踪
    private var lastCallState = TelephonyManager.CALL_STATE_IDLE
    private var lastIncomingNumber: String? = null

    // 短信状态跟踪
    private var lastSmsCount = 0
    private var lastCallLogCount = 0

    /**
     * 开始监听通信状态
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Communication monitoring already started")
            return
        }

        // 检查权限
        if (!CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
            Log.w(TAG, "Communication permissions not granted, cannot start monitoring")
            return
        }

        try {
            setupSmsMonitoring()
            setupCallMonitoring()
            setupCallLogMonitoring()

            isMonitoring = true
            Log.d(TAG, "Communication monitoring started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting communication monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监听通信状态
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            return
        }

        try {
            // 注销广播接收器
            smsReceiver?.let { context.unregisterReceiver(it) }
            callReceiver?.let { context.unregisterReceiver(it) }

            // 注销内容观察器
            callLogObserver?.let { context.contentResolver.unregisterContentObserver(it) }
            smsObserver?.let { context.contentResolver.unregisterContentObserver(it) }

            // 注销电话状态监听器
            phoneStateListener?.let { telephonyManager?.listen(it, PhoneStateListener.LISTEN_NONE) }

            // 清理资源
            smsReceiver = null
            callReceiver = null
            callLogObserver = null
            smsObserver = null
            phoneStateListener = null
            telephonyManager = null

            isMonitoring = false
            Log.d(TAG, "Communication monitoring stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping communication monitoring", e)
        }
    }

    /**
     * 注册通信状态条件
     */
    fun registerCondition(condition: CommunicationStateCondition) {
        if (!registeredConditions.any { it.id == condition.id }) {
            registeredConditions.add(condition)
            Log.d(TAG, "Registered communication condition: ${condition.getDescription()}")
        }
    }

    /**
     * 注销通信状态条件
     */
    fun unregisterCondition(conditionId: String) {
        registeredConditions.removeAll { it.id == conditionId }
        Log.d(TAG, "Unregistered communication condition: $conditionId")
    }

    /**
     * 清除所有注册的条件
     */
    fun clearConditions() {
        registeredConditions.clear()
        Log.d(TAG, "Cleared all communication conditions")
    }

    /**
     * 设置短信监听
     */
    private fun setupSmsMonitoring() {
        // 监听短信接收
        smsReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == Telephony.Sms.Intents.SMS_RECEIVED_ACTION) {
                    handleSmsReceived(intent)
                }
            }
        }

        val smsFilter = IntentFilter(Telephony.Sms.Intents.SMS_RECEIVED_ACTION)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(smsReceiver, smsFilter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            context.registerReceiver(smsReceiver, smsFilter)
        }

        // 监听短信发送（通过内容观察器）
        smsObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                super.onChange(selfChange, uri)
                handleSmsContentChange(uri)
            }
        }

        context.contentResolver.registerContentObserver(
            Telephony.Sms.CONTENT_URI,
            true,
            smsObserver!!
        )

        Log.d(TAG, "SMS monitoring setup completed")
    }

    /**
     * 设置通话监听
     */
    private fun setupCallMonitoring() {
        telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

        phoneStateListener = object : PhoneStateListener() {
            override fun onCallStateChanged(state: Int, phoneNumber: String?) {
                super.onCallStateChanged(state, phoneNumber)
                handleCallStateChanged(state, phoneNumber)
            }
        }

        telephonyManager?.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE)
        Log.d(TAG, "Call state monitoring setup completed")
    }

    /**
     * 设置通话记录监听
     */
    private fun setupCallLogMonitoring() {
        callLogObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                super.onChange(selfChange, uri)
                handleCallLogChange(uri)
            }
        }

        context.contentResolver.registerContentObserver(
            CallLog.Calls.CONTENT_URI,
            true,
            callLogObserver!!
        )
    }

    /**
     * 处理短信接收事件
     */
    private fun handleSmsReceived(intent: Intent) {
        monitorScope.launch {
            try {
                val messages = Telephony.Sms.Intents.getMessagesFromIntent(intent)
                if (messages.isNullOrEmpty()) return@launch

                for (message in messages) {
                    val phoneNumber = message.originatingAddress ?: continue
                    val messageBody = message.messageBody ?: ""

                    Log.d(TAG, "SMS received from: $phoneNumber")

                    // 检查匹配的条件
                    val matchingConditions = registeredConditions.filter { condition ->
                        condition.stateType == CommunicationStateType.SMS_RECEIVED &&
                        isPhoneNumberMatching(condition, phoneNumber)
                    }

                    // 触发匹配的条件
                    for (condition in matchingConditions) {
                        val eventData = mapOf(
                            "phoneNumber" to phoneNumber,
                            "messageBody" to messageBody,
                            "timestamp" to System.currentTimeMillis()
                        )
                        onConditionTriggered(condition, eventData)
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error handling SMS received", e)
            }
        }
    }

    /**
     * 处理短信内容变化（用于检测发送的短信）
     */
    private fun handleSmsContentChange(uri: Uri?) {
        monitorScope.launch {
            try {
                Log.d(TAG, "SMS content changed: $uri")

                // 查询最新的短信记录
                val cursor = context.contentResolver.query(
                    Telephony.Sms.CONTENT_URI,
                    arrayOf(
                        Telephony.Sms._ID,
                        Telephony.Sms.ADDRESS,
                        Telephony.Sms.BODY,
                        Telephony.Sms.TYPE,
                        Telephony.Sms.DATE
                    ),
                    null,
                    null,
                    "${Telephony.Sms.DATE} DESC LIMIT 1"
                )

                cursor?.use { c ->
                    if (c.moveToFirst()) {
                        val id = c.getLong(c.getColumnIndexOrThrow(Telephony.Sms._ID))
                        val address = c.getString(c.getColumnIndexOrThrow(Telephony.Sms.ADDRESS)) ?: ""
                        val body = c.getString(c.getColumnIndexOrThrow(Telephony.Sms.BODY)) ?: ""
                        val type = c.getInt(c.getColumnIndexOrThrow(Telephony.Sms.TYPE))
                        val date = c.getLong(c.getColumnIndexOrThrow(Telephony.Sms.DATE))

                        // 检查是否为发送的短信（TYPE_SENT = 2）
                        if (type == Telephony.Sms.MESSAGE_TYPE_SENT) {
                            // 检查是否为新的短信（避免重复处理）
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - date < 5000) { // 5秒内的短信认为是新发送的
                                Log.d(TAG, "SMS sent to: $address")
                                handleSmsSent(address, body, date)
                            }
                        }
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error handling SMS content change", e)
            }
        }
    }

    /**
     * 处理短信发送事件
     */
    private suspend fun handleSmsSent(phoneNumber: String, messageBody: String, timestamp: Long) {
        Log.d(TAG, "SMS sent to: $phoneNumber")

        // 检查匹配的条件
        val matchingConditions = registeredConditions.filter { condition ->
            condition.stateType == CommunicationStateType.SMS_SENT &&
            isPhoneNumberMatching(condition, phoneNumber)
        }

        // 触发匹配的条件
        for (condition in matchingConditions) {
            val eventData = mapOf(
                "phoneNumber" to phoneNumber,
                "messageBody" to messageBody,
                "timestamp" to timestamp
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理通话状态变化
     */
    private fun handleCallStateChanged(state: Int, phoneNumber: String?) {
        monitorScope.launch {
            try {
                Log.d(TAG, "Call state changed: $lastCallState -> $state, number: $phoneNumber")

                when (state) {
                    TelephonyManager.CALL_STATE_RINGING -> {
                        // 来电响铃
                        lastIncomingNumber = phoneNumber
                        handleIncomingCall(phoneNumber)
                    }
                    TelephonyManager.CALL_STATE_OFFHOOK -> {
                        // 通话中
                        if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                            // 从响铃到接听
                            handleCallAnswered(lastIncomingNumber)
                        } else if (lastCallState == TelephonyManager.CALL_STATE_IDLE) {
                            // 从空闲到通话中（拨出电话）
                            handleOutgoingCall(phoneNumber)
                        }
                        handleCallActive()
                    }
                    TelephonyManager.CALL_STATE_IDLE -> {
                        // 通话结束
                        if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                            // 从响铃到空闲（未接来电）
                            handleMissedCall(lastIncomingNumber)
                        } else if (lastCallState == TelephonyManager.CALL_STATE_OFFHOOK) {
                            // 从通话中到空闲（通话结束）
                            handleCallEnded(lastIncomingNumber ?: phoneNumber)
                        }
                        lastIncomingNumber = null
                    }
                }

                lastCallState = state

            } catch (e: Exception) {
                Log.e(TAG, "Error handling call state change", e)
            }
        }
    }

    /**
     * 处理通话记录变化
     */
    private fun handleCallLogChange(uri: Uri?) {
        monitorScope.launch {
            try {
                Log.d(TAG, "Call log changed: $uri")

                // 查询最新的通话记录
                val cursor = context.contentResolver.query(
                    CallLog.Calls.CONTENT_URI,
                    arrayOf(
                        CallLog.Calls._ID,
                        CallLog.Calls.NUMBER,
                        CallLog.Calls.TYPE,
                        CallLog.Calls.DATE,
                        CallLog.Calls.DURATION
                    ),
                    null,
                    null,
                    "${CallLog.Calls.DATE} DESC LIMIT 1"
                )

                cursor?.use { c ->
                    if (c.moveToFirst()) {
                        val id = c.getLong(c.getColumnIndexOrThrow(CallLog.Calls._ID))
                        val number = c.getString(c.getColumnIndexOrThrow(CallLog.Calls.NUMBER)) ?: ""
                        val type = c.getInt(c.getColumnIndexOrThrow(CallLog.Calls.TYPE))
                        val date = c.getLong(c.getColumnIndexOrThrow(CallLog.Calls.DATE))
                        val duration = c.getLong(c.getColumnIndexOrThrow(CallLog.Calls.DURATION))

                        // 检查是否为新的通话记录（避免重复处理）
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - date < 10000) { // 10秒内的记录认为是新的
                            when (type) {
                                CallLog.Calls.MISSED_TYPE -> {
                                    Log.d(TAG, "Missed call detected in call log: $number")
                                    handleMissedCallFromLog(number, date, duration)
                                }
                                CallLog.Calls.INCOMING_TYPE -> {
                                    Log.d(TAG, "Incoming call completed in call log: $number")
                                    // 可以用于检测接听的来电
                                }
                                CallLog.Calls.OUTGOING_TYPE -> {
                                    Log.d(TAG, "Outgoing call completed in call log: $number")
                                    // 可以用于检测拨出电话的完成
                                }
                            }
                        }
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error handling call log change", e)
            }
        }
    }

    /**
     * 处理从通话记录检测到的未接来电
     */
    private suspend fun handleMissedCallFromLog(phoneNumber: String, timestamp: Long, duration: Long) {
        Log.d(TAG, "Missed call from log: $phoneNumber")

        // 检查匹配的条件
        val matchingConditions = registeredConditions.filter { condition ->
            condition.stateType == CommunicationStateType.MISSED_CALL &&
            isPhoneNumberMatching(condition, phoneNumber)
        }

        // 触发匹配的条件
        for (condition in matchingConditions) {
            val eventData = mapOf(
                "phoneNumber" to phoneNumber,
                "timestamp" to timestamp,
                "duration" to duration,
                "callType" to "missed",
                "source" to "call_log"
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理来电事件
     */
    private suspend fun handleIncomingCall(phoneNumber: String?) {
        if (phoneNumber == null) return

        Log.d(TAG, "Incoming call from: $phoneNumber")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.stateType == CommunicationStateType.INCOMING_CALL &&
            isPhoneNumberMatching(condition, phoneNumber)
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "phoneNumber" to phoneNumber,
                "timestamp" to System.currentTimeMillis(),
                "callType" to "incoming"
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理拨出电话事件
     */
    private suspend fun handleOutgoingCall(phoneNumber: String?) {
        if (phoneNumber == null) return

        Log.d(TAG, "Outgoing call to: $phoneNumber")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.stateType == CommunicationStateType.OUTGOING_CALL &&
            isPhoneNumberMatching(condition, phoneNumber)
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "phoneNumber" to phoneNumber,
                "timestamp" to System.currentTimeMillis(),
                "callType" to "outgoing"
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理通话接听事件
     */
    private suspend fun handleCallAnswered(phoneNumber: String?) {
        if (phoneNumber == null) return

        Log.d(TAG, "Call answered: $phoneNumber")

        // 这里可以触发特定的"接听电话"条件
        // 目前暂时不实现，因为INCOMING_CALL已经涵盖了来电检测
    }

    /**
     * 处理通话中状态
     */
    private suspend fun handleCallActive() {
        Log.d(TAG, "Call is active")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.stateType == CommunicationStateType.CALL_ACTIVE
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "timestamp" to System.currentTimeMillis(),
                "callState" to "active"
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理通话结束事件
     */
    private suspend fun handleCallEnded(phoneNumber: String?) {
        Log.d(TAG, "Call ended: $phoneNumber")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.stateType == CommunicationStateType.CALL_ENDED &&
            (phoneNumber == null || isPhoneNumberMatching(condition, phoneNumber))
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "phoneNumber" to (phoneNumber ?: ""),
                "timestamp" to System.currentTimeMillis(),
                "callType" to "ended"
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理未接来电事件
     */
    private suspend fun handleMissedCall(phoneNumber: String?) {
        if (phoneNumber == null) return

        Log.d(TAG, "Missed call from: $phoneNumber")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.stateType == CommunicationStateType.MISSED_CALL &&
            isPhoneNumberMatching(condition, phoneNumber)
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "phoneNumber" to phoneNumber,
                "timestamp" to System.currentTimeMillis(),
                "callType" to "missed"
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 检查电话号码是否匹配条件
     */
    private suspend fun isPhoneNumberMatching(condition: CommunicationStateCondition, phoneNumber: String): Boolean {
        return try {
            when (condition.filterType) {
                ContactFilterType.ANY_CONTACT -> {
                    // 任何联系人：检查是否在联系人中
                    val contact = ContactsHelper.getContactByNumber(context, phoneNumber)
                    val isContact = contact != null
                    applyFilterMode(condition.filterMode, isContact)
                }
                ContactFilterType.NO_CONTACT -> {
                    // 陌生号码：检查是否不在联系人中
                    val contact = ContactsHelper.getContactByNumber(context, phoneNumber)
                    val isStranger = contact == null
                    applyFilterMode(condition.filterMode, isStranger)
                }
                ContactFilterType.SPECIFIC_CONTACTS -> {
                    // 指定联系人：检查是否在指定联系人列表中
                    val contact = ContactsHelper.getContactByNumber(context, phoneNumber)
                    val isSpecificContact = contact != null && condition.selectedContactIds.contains(contact.id)
                    applyFilterMode(condition.filterMode, isSpecificContact)
                }
                ContactFilterType.CONTACT_GROUP -> {
                    // 联系人分组：检查联系人是否属于指定分组
                    val contact = ContactsHelper.getContactByNumber(context, phoneNumber)
                    if (contact != null && condition.selectedGroupId.isNotEmpty()) {
                        val isInGroup = isContactInGroup(contact.id, listOf(condition.selectedGroupId))
                        applyFilterMode(condition.filterMode, isInGroup)
                    } else {
                        false
                    }
                }
                ContactFilterType.ANY_NUMBER -> {
                    // 任何号码：总是匹配
                    true
                }
                ContactFilterType.SPECIFIC_NUMBER -> {
                    // 指定号码：检查是否匹配指定号码
                    val isSpecificNumber = ContactsHelper.phoneNumbersMatch(phoneNumber, condition.specificNumber)
                    applyFilterMode(condition.filterMode, isSpecificNumber)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking phone number matching", e)
            false
        }
    }

    /**
     * 应用筛选模式（包含/排除）
     */
    private fun applyFilterMode(filterMode: ContactFilterMode, isMatch: Boolean): Boolean {
        return when (filterMode) {
            ContactFilterMode.INCLUDE -> isMatch
            ContactFilterMode.EXCLUDE -> !isMatch
        }
    }

    /**
     * 检查联系人是否属于指定分组
     */
    private suspend fun isContactInGroup(contactId: String, groupIds: List<String>): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (groupIds.isEmpty()) return@withContext false

                // 查询联系人的分组信息
                val cursor = context.contentResolver.query(
                    ContactsContract.Data.CONTENT_URI,
                    arrayOf(ContactsContract.CommonDataKinds.GroupMembership.GROUP_ROW_ID),
                    "${ContactsContract.Data.CONTACT_ID} = ? AND ${ContactsContract.Data.MIMETYPE} = ?",
                    arrayOf(contactId, ContactsContract.CommonDataKinds.GroupMembership.CONTENT_ITEM_TYPE),
                    null
                )

                cursor?.use { c ->
                    while (c.moveToNext()) {
                        val groupRowId = c.getString(c.getColumnIndexOrThrow(
                            ContactsContract.CommonDataKinds.GroupMembership.GROUP_ROW_ID
                        ))

                        if (groupIds.contains(groupRowId)) {
                            return@withContext true
                        }
                    }
                }

                false
            } catch (e: Exception) {
                Log.e(TAG, "Error checking contact group membership", e)
                false
            }
        }
    }

    /**
     * 获取监听状态
     */
    fun isMonitoring(): Boolean = isMonitoring

    /**
     * 获取注册的条件数量
     */
    fun getRegisteredConditionCount(): Int = registeredConditions.size

    /**
     * 清理资源
     */
    fun cleanup() {
        stopMonitoring()
        monitorScope.cancel()
        registeredConditions.clear()
        Log.d(TAG, "Communication monitor cleaned up")
    }
}

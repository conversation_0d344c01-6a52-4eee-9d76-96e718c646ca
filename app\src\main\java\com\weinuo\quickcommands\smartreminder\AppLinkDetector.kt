package com.weinuo.quickcommands.smartreminder

import android.util.Log

/**
 * 应用链接检测器
 *
 * 负责检测剪贴板中的应用链接，识别对应的应用平台。
 * 支持主流应用平台的链接检测，包括：
 * - 抖音
 * - 快手
 * - 小红书
 * - 百度网盘
 * - 夸克网盘
 *
 * 设计特点：
 * - 高准确性：使用正则表达式精确匹配
 * - 高性能：优化的检测算法
 * - 易扩展：便于添加新的应用平台
 * - 容错性：处理各种链接格式变体
 * - 智能链接识别：自动清理干扰字符，还原真实链接
 * - 遵循现有架构：与购物应用提醒保持一致的设计模式
 */
object AppLinkDetector {

    private const val TAG = "AppLinkDetector"

    /**
     * 应用平台信息
     */
    data class AppPlatform(
        val name: String,
        val packageName: String,
        val urlPatterns: List<String>
    )

    /**
     * 检测结果
     */
    data class DetectionResult(
        val isAppLink: Boolean,
        val platform: AppPlatform? = null,
        val originalUrl: String = "",
        val cleanedUrl: String = "" // 智能识别后的清理链接
    )

    /**
     * 智能链接识别结果
     */
    data class IntelligentRecognitionResult(
        val originalText: String,
        val cleanedText: String,
        val appliedRules: List<String>,
        val confidence: Float
    )

    /**
     * 支持的应用平台配置
     */
    private val appPlatforms = listOf(
        AppPlatform(
            name = "抖音",
            packageName = "com.ss.android.ugc.aweme",
            urlPatterns = listOf(
                ".*douyin\\.com.*",
                ".*v\\.douyin\\.com.*",
                ".*iesdouyin\\.com.*",
                ".*dy\\.163\\.com.*",
                ".*share\\.douyin\\.com.*"
            )
        ),
        AppPlatform(
            name = "快手",
            packageName = "com.smile.gifmaker",
            urlPatterns = listOf(
                ".*kuaishou\\.com.*",
                ".*v\\.kuaishou\\.com.*",
                ".*kwai\\.com.*",
                ".*ks\\.cn.*",
                ".*share\\.kuaishou\\.com.*"
            )
        ),
        AppPlatform(
            name = "小红书",
            packageName = "com.xingin.xhs",
            urlPatterns = listOf(
                ".*xiaohongshu\\.com.*",
                ".*xhslink\\.com.*",
                ".*xhs\\.cn.*",
                ".*share\\.xiaohongshu\\.com.*"
            )
        ),
        AppPlatform(
            name = "百度网盘",
            packageName = "com.baidu.netdisk",
            urlPatterns = listOf(
                ".*pan\\.baidu\\.com.*",
                ".*yun\\.baidu\\.com.*",
                ".*share\\.baidu\\.com.*",
                ".*bdurl\\.to.*"
            )
        ),
        AppPlatform(
            name = "夸克网盘",
            packageName = "com.quark.browser",
            urlPatterns = listOf(
                ".*pan\\.quark\\.cn.*",
                ".*drive\\.quark\\.cn.*",
                ".*share\\.quark\\.cn.*",
                ".*quark\\.cn/s/.*"
            )
        )
    )

    /**
     * 检测文本中是否包含应用链接
     *
     * @param text 要检测的文本内容
     * @param enabledPlatforms 启用的平台列表，默认启用所有平台
     * @param customPlatforms 自定义平台列表
     * @param intelligentRecognitionEnabled 是否启用智能链接识别
     * @return 检测结果，包含是否为应用链接和对应平台信息
     */
    fun detectAppLink(
        text: String,
        enabledPlatforms: Set<String> = setOf("抖音", "快手", "小红书", "百度网盘", "夸克网盘"),
        customPlatforms: List<com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.CustomAppPlatform> = emptyList(),
        intelligentRecognitionEnabled: Boolean = true
    ): DetectionResult {
        if (text.isBlank()) {
            return DetectionResult(false)
        }

        try {
            Log.d(TAG, "Detecting app link in text: ${text.take(100)}...")

            // 预处理文本，移除多余的空白字符
            var cleanText = text.trim()

            // 应用智能链接识别
            if (intelligentRecognitionEnabled) {
                val recognitionResult = performIntelligentRecognition(cleanText)
                cleanText = recognitionResult.cleanedText
                Log.d(TAG, "Intelligent recognition applied. Original: ${text.take(50)}, Cleaned: ${cleanText.take(50)}")
                if (recognitionResult.appliedRules.isNotEmpty()) {
                    Log.d(TAG, "Applied rules: ${recognitionResult.appliedRules.joinToString(", ")}")
                    Log.d(TAG, "Confidence: ${recognitionResult.confidence}")
                }
            }

            // 检查是否包含URL
            if (!containsUrl(cleanText)) {
                Log.d(TAG, "No URL found in text")
                return DetectionResult(false, originalUrl = text, cleanedUrl = cleanText)
            }

            // 检查启用的内置平台
            for (platform in appPlatforms) {
                if (platform.name in enabledPlatforms && matchesPlatform(cleanText, platform)) {
                    Log.d(TAG, "Detected ${platform.name} app link")
                    return DetectionResult(
                        isAppLink = true,
                        platform = platform,
                        originalUrl = text,
                        cleanedUrl = cleanText
                    )
                }
            }

            // 检查自定义平台
            for (customPlatform in customPlatforms) {
                if (matchesCustomPlatform(cleanText, customPlatform)) {
                    Log.d(TAG, "Detected custom platform ${customPlatform.name} app link")
                    // 将自定义平台转换为AppPlatform格式
                    val platform = AppPlatform(
                        name = customPlatform.name,
                        packageName = customPlatform.packageName,
                        urlPatterns = customPlatform.urlPatterns
                    )
                    return DetectionResult(
                        isAppLink = true,
                        platform = platform,
                        originalUrl = text,
                        cleanedUrl = cleanText
                    )
                }
            }

            Log.d(TAG, "No app platform matched")
            return DetectionResult(false, originalUrl = text, cleanedUrl = cleanText)

        } catch (e: Exception) {
            Log.e(TAG, "Error detecting app link", e)
            return DetectionResult(false)
        }
    }

    /**
     * 执行智能链接识别，标准化链接格式
     */
    private fun performIntelligentRecognition(text: String): IntelligentRecognitionResult {
        var cleanedText = text
        val appliedRules = mutableListOf<String>()

        try {
            // 1. 协议部分清理
            val protocolResult = cleanProtocolInterference(cleanedText)
            if (protocolResult != cleanedText) {
                cleanedText = protocolResult
                appliedRules.add("协议清理")
            }

            // 2. 域名分隔符清理
            val domainResult = cleanDomainSeparators(cleanedText)
            if (domainResult != cleanedText) {
                cleanedText = domainResult
                appliedRules.add("域名分隔符清理")
            }

            // 3. 移除干扰词汇
            val interferenceResult = removeInterferenceWords(cleanedText)
            if (interferenceResult != cleanedText) {
                cleanedText = interferenceResult
                appliedRules.add("内容优化")
            }

            // 4. 移除表情符号和特殊字符
            val emojiResult = removeEmojisAndSpecialChars(cleanedText)
            if (emojiResult != cleanedText) {
                cleanedText = emojiResult
                appliedRules.add("字符标准化")
            }

            // 5. 移除方括号内容
            val bracketResult = removeBracketContent(cleanedText)
            if (bracketResult != cleanedText) {
                cleanedText = bracketResult
                appliedRules.add("格式清理")
            }

            // 6. 最终中文字符清理
            val chineseResult = removeAllChineseCharacters(cleanedText)
            if (chineseResult != cleanedText) {
                cleanedText = chineseResult
                appliedRules.add("中文字符清理")
            }

            // 7. 标准化空白字符
            val whitespaceResult = normalizeWhitespace(cleanedText)
            if (whitespaceResult != cleanedText) {
                cleanedText = whitespaceResult
                appliedRules.add("空白字符标准化")
            }

            // 计算置信度
            val confidence = calculateCleaningConfidence(text, cleanedText)

            return IntelligentRecognitionResult(
                originalText = text,
                cleanedText = cleanedText,
                appliedRules = appliedRules,
                confidence = confidence
            )

        } catch (e: Exception) {
            Log.w(TAG, "Error in intelligent recognition", e)
            return IntelligentRecognitionResult(
                originalText = text,
                cleanedText = text,
                appliedRules = emptyList(),
                confidence = 0f
            )
        }
    }

    /**
     * 标准化协议格式 - 智能识别和修复协议部分
     */
    private fun cleanProtocolInterference(text: String): String {
        var result = text

        // 1. 处理空格分割的协议
        result = result.replace(Regex("h\\s*t\\s*t\\s*p\\s*s?\\s*:", RegexOption.IGNORE_CASE), "https:")

        // 2. 处理字符插入的协议 - 使用更智能的模式匹配
        // 匹配 h[任意字符]t[任意字符]t[任意字符]p[s?]://
        result = result.replace(Regex("h[^t]*t[^t]*t[^p]*ps?://", RegexOption.IGNORE_CASE), "https://")
        result = result.replace(Regex("h[^t]*t[^t]*t[^p]*p://", RegexOption.IGNORE_CASE), "http://")

        // 3. 处理协议中的重复字符
        result = result.replace(Regex("h+t+p+s?://", RegexOption.IGNORE_CASE)) { matchResult ->
            if (matchResult.value.contains("s", ignoreCase = true)) "https://" else "http://"
        }

        // 4. 处理协议的替代表达（中文词汇）
        result = result.replace(Regex("(网址|链接|地址)\\s*[:：]?\\s*//", RegexOption.IGNORE_CASE), "http://")

        // 5. 移除协议部分的中文字符
        result = result.replace(Regex("(h[^:/]*?)[\u4e00-\u9fff]+([^:/]*?://)", RegexOption.IGNORE_CASE)) { matchResult ->
            val prefix = matchResult.groupValues[1]
            val suffix = matchResult.groupValues[2]
            if (suffix.contains("s", ignoreCase = true)) "https://" else "http://"
        }

        // 6. 修复可能的协议格式错误
        result = result.replace(Regex("https?[^:/]{1,5}://", RegexOption.IGNORE_CASE)) { matchResult ->
            if (matchResult.value.lowercase().contains("https")) "https://" else "http://"
        }

        // 7. 处理协议前的无关字符（包括中文）
        result = result.replace(Regex("^[^h]*?(https?://)", RegexOption.IGNORE_CASE), "$1")

        return result
    }

    /**
     * 标准化域名分隔符 - 智能识别域名中的分隔符替代
     */
    private fun cleanDomainSeparators(text: String): String {
        var result = text

        // 1. 在域名部分智能替换点号，同时移除中文字符
        result = result.replace(Regex("(://[^/]*?)([点。·丶])", RegexOption.IGNORE_CASE)) { matchResult ->
            "${matchResult.groupValues[1]}."
        }

        // 1.1 移除域名部分的其他中文字符
        result = result.replace(Regex("(://[^/]*?)[\u4e00-\u9fff]+([^/]*?)")) { matchResult ->
            "${matchResult.groupValues[1]}${matchResult.groupValues[2]}"
        }

        // 2. 处理括号包围的分隔符
        result = result.replace(Regex("\\[([点。·丶:：/])\\]"), "$1")
        result = result.replace(Regex("\\(([点。·丶:：/])\\)"), "$1")
        result = result.replace(Regex("【([点。·丶:：/])】"), "$1")
        result = result.replace(Regex("\\{([点。·丶:：/])\\}"), "$1")

        // 3. 标准化常见的分隔符替代
        val separatorMap = mapOf(
            "点" to ".",
            "。" to ".",
            "·" to ".",
            "丶" to ".",
            "：" to ":",
            "冒号" to ":",
            "斜杠" to "/"
        )

        separatorMap.forEach { (from, to) ->
            result = result.replace(from, to)
        }

        // 4. 处理域名中不合理的字符替换（只在域名部分）
        result = result.replace(Regex("(://[^/]*?)([*_@#]+)([^/]*?)")) { matchResult ->
            val prefix = matchResult.groupValues[1]
            val suffix = matchResult.groupValues[3]
            "$prefix.$suffix"
        }

        // 5. 修复可能的双重分隔符
        result = result.replace(Regex("\\.{2,}"), ".")
        result = result.replace(Regex(":{2,}"), ":")
        result = result.replace(Regex("/{3,}"), "//")

        return result
    }

    /**
     * 优化文本内容 - 使用模式识别移除干扰内容
     */
    private fun removeInterferenceWords(text: String): String {
        var result = text

        // 1. 移除明显的提示性词汇
        val instructionPatterns = listOf(
            "删掉", "删除", "去掉", "移除", "拿掉", "干掉", "去除",
            "替换", "改成", "换成", "变成"
        )
        instructionPatterns.forEach { word ->
            result = result.replace(word, "")
        }

        // 2. 移除重复字符模式（如：aaa、111、黑黑黑等）
        result = result.replace(Regex("(.)\\1{2,}"), "$1")

        // 3. 移除常见的无意义填充字符
        result = result.replace(Regex("[xX]{2,}"), "")
        result = result.replace(Regex("[*]{2,}"), "")
        result = result.replace(Regex("[-]{3,}"), "")
        result = result.replace(Regex("[_]{3,}"), "")

        // 4. 移除URL中的所有中文字符
        // URL标准中不包含中文字符，全部移除以提高识别准确性
        result = result.replace(Regex("[\u4e00-\u9fff]+"), "")

        return result
    }

    /**
     * 标准化字符格式
     */
    private fun removeEmojisAndSpecialChars(text: String): String {
        // 移除表情符号 (Unicode范围)
        return text.replace(Regex("[\uD83C-\uDBFF\uDC00-\uDFFF]+"), "")
    }

    /**
     * 清理格式标记 - 智能识别和移除括号内的干扰内容
     */
    private fun removeBracketContent(text: String): String {
        var result = text

        // 1. 移除明显的提示性括号内容
        val hintPatterns = listOf(
            "\\[.*?删.*?\\]", "\\[.*?除.*?\\]", "\\[.*?去.*?\\]", "\\[.*?移.*?\\]",
            "【.*?删.*?】", "【.*?除.*?】", "【.*?去.*?】", "【.*?移.*?】",
            "\\(.*?删.*?\\)", "\\(.*?除.*?\\)", "\\(.*?去.*?\\)", "\\(.*?移.*?\\)"
        )

        hintPatterns.forEach { pattern ->
            result = result.replace(Regex(pattern), "")
        }

        // 2. 移除包含广告、提示等关键词的括号内容
        val adPatterns = listOf(
            "\\[.*?广告.*?\\]", "\\[.*?推广.*?\\]", "\\[.*?赞助.*?\\]",
            "【.*?广告.*?】", "【.*?推广.*?】", "【.*?赞助.*?】"
        )

        adPatterns.forEach { pattern ->
            result = result.replace(Regex(pattern), "")
        }

        // 3. 移除纯符号或表情的括号内容
        result = result.replace(Regex("\\[[^\\w\\u4e00-\\u9fff]*\\]"), "")
        result = result.replace(Regex("【[^\\w\\u4e00-\\u9fff]*】"), "")
        result = result.replace(Regex("\\([^\\w\\u4e00-\\u9fff]*\\)"), "")

        // 4. 移除包含中文字符的括号内容
        // URL中不应包含中文，包含中文的括号内容都是干扰
        result = result.replace(Regex("\\[.*?[\u4e00-\u9fff].*?\\]"), "")
        result = result.replace(Regex("【.*?[\u4e00-\u9fff].*?】"), "")
        result = result.replace(Regex("\\(.*?[\u4e00-\u9fff].*?\\)"), "")

        // 5. 保留可能有用的纯英文数字括号内容（如端口号、参数等）
        // 只保留短的、纯英文数字的括号内容
        result = result.replace(Regex("\\[([^\\]]{1,10})\\]")) { matchResult ->
            val content = matchResult.groupValues[1]
            if (content.matches(Regex("^[a-zA-Z0-9._-]+$"))) {
                matchResult.value // 保留纯英文数字内容
            } else {
                "" // 移除其他内容
            }
        }

        return result
    }

    /**
     * 移除所有中文字符 - URL中不应包含中文字符
     */
    private fun removeAllChineseCharacters(text: String): String {
        // 移除所有中文字符（包括中文标点符号）
        return text
            .replace(Regex("[\u4e00-\u9fff]+"), "") // 中文汉字
            .replace(Regex("[\u3000-\u303f]+"), "") // 中文标点符号
            .replace(Regex("[\uff00-\uffef]+"), "") // 全角字符
    }

    /**
     * 标准化空白字符 - 智能处理URL中的空白字符
     */
    private fun normalizeWhitespace(text: String): String {
        var result = text.trim()

        // 1. 移除协议部分的空白字符
        result = result.replace(Regex("(https?\\s*:\\s*/\\s*/\\s*)"), "://")

        // 2. 移除域名部分的空白字符
        result = result.replace(Regex("(://\\s*[^/\\s]*\\s+[^/\\s]*\\s*)")) { matchResult ->
            matchResult.value.replace(Regex("\\s+"), "")
        }

        // 3. 移除路径中的空白字符，但保留参数值中可能需要的空格
        result = result.replace(Regex("(/[^?]*?)\\s+([^?]*)")) { matchResult ->
            "${matchResult.groupValues[1]}${matchResult.groupValues[2]}"
        }

        // 4. 处理参数部分的空白字符（保留参数值中的空格，移除分隔符周围的空格）
        result = result.replace(Regex("\\s*([&=])\\s*"), "$1")

        // 5. 移除URL末尾的空白字符
        result = result.replace(Regex("\\s+$"), "")

        return result
    }

    /**
     * 计算清理置信度
     */
    private fun calculateCleaningConfidence(original: String, cleaned: String): Float {
        if (original == cleaned) return 1.0f

        val originalLength = original.length
        val cleanedLength = cleaned.length

        // 基于长度变化计算置信度
        val lengthRatio = cleanedLength.toFloat() / originalLength.toFloat()

        // 检查是否包含有效的URL模式
        val hasValidUrl = containsUrl(cleaned)

        return when {
            hasValidUrl && lengthRatio > 0.5f -> 0.9f
            hasValidUrl && lengthRatio > 0.3f -> 0.7f
            hasValidUrl -> 0.5f
            else -> 0.2f
        }
    }

    /**
     * 检查文本是否包含URL
     */
    private fun containsUrl(text: String): Boolean {
        val urlPattern = "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+".toRegex(RegexOption.IGNORE_CASE)
        return urlPattern.containsMatchIn(text)
    }

    /**
     * 检查文本是否匹配指定应用平台
     */
    private fun matchesPlatform(text: String, platform: AppPlatform): Boolean {
        return platform.urlPatterns.any { pattern ->
            try {
                val regex = pattern.toRegex(RegexOption.IGNORE_CASE)
                regex.containsMatchIn(text)
            } catch (e: Exception) {
                Log.w(TAG, "Invalid regex pattern: $pattern", e)
                false
            }
        }
    }

    /**
     * 检查文本是否匹配自定义应用平台
     */
    private fun matchesCustomPlatform(text: String, platform: com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.CustomAppPlatform): Boolean {
        return platform.urlPatterns.any { pattern ->
            try {
                val regex = pattern.toRegex(RegexOption.IGNORE_CASE)
                regex.containsMatchIn(text)
            } catch (e: Exception) {
                Log.w(TAG, "Invalid regex pattern: $pattern", e)
                false
            }
        }
    }

    /**
     * 获取所有支持的应用平台
     */
    fun getSupportedPlatforms(): List<AppPlatform> {
        return appPlatforms.toList()
    }

    /**
     * 根据包名获取应用平台信息
     */
    fun getPlatformByPackageName(packageName: String): AppPlatform? {
        return appPlatforms.find { it.packageName == packageName }
    }

    /**
     * 根据平台名称获取应用平台信息
     */
    fun getPlatformByName(name: String): AppPlatform? {
        return appPlatforms.find { it.name == name }
    }
}

package com.weinuo.quickcommands.ui.components.integrated

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import dev.chrisbanes.haze.HazeState

/**
 * 整合设计TopAppBar配置提供者
 * 
 * 这个机制允许天空蓝主题的页面向IntegratedMainLayout提供TopAppBar配置，
 * 而不需要在MainActivity中硬编码所有页面的TopAppBar。
 * 
 * 架构优势：
 * 1. 保持主题系统的独立性 - 每个主题自己管理TopAppBar
 * 2. 页面级配置 - 每个页面可以有自己独特的TopAppBar配置
 * 3. 动态配置 - 支持运行时动态更改TopAppBar配置
 * 4. 类型安全 - 使用强类型配置，避免运行时错误
 */

/**
 * TopAppBar配置状态
 */
data class IntegratedTopAppBarState(
    val config: TopAppBarConfig? = null,
    val updateConfig: (TopAppBarConfig?) -> Unit = {}
)

/**
 * 整合设计TopAppBar配置的CompositionLocal
 */
val LocalIntegratedTopAppBarState = compositionLocalOf<IntegratedTopAppBarState> {
    IntegratedTopAppBarState()
}

/**
 * 整合设计TopAppBar配置提供者
 * 
 * 为子组件提供TopAppBar配置管理功能
 * 
 * @param content 子组件内容
 */
@Composable
fun IntegratedTopAppBarProvider(
    content: @Composable () -> Unit
) {
    var topAppBarConfig by remember { mutableStateOf<TopAppBarConfig?>(null) }

    val topAppBarState = remember(topAppBarConfig) {
        IntegratedTopAppBarState(
            config = topAppBarConfig,
            updateConfig = { config ->
                topAppBarConfig = config
            }
        )
    }

    CompositionLocalProvider(
        LocalIntegratedTopAppBarState provides topAppBarState
    ) {
        content()
    }
}

/**
 * 设置当前页面的TopAppBar配置
 * 
 * 在天空蓝主题页面中调用此函数来设置TopAppBar配置
 * 
 * @param config TopAppBar配置，null表示不显示TopAppBar
 */
@Composable
fun SetIntegratedTopAppBar(config: TopAppBarConfig?) {
    val topAppBarState = LocalIntegratedTopAppBarState.current

    // 当配置变化时更新状态
    LaunchedEffect(config) {
        topAppBarState.updateConfig(config)
    }
}

/**
 * 获取当前的TopAppBar配置
 * 
 * 在IntegratedMainLayout中调用此函数来获取当前页面的TopAppBar配置
 * 
 * @return 当前的TopAppBar配置，null表示不显示TopAppBar
 */
@Composable
fun rememberIntegratedTopAppBarConfig(): TopAppBarConfig? {
    val topAppBarState = LocalIntegratedTopAppBarState.current
    return topAppBarState.config
}

package com.weinuo.quickcommands.widget

import android.content.Context
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import com.weinuo.quickcommands.data.QuickCommandRepository
import com.weinuo.quickcommands.execution.SharedExecutionHandler
import com.weinuo.quickcommands.shortcut.ShortcutManager

/**
 * 桌面小组件点击处理活动
 *
 * 当用户点击桌面小组件时启动，根据小组件ID执行对应的一键指令
 * 如果小组件尚未配置，则显示提示信息
 */
class WidgetClickHandlerActivity : ComponentActivity() {

    private lateinit var quickCommandRepository: QuickCommandRepository
    private lateinit var shortcutManager: ShortcutManager
    private lateinit var executionHandler: SharedExecutionHandler

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        quickCommandRepository = QuickCommandRepository.getInstance(applicationContext)
        shortcutManager = ShortcutManager(applicationContext)
        executionHandler = SharedExecutionHandler(applicationContext)

        // 获取小组件ID
        val widgetId = intent.getStringExtra(EXTRA_WIDGET_ID)

        if (widgetId != null) {
            handleWidgetClick(widgetId)
        } else {
            Toast.makeText(this, "无效的小组件", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    /**
     * 处理小组件点击
     *
     * 根据小组件ID获取关联的指令并执行
     * 支持一键指令和条件指令两种类型
     * 如果小组件尚未配置，则显示提示信息
     */
    private fun handleWidgetClick(widgetId: String) {
        // 从小组件ID中提取槽位索引
        val slotIndex = when (widgetId) {
            OneClickCommandWidget1.WIDGET_ID -> 0
            OneClickCommandWidget2.WIDGET_ID -> 1
            OneClickCommandWidget3.WIDGET_ID -> 2
            OneClickCommandWidget4.WIDGET_ID -> 3
            else -> -1
        }

        if (slotIndex == -1) {
            Toast.makeText(this, "无效的小组件ID", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        val prefs = getSharedPreferences(ShortcutManager.PREFS_NAME, Context.MODE_PRIVATE)

        // 获取快捷指令ID
        val quickCommandId = prefs.getString(ShortcutManager.PREF_WIDGET_PREFIX + slotIndex, null)

        if (quickCommandId != null) {
            // 执行快捷指令
            executeQuickCommand(quickCommandId)
        } else {
            // 小组件尚未配置
            Toast.makeText(
                this,
                "此小组件尚未配置，请在应用中创建快捷指令并选择使用此桌面小组件",
                Toast.LENGTH_LONG
            ).show()
            finish()
        }
    }

    /**
     * 执行快捷指令
     */
    private fun executeQuickCommand(commandId: String) {
        // 从内存中的数据流获取快捷指令
        val command = quickCommandRepository.quickCommands.value.find { it.id == commandId }

        if (command != null) {
            // 显示提示信息
            Toast.makeText(
                this,
                "正在执行快捷指令：${command.name}（${command.tasks.size}个任务）",
                Toast.LENGTH_LONG
            ).show()

            // 使用共享执行处理器手动执行快捷指令（跳过触发条件检查）
            executionHandler.executeQuickCommandManually(
                command = command,
                onExecutionCompleted = {
                    // 所有任务执行完成时的回调
                    runOnUiThread {
                        Toast.makeText(
                            this,
                            "快捷指令执行完成：${command.name}",
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    }
                },
                onExecutionAborted = { abortConditions ->
                    // 执行被中止时的回调
                    val conditionNames = abortConditions.joinToString(", ") { it.displayName }
                    runOnUiThread {
                        Toast.makeText(
                            this,
                            "快捷指令执行被中止：${command.name}，中止条件：$conditionNames",
                            Toast.LENGTH_LONG
                        ).show()
                        finish()
                    }
                }
            )
        } else {
            Toast.makeText(this, "找不到关联的快捷指令", Toast.LENGTH_SHORT).show()
            finish()
        }
    }



    companion object {
        const val EXTRA_WIDGET_ID = "widget_id"
    }
}

# rememberSaveable 崩溃问题修复指南

## 问题描述

### 崩溃现象
点击后台时间条件时应用崩溃，出现以下错误：

```
java.lang.IllegalArgumentException: com.weinuo.quickcommands.storage.UIStateStorageManager@b141c44 cannot be saved using the current SaveableStateRegistry. The default implementation only supports types which can be stored inside the Bundle. Please consider implementing a custom Saver for this class and pass it to rememberSaveable().
```

### 错误原因
在 `rememberSaveable` 中使用了无法序列化到 Bundle 的自定义对象，如：
- `UIStateStorageManager`
- `SettingsRepository`
- `NavigationDataStorageManager`
- 其他自定义类实例

## 根本原因分析

### Bundle 序列化限制
`rememberSaveable` 使用 Android 的 Bundle 机制来保存状态，Bundle 只支持以下基本数据类型：
- 基本类型：`Int`、`Long`、`Float`、`Double`、`Boolean`、`String`
- 基本类型数组
- `Parcelable` 对象
- `Serializable` 对象

### 错误用法示例
```kotlin
// ❌ 错误：自定义对象无法序列化到 Bundle
val uiStateManager = rememberSaveable { UIStateStorageManager(context) }
val settingsRepository = rememberSaveable { SettingsRepository(context) }
```

### 正确用法示例
```kotlin
// ✅ 正确：使用 remember 创建管理器对象
val uiStateManager = remember { UIStateStorageManager(context) }
val settingsRepository = remember { SettingsRepository(context) }
```

## 修复方案

### 1. 识别问题代码
搜索所有在 `rememberSaveable` 中使用自定义对象的代码：

```bash
# 搜索 rememberSaveable 中的自定义对象使用
grep -r "rememberSaveable.*{.*Manager\|Repository\|Storage" app/src/main/java/
```

### 2. 修复步骤

#### 步骤1：将管理器对象改为使用 remember
```kotlin
// 修复前
val uiStateManager = rememberSaveable { UIStateStorageManager(context) }

// 修复后
val uiStateManager = remember { UIStateStorageManager(context) }
```

#### 步骤2：保持状态变量使用 rememberSaveable
```kotlin
// 这些基本类型状态可以继续使用 rememberSaveable
var backgroundTimeThresholdMinutes by rememberSaveable { mutableStateOf(5) }
var appStateTriggerMode by rememberSaveable { mutableStateOf(AppStateTriggerMode.ANY_APP) }
var skipForegroundApp by rememberSaveable { mutableStateOf(true) }
```

### 3. 本次修复的具体文件

#### 文件1：AppStateConfigProvider.kt
**位置**：`app/src/main/java/com/weinuo/quickcommands/ui/configuration/AppStateConfigProvider.kt`
**修复行数**：1014行和1018行

```kotlin
// 修复前
val uiStateManager = rememberSaveable { UIStateStorageManager(context) }
val settingsRepository = rememberSaveable { SettingsRepository(context) }

// 修复后
val uiStateManager = remember { UIStateStorageManager(context) }
val settingsRepository = remember { SettingsRepository(context) }
```

#### 文件2：DeviceEventConfigProvider.kt
**位置**：`app/src/main/java/com/weinuo/quickcommands/ui/configuration/DeviceEventConfigProvider.kt`
**修复行数**：932行

```kotlin
// 修复前
val uiStateManager = rememberSaveable { UIStateStorageManager(context) }

// 修复后
val uiStateManager = remember { UIStateStorageManager(context) }
```

## 预防措施

### 1. 代码审查检查点
在代码审查时，重点检查以下内容：
- 所有 `rememberSaveable` 的使用是否只包含可序列化的基本类型
- 管理器对象是否正确使用 `remember` 而不是 `rememberSaveable`

### 2. 使用规则
- **remember**：用于创建管理器对象、Repository、存储引擎等不需要跨配置更改保存的对象
- **rememberSaveable**：仅用于基本数据类型的状态变量，如 `Int`、`String`、`Boolean`、枚举等

### 3. 自定义 Saver 的替代方案
如果确实需要保存复杂对象的状态，应该：
1. 将复杂对象拆分为基本类型字段
2. 分别使用 `rememberSaveable` 保存每个基本类型字段
3. 或者使用项目中已有的 `NativeStateSaver` 工具类

## 验证修复

### 1. 编译验证
```bash
./gradlew assembleDebug
```

### 2. 功能测试
1. 启动应用
2. 进入快捷指令创建界面
3. 点击"添加触发条件"
4. 选择"应用状态" → "后台时间"
5. 验证不再崩溃，配置界面正常显示

## 相关文档
- [Jetpack Compose 状态管理最佳实践](navigation_state_management_best_practices.md)
- [原生数据类型存储架构](原生数据类型存储架构重构方案.md)

## 修复历史
- **2025-06-23**：修复 `BackgroundTimeConfigContent` 和 `NotificationEventConfigContent` 中的 `rememberSaveable` 崩溃问题

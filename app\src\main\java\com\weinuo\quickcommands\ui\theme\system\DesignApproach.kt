package com.weinuo.quickcommands.ui.theme.system

/**
 * 设计方法枚举
 *
 * 定义不同主题采用的设计理念和视觉风格
 */
enum class DesignApproach {
    /**
     * 分层设计
     * 
     * 特点：
     * - 明确的层次结构
     * - 使用阴影和高度表现层次
     * - 组件间有明显的分离感
     * - 适合复杂信息的展示
     */
    LAYERED_DESIGN,

    /**
     * 整合设计
     * 
     * 特点：
     * - 统一的视觉体验
     * - 减少视觉分离，增强整体感
     * - 支持模糊效果和透明度
     * - 现代化的扁平设计风格
     */
    INTEGRATED_DESIGN,

    /**
     * 极简设计
     * 
     * 特点：
     * - 最小化的视觉元素
     * - 纯文字或线条为主
     * - 极高的信息密度
     * - 适合专业用户
     * 
     * 注：为未来扩展预留
     */
    MINIMAL_DESIGN,

    /**
     * 动态设计
     * 
     * 特点：
     * - 根据内容动态调整样式
     * - 智能的颜色和布局适配
     * - 丰富的动画和过渡效果
     * - 个性化的视觉体验
     * 
     * 注：为未来扩展预留
     */
    DYNAMIC_DESIGN
}

/**
 * 设计方法扩展属性
 */
val DesignApproach.displayName: String
    get() = when (this) {
        DesignApproach.LAYERED_DESIGN -> "分层设计"
        DesignApproach.INTEGRATED_DESIGN -> "整合设计"
        DesignApproach.MINIMAL_DESIGN -> "极简设计"
        DesignApproach.DYNAMIC_DESIGN -> "动态设计"
    }

val DesignApproach.description: String
    get() = when (this) {
        DesignApproach.LAYERED_DESIGN -> "清晰的层次结构，使用阴影表现深度"
        DesignApproach.INTEGRATED_DESIGN -> "统一的视觉体验，支持模糊效果"
        DesignApproach.MINIMAL_DESIGN -> "极简的视觉元素，专注内容本身"
        DesignApproach.DYNAMIC_DESIGN -> "智能适配的动态视觉体验"
    }

val DesignApproach.supportsShadows: Boolean
    get() = when (this) {
        DesignApproach.LAYERED_DESIGN -> true
        DesignApproach.INTEGRATED_DESIGN -> false
        DesignApproach.MINIMAL_DESIGN -> false
        DesignApproach.DYNAMIC_DESIGN -> true
    }

val DesignApproach.supportsBlur: Boolean
    get() = when (this) {
        DesignApproach.LAYERED_DESIGN -> false
        DesignApproach.INTEGRATED_DESIGN -> true
        DesignApproach.MINIMAL_DESIGN -> false
        DesignApproach.DYNAMIC_DESIGN -> true
    }

val DesignApproach.supportsTransparency: Boolean
    get() = when (this) {
        DesignApproach.LAYERED_DESIGN -> false
        DesignApproach.INTEGRATED_DESIGN -> true
        DesignApproach.MINIMAL_DESIGN -> true
        DesignApproach.DYNAMIC_DESIGN -> true
    }

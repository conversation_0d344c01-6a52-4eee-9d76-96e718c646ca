package com.weinuo.quickcommands.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import android.widget.LinearLayout
import com.weinuo.quickcommands.utils.VibrationManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * 闹钟悬浮窗服务
 *
 * 负责显示闹钟提醒的悬浮窗，使用传统View系统实现
 */
class AlarmOverlayService : Service() {
    private val TAG = "AlarmOverlayService"

    private lateinit var windowManager: WindowManager
    private var overlayView: View? = null
    private var mediaPlayer: MediaPlayer? = null
    private var requireTask = false
    private var enableSound = true
    private var reminderMessage = ""
    private var enableVibration = false
    private var vibrationMode = "CONTINUOUS"
    private var vibrationIntensity = "MEDIUM"

    // 铃声相关
    private var selectedRingtoneUri = ""
    private var selectedRingtoneName = ""

    // 点击计数和状态
    private var clickCount = 0
    private var targetClicks = 5 // 将从Intent参数中获取
    private var isAlarmActive = true

    // 协程相关
    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())
    private var soundJob: Job? = null
    private var dotPositionJob: Job? = null

    // UI组件
    private var messageTextView: TextView? = null
    private var instructionTextView: TextView? = null
    private var progressTextView: TextView? = null
    private var dismissButton: android.widget.Button? = null
    private var dotView: View? = null

    // 悬浮窗参数
    private val overlayParams by lazy {
        WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
            type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
            format = PixelFormat.TRANSLUCENT
            gravity = Gravity.TOP or Gravity.START
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        Log.d(TAG, "AlarmOverlayService created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "AlarmOverlayService started")

        // 获取参数
        requireTask = intent?.getBooleanExtra("requireTask", false) ?: false
        enableSound = intent?.getBooleanExtra("enableSound", true) ?: true
        reminderMessage = intent?.getStringExtra("reminderMessage") ?: ""
        enableVibration = intent?.getBooleanExtra("enableVibration", false) ?: false
        vibrationMode = intent?.getStringExtra("vibrationMode") ?: "CONTINUOUS"
        vibrationIntensity = intent?.getStringExtra("vibrationIntensity") ?: "MEDIUM"
        targetClicks = intent?.getIntExtra("clicksRequired", 5) ?: 5
        selectedRingtoneUri = intent?.getStringExtra("selectedRingtoneUri") ?: ""
        selectedRingtoneName = intent?.getStringExtra("selectedRingtoneName") ?: ""

        // 检查悬浮窗权限
        if (!Settings.canDrawOverlays(this)) {
            Log.e(TAG, "No overlay permission, stopping service")
            stopSelf()
            return START_NOT_STICKY
        }

        // 显示悬浮窗
        showOverlay()

        // 播放铃声
        if (enableSound) {
            startAlarmSound()
        }

        // 启动震动
        if (enableVibration) {
            startAlarmVibration()
        }

        return START_STICKY
    }

    private fun showOverlay() {
        try {
            // 创建主容器
            overlayView = LinearLayout(this).apply {
                orientation = LinearLayout.VERTICAL
                setBackgroundColor(Color.parseColor("#FF000000")) // 纯黑色背景
                gravity = Gravity.CENTER
                setPadding(32, 32, 32, 32)
            }

            val container = overlayView as LinearLayout

            // 添加提醒消息
            if (reminderMessage.isNotEmpty()) {
                messageTextView = TextView(this).apply {
                    text = reminderMessage
                    setTextColor(Color.WHITE)
                    textSize = 24f
                    gravity = Gravity.CENTER
                    setPadding(0, 0, 0, 32)
                }
                container.addView(messageTextView)
            }

            // 添加指令文本
            instructionTextView = TextView(this).apply {
                setTextColor(Color.WHITE)
                textSize = 18f
                gravity = Gravity.CENTER
                setPadding(0, 0, 0, 16)
            }
            container.addView(instructionTextView)

            if (requireTask) {
                // 需要任务时的UI
                setupTaskMode(container)
            } else {
                // 不需要任务时的UI
                setupDirectMode(container)
            }

            // 添加到窗口管理器
            windowManager.addView(overlayView, overlayParams)

            Log.d(TAG, "Overlay view added to window manager")
        } catch (e: Exception) {
            Log.e(TAG, "Error showing overlay", e)
            stopSelf()
        }
    }

    private fun setupTaskMode(container: LinearLayout) {
        instructionTextView?.text = "点击白色小点关闭闹钟"

        // 添加进度文本
        progressTextView = TextView(this).apply {
            setTextColor(Color.WHITE)
            textSize = 16f
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 32)
        }
        container.addView(progressTextView)
        updateProgress()

        // 创建白色小点
        createDotView(container)
    }

    private fun setupDirectMode(container: LinearLayout) {
        instructionTextView?.text = "点击按钮关闭闹钟"

        // 创建一个相对布局来放置随机位置的按钮
        val buttonContainer = android.widget.RelativeLayout(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                400 // 固定高度，与任务模式保持一致
            )
        }

        // 添加关闭按钮，设置较窄的宽度
        dismissButton = android.widget.Button(this).apply {
            text = "关闭闹钟"
            textSize = 14f
            setPadding(16, 12, 16, 12)
            layoutParams = android.widget.RelativeLayout.LayoutParams(
                200, // 固定宽度，比原来窄
                android.widget.RelativeLayout.LayoutParams.WRAP_CONTENT
            )
            setOnClickListener { dismissAlarm() }
        }

        buttonContainer.addView(dismissButton)
        container.addView(buttonContainer)

        // 设置按钮的随机位置
        updateButtonPosition()
    }

    private fun createDotView(container: LinearLayout) {
        // 创建一个相对布局来放置随机位置的小点
        val dotContainer = android.widget.RelativeLayout(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                400 // 固定高度
            )
        }

        dotView = View(this).apply {
            setBackgroundColor(Color.WHITE)
            layoutParams = android.widget.RelativeLayout.LayoutParams(50, 50)
            setOnClickListener { handleDotClick() }
        }

        dotContainer.addView(dotView)
        container.addView(dotContainer)

        // 设置初始位置
        updateDotPosition()
    }

    private fun updateDotPosition() {
        dotView?.let { dot ->
            val params = dot.layoutParams as android.widget.RelativeLayout.LayoutParams
            val containerWidth = 800 // 估算容器宽度
            val containerHeight = 400 // 容器高度

            params.leftMargin = Random.nextInt(containerWidth - 50)
            params.topMargin = Random.nextInt(containerHeight - 50)
            dot.layoutParams = params
        }
    }

    private fun updateButtonPosition() {
        dismissButton?.let { button ->
            val params = button.layoutParams as android.widget.RelativeLayout.LayoutParams
            val containerWidth = 800 // 估算容器宽度
            val containerHeight = 400 // 容器高度
            val buttonWidth = 200 // 按钮宽度

            params.leftMargin = Random.nextInt(containerWidth - buttonWidth)
            params.topMargin = Random.nextInt(containerHeight - 100) // 预留按钮高度空间
            button.layoutParams = params
        }
    }

    private fun updateProgress() {
        progressTextView?.text = "进度: $clickCount / $targetClicks"
    }

    private fun handleDotClick() {
        if (!isAlarmActive) return

        clickCount++
        Log.d(TAG, "Dot clicked: $clickCount/$targetClicks")

        // 更新进度显示
        updateProgress()

        // 第一次点击时停止铃声
        if (clickCount == 1 && enableSound) {
            stopAlarmSound()
            // 启动5秒后重新播放铃声的任务
            soundJob = serviceScope.launch {
                delay(5000)
                if (isAlarmActive && clickCount < targetClicks) {
                    startAlarmSound()
                }
            }
        }

        // 检查是否达到目标点击次数
        if (clickCount >= targetClicks) {
            dismissAlarm()
        } else {
            // 更新小点位置
            updateDotPosition()

            // 重置5秒计时器
            soundJob?.cancel()
            soundJob = serviceScope.launch {
                delay(5000)
                if (isAlarmActive && enableSound && clickCount < targetClicks) {
                    startAlarmSound()
                }
            }
        }
    }

    private fun startAlarmSound() {
        try {
            stopAlarmSound() // 先停止之前的播放

            // 优先使用自定义铃声，如果没有则使用系统默认闹钟铃声
            val alarmUri = if (selectedRingtoneUri.isNotEmpty()) {
                try {
                    android.net.Uri.parse(selectedRingtoneUri)
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to parse custom ringtone URI: $selectedRingtoneUri", e)
                    null
                }
            } else {
                null
            } ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)

            if (alarmUri != null) {
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(this@AlarmOverlayService, alarmUri)

                    // 设置音频属性为闹钟类型
                    setAudioAttributes(
                        AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_ALARM)
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .build()
                    )

                    isLooping = true
                    prepare()
                    start()
                }

                val ringtoneName = if (selectedRingtoneName.isNotEmpty()) selectedRingtoneName else "系统默认"
                Log.d(TAG, "Alarm sound started: $ringtoneName")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting alarm sound", e)
        }
    }

    private fun stopAlarmSound() {
        try {
            mediaPlayer?.let {
                if (it.isPlaying) {
                    it.stop()
                }
                it.release()
                mediaPlayer = null
                Log.d(TAG, "Alarm sound stopped")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping alarm sound", e)
        }
    }

    private fun startAlarmVibration() {
        try {
            val mode = VibrationManager.VibrationMode.valueOf(vibrationMode)
            val intensity = VibrationManager.VibrationIntensity.valueOf(vibrationIntensity)

            val vibrationConfig = VibrationManager.VibrationConfig(
                mode = mode,
                intensity = intensity,
                duration = if (mode == VibrationManager.VibrationMode.CONTINUOUS) Long.MAX_VALUE else 1000L,
                interval = 500L,
                repeatCount = -1 // 无限重复，直到手动停止
            )

            VibrationManager.startVibration(this, vibrationConfig)
            Log.d(TAG, "Alarm vibration started: mode=$vibrationMode, intensity=$vibrationIntensity")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting alarm vibration", e)
        }
    }

    private fun stopAlarmVibration() {
        try {
            VibrationManager.stopVibration(this)
            Log.d(TAG, "Alarm vibration stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping alarm vibration", e)
        }
    }

    private fun dismissAlarm() {
        Log.d(TAG, "Alarm dismissed after $clickCount clicks")
        isAlarmActive = false

        // 停止铃声
        stopAlarmSound()

        // 停止震动
        if (enableVibration) {
            stopAlarmVibration()
        }

        // 取消所有协程任务
        soundJob?.cancel()
        dotPositionJob?.cancel()

        // 移除悬浮窗
        removeOverlay()

        // 停止服务
        stopSelf()
    }

    private fun removeOverlay() {
        try {
            overlayView?.let {
                windowManager.removeView(it)
                overlayView = null
                Log.d(TAG, "Overlay view removed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error removing overlay", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "AlarmOverlayService destroyed")

        // 清理资源
        stopAlarmSound()
        if (enableVibration) {
            stopAlarmVibration()
        }
        removeOverlay()
        soundJob?.cancel()
        dotPositionJob?.cancel()
    }
}

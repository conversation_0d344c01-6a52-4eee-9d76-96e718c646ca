package com.weinuo.quickcommands.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import androidx.compose.runtime.Composable
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 网络权限工具类
 * 管理网络相关权限的检查、申请和引导
 * 适用于网络连接检测、WiFi状态监控、通知功能等
 */
object NetworkPermissionUtil {
    
    /**
     * 网络相关权限列表
     */
    private val NETWORK_PERMISSIONS = arrayOf(
        Manifest.permission.INTERNET,
        Manifest.permission.ACCESS_NETWORK_STATE,
        Manifest.permission.ACCESS_WIFI_STATE
    )
    
    /**
     * 通知权限列表（Android 13+）
     */
    private val NOTIFICATION_PERMISSIONS = arrayOf(
        Manifest.permission.POST_NOTIFICATIONS
    )
    
    /**
     * 检查是否拥有网络权限
     * @param context 上下文
     * @return 是否拥有网络权限
     */
    fun hasNetworkPermissions(context: Context): Boolean {
        return NETWORK_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查是否拥有INTERNET权限
     * @param context 上下文
     * @return 是否拥有INTERNET权限
     */
    fun hasInternetPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.INTERNET
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否拥有网络状态权限
     * @param context 上下文
     * @return 是否拥有网络状态权限
     */
    fun hasNetworkStatePermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.ACCESS_NETWORK_STATE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否拥有WiFi状态权限
     * @param context 上下文
     * @return 是否拥有WiFi状态权限
     */
    fun hasWifiStatePermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.ACCESS_WIFI_STATE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否拥有通知权限（Android 13+）
     * @param context 上下文
     * @return 是否拥有通知权限
     */
    fun hasPostNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context, 
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 13以下版本不需要此权限
            true
        }
    }
    
    /**
     * 检查是否拥有所有网络和通知权限
     * @param context 上下文
     * @return 是否拥有所有权限
     */
    fun hasAllNetworkAndNotificationPermissions(context: Context): Boolean {
        return hasNetworkPermissions(context) && hasPostNotificationPermission(context)
    }
    
    /**
     * 获取需要申请的网络权限列表
     * @return 网络权限数组
     */
    fun getRequiredNetworkPermissions(): Array<String> {
        return NETWORK_PERMISSIONS
    }
    
    /**
     * 获取需要申请的通知权限列表
     * @return 通知权限数组
     */
    fun getRequiredNotificationPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            NOTIFICATION_PERMISSIONS
        } else {
            emptyArray()
        }
    }
    
    /**
     * 获取缺失的网络权限列表
     * @param context 上下文
     * @return 缺失的权限列表
     */
    fun getMissingNetworkPermissions(context: Context): List<String> {
        return NETWORK_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 申请网络权限
     * @param launcher 权限申请启动器
     */
    fun requestNetworkPermissions(launcher: androidx.activity.result.ActivityResultLauncher<Array<String>>) {
        launcher.launch(NETWORK_PERMISSIONS)
    }

    /**
     * 申请通知权限
     * @param launcher 权限申请启动器
     */
    fun requestNotificationPermissions(launcher: androidx.activity.result.ActivityResultLauncher<Array<String>>) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            launcher.launch(NOTIFICATION_PERMISSIONS)
        }
    }
    
    /**
     * 网络权限被拒绝后的说明对话框
     */
    @Composable
    fun NetworkPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "网络权限被拒绝",
            message = """
                网络功能需要网络权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动授予网络权限：
                • 连接检查功能
                • 网络状态监控
                • WiFi状态检测
                • 网络连接条件判断

                您可以点击"打开设置"直接跳转到应用权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }
    
    /**
     * 通知权限被拒绝后的说明对话框
     */
    @Composable
    fun NotificationPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "通知权限被拒绝",
            message = """
                通知功能需要通知权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动授予通知权限：
                • 系统通知显示
                • 任务执行结果通知
                • 条件触发提醒

                您可以点击"打开设置"直接跳转到应用权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }
}

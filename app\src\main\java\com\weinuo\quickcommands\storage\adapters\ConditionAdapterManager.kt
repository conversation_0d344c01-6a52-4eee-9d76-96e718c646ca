package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.AppListStorageEngine
import com.weinuo.quickcommands.storage.NativeTypeStorageManager

/**
 * 条件适配器管理器
 *
 * 统一管理所有条件类型的存储适配器，提供类型安全的条件存储和加载功能。
 * 支持动态注册新的条件适配器，便于扩展新的条件类型。
 *
 * 设计原则：
 * - 类型安全：通过泛型确保条件类型匹配
 * - 可扩展：支持动态注册新的适配器
 * - 统一接口：提供一致的存储和加载API
 * - 错误处理：完善的异常处理和日志记录
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class ConditionAdapterManager(
    private val storageManager: NativeTypeStorageManager,
    private val appListEngine: AppListStorageEngine
) {
    companion object {
        private const val TAG = "ConditionAdapterManager"
    }

    // 条件适配器注册表
    private val adapters = mutableMapOf<String, ConditionStorageAdapter<*>>()

    init {
        // 注册默认的条件适配器
        registerDefaultAdapters()
    }

    /**
     * 注册默认的条件适配器
     */
    private fun registerDefaultAdapters() {
        try {
            // 注册应用状态条件适配器
            registerAdapter(AppStateConditionAdapter(storageManager, appListEngine))

            // 注册时间条件适配器
            registerAdapter(TimeBasedConditionAdapter(storageManager, appListEngine))

            // 注册连接状态条件适配器
            registerAdapter(ConnectionStateConditionAdapter(storageManager, appListEngine))

            // 注册设备事件条件适配器
            registerAdapter(DeviceEventConditionAdapter(storageManager, appListEngine))

            // 注册手动触发条件适配器
            registerAdapter(ManualTriggerConditionAdapter(storageManager, appListEngine))

            // 注册通信状态条件适配器
            registerAdapter(CommunicationStateConditionAdapter(storageManager, appListEngine))

            // 注册传感器状态条件适配器
            registerAdapter(SensorStateConditionAdapter(storageManager, appListEngine))

            Log.d(TAG, "默认条件适配器注册完成，共注册 ${adapters.size} 个适配器")
        } catch (e: Exception) {
            Log.e(TAG, "注册默认条件适配器时发生错误", e)
        }
    }

    /**
     * 注册条件适配器
     *
     * @param adapter 要注册的适配器
     */
    fun <T : SharedTriggerCondition> registerAdapter(adapter: ConditionStorageAdapter<T>) {
        val conditionType = adapter.getConditionType()
        adapters[conditionType] = adapter
        Log.d(TAG, "已注册条件适配器: $conditionType")
    }

    /**
     * 获取条件适配器
     *
     * @param conditionType 条件类型
     * @return 对应的适配器，如果不存在则返回null
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T : SharedTriggerCondition> getAdapter(conditionType: String): ConditionStorageAdapter<T>? {
        return adapters[conditionType] as? ConditionStorageAdapter<T>
    }

    /**
     * 保存条件
     *
     * @param condition 要保存的条件
     * @return 操作是否成功
     */
    fun <T : SharedTriggerCondition> saveCondition(condition: T): Boolean {
        return try {
            val conditionType = getConditionTypeFromInstance(condition)
            val adapter = getAdapter<T>(conditionType)

            if (adapter != null) {
                val success = adapter.save(condition)
                if (success) {
                    Log.d(TAG, "条件保存成功: ${condition.id} (类型: $conditionType)")
                } else {
                    Log.e(TAG, "条件保存失败: ${condition.id} (类型: $conditionType)")
                }
                success
            } else {
                Log.e(TAG, "未找到条件类型 '$conditionType' 的适配器")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存条件时发生异常: ${condition.id}", e)
            false
        }
    }

    /**
     * 加载条件
     *
     * @param conditionId 条件ID
     * @param conditionType 条件类型
     * @return 加载的条件，失败时返回null
     */
    @Suppress("UNCHECKED_CAST")
    fun <T : SharedTriggerCondition> loadCondition(conditionId: String, conditionType: String): T? {
        return try {
            val adapter = getAdapter<T>(conditionType)

            if (adapter != null) {
                val condition = adapter.load(conditionId)
                if (condition != null) {
                    Log.d(TAG, "条件加载成功: $conditionId (类型: $conditionType)")
                } else {
                    Log.d(TAG, "条件不存在或加载失败: $conditionId (类型: $conditionType)")
                }
                condition as? T
            } else {
                Log.e(TAG, "未找到条件类型 '$conditionType' 的适配器")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载条件时发生异常: $conditionId (类型: $conditionType)", e)
            null
        }
    }

    /**
     * 删除条件
     *
     * @param conditionId 条件ID
     * @param conditionType 条件类型
     * @return 操作是否成功
     */
    fun deleteCondition(conditionId: String, conditionType: String): Boolean {
        return try {
            val adapter = getAdapter<SharedTriggerCondition>(conditionType)

            if (adapter != null) {
                val success = adapter.delete(conditionId)
                if (success) {
                    Log.d(TAG, "条件删除成功: $conditionId (类型: $conditionType)")
                } else {
                    Log.e(TAG, "条件删除失败: $conditionId (类型: $conditionType)")
                }
                success
            } else {
                Log.e(TAG, "未找到条件类型 '$conditionType' 的适配器")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除条件时发生异常: $conditionId (类型: $conditionType)", e)
            false
        }
    }

    /**
     * 检查条件是否存在
     *
     * @param conditionId 条件ID
     * @param conditionType 条件类型
     * @return 条件是否存在
     */
    fun conditionExists(conditionId: String, conditionType: String): Boolean {
        return try {
            val adapter = getAdapter<SharedTriggerCondition>(conditionType)
            adapter?.exists(conditionId) ?: false
        } catch (e: Exception) {
            Log.e(TAG, "检查条件存在性时发生异常: $conditionId (类型: $conditionType)", e)
            false
        }
    }

    /**
     * 获取所有已注册的条件类型
     *
     * @return 条件类型列表
     */
    fun getRegisteredConditionTypes(): List<String> {
        return adapters.keys.toList()
    }

    /**
     * 批量保存条件
     *
     * @param conditions 要保存的条件列表
     * @return 成功保存的条件数量
     */
    fun saveConditions(conditions: List<SharedTriggerCondition>): Int {
        var successCount = 0
        conditions.forEach { condition ->
            if (saveCondition(condition)) {
                successCount++
            }
        }
        Log.d(TAG, "批量保存条件完成: 成功 $successCount/${conditions.size}")
        return successCount
    }

    /**
     * 批量删除条件
     *
     * @param conditionInfos 要删除的条件信息列表 (ID和类型的配对)
     * @return 成功删除的条件数量
     */
    fun deleteConditions(conditionInfos: List<Pair<String, String>>): Int {
        var successCount = 0
        conditionInfos.forEach { (conditionId, conditionType) ->
            if (deleteCondition(conditionId, conditionType)) {
                successCount++
            }
        }
        Log.d(TAG, "批量删除条件完成: 成功 $successCount/${conditionInfos.size}")
        return successCount
    }

    /**
     * 从条件实例获取条件类型
     *
     * @param condition 条件实例
     * @return 条件类型字符串
     */
    private fun getConditionTypeFromInstance(condition: SharedTriggerCondition): String {
        return when (condition) {
            is AppStateCondition -> "app_state"
            is TimeBasedCondition -> "time_based"
            is ConnectionStateCondition -> "connection_state"
            is DeviceEventCondition -> "device_event"
            is ManualTriggerCondition -> "manual_trigger"
            is CommunicationStateCondition -> "communication_state"
            is SensorStateCondition -> "sensor_state"
            else -> {
                Log.w(TAG, "未知的条件类型: ${condition::class.simpleName}")
                condition.type
            }
        }
    }

    /**
     * 类型安全的条件保存方法（针对具体条件类型）
     */
    fun saveAppStateCondition(condition: AppStateCondition): Boolean = saveCondition(condition)
    fun saveTimeBasedCondition(condition: TimeBasedCondition): Boolean = saveCondition(condition)
    fun saveConnectionStateCondition(condition: ConnectionStateCondition): Boolean = saveCondition(condition)
    fun saveDeviceEventCondition(condition: DeviceEventCondition): Boolean = saveCondition(condition)
    fun saveManualTriggerCondition(condition: ManualTriggerCondition): Boolean = saveCondition(condition)
    fun saveCommunicationStateCondition(condition: CommunicationStateCondition): Boolean = saveCondition(condition)
    fun saveSensorStateCondition(condition: SensorStateCondition): Boolean = saveCondition(condition)

    /**
     * 类型安全的条件加载方法（针对具体条件类型）
     */
    fun loadAppStateCondition(conditionId: String): AppStateCondition? = loadCondition(conditionId, "app_state")
    fun loadTimeBasedCondition(conditionId: String): TimeBasedCondition? = loadCondition(conditionId, "time_based")
    fun loadConnectionStateCondition(conditionId: String): ConnectionStateCondition? = loadCondition(conditionId, "connection_state")
    fun loadDeviceEventCondition(conditionId: String): DeviceEventCondition? = loadCondition(conditionId, "device_event")
    fun loadManualTriggerCondition(conditionId: String): ManualTriggerCondition? = loadCondition(conditionId, "manual_trigger")
    fun loadCommunicationStateCondition(conditionId: String): CommunicationStateCondition? = loadCondition(conditionId, "communication_state")
    fun loadSensorStateCondition(conditionId: String): SensorStateCondition? = loadCondition(conditionId, "sensor_state")

    /**
     * 通用条件加载方法
     * 根据存储的类型信息自动选择正确的适配器
     *
     * @param conditionId 条件ID
     * @return 加载的条件，失败时返回null
     */
    fun loadConditionByStoredType(conditionId: String): SharedTriggerCondition? {
        return try {
            // 首先读取存储的条件类型
            val typeKey = "condition_${conditionId}_type"
            val storedType = storageManager.loadString(com.weinuo.quickcommands.storage.StorageDomain.CONDITIONS, typeKey)

            if (storedType.isEmpty()) {
                Log.w(TAG, "条件类型信息不存在: $conditionId")
                return null
            }

            // 根据存储的类型加载条件
            when (storedType) {
                "app_state" -> loadAppStateCondition(conditionId)
                "time_based" -> loadTimeBasedCondition(conditionId)
                "connection_state" -> loadConnectionStateCondition(conditionId)
                "device_event" -> loadDeviceEventCondition(conditionId)
                "manual_trigger" -> loadManualTriggerCondition(conditionId)
                "communication_state" -> loadCommunicationStateCondition(conditionId)
                "sensor_state" -> loadSensorStateCondition(conditionId)
                else -> {
                    Log.w(TAG, "未知的存储条件类型: $storedType for condition: $conditionId")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "根据存储类型加载条件时发生异常: $conditionId", e)
            null
        }
    }
}

package com.weinuo.quickcommands.model

/**
 * 快捷指令数据模型
 *
 * @property id 指令唯一标识
 * @property name 指令名称
 * @property isAllDayEffective 是否全天生效
 * @property effectiveStartTime 生效开始时间（格式：HH:MM，如 09:00）
 * @property effectiveEndTime 生效结束时间（格式：HH:MM，如 18:00）
 * @property triggerConditions 触发条件列表
 * @property requireAllConditions 触发条件组合逻辑，true: 所有条件满足时触发, false: 任一条件满足时触发
 * @property tasks 要执行的任务列表
 * @property abortConditions 中止执行条件列表（可选）
 * @property requireAllAbortConditions 中止条件组合逻辑，true: 所有条件满足时中止, false: 任一条件满足时中止
 * @property isEnabled 是否启用此快捷指令（默认为启用）
 * @property iconUri 自定义图标文件路径（可选）
 * @property hasCustomIcon 是否使用自定义图标
 */
data class QuickCommand(
    val id: String,
    val name: String,
    val isAllDayEffective: Boolean = true,
    val effectiveStartTime: String = "00:00",
    val effectiveEndTime: String = "23:59",
    val triggerConditions: List<SharedTriggerCondition> = emptyList(),
    val requireAllConditions: Boolean = true, // true: 所有条件满足时触发, false: 任一条件满足时触发
    val tasks: List<SharedTask> = emptyList(),
    val abortConditions: List<SharedTriggerCondition> = emptyList(),
    val requireAllAbortConditions: Boolean = false, // true: 所有条件满足时中止, false: 任一条件满足时中止
    val isEnabled: Boolean = true,
    val iconUri: String? = null, // 自定义图标文件路径
    val hasCustomIcon: Boolean = false // 是否使用自定义图标
)



package com.weinuo.quickcommands.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.permission.GlobalPermissionManager
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration

/**
 * Shizuku权限提示卡片组件
 * 
 * 一次性显示的提示卡片，用于引导用户开启Shizuku权限以获得更好的优化效果。
 * 支持动画效果和状态保存，关闭后不再显示。
 * 
 * @param visible 是否显示卡片
 * @param onDismiss 关闭卡片的回调
 * @param onOpenShizuku 开启Shizuku权限的回调
 * @param modifier 修饰符
 */
@Composable
fun ShizukuTipCard(
    visible: Boolean,
    onDismiss: () -> Unit,
    onOpenShizuku: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取全局设置以使用动态字体大小（仅在天空蓝主题下使用）
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的字体样式配置 - 与快捷指令卡片保持一致
    val titleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.titleMedium.copy(
            fontSize = globalSettings.cardTitleFontSize.sp,
            fontWeight = when (globalSettings.cardTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.titleSmall
    }

    val contentStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小
        MaterialTheme.typography.bodyMedium.copy(
            fontSize = globalSettings.cardContentFontSize.sp
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.bodySmall
    }

    // 根据主题选择背景颜色和样式
    val backgroundColor = if (themeManager.getCurrentThemeId() == "sky_blue") {
        MaterialTheme.colorScheme.surfaceContainerLow // 天空蓝主题使用白色背景
    } else {
        MaterialTheme.colorScheme.primaryContainer // 其他主题保持原样
    }

    val elevation = if (themeManager.getCurrentThemeId() == "sky_blue") {
        0.dp // 天空蓝主题无阴影
    } else {
        4.dp // 其他主题保持原样
    }

    // 根据主题选择标题和描述颜色 - 与快捷指令卡片保持一致
    val titleColor = if (themeManager.getCurrentThemeId() == "sky_blue") {
        MaterialTheme.colorScheme.onSurface // 天空蓝主题使用onSurface
    } else {
        MaterialTheme.colorScheme.primary // 其他主题保持原样
    }

    val contentColor = MaterialTheme.colorScheme.onSurfaceVariant // 描述文本颜色保持一致

    AnimatedVisibility(
        visible = visible,
        enter = slideInVertically(
            initialOffsetY = { -it }
        ) + fadeIn(),
        exit = slideOutVertically(
            targetOffsetY = { -it }
        ) + fadeOut(),
        modifier = modifier
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = backgroundColor
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = elevation
            ),
            shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
        ) {
            // 使用与快捷指令卡片完全相同的布局结构
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 主要内容区域 - 使用Column布局，确保间距一致
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = cardStyle.defaultHorizontalPadding,
                            end = cardStyle.defaultHorizontalPadding + 0.dp, // 不留额外右边距，让开启按钮最大程度往右移动
                            top = cardStyle.defaultVerticalPadding,
                            bottom = cardStyle.defaultVerticalPadding
                        )
                ) {
                    // 根据主题决定是否显示图标
                    if (themeManager.getCurrentThemeId() != "sky_blue") {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(cardStyle.contentHorizontalSpacing)) // 使用卡片样式的水平间距
                            Text(
                                text = "获得更好的优化效果",
                                style = titleStyle,
                                color = titleColor
                            )
                        }
                    } else {
                        // 天空蓝主题：无图标，直接显示标题
                        Text(
                            text = "获得更好的优化效果",
                            style = titleStyle,
                            color = titleColor
                        )
                    }

                    // 标题与描述之间的间距
                    Spacer(modifier = Modifier.height(cardStyle.contentVerticalSpacing))

                    // 描述文本
                    Text(
                        text = "开启Shizuku权限可以获得更好的优化效果",
                        style = contentStyle,
                        color = contentColor
                    )

                    // 描述与开启按钮之间的间距 - 与标题-描述间距相同
                    Spacer(modifier = Modifier.height(cardStyle.contentVerticalSpacing))

                    // 开启按钮区域 - 右对齐，往右移动多一点
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        // 使用Box替代TextButton来完全控制间距
                        Box(
                            modifier = Modifier
                                .clickable { onOpenShizuku() }
                                .padding(0.dp), // 确保没有任何padding
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "开启",
                                color = MaterialTheme.colorScheme.primary,
                                style = MaterialTheme.typography.labelLarge
                            )
                        }
                    }
                }

                // 右上角关闭按钮 - 调整位置
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .offset(x = 2.dp, y = 4.dp) // 往右移动多一点，往下调一点
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 带有权限管理集成的Shizuku提示卡片
 *
 * 自动处理Shizuku权限申请逻辑的便捷组件
 *
 * @param visible 是否显示卡片
 * @param onDismiss 关闭卡片的回调
 * @param modifier 修饰符
 */
@Composable
fun ShizukuTipCardWithPermission(
    visible: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val globalPermissionManager = remember { GlobalPermissionManager.getInstance(context) }

    ShizukuTipCard(
        visible = visible,
        onDismiss = onDismiss,
        onOpenShizuku = {
            // 直接申请Shizuku权限，跳过确认对话框
            globalPermissionManager.requestPermissionAfterConfirmation(
                GlobalPermissionManager.PermissionType.SHIZUKU
            )
        },
        modifier = modifier
    )
}

package com.weinuo.quickcommands.storage

import android.util.Log
import com.weinuo.quickcommands.model.SimpleAppInfo

/**
 * 应用列表存储引擎
 *
 * 专门处理List<SimpleAppInfo>的拆分存储和重建，解决最常见的序列化失败问题。
 * 将应用列表拆分为独立的原生数据类型字段进行存储，完全避免JSON序列化。
 *
 * 核心特性：
 * - 字段拆分存储：将每个SimpleAppInfo的字段分别存储为原生类型
 * - 索引化管理：使用计数+索引的方式管理列表结构
 * - 增量更新：支持单个应用信息的更新，无需重写整个列表
 * - 数据完整性：提供数据验证和修复功能
 * - 性能优化：大列表的分批处理和缓存机制
 *
 * 存储格式示例：
 * app_list_{key}_count = 3
 * app_list_{key}_index_0_package_name = "com.example.app1"
 * app_list_{key}_index_0_app_name = "应用1"
 * app_list_{key}_index_0_is_system_app = false
 * app_list_{key}_index_0_is_running = true
 *
 * @param storageManager 原生类型存储管理器
 */
class AppListStorageEngine(private val storageManager: NativeTypeStorageManager) {

    companion object {
        private const val TAG = "AppListStorageEngine"

        // 字段名常量
        private const val FIELD_COUNT = "count"
        private const val FIELD_PACKAGE_NAME = "package_name"
        private const val FIELD_APP_NAME = "app_name"
        private const val FIELD_IS_SYSTEM_APP = "is_system_app"
        private const val FIELD_IS_RUNNING = "is_running"

        // 批处理大小，避免一次性处理过多数据
        private const val BATCH_SIZE = 50
    }

    /**
     * 保存应用列表
     * 将List<SimpleAppInfo>拆分为独立字段存储
     *
     * @param key 列表键名（用于区分不同的应用列表）
     * @param apps 应用信息列表
     * @return 操作是否成功
     */
    fun saveAppList(key: String, apps: List<SimpleAppInfo>): Boolean {
        if (key.isBlank()) {
            Log.e(TAG, "Invalid key: key cannot be blank")
            return false
        }

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存应用数量
            operations.add(StorageOperation.createIntOperation(
                StorageDomain.APP_LISTS,
                StorageKeyGenerator.generateAppListKey(key, FIELD_COUNT),
                apps.size
            ))

            // 保存每个应用的详细信息
            apps.forEachIndexed { index, app ->
                val indexPrefix = "index_${index}_"

                operations.addAll(listOf(
                    StorageOperation.createStringOperation(
                        StorageDomain.APP_LISTS,
                        StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_PACKAGE_NAME}"),
                        app.packageName
                    ),
                    StorageOperation.createStringOperation(
                        StorageDomain.APP_LISTS,
                        StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_APP_NAME}"),
                        app.appName
                    ),
                    StorageOperation.createBooleanOperation(
                        StorageDomain.APP_LISTS,
                        StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_SYSTEM_APP}"),
                        app.isSystemApp
                    ),
                    StorageOperation.createBooleanOperation(
                        StorageDomain.APP_LISTS,
                        StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_RUNNING}"),
                        app.isRunning
                    )
                ))
            }

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "Successfully saved app list: key=$key, count=${apps.size}")
            } else {
                Log.e(TAG, "Failed to save app list: key=$key")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "Exception while saving app list: key=$key", e)
            false
        }
    }

    /**
     * 加载应用列表
     * 从拆分的字段重建List<SimpleAppInfo>
     *
     * @param key 列表键名
     * @return 应用信息列表，失败时返回空列表
     */
    fun loadAppList(key: String): List<SimpleAppInfo> {
        if (key.isBlank()) {
            Log.e(TAG, "Invalid key: key cannot be blank")
            return emptyList()
        }

        return try {
            val countKey = StorageKeyGenerator.generateAppListKey(key, FIELD_COUNT)
            val count = storageManager.loadInt(StorageDomain.APP_LISTS, countKey, 0)

            if (count == 0) {
                Log.d(TAG, "No apps found for key: $key")
                return emptyList()
            }

            val apps = mutableListOf<SimpleAppInfo>()

            for (index in 0 until count) {
                val indexPrefix = "index_${index}_"

                val packageName = storageManager.loadString(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_PACKAGE_NAME}")
                )
                val appName = storageManager.loadString(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_APP_NAME}")
                )
                val isSystemApp = storageManager.loadBoolean(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_SYSTEM_APP}")
                )
                val isRunning = storageManager.loadBoolean(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_RUNNING}")
                )

                // 只有包名不为空才创建应用信息对象
                if (packageName.isNotEmpty()) {
                    apps.add(SimpleAppInfo(
                        packageName = packageName,
                        appName = appName,
                        isSystemApp = isSystemApp,
                        isRunning = isRunning,
                        icon = null // 图标需要在使用时重新加载
                    ))
                } else {
                    Log.w(TAG, "Skipping app at index $index due to empty package name")
                }
            }

            Log.d(TAG, "Successfully loaded app list: key=$key, count=${apps.size}")
            apps

        } catch (e: Exception) {
            Log.e(TAG, "Exception while loading app list: key=$key", e)
            emptyList()
        }
    }

    /**
     * 更新应用列表中的单个应用信息
     * 增量更新，无需重写整个列表
     *
     * @param key 列表键名
     * @param index 应用在列表中的索引
     * @param app 新的应用信息
     * @return 操作是否成功
     */
    fun updateAppInList(key: String, index: Int, app: SimpleAppInfo): Boolean {
        if (key.isBlank()) {
            Log.e(TAG, "Invalid key: key cannot be blank")
            return false
        }

        if (index < 0) {
            Log.e(TAG, "Invalid index: $index")
            return false
        }

        return try {
            val indexPrefix = "index_${index}_"

            val operations = listOf(
                StorageOperation.createStringOperation(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_PACKAGE_NAME}"),
                    app.packageName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_APP_NAME}"),
                    app.appName
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_SYSTEM_APP}"),
                    app.isSystemApp
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_RUNNING}"),
                    app.isRunning
                )
            )

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "Successfully updated app at index $index: key=$key, package=${app.packageName}")
            } else {
                Log.e(TAG, "Failed to update app at index $index: key=$key")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "Exception while updating app at index $index: key=$key", e)
            false
        }
    }

    /**
     * 删除应用列表
     * 删除指定键名的所有相关数据
     *
     * @param key 列表键名
     * @return 操作是否成功
     */
    fun deleteAppList(key: String): Boolean {
        if (key.isBlank()) {
            Log.e(TAG, "Invalid key: key cannot be blank")
            return false
        }

        return try {
            val prefix = StorageKeyGenerator.getAppListPrefix(key)
            val success = storageManager.deleteByPrefix(StorageDomain.APP_LISTS, prefix)
            if (success) {
                Log.d(TAG, "Successfully deleted app list: key=$key")
            } else {
                Log.e(TAG, "Failed to delete app list: key=$key")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Exception while deleting app list: key=$key", e)
            false
        }
    }

    /**
     * 检查应用列表是否存在
     *
     * @param key 列表键名
     * @return 列表是否存在
     */
    fun containsAppList(key: String): Boolean {
        if (key.isBlank()) {
            return false
        }

        val countKey = StorageKeyGenerator.generateAppListKey(key, FIELD_COUNT)
        return storageManager.containsKey(StorageDomain.APP_LISTS, countKey)
    }

    /**
     * 获取应用列表的大小
     *
     * @param key 列表键名
     * @return 列表大小，不存在时返回0
     */
    fun getAppListSize(key: String): Int {
        if (key.isBlank()) {
            return 0
        }

        val countKey = StorageKeyGenerator.generateAppListKey(key, FIELD_COUNT)
        return storageManager.loadInt(StorageDomain.APP_LISTS, countKey, 0)
    }

    /**
     * 验证应用列表数据完整性
     * 检查是否有缺失或损坏的数据
     *
     * @param key 列表键名
     * @return 验证结果
     */
    fun validateAppListIntegrity(key: String): AppListValidationResult {
        if (key.isBlank()) {
            return AppListValidationResult(false, "Invalid key")
        }

        return try {
            val countKey = StorageKeyGenerator.generateAppListKey(key, FIELD_COUNT)
            val count = storageManager.loadInt(StorageDomain.APP_LISTS, countKey, 0)

            if (count == 0) {
                return AppListValidationResult(true, "Empty list")
            }

            val issues = mutableListOf<String>()

            for (index in 0 until count) {
                val indexPrefix = "index_${index}_"

                val packageNameKey = StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_PACKAGE_NAME}")
                val appNameKey = StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_APP_NAME}")
                val isSystemAppKey = StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_SYSTEM_APP}")
                val isRunningKey = StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_RUNNING}")

                if (!storageManager.containsKey(StorageDomain.APP_LISTS, packageNameKey)) {
                    issues.add("Missing package name for index $index")
                }
                if (!storageManager.containsKey(StorageDomain.APP_LISTS, appNameKey)) {
                    issues.add("Missing app name for index $index")
                }
                if (!storageManager.containsKey(StorageDomain.APP_LISTS, isSystemAppKey)) {
                    issues.add("Missing system app flag for index $index")
                }
                if (!storageManager.containsKey(StorageDomain.APP_LISTS, isRunningKey)) {
                    issues.add("Missing running flag for index $index")
                }

                val packageName = storageManager.loadString(StorageDomain.APP_LISTS, packageNameKey)
                if (packageName.isEmpty()) {
                    issues.add("Empty package name for index $index")
                }
            }

            AppListValidationResult(issues.isEmpty(), if (issues.isEmpty()) "Valid" else issues.joinToString("; "))

        } catch (e: Exception) {
            Log.e(TAG, "Exception while validating app list: key=$key", e)
            AppListValidationResult(false, "Validation failed: ${e.message}")
        }
    }

    /**
     * 修复应用列表数据
     * 尝试修复损坏或不完整的数据
     *
     * @param key 列表键名
     * @return 修复是否成功
     */
    fun repairAppList(key: String): Boolean {
        if (key.isBlank()) {
            Log.e(TAG, "Invalid key for repair: key cannot be blank")
            return false
        }

        return try {
            val validationResult = validateAppListIntegrity(key)
            if (validationResult.isValid) {
                Log.d(TAG, "App list is already valid: key=$key")
                return true
            }

            Log.w(TAG, "Attempting to repair app list: key=$key, issues=${validationResult.message}")

            // 加载现有的有效应用数据
            val validApps = mutableListOf<SimpleAppInfo>()
            val countKey = StorageKeyGenerator.generateAppListKey(key, FIELD_COUNT)
            val count = storageManager.loadInt(StorageDomain.APP_LISTS, countKey, 0)

            for (index in 0 until count) {
                val indexPrefix = "index_${index}_"

                val packageName = storageManager.loadString(
                    StorageDomain.APP_LISTS,
                    StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_PACKAGE_NAME}")
                )

                if (packageName.isNotEmpty()) {
                    val appName = storageManager.loadString(
                        StorageDomain.APP_LISTS,
                        StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_APP_NAME}")
                    )
                    val isSystemApp = storageManager.loadBoolean(
                        StorageDomain.APP_LISTS,
                        StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_SYSTEM_APP}")
                    )
                    val isRunning = storageManager.loadBoolean(
                        StorageDomain.APP_LISTS,
                        StorageKeyGenerator.generateAppListKey(key, "${indexPrefix}${FIELD_IS_RUNNING}")
                    )

                    validApps.add(SimpleAppInfo(packageName, appName, isSystemApp, isRunning))
                }
            }

            // 删除旧数据并重新保存有效数据
            deleteAppList(key)
            val success = saveAppList(key, validApps)

            if (success) {
                Log.d(TAG, "Successfully repaired app list: key=$key, recovered ${validApps.size} apps")
            } else {
                Log.e(TAG, "Failed to repair app list: key=$key")
            }

            success

        } catch (e: Exception) {
            Log.e(TAG, "Exception while repairing app list: key=$key", e)
            false
        }
    }
}

/**
 * 应用列表验证结果
 *
 * @property isValid 数据是否有效
 * @property message 验证消息或错误描述
 */
data class AppListValidationResult(
    val isValid: Boolean,
    val message: String
)

package com.weinuo.quickcommands.ui.screens.themed

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.weinuo.quickcommands.ui.screens.oceanblue.OceanBluePhoneCheckupScreen
import com.weinuo.quickcommands.ui.screens.skyblue.SkyBluePhoneCheckupScreen
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.system.DesignApproach

/**
 * 主题感知的手机体检界面
 * 
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用分层设计风格（OceanBluePhoneCheckupScreen）
 * - 天空蓝主题：使用整合设计风格（SkyBluePhoneCheckupScreen）
 * - 其他主题：使用通用实现（PhoneCheckupScreen）
 * 
 * 这个组件将主题选择逻辑从MainActivity中分离出来，
 * 让不同主题可以有完全不同的手机体检界面实现。
 */
@Composable
fun ThemedPhoneCheckupScreen(
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    // 根据设计方法选择不同的界面实现
    when (themeContext.designApproach) {
        DesignApproach.LAYERED_DESIGN -> {
            // 海洋蓝主题：分层设计风格
            OceanBluePhoneCheckupScreen(modifier = modifier)
        }
        
        DesignApproach.INTEGRATED_DESIGN -> {
            // 天空蓝主题：整合设计风格
            SkyBluePhoneCheckupScreen(modifier = modifier)
        }
        
        DesignApproach.MINIMAL_DESIGN -> {
            // 未来的极简设计：暂时降级到海洋蓝主题
            // TODO: 实现 MinimalPhoneCheckupScreen
            OceanBluePhoneCheckupScreen(modifier = modifier)
        }

        DesignApproach.DYNAMIC_DESIGN -> {
            // 未来的动态设计：暂时降级到天空蓝主题
            // TODO: 实现 DynamicPhoneCheckupScreen
            SkyBluePhoneCheckupScreen(modifier = modifier)
        }
    }
}

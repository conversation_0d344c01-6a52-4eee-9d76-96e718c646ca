package com.weinuo.quickcommands.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog

/**
 * 媒体权限工具类
 * 管理媒体相关权限的检查、申请和引导
 * 适用于录音、文件访问等媒体功能
 */
object MediaPermissionUtil {

    /**
     * 录音权限
     */
    private const val RECORD_AUDIO_PERMISSION = Manifest.permission.RECORD_AUDIO

    /**
     * 媒体相关权限列表
     */
    private val MEDIA_PERMISSIONS = arrayOf(
        RECORD_AUDIO_PERMISSION
    )

    /**
     * 检查是否拥有录音权限
     * @param context 上下文
     * @return 是否拥有录音权限
     */
    fun hasRecordAudioPermission(context: Context): Bo<PERSON>an {
        return ContextCompat.checkSelfPermission(
            context,
            RECORD_AUDIO_PERMISSION
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 检查是否拥有所有媒体权限
     * @param context 上下文
     * @return 是否拥有所有媒体权限
     */
    fun hasAllMediaPermissions(context: Context): Boolean {
        return MEDIA_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查是否拥有录音相关的必要权限
     * @param context 上下文
     * @return 是否拥有录音相关权限
     */
    fun hasRequiredPermissions(context: Context): Boolean {
        return hasRecordAudioPermission(context)
    }

    /**
     * 申请媒体权限
     * @param launcher 权限申请启动器
     */
    fun requestMediaPermissions(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(MEDIA_PERMISSIONS)
    }

    /**
     * 申请录音权限
     * @param launcher 权限申请启动器
     */
    fun requestRecordAudioPermission(launcher: ActivityResultLauncher<Array<String>>) {
        launcher.launch(arrayOf(RECORD_AUDIO_PERMISSION))
    }

    /**
     * 创建权限申请启动器
     * 在Activity中使用，用于申请媒体权限
     */
    fun createPermissionLauncher(
        activity: ComponentActivity,
        onPermissionsResult: (Map<String, Boolean>) -> Unit
    ): ActivityResultLauncher<Array<String>> {
        return activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            onPermissionsResult(permissions)
        }
    }

    /**
     * 打开应用详情设置页面
     * @param context 上下文
     */
    fun openAppDetailsSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在非Activity context中也能正常启动
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 忽略异常，用户可以手动到设置中授权
        }
    }

    /**
     * 获取需要的媒体权限列表
     * @return 媒体权限数组
     */
    fun getRequiredMediaPermissions(): Array<String> {
        return MEDIA_PERMISSIONS
    }

    /**
     * 检查是否拥有麦克风权限（录音权限的别名）
     * @param context 上下文
     * @return 是否拥有麦克风权限
     */
    fun hasMicrophonePermission(context: Context): Boolean {
        return hasRecordAudioPermission(context)
    }

    /**
     * 申请麦克风权限（录音权限的别名）
     * @param launcher 权限申请启动器
     */
    fun requestMicrophonePermission(launcher: ActivityResultLauncher<Array<String>>) {
        requestRecordAudioPermission(launcher)
    }

    /**
     * 获取需要的麦克风权限列表
     * @return 麦克风权限数组
     */
    fun getRequiredMicrophonePermissions(): Array<String> {
        return arrayOf(RECORD_AUDIO_PERMISSION)
    }

    /**
     * 录音权限被拒绝后的说明对话框
     */
    @Composable
    fun RecordAudioPermissionDeniedDialog(
        onOpenSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "录音权限被拒绝",
            message = """
                录音功能需要录音权限才能正常工作。

                如果您想使用以下功能，请在系统设置中手动授予录音权限：
                • 麦克风录音
                • 录制音频文件
                • 语音相关的媒体功能

                您可以点击"打开设置"直接跳转到应用权限设置页面。
            """.trimIndent(),
            confirmText = "打开设置",
            dismissText = "取消",
            onConfirm = onOpenSettings,
            onDismiss = onDismiss
        )
    }

    /**
     * 媒体权限说明对话框
     */
    @Composable
    fun MediaPermissionExplanationDialog(
        onRequestPermission: () -> Unit,
        onDismiss: () -> Unit
    ) {
        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "需要录音权限",
            message = """
                为了使用录音功能，应用需要麦克风权限：

                • 录音权限：用于麦克风录音功能
                • 语音搜索：启动语音搜索功能
                • 语音识别：处理语音输入

                这些权限仅用于您主动触发的录音任务，不会在后台自动录音。
            """.trimIndent(),
            confirmText = "申请权限",
            dismissText = "取消",
            onConfirm = onRequestPermission,
            onDismiss = onDismiss
        )
    }
}

package com.weinuo.quickcommands.ui.screens.oceanblue

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.CheckBox
import androidx.compose.material.icons.filled.CheckBoxOutlineBlank
import androidx.compose.material3.ExperimentalMaterial3Api
import com.weinuo.quickcommands.ui.components.themed.ThemedFloatingActionButton
import com.weinuo.quickcommands.ui.theme.config.FloatingActionButtonConfig
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import com.weinuo.quickcommands.ui.components.themed.ThemedScaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.QuickCommandRepository
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.model.QuickCommand
import com.weinuo.quickcommands.model.CameraTask
import com.weinuo.quickcommands.model.CameraOperation
import com.weinuo.quickcommands.model.MediaTask
import com.weinuo.quickcommands.model.MediaOperation
import com.weinuo.quickcommands.model.ApplicationTask
import com.weinuo.quickcommands.model.ApplicationOperation
import com.weinuo.quickcommands.model.FileOperationTask
import com.weinuo.quickcommands.model.FileOperation
import com.weinuo.quickcommands.model.AppStateCondition
import com.weinuo.quickcommands.model.AppStateType
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.shortcut.ShortcutManager
import com.weinuo.quickcommands.ui.activities.QuickCommandFormActivity
import com.weinuo.quickcommands.ui.components.layered.LayeredTopAppBar
import com.weinuo.quickcommands.ui.components.themed.ThemedQuickCommandCard
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands.ui.components.QuickCommandSelectionBottomAppBar
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog
import com.weinuo.quickcommands.ui.components.themed.ThemedSearchTextField

import com.weinuo.quickcommands.ui.components.rememberSelectionState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 海洋蓝主题专用 - 快捷指令屏幕
 *
 * 特点：
 * - 分层设计风格
 * - 使用标准Material 3组件
 * - 清晰的视觉层次
 *
 * 显示用户创建的快捷指令列表，并提供创建新指令的功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OceanBlueQuickCommandsScreen(
    navController: NavController,
    quickCommandRepository: QuickCommandRepository = QuickCommandRepository.getInstance(navController.context),
    shortcutManager: ShortcutManager = ShortcutManager(navController.context)
) {
    // 顶部应用栏滚动行为
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior(
        rememberTopAppBarState()
    )
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current

    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }

    // 获取实验性功能状态
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 获取指令列表
    val allQuickCommands by quickCommandRepository.quickCommands.collectAsState()

    // 过滤包含实验性功能的快捷指令
    val quickCommands = remember(allQuickCommands, globalSettings.experimentalFeaturesEnabled) {
        if (globalSettings.experimentalFeaturesEnabled) {
            // 实验性功能启用时，显示所有指令
            allQuickCommands
        } else {
            // 实验性功能未启用时，过滤掉包含实验性功能的指令
            allQuickCommands.filter { command ->
                !containsExperimentalFeatures(command)
            }
        }
    }

    // 根据搜索查询过滤快捷指令
    val filteredQuickCommands = remember(quickCommands, searchQuery) {
        if (searchQuery.isEmpty()) {
            quickCommands
        } else {
            quickCommands.filter { command ->
                command.name.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    // 新建指令对话框已移除，现在直接启动Activity

    // 控制删除确认对话框的显示状态
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var quickCommandToDelete by remember { mutableStateOf<QuickCommand?>(null) }

    // 控制批量删除确认对话框的显示状态
    var showBatchDeleteConfirmDialog by remember { mutableStateOf(false) }

    // 选择模式状态管理
    val selectionState = rememberSelectionState()

    // 缓存所有指令ID列表，避免重组时重新计算
    val allCommandIds = remember(filteredQuickCommands) {
        filteredQuickCommands.map { it.id }
    }

    Scaffold(
        topBar = {
            // 根据是否处于选择模式显示不同的顶部应用栏
            AnimatedVisibility(
                visible = !selectionState.isInSelectionMode,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                LayeredTopAppBar(
                    config = TopAppBarConfig(
                        title = stringResource(R.string.quick_commands_title),
                        style = TopAppBarStyle.STANDARD,
                        collapsible = true,
                        scrollBehavior = scrollBehavior,
                        windowInsets = WindowInsets.statusBars
                    )
                )
            }

            // 选择模式下显示选择模式顶部应用栏（已删除，使用简化版本）
            AnimatedVisibility(
                visible = selectionState.isInSelectionMode,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                TopAppBar(
                    title = { Text("已选择 ${selectionState.selectedCount} 项") },
                    navigationIcon = {
                        IconButton(onClick = { selectionState.exitSelectionMode() }) {
                            Icon(Icons.Default.Close, contentDescription = "退出选择模式")
                        }
                    },
                    actions = {
                        IconButton(
                            onClick = {
                                if (selectionState.isAllSelected(allCommandIds)) {
                                    selectionState.deselectAll()
                                } else {
                                    selectionState.selectAll(allCommandIds)
                                }
                            }
                        ) {
                            Icon(
                                if (selectionState.isAllSelected(allCommandIds)) Icons.Default.CheckBoxOutlineBlank else Icons.Default.CheckBox,
                                contentDescription = if (selectionState.isAllSelected(allCommandIds)) "取消全选" else "全选"
                            )
                        }
                    }
                )
            }
        },
        floatingActionButton = {
            // 非选择模式下显示添加按钮
            AnimatedVisibility(
                visible = !selectionState.isInSelectionMode,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                ThemedFloatingActionButton(
                    config = FloatingActionButtonConfig(
                        onClick = {
                            // 启动快捷指令表单Activity（创建模式）
                            QuickCommandFormActivity.startForCreate(context)
                        },
                        content = {
                            Icon(
                                imageVector = Icons.Filled.Add,
                                contentDescription = stringResource(R.string.quick_command_new)
                            )
                        }
                    )
                )
            }
        },
        bottomBar = {
            // 选择模式下显示底部应用栏
            AnimatedVisibility(
                visible = selectionState.isInSelectionMode,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                QuickCommandSelectionBottomAppBar(
                    onDelete = {
                        // 显示批量删除确认对话框
                        if (selectionState.selectedItems.isNotEmpty()) {
                            showBatchDeleteConfirmDialog = true
                        }
                    }
                )
            }
        },
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection)
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = {
                    searchQuery = it
                    // 在选择模式下，如果开始搜索则退出选择模式
                    if (selectionState.isInSelectionMode && it.isNotEmpty()) {
                        selectionState.exitSelectionMode()
                    }
                },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = stringResource(R.string.search_quick_commands),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )

            if (quickCommands.isEmpty()) {
                // 显示空状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(R.string.no_quick_commands),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            } else if (filteredQuickCommands.isEmpty()) {
                // 显示搜索无结果状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(R.string.no_search_results),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            } else {
                // 显示快捷指令列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    contentPadding = PaddingValues(bottom = 80.dp) // 为底部导航栏留出空间
                ) {
                    item {
                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    // 显示快捷指令
                    items(filteredQuickCommands) { command ->
                        ThemedQuickCommandCard(
                            command = command,
                            onClick = { clickedCommand ->
                                // 启动快捷指令表单Activity（编辑模式）
                                QuickCommandFormActivity.startForEdit(context, clickedCommand.id)
                            },
                            onLongClick = {
                                if (!selectionState.isInSelectionMode) {
                                    selectionState.enterSelectionMode()
                                    selectionState.toggleSelection(command.id)
                                }
                            },
                            onEnabledChanged = { isEnabled ->
                                // 更新快捷指令的启用状态（使用协程）
                                val updatedCommand = command.copy(isEnabled = isEnabled)
                                CoroutineScope(Dispatchers.IO).launch {
                                    quickCommandRepository.saveCommand(updatedCommand)
                                }
                            },
                            isInSelectionMode = selectionState.isInSelectionMode,
                            isSelected = selectionState.isSelected(command.id),
                            onSelectionToggle = {
                                selectionState.toggleSelection(command.id)
                            }
                        )

                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    item {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }
    }

    // 新建指令对话框已移除，现在直接启动Activity

    // 删除确认对话框
    if (showDeleteConfirmDialog && quickCommandToDelete != null) {
        ScrollableAlertDialog(
            onDismissRequest = {
                showDeleteConfirmDialog = false
                quickCommandToDelete = null
            },
            title = { Text(stringResource(R.string.delete_quick_command)) },
            text = {
                Column {
                    Text(stringResource(R.string.delete_quick_command_confirm, quickCommandToDelete!!.name))
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(stringResource(R.string.delete_quick_command_warning))
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(stringResource(R.string.delete_operation_irreversible))
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        val commandId = quickCommandToDelete!!.id

                        // 从存储中删除指令数据（使用协程）
                        CoroutineScope(Dispatchers.IO).launch {
                            quickCommandRepository.deleteCommand(commandId)
                        }

                        // 关闭对话框
                        showDeleteConfirmDialog = false
                        quickCommandToDelete = null
                    }
                ) {
                    Text(stringResource(R.string.delete))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeleteConfirmDialog = false
                        quickCommandToDelete = null
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }

    // 批量删除确认对话框
    if (showBatchDeleteConfirmDialog) {
        ScrollableAlertDialog(
            onDismissRequest = {
                showBatchDeleteConfirmDialog = false
            },
            title = { Text(stringResource(R.string.batch_delete_quick_commands)) },
            text = {
                Column {
                    Text(stringResource(R.string.batch_delete_confirm, selectionState.selectedCount))
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(stringResource(R.string.batch_delete_warning))
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(stringResource(R.string.delete_operation_irreversible))
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        // 获取所有选中的指令ID
                        val selectedIds = selectionState.selectedItems.toList()

                        // 删除选中的快捷指令（使用协程）
                        CoroutineScope(Dispatchers.IO).launch {
                            quickCommandRepository.deleteCommands(selectedIds)
                        }

                        // 关闭对话框并退出选择模式
                        showBatchDeleteConfirmDialog = false
                        selectionState.exitSelectionMode()
                    }
                ) {
                    Text(stringResource(R.string.delete))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showBatchDeleteConfirmDialog = false
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 检查快捷指令是否包含实验性功能
 *
 * @param command 要检查的快捷指令
 * @return 如果包含实验性功能返回true，否则返回false
 */
private fun containsExperimentalFeatures(command: QuickCommand): Boolean {
    // 检查任务列表中是否包含实验性功能
    val hasExperimentalTasks = command.tasks.any { task ->
        when (task) {
            is CameraTask -> {
                // 检查是否是实验性的相机操作（拍照、录像）
                task.operation == CameraOperation.TAKE_PHOTO ||
                task.operation == CameraOperation.RECORD_VIDEO
            }
            is MediaTask -> {
                // 检查是否是实验性的媒体操作（麦克风录音）
                task.operation == MediaOperation.MICROPHONE_RECORDING
            }
            is ApplicationTask -> {
                // 检查是否使用了实验性的应用任务功能（跳过VPN应用）
                task.operation == ApplicationOperation.FORCE_STOP_APP && task.skipVpnApp
            }
            is FileOperationTask -> {
                // 检查是否是实验性的文件操作（文件管理，但不包括写入文件和打开文件）
                task.operation == FileOperation.FILE_OPERATION
            }
            else -> false
        }
    }

    // 检查触发条件中是否包含实验性功能
    val hasExperimentalTriggerConditions = command.triggerConditions.any { condition ->
        when (condition) {
            is AppStateCondition -> {
                // 检查是否是后台时间条件且使用了VPN配置
                condition.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED && condition.skipVpnApp
            }
            else -> false
        }
    }

    // 检查中止条件中是否包含实验性功能
    val hasExperimentalAbortConditions = command.abortConditions.any { condition ->
        when (condition) {
            is AppStateCondition -> {
                // 检查是否是后台时间条件且使用了VPN配置
                condition.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED && condition.skipVpnApp
            }
            else -> false
        }
    }

    return hasExperimentalTasks || hasExperimentalTriggerConditions || hasExperimentalAbortConditions
}



package com.weinuo.quickcommands.ui.effects

import android.os.Build
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.config.BlurComponent
import com.weinuo.quickcommands.ui.theme.manager.BlurConfigurationManager
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.materials.HazeMaterials

/**
 * iOS风格背景模糊修饰符
 *
 * 提供真正的iOS风格毛玻璃效果，支持所有Android版本
 * 包括Android 11及以下的实验性RenderScript模糊
 *
 * 核心概念：
 * - hazeSource: 标记内容为模糊源（被模糊的内容）
 * - hazeEffect: 应用模糊效果（模糊其下方的hazeSource内容）
 *
 * Android版本支持：
 * - Android 14+ (API 33+): RenderEffect，最佳性能
 * - Android 12-13 (API 31-32): RenderEffect with workarounds
 * - Android 12及以下 (API ≤31): 手动失效机制
 * - Android 11及以下 (API ≤30): 实验性RenderScript模糊
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */

/**
 * 为内容标记为模糊源
 * 
 * 应用于主要内容区域（如列表、图片等），这些内容会被导航栏模糊
 * 
 * @param hazeState 连接source和effect的状态管理器
 * @param zIndex 层级索引，默认为0f（背景层）
 * @return 修饰符
 */
@Composable
fun Modifier.backgroundBlurSource(
    hazeState: HazeState,
    zIndex: Float = 0f
): Modifier {
    Log.d("BackgroundBlurModifier", "backgroundBlurSource: 设置hazeSource，zIndex=$zIndex")
    return this.hazeSource(
        state = hazeState,
        zIndex = zIndex
    )
}

/**
 * 应用背景模糊效果
 *
 * 应用于导航栏、工具栏等需要模糊背景的组件
 *
 * @param hazeState 连接source和effect的状态管理器
 * @param style 模糊样式，null表示不应用模糊
 * @param backgroundColor 背景颜色，当不使用模糊时的备用颜色
 * @param component 模糊组件类型，用于获取特定的背景透明度配置
 * @return 修饰符
 */
@Composable
fun Modifier.backgroundBlurEffect(
    hazeState: HazeState,
    style: HazeStyle?,
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    component: BlurComponent? = null
): Modifier = composed {
    val context = LocalContext.current
    val blurConfigManager = remember { BlurConfigurationManager.getInstance(context) }
    val blurConfig = blurConfigManager.getBlurConfiguration()

    // 获取组件特定的背景透明度，如果没有指定组件则使用顶部栏的配置作为默认值
    val backgroundAlpha = component?.let { blurConfig.getComponentBackgroundAlpha(it) }
        ?: blurConfig.getComponentBackgroundAlpha(BlurComponent.TOP_BAR)

    // 获取模糊样式类型
    val blurStyle = component?.let { blurConfig.getComponentBlurStyle(it) } ?: "preset"
    val isBlurEnabled = component?.let { blurConfig.isComponentBlurEnabled(it) } ?: true

    Log.d("BackgroundBlurModifier", "backgroundBlurEffect: style=${style != null}, blurStyle=$blurStyle, isBlurEnabled=$isBlurEnabled, backgroundColor=$backgroundColor, component=$component, backgroundAlpha=$backgroundAlpha")

    if (isBlurEnabled) {
        when (blurStyle) {
            "preset" -> {
                // 使用预设材质
                if (style != null) {
                    Log.d("BackgroundBlurModifier", "应用预设模糊效果，style=$style, component=$component")
                    this
                        .background(backgroundColor.copy(alpha = backgroundAlpha))
                        .hazeEffect(
                            state = hazeState,
                            style = style
                        ) {
                            blurEnabled = true
                            Log.d("BackgroundBlurModifier", "预设模糊效果配置: blurEnabled=true, Android API=${Build.VERSION.SDK_INT}")
                        }
                } else {
                    this.background(backgroundColor)
                }
            }
            "custom" -> {
                // 使用自定义样式，通过BlurConfigurationManager创建自定义HazeStyle
                if (component != null) {
                    val customStyle = blurConfigManager.getCustomHazeStyle(component)
                    Log.d("BackgroundBlurModifier", "应用自定义模糊效果，component=$component, customStyle=$customStyle")
                    this.hazeEffect(
                        state = hazeState,
                        style = customStyle
                    ) {
                        blurEnabled = true
                        Log.d("BackgroundBlurModifier", "自定义模糊效果已应用，使用HazeStyle的tint处理颜色")
                    }
                } else {
                    // 如果没有指定组件，使用默认背景色
                    Log.w("BackgroundBlurModifier", "自定义模糊效果缺少组件信息，使用默认背景色")
                    this.background(backgroundColor)
                }
            }
            else -> {
                // 默认不应用模糊效果
                this.background(backgroundColor)
            }
        }
    } else {
        // 模糊效果未启用，只设置背景色
        Log.d("BackgroundBlurModifier", "模糊效果未启用，只设置背景色: $backgroundColor")
        this.background(backgroundColor)
    }
}

/**
 * 导航栏模糊修饰符
 *
 * 专门为底部导航栏设计的模糊效果
 * 支持所有Android版本，包括Android 11及以下的实验性RenderScript模糊
 *
 * @param enabled 是否启用模糊效果
 * @param intensity 模糊强度 (0.0 - 1.0)
 * @param hazeState 模糊状态管理器
 * @return 修饰符
 */
@Composable
fun Modifier.navigationBarBlur(
    enabled: Boolean,
    intensity: Float = 0.6f,
    hazeState: HazeState
): Modifier = composed {
    val style = if (enabled) {
        getHazeMaterialByIntensity(intensity)
    } else {
        null
    }

    backgroundBlurEffect(
        hazeState = hazeState,
        style = style,
        backgroundColor = MaterialTheme.colorScheme.surface,
        component = BlurComponent.BOTTOM_BAR
    )
}

/**
 * 顶部应用栏模糊修饰符
 *
 * 专门为顶部应用栏设计的模糊效果
 * 支持所有Android版本，包括Android 11及以下的实验性RenderScript模糊
 *
 * @param enabled 是否启用模糊效果
 * @param intensity 模糊强度 (0.0 - 1.0)
 * @param hazeState 模糊状态管理器
 * @return 修饰符
 */
@Composable
fun Modifier.topAppBarBlur(
    enabled: Boolean,
    intensity: Float = 0.6f,
    hazeState: HazeState
): Modifier = composed {
    val style = if (enabled) {
        getHazeMaterialByIntensity(intensity)
    } else {
        null
    }

    backgroundBlurEffect(
        hazeState = hazeState,
        style = style,
        backgroundColor = MaterialTheme.colorScheme.surface,
        component = BlurComponent.TOP_BAR
    )
}

/**
 * 对话框模糊修饰符
 *
 * 专门为对话框设计的模糊效果
 * 支持所有Android版本，包括Android 11及以下的实验性RenderScript模糊
 *
 * @param enabled 是否启用模糊效果
 * @param intensity 模糊强度 (0.0 - 1.0)
 * @param hazeState 模糊状态管理器
 * @return 修饰符
 */
@Composable
fun Modifier.dialogBlur(
    enabled: Boolean,
    intensity: Float = 0.7f,
    hazeState: HazeState
): Modifier = composed {
    val style = if (enabled) {
        getHazeMaterialByIntensity(intensity)
    } else {
        null
    }

    backgroundBlurEffect(
        hazeState = hazeState,
        style = style,
        backgroundColor = MaterialTheme.colorScheme.surface,
        component = BlurComponent.DIALOG
    )
}

/**
 * 根据强度获取Haze材质
 *
 * @param intensity 模糊强度 (0.0 - 1.0)
 * @return 对应的HazeStyle
 */
@Composable
private fun getHazeMaterialByIntensity(intensity: Float): HazeStyle {
    return when (intensity.coerceIn(0f, 1f)) {
        in 0.0f..0.25f -> HazeMaterials.ultraThin()
        in 0.25f..0.5f -> HazeMaterials.thin()
        in 0.5f..0.75f -> HazeMaterials.regular()
        else -> HazeMaterials.thick()
    }
}



# 界面交互功能实现总结

## 功能概述

本次更新成功实现了界面交互相关的条件和任务功能，包括：

### 新增条件类型
1. **界面点击条件** - 检测到对特定文本内容的点击时触发条件
2. **屏幕内容条件** - 当屏幕出现或移出某些文本内容时触发条件

### 新增任务类型
1. **检查屏幕文字任务** - 检查特定文本字符串当前是否显示在屏幕上，检查结果将保存到文本文件中
2. **读取屏幕内容任务** - 将当前屏幕的内容捕获到文本文件中

### 新增无障碍服务
- **快捷指令-界面交互服务** - 专门处理界面交互相关功能的无障碍服务

## 技术实现详情

### 1. 数据模型扩展

#### AppStateType 枚举新增
```kotlin
INTERFACE_CLICK,  // 界面点击（新增）
SCREEN_CONTENT,   // 屏幕内容（新增）
```

#### AppStateCategoryType 枚举新增
```kotlin
INTERFACE_INTERACTION, // 界面交互类：界面点击、屏幕内容（新增）
```

#### ScreenControlOperation 枚举新增
```kotlin
CHECK_SCREEN_TEXT,     // 检查屏幕上的文字
READ_SCREEN_CONTENT    // 读取屏幕内容
```

#### 新增枚举类型
```kotlin
enum class ScreenContentTriggerMode(val displayName: String) {
    APPEAR("出现时触发"),
    DISAPPEAR("消失时触发")
}
```

#### AppStateCondition 数据类扩展
新增界面交互相关字段：
- `clickTargetText: String` - 界面点击目标文本
- `screenContentText: String` - 屏幕内容文本
- `screenContentTriggerMode: ScreenContentTriggerMode` - 屏幕内容触发模式
- `caseSensitive: Boolean` - 是否区分大小写
- `useRegex: Boolean` - 是否使用正则表达式

#### ScreenControlTask 数据类扩展
新增屏幕任务相关字段：
- `checkTextContent: String` - 要检查的文本内容
- `checkTextCaseSensitive: Boolean` - 是否区分大小写
- `checkTextUseRegex: Boolean` - 是否使用正则表达式
- `checkTextOutputFile: String` - 检查结果输出文件路径
- `readContentOutputFile: String` - 屏幕内容输出文件路径

### 2. 无障碍服务实现

#### InterfaceInteractionAccessibilityService
- **按需激活设计** - 默认不监听任何无障碍事件，只在需要时动态配置事件监听
- **条件管理** - 支持注册/取消注册界面交互条件
- **事件处理** - 处理点击事件和内容变化事件
- **文本匹配** - 支持普通文本匹配和正则表达式匹配
- **屏幕内容检测** - 支持屏幕内容出现/消失检测

核心功能：
- `registerCondition()` - 注册界面交互条件
- `unregisterCondition()` - 取消注册条件
- `checkScreenText()` - 检查屏幕文字
- `readScreenContent()` - 读取屏幕内容
- `saveCheckResultToFile()` - 保存检查结果到文件
- `saveContentToFile()` - 保存屏幕内容到文件

### 3. 配置界面实现

#### InterfaceClickConfigContent
界面点击配置内容组件，支持：
- 检测模式选择（任何应用/指定应用）
- 应用选择（支持单选）
- 点击目标文本配置
- 匹配选项配置（区分大小写、正则表达式）

#### ScreenContentConfigContent
屏幕内容配置内容组件，支持：
- 检测模式选择（任何应用/指定应用）
- 应用选择（支持单选）
- 屏幕内容文本配置
- 触发模式配置（出现时触发/消失时触发）
- 匹配选项配置（区分大小写、正则表达式）

#### CheckScreenTextConfigContent & ReadScreenContentConfigContent
屏幕控制任务配置组件，支持：
- 文本内容配置
- 匹配选项配置
- 输出文件路径配置

### 4. 权限管理集成

#### PermissionRegistry 扩展
- 新增 `hasInterfaceInteractionAccessibilityService()` 方法
- 在 `checkAppStatePermissions()` 中添加界面交互权限检查
- 在 `checkScreenControlPermissions()` 中添加新任务权限检查

#### 统一权限检查策略
严格遵循统一权限检查策略：
- 权限检查时机：只在卡片展开前进行权限检查
- 权限检查位置：在ExpandableConfigurationCard的点击处理逻辑中进行
- 权限检查组件：使用PermissionAwareOperationSelector统一权限检查组件
- 配置内容组件职责：专注于UI逻辑，不包含任何权限检查代码

### 5. 执行引擎集成

#### SharedExecutionHandler 扩展
新增任务执行方法：
- `executeCheckScreenText()` - 执行检查屏幕文字任务
- `executeReadScreenContent()` - 执行读取屏幕内容任务

#### SharedConditionEvaluator 扩展
添加对新条件类型的支持，将界面交互条件分流到InterfaceInteractionAccessibilityService处理

#### PermissionRegistry 扩展
- 新增 `hasInterfaceInteractionAccessibilityService()` 方法
- 在 `checkAppStatePermissions()` 中添加界面交互权限检查
- 在 `checkScreenControlPermissions()` 中添加新任务权限检查

#### AppStateConditionMonitor 集成
- 在条件注册时自动注册到InterfaceInteractionAccessibilityService
- 在监听启动时初始化界面交互服务
- 支持条件触发回调

### 6. 服务注册

#### AndroidManifest.xml
新增服务声明：
```xml
<service
    android:name=".service.InterfaceInteractionAccessibilityService"
    android:label="@string/interface_interaction_accessibility_service_name"
    android:description="@string/interface_interaction_accessibility_service_description"
    android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
    android:exported="true">
    <intent-filter>
        <action android:name="android.accessibilityservice.AccessibilityService" />
    </intent-filter>
    <meta-data
        android:name="android.accessibilityservice"
        android:resource="@xml/interface_interaction_accessibility_service_config" />
</service>
```

#### 服务配置文件
创建 `interface_interaction_accessibility_service_config.xml` 配置文件

#### 字符串资源
添加服务名称和描述：
- `interface_interaction_accessibility_service_name`
- `interface_interaction_accessibility_service_description`

## 设计特点

### 1. 按需激活
- 无障碍服务默认不监听任何事件，只在有相关条件注册时才启用对应的事件监听
- 最小化资源消耗和电量使用

### 2. 模块化设计
- 界面交互功能独立封装在专门的无障碍服务中
- 与现有系统松耦合，易于维护和扩展

### 3. 统一权限管理
- 严格遵循项目的统一权限检查策略
- 权限检查逻辑集中管理，避免重复代码

### 4. 可扩展性
- 支持正则表达式匹配，提供更强大的文本匹配能力
- 支持自定义输出文件路径
- 预留了未来功能扩展的接口

## 使用说明

### 界面点击条件
1. 在配置应用状态条件界面选择"界面点击"卡片
2. 选择检测模式（任何应用/指定应用）
3. 如果选择指定应用，需要选择目标应用
4. 输入要检测点击的文本内容
5. 配置匹配选项（区分大小写、正则表达式）

### 屏幕内容条件
1. 在配置应用状态条件界面选择"屏幕内容"卡片
2. 选择检测模式（任何应用/指定应用）
3. 如果选择指定应用，需要选择目标应用
4. 输入要检测的屏幕文本内容
5. 选择触发模式（出现时触发/消失时触发）
6. 配置匹配选项（区分大小写、正则表达式）

### 检查屏幕文字任务
1. 输入要检查的文本内容
2. 配置匹配选项（区分大小写、正则表达式）
3. 设置输出文件路径（可选，留空使用默认路径）

### 读取屏幕内容任务
1. 设置输出文件路径（可选，留空使用默认路径）

## 注意事项

1. **权限要求** - 所有界面交互功能都需要启用"快捷指令-界面交互服务"无障碍服务
2. **性能考虑** - 服务采用按需激活设计，最小化资源消耗
3. **文件权限** - 输出文件需要应用有相应的文件写入权限
4. **兼容性** - 功能依赖Android无障碍服务API，在不同设备上可能有差异

## 总结

本次实现成功添加了完整的界面交互功能，包括条件检测和任务执行，严格遵循了项目的设计原则和编码规范。

### 关键设计决策

1. **独立卡片设计**：界面点击和屏幕内容检测被设计为两个独立的配置卡片，而不是合并在一个界面交互卡片中，这样用户可以更清晰地理解和配置每种功能。

2. **统一权限策略**：严格遵循统一权限检查策略，只在卡片展开前使用PermissionAwareOperationSelector进行权限检查，权限未授予时卡片保持收起状态。

3. **按需激活服务**：无障碍服务采用按需激活设计，默认不监听任何事件，只在有相关条件注册时才启用对应的事件监听，最小化资源消耗。

4. **模块化架构**：界面交互功能独立封装在专门的无障碍服务中，与现有系统松耦合，易于维护和扩展。

功能设计具有良好的可扩展性和可维护性，为后续功能扩展奠定了坚实基础。

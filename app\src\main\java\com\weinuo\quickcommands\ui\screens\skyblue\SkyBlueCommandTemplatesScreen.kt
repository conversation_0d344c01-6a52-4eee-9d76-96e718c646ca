package com.weinuo.quickcommands.ui.screens.skyblue

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import com.weinuo.quickcommands.ui.components.themed.ThemedScaffold

import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.CommandTemplate
import com.weinuo.quickcommands.model.CommandTemplateProvider
import com.weinuo.quickcommands.navigation.Screen

import com.weinuo.quickcommands.ui.components.themed.ThemedCommandTemplateCardWithImage
import com.weinuo.quickcommands.ui.components.integrated.IntegratedTopAppBar
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import com.weinuo.quickcommands.ui.components.themed.ThemedSearchTextField
import com.weinuo.quickcommands.model.DeviceEventCondition
import com.weinuo.quickcommands.model.DeviceActionTask
import com.weinuo.quickcommands.model.ConnectivityTask
import com.weinuo.quickcommands.model.MediaTask
import com.weinuo.quickcommands.model.VolumeTask
import com.weinuo.quickcommands.model.SensorStateCondition
import com.weinuo.quickcommands.data.QuickCommandRepository
import java.util.UUID
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands.ui.activities.QuickCommandFormActivity
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.effects.HazeManager
import com.weinuo.quickcommands.ui.components.integrated.SetIntegratedTopAppBar
import com.weinuo.quickcommands.ui.components.integrated.rememberIntegratedTopAppBarScrollBehavior
import androidx.compose.ui.input.nestedscroll.nestedScroll
// import dev.chrisbanes.haze.hazeSource // 不再需要，由ThemedScaffold内部处理

/**
 * 天空蓝主题专用 - 指令模板界面
 *
 * 展示预设的快捷指令模板，用户可以选择模板快速创建快捷指令
 * 此版本专为天空蓝主题设计，支持整合设计风格
 *
 * @param navController 导航控制器
 * @param quickCommandRepository 快捷指令仓库
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SkyBlueCommandTemplatesScreen(
    navController: NavController,
    quickCommandRepository: QuickCommandRepository
) {
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current

    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }

    // 获取所有模板
    val allTemplates = remember { CommandTemplateProvider.getAllTemplates() }

    // 根据搜索查询过滤模板
    val filteredTemplates = remember(allTemplates, searchQuery, context) {
        if (searchQuery.isEmpty()) {
            allTemplates
        } else {
            allTemplates.filter { template ->
                template.getLocalizedTitle(context).contains(searchQuery, ignoreCase = true) ||
                template.getLocalizedDescription(context).contains(searchQuery, ignoreCase = true) ||
                template.category.getLocalizedDisplayName(context).contains(searchQuery, ignoreCase = true)
            }
        }
    }

    // 按分类分组模板
    val templatesByCategory = remember(filteredTemplates) {
        filteredTemplates.groupBy { it.category }
    }

    // 计算TopAppBar高度，用于内容的初始顶部padding
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 获取页面布局配置（天空蓝主题专用）
    val pageLayoutConfig = SkyBlueStyleConfiguration.getDynamicPageLayoutConfig(settingsRepository)
    val cardStyleConfig = SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)

    val density = LocalDensity.current
    val statusBarHeight = with(density) { WindowInsets.statusBars.getTop(density).toDp() }
    val topAppBarHeight = globalSettings.topAppBarHeight.dp + statusBarHeight // StandardTopAppBar高度

    // 创建LazyColumn滚动状态
    val lazyListState = rememberLazyListState()

    // 创建滚动行为 - 支持可折叠标题栏，只有在内容可滚动时才允许标题栏折叠
    val scrollBehavior = rememberIntegratedTopAppBarScrollBehavior(
        canScroll = {
            // 检查LazyColumn是否可以滚动
            lazyListState.canScrollBackward || lazyListState.canScrollForward
        }
    )

    // 配置TopAppBar - 支持可折叠类型
    SetIntegratedTopAppBar(
        config = TopAppBarConfig(
            title = stringResource(R.string.nav_command_templates),
            style = TopAppBarStyle.STANDARD,
            scrollBehavior = scrollBehavior, // 关键：添加滚动行为
            windowInsets = WindowInsets.statusBars
        )
    )

    // 动态计算顶部padding - 根据标题栏类型优化内容定位
    val topPadding = remember(globalSettings.topAppBarType, statusBarHeight, topAppBarHeight) {
        if (globalSettings.topAppBarType == "collapsible") {
            // 可折叠模式：使用展开状态高度，确保内容不被遮挡
            152.dp + statusBarHeight
        } else {
            // 标准模式：使用固定高度
            topAppBarHeight
        }
    }

    // 直接返回页面内容，TopAppBar由IntegratedMainLayout处理
    Box(modifier = Modifier.fillMaxSize()) {
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(scrollBehavior.nestedScrollConnection),
            contentPadding = PaddingValues(
                top = topPadding, // 动态计算的顶部padding
                bottom = pageLayoutConfig.bottomPadding // 使用动态配置的底部边距
            ),
            verticalArrangement = Arrangement.spacedBy(cardStyleConfig.itemSpacing)
        ) {
            // 搜索框
            item {
                ThemedSearchTextField(
                    searchQuery = searchQuery,
                    onSearchQueryChange = { searchQuery = it },
                    onClearSearch = {
                        searchQuery = ""
                        focusManager.clearFocus()
                    },
                    placeholder = stringResource(R.string.search_templates),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(pageLayoutConfig.searchFieldMargin)
                )
            }

            if (allTemplates.isEmpty()) {
                // 显示空状态
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp), // 给一个固定高度
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "暂无可用模板",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            } else if (filteredTemplates.isEmpty()) {
                // 显示搜索无结果状态
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp), // 给一个固定高度
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "未找到匹配的模板",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            } else {
                // 显示模板列表
                // 直接显示所有模板，不按分类
                items(filteredTemplates) { template ->
                    ThemedCommandTemplateCardWithImage(
                        template = template,
                        onClick = { selectedTemplate ->
                            // 基于模板创建新的快捷指令
                            createCommandFromTemplate(
                                template = selectedTemplate,
                                navController = navController,
                                quickCommandRepository = quickCommandRepository
                            )
                        },
                        modifier = Modifier.padding(horizontal = pageLayoutConfig.contentHorizontalPadding)
                    )
                }
            }
        }
    }
}

/**
 * 基于模板创建快捷指令
 *
 * @param template 选中的模板
 * @param navController 导航控制器
 * @param quickCommandRepository 快捷指令仓库
 */
private fun createCommandFromTemplate(
    template: CommandTemplate,
    navController: NavController,
    quickCommandRepository: QuickCommandRepository
) {
    // 为模板中的指令和所有子项生成新的ID
    val newCommand = template.command.copy(
        id = UUID.randomUUID().toString(),
        triggerConditions = template.command.triggerConditions.map { condition ->
            when (condition) {
                is DeviceEventCondition -> condition.copy(id = UUID.randomUUID().toString())
                is SensorStateCondition -> condition.copy(id = UUID.randomUUID().toString())
                else -> condition // 其他类型条件保持原样，实际使用时需要根据具体类型处理
            }
        },
        tasks = template.command.tasks.map { task ->
            when (task) {
                is DeviceActionTask -> task.copy(id = UUID.randomUUID().toString())
                is ConnectivityTask -> task.copy(id = UUID.randomUUID().toString())
                is MediaTask -> task.copy(id = UUID.randomUUID().toString())
                is VolumeTask -> task.copy(id = UUID.randomUUID().toString())
                else -> task // 其他类型任务保持原样，实际使用时需要根据具体类型处理
            }
        },
        abortConditions = template.command.abortConditions.map { condition ->
            when (condition) {
                is DeviceEventCondition -> condition.copy(id = UUID.randomUUID().toString())
                is SensorStateCondition -> condition.copy(id = UUID.randomUUID().toString())
                else -> condition // 其他类型条件保持原样，实际使用时需要根据具体类型处理
            }
        }
    )

    // 保存新创建的快捷指令（使用协程）
    CoroutineScope(Dispatchers.IO).launch {
        quickCommandRepository.saveCommand(newCommand)
    }

    // 启动快捷指令表单Activity（编辑模式），让用户可以进一步自定义
    QuickCommandFormActivity.startForEdit(navController.context, newCommand.id)
}

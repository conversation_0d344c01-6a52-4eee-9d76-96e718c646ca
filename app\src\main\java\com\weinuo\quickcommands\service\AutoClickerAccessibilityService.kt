package com.weinuo.quickcommands.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.accessibilityservice.GestureDescription
import android.content.Intent
import android.graphics.Path
import android.graphics.PointF
import android.os.Build
import android.os.Looper
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.MotionEvent
import android.view.View
import androidx.annotation.RequiresApi
import kotlinx.coroutines.*
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import com.weinuo.quickcommands.model.QuickOperationType
import com.weinuo.quickcommands.model.getDetailedDescription
import com.weinuo.quickcommands.storage.RecordingInfo
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 快捷指令-自动点击器服务
 *
 * 专门用于自动点击器功能，包括：
 * - 录制用户的触摸操作（点击、滑动、长按等）
 * - 回放录制的操作序列
 * - 管理录制文件（保存、加载、编辑）
 *
 * 采用按需激活设计，最小化资源消耗：
 * - 仅在录制或回放时才激活相关功能
 * - 不监听不必要的无障碍事件
 * - 专注于手势录制和回放功能
 *
 * 技术特点：
 * - 使用AccessibilityService.dispatchGesture()执行手势（Android 7.0+）
 * - 支持多点触控和复杂手势序列
 * - 支持录制文件的导入导出
 * - 支持相对坐标和绝对坐标模式
 */
class AutoClickerAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "AutoClickerService"

        // 服务实例（用于外部调用）
        @Volatile
        private var instance: AutoClickerAccessibilityService? = null

        fun getInstance(): AutoClickerAccessibilityService? = instance

        // 录制状态常量
        const val STATE_IDLE = 0           // 空闲状态
        const val STATE_RECORDING = 1      // 录制中
        const val STATE_PLAYING = 2        // 回放中
        const val STATE_PAUSED = 3         // 暂停状态

        // 操作类型常量
        const val ACTION_START_RECORDING = "action_start_recording"
        const val ACTION_STOP_RECORDING = "action_stop_recording"
        const val ACTION_PAUSE_RECORDING = "action_pause_recording"
        const val ACTION_RESUME_RECORDING = "action_resume_recording"
        const val ACTION_START_PLAYBACK = "action_start_playback"
        const val ACTION_STOP_PLAYBACK = "action_stop_playback"
        const val ACTION_SAVE_RECORDING = "action_save_recording"
        const val ACTION_LOAD_RECORDING = "action_load_recording"

        // 录制回调接口
        interface RecordingCallback {
            fun onRecordingStateChanged(state: Int)
            fun onRecordingProgress(eventCount: Int, duration: Long)
            fun onPlaybackProgress(currentEvent: Int, totalEvents: Int)
            fun onError(error: String)
        }

        // 注册的录制回调
        private val recordingCallbacks = mutableSetOf<RecordingCallback>()

        /**
         * 注册录制回调
         */
        fun registerRecordingCallback(callback: RecordingCallback) {
            recordingCallbacks.add(callback)
            Log.d(TAG, "注册录制回调，当前回调数量: ${recordingCallbacks.size}")
        }

        /**
         * 取消注册录制回调
         */
        fun unregisterRecordingCallback(callback: RecordingCallback) {
            recordingCallbacks.remove(callback)
            Log.d(TAG, "取消注册录制回调，当前回调数量: ${recordingCallbacks.size}")
        }

        /**
         * 检查服务是否可用
         */
        fun isServiceAvailable(): Boolean {
            return instance != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
        }

        /**
         * 检查是否支持手势回放功能
         */
        fun isGesturePlaybackSupported(): Boolean {
            return Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
        }

        /**
         * 获取当前服务状态
         */
        fun getCurrentState(): Int = instance?.currentState ?: STATE_IDLE

        /**
         * 开始录制（静态方法）
         */
        fun startRecording(recordingName: String): Boolean {
            return instance?.let { service ->
                service.startRecording(recordingName)
                true
            } ?: false
        }

        /**
         * 停止录制（静态方法）
         */
        fun stopRecording(): Boolean {
            return instance?.let { service ->
                service.stopRecording()
                true
            } ?: false
        }

        /**
         * 开始回放（静态方法）
         */
        fun startPlayback(filePath: String, speed: Float = 1.0f, loopCount: Int = 1): Boolean {
            return instance?.let { service ->
                service.loadRecording(filePath)
                service.startPlayback(null, loopCount, speed)
                true
            } ?: false
        }

        /**
         * 停止回放（静态方法）
         */
        fun stopPlayback(): Boolean {
            return instance?.let { service ->
                service.stopPlayback()
                true
            } ?: false
        }

        /**
         * 回放录制的手势数据（静态方法）
         */
        fun playbackRecordedGesture(
            gestureData: String,
            loopCount: Int = 1,
            playbackSpeed: Float = 1.0f,
            delayBetweenLoops: Long = 1000L
        ): Boolean {
            return instance?.let { service ->
                service.playbackRecordedGesture(gestureData, loopCount, playbackSpeed, delayBetweenLoops)
            } ?: false
        }

        /**
         * 执行快速操作（静态方法）
         */
        fun executeQuickOperation(
            operationType: QuickOperationType,
            clickX: Float,
            clickY: Float,
            clickCount: Int,
            clickInterval: Long,
            longPressDuration: Long,
            swipeStartX: Float,
            swipeStartY: Float,
            swipeEndX: Float,
            swipeEndY: Float,
            swipeDuration: Long,
            loopCount: Int = 1,
            playbackSpeed: Float = 1.0f,
            delayBetweenLoops: Long = 1000L
        ): Boolean {
            return instance?.let { service ->
                service.executeQuickOperation(
                    operationType, clickX, clickY, clickCount, clickInterval,
                    longPressDuration, swipeStartX, swipeStartY, swipeEndX, swipeEndY,
                    swipeDuration, loopCount, playbackSpeed, delayBetweenLoops
                )
            } ?: false
        }

        /**
         * 获取所有录制信息
         */
        suspend fun getAllRecordings(): List<RecordingInfo> {
            return try {
                val manager = instance?.gestureRecordingManager
                if (manager != null) {
                    manager.getAllRecordings()
                } else {
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取录制列表失败", e)
                emptyList()
            }
        }

        /**
         * 删除录制数据
         */
        suspend fun deleteRecording(recordingId: String): Boolean {
            return try {
                val manager = instance?.gestureRecordingManager
                if (manager != null) {
                    manager.deleteRecording(recordingId)
                } else {
                    false
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除录制失败: $recordingId", e)
                false
            }
        }
    }

    // 当前状态
    @Volatile
    private var currentState = STATE_IDLE

    // 录制相关
    private var recordingStartTime = 0L
    private val recordedEvents = mutableListOf<com.weinuo.quickcommands.model.TouchEvent>()
    private var recordingJob: Job? = null
    private var lastTouchTime = 0L
    private var lastTouchEvent: MotionEvent? = null
    private var touchEventRecorder: TouchEventRecorder? = null
    private var gestureRecordingManager: com.weinuo.quickcommands.storage.GestureRecordingNativeManager? = null

    // 回放相关
    private var playbackJob: Job? = null
    private var currentRecording: List<com.weinuo.quickcommands.model.TouchEvent>? = null

    // 协程作用域
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        Log.d(TAG, "自动点击器服务已连接")

        // 配置服务
        configureService()

        // 初始化触摸事件录制器
        touchEventRecorder = TouchEventRecorder(this, object : TouchEventRecorder.TouchEventCallback {
            override fun onTouchEvent(event: MotionEvent): Boolean {
                return <EMAIL>(event)
            }
        })

        // 初始化手势录制管理器
        gestureRecordingManager = com.weinuo.quickcommands.storage.GestureRecordingNativeManager(this)

        // 检查Android版本兼容性
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            Log.w(TAG, "当前Android版本不支持手势回放功能，需要Android 7.0+")
            notifyError("当前Android版本不支持手势回放功能，需要Android 7.0+")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null

        // 停止所有操作
        stopRecording()
        stopPlayback()

        // 清理触摸事件录制器
        touchEventRecorder?.cleanup()
        touchEventRecorder = null

        // 清理手势录制管理器
        gestureRecordingManager = null

        // 取消协程作用域
        serviceScope.cancel()

        Log.d(TAG, "自动点击器服务已销毁")
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 此服务专注于手势录制和回放，不处理常规无障碍事件
        // 录制功能通过监听触摸事件实现，不依赖AccessibilityEvent
        // 保持空实现以最小化资源消耗
    }

    override fun onInterrupt() {
        Log.d(TAG, "自动点击器服务被中断")

        // 中断时停止所有操作
        stopRecording()
        stopPlayback()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let { handleAutoClickerOperation(it) }
        return START_NOT_STICKY // 不需要自动重启
    }

    /**
     * 配置无障碍服务 - 保持XML配置，支持全局手势执行
     */
    private fun configureService() {
        // 不重新创建AccessibilityServiceInfo，保持XML配置文件中的设置
        // XML配置文件已经正确设置了全局作用域和手势执行权限

        serviceInfo?.let { info ->
            // 确保支持全局手势执行
            info.flags = info.flags or AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS

            // 支持所有事件类型以确保全局作用域
            info.eventTypes = AccessibilityEvent.TYPES_ALL_MASK
            info.notificationTimeout = 0

            Log.d(TAG, "无障碍服务配置完成")
            Log.d(TAG, "支持手势执行: ${Build.VERSION.SDK_INT >= Build.VERSION_CODES.N}")
            Log.d(TAG, "事件类型: ${info.eventTypes}")
            Log.d(TAG, "服务标志: ${info.flags}")
            Log.d(TAG, "包名限制: ${info.packageNames?.joinToString() ?: "无限制(全局)"}")
        }
    }

    /**
     * 处理自动点击器操作请求
     */
    private fun handleAutoClickerOperation(intent: Intent) {
        val action = intent.getStringExtra("action") ?: return

        Log.d(TAG, "执行自动点击器操作: $action")

        when (action) {
            ACTION_START_RECORDING -> {
                val recordingName = intent.getStringExtra("recording_name") ?: "recording_${System.currentTimeMillis()}"
                startRecording(recordingName)
            }
            ACTION_STOP_RECORDING -> stopRecording()
            ACTION_PAUSE_RECORDING -> pauseRecording()
            ACTION_RESUME_RECORDING -> resumeRecording()
            ACTION_START_PLAYBACK -> {
                val recordingPath = intent.getStringExtra("recording_path")
                val loopCount = intent.getIntExtra("loop_count", 1)
                val playbackSpeed = intent.getFloatExtra("playback_speed", 1.0f)
                startPlayback(recordingPath, loopCount, playbackSpeed)
            }
            ACTION_STOP_PLAYBACK -> stopPlayback()
            ACTION_SAVE_RECORDING -> {
                val savePath = intent.getStringExtra("save_path")
                saveRecording(savePath)
            }
            ACTION_LOAD_RECORDING -> {
                val loadPath = intent.getStringExtra("load_path")
                loadRecording(loadPath)
            }
            else -> {
                Log.w(TAG, "未知的自动点击器操作: $action")
            }
        }
    }

    /**
     * 开始录制
     */
    private fun startRecording(recordingName: String) {
        if (currentState != STATE_IDLE) {
            Log.w(TAG, "无法开始录制，当前状态: $currentState")
            notifyError("无法开始录制，服务正忙")
            return
        }

        // 检查悬浮窗权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
            !android.provider.Settings.canDrawOverlays(this)) {
            notifyError("需要悬浮窗权限才能录制触摸事件")
            return
        }

        Log.d(TAG, "开始录制: $recordingName")

        currentState = STATE_RECORDING
        recordingStartTime = System.currentTimeMillis()
        recordedEvents.clear()

        // 启动触摸事件录制器
        val success = touchEventRecorder?.startRecording() ?: false
        if (!success) {
            notifyError("启动触摸事件录制失败")
            currentState = STATE_IDLE
            return
        }

        // 通知状态变化
        notifyStateChanged(STATE_RECORDING)

        // 启动录制监控
        recordingJob = serviceScope.launch {
            monitorRecording()
        }
    }

    /**
     * 停止录制
     */
    private fun stopRecording() {
        if (currentState != STATE_RECORDING && currentState != STATE_PAUSED) {
            Log.w(TAG, "无法停止录制，当前状态: $currentState")
            return
        }

        Log.d(TAG, "停止录制，共录制 ${recordedEvents.size} 个事件")

        // 停止触摸事件录制器
        touchEventRecorder?.stopRecording()

        recordingJob?.cancel()
        recordingJob = null

        currentState = STATE_IDLE
        notifyStateChanged(STATE_IDLE)
    }

    /**
     * 暂停录制
     */
    private fun pauseRecording() {
        if (currentState != STATE_RECORDING) {
            Log.w(TAG, "无法暂停录制，当前状态: $currentState")
            return
        }

        Log.d(TAG, "暂停录制")
        currentState = STATE_PAUSED
        notifyStateChanged(STATE_PAUSED)
    }

    /**
     * 恢复录制
     */
    private fun resumeRecording() {
        if (currentState != STATE_PAUSED) {
            Log.w(TAG, "无法恢复录制，当前状态: $currentState")
            return
        }

        Log.d(TAG, "恢复录制")
        currentState = STATE_RECORDING
        notifyStateChanged(STATE_RECORDING)
    }

    /**
     * 开始回放
     */
    private fun startPlayback(recordingPath: String?, loopCount: Int, playbackSpeed: Float) {
        if (currentState != STATE_IDLE) {
            Log.w(TAG, "无法开始回放，当前状态: $currentState")
            notifyError("无法开始回放，服务正忙")
            return
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            notifyError("当前Android版本不支持手势回放功能")
            return
        }

        val recording = currentRecording ?: run {
            if (recordingPath != null) {
                // 同步加载录制数据
                runBlocking {
                    val manager = gestureRecordingManager
                    if (manager != null) {
                        val gestureRecording = manager.loadRecording(recordingPath)
                        gestureRecording?.events
                    } else {
                        null
                    }
                }
            } else {
                null
            }
        }

        if (recording == null || recording.isEmpty()) {
            notifyError("没有可回放的录制内容")
            return
        }

        Log.d(TAG, "开始回放，事件数量: ${recording.size}")

        currentState = STATE_PLAYING
        notifyStateChanged(STATE_PLAYING)

        playbackJob = serviceScope.launch {
            executePlayback(recording, loopCount, playbackSpeed)
        }
    }

    /**
     * 开始回放（使用事件列表）
     */
    private fun startPlaybackWithEvents(events: List<com.weinuo.quickcommands.model.TouchEvent>, loopCount: Int, playbackSpeed: Float) {
        if (currentState != STATE_IDLE) {
            Log.w(TAG, "无法开始回放，当前状态: $currentState")
            notifyError("无法开始回放，服务正忙")
            return
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            notifyError("当前Android版本不支持手势回放功能")
            return
        }

        if (events.isEmpty()) {
            notifyError("没有可回放的事件")
            return
        }

        Log.d(TAG, "开始回放，事件数量: ${events.size}")

        currentState = STATE_PLAYING
        notifyStateChanged(STATE_PLAYING)

        playbackJob = serviceScope.launch {
            executePlayback(events, loopCount, playbackSpeed)
        }
    }

    /**
     * 停止回放
     */
    private fun stopPlayback() {
        if (currentState != STATE_PLAYING) {
            Log.w(TAG, "无法停止回放，当前状态: $currentState")
            return
        }

        Log.d(TAG, "停止回放")

        playbackJob?.cancel()
        playbackJob = null

        currentState = STATE_IDLE
        notifyStateChanged(STATE_IDLE)
    }

    /**
     * 保存录制
     */
    private fun saveRecording(savePath: String?) {
        if (recordedEvents.isEmpty()) {
            notifyError("没有可保存的录制内容")
            return
        }

        serviceScope.launch {
            try {
                val recording = createGestureRecording(savePath ?: "recording_${System.currentTimeMillis()}")
                val manager = gestureRecordingManager
                if (manager != null) {
                    val recordingId = manager.saveRecording(recording, savePath)
                    if (recordingId != null) {
                        Log.d(TAG, "录制保存成功: $recordingId")
                    } else {
                        notifyError("录制保存失败")
                    }
                } else {
                    notifyError("录制管理器未初始化")
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存录制时发生错误", e)
                notifyError("保存录制时发生错误: ${e.message}")
            }
        }
    }

    /**
     * 加载录制
     */
    private fun loadRecording(recordingId: String?) {
        if (recordingId == null) {
            notifyError("录制ID为空")
            return
        }

        serviceScope.launch {
            try {
                val manager = gestureRecordingManager
                if (manager != null) {
                    val recording = manager.loadRecording(recordingId)
                    if (recording != null) {
                        currentRecording = recording.events
                        Log.d(TAG, "录制加载成功: $recordingId，事件数量: ${recording.events.size}")
                    } else {
                        notifyError("录制数据加载失败")
                    }
                } else {
                    notifyError("录制管理器未初始化")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载录制时发生错误", e)
                notifyError("加载录制时发生错误: ${e.message}")
            }
        }
    }

    /**
     * 监控录制过程
     */
    private suspend fun monitorRecording() {
        while (currentState == STATE_RECORDING || currentState == STATE_PAUSED) {
            delay(100) // 每100ms检查一次

            if (currentState == STATE_RECORDING) {
                val duration = System.currentTimeMillis() - recordingStartTime
                notifyRecordingProgress(recordedEvents.size, duration)
            }
        }
    }

    /**
     * 处理触摸事件（用于录制）
     */
    fun onTouchEvent(event: MotionEvent): Boolean {
        if (currentState != STATE_RECORDING) {
            return false
        }

        try {
            val currentTime = System.currentTimeMillis()
            val relativeTime = currentTime - recordingStartTime

            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_DOWN -> {
                    // 触摸开始
                    recordTouchEvent(
                        relativeTime,
                        com.weinuo.quickcommands.model.TouchEventType.TAP,
                        event,
                        0L
                    )
                    lastTouchTime = currentTime
                    lastTouchEvent = MotionEvent.obtain(event)
                }

                MotionEvent.ACTION_UP -> {
                    // 触摸结束
                    val duration = currentTime - lastTouchTime
                    if (duration > 500) {
                        // 长按
                        recordTouchEvent(
                            relativeTime,
                            com.weinuo.quickcommands.model.TouchEventType.LONG_PRESS,
                            event,
                            duration
                        )
                    }
                    lastTouchEvent?.recycle()
                    lastTouchEvent = null
                }

                MotionEvent.ACTION_MOVE -> {
                    // 移动（滑动）
                    lastTouchEvent?.let { startEvent ->
                        val deltaX = kotlin.math.abs(event.x - startEvent.x)
                        val deltaY = kotlin.math.abs(event.y - startEvent.y)

                        if (deltaX > 50 || deltaY > 50) { // 移动距离超过阈值
                            recordSwipeEvent(relativeTime, startEvent, event)
                        }
                    }
                }

                MotionEvent.ACTION_POINTER_DOWN -> {
                    // 多点触控开始
                    recordMultiTouchEvent(relativeTime, event)
                }
            }

            return true
        } catch (e: Exception) {
            Log.e(TAG, "处理触摸事件时发生错误", e)
            return false
        }
    }

    /**
     * 录制触摸事件
     */
    private fun recordTouchEvent(
        timestamp: Long,
        type: com.weinuo.quickcommands.model.TouchEventType,
        event: MotionEvent,
        duration: Long
    ) {
        val screenWidth = resources.displayMetrics.widthPixels
        val screenHeight = resources.displayMetrics.heightPixels

        // 使用第一个触摸点作为主要位置
        val x = event.getX(0) / screenWidth // 转换为相对坐标
        val y = event.getY(0) / screenHeight

        val position = com.weinuo.quickcommands.model.TouchPosition(
            startX = x,
            startY = y,
            endX = x,
            endY = y,
            touchPoints = event.pointerCount
        )

        val touchEvent = com.weinuo.quickcommands.model.TouchEvent(
            type = type,
            position = position,
            duration = duration,
            description = "${type.displayName} at (${(event.x).toInt()}, ${(event.y).toInt()})"
        )

        recordedEvents.add(touchEvent)
        Log.d(TAG, "录制事件: ${touchEvent.getDetailedDescription(screenWidth, screenHeight)}")
    }

    /**
     * 录制滑动事件
     */
    private fun recordSwipeEvent(timestamp: Long, startEvent: MotionEvent, endEvent: MotionEvent) {
        val screenWidth = resources.displayMetrics.widthPixels
        val screenHeight = resources.displayMetrics.heightPixels

        val position = com.weinuo.quickcommands.model.TouchPosition(
            startX = startEvent.x / screenWidth,
            startY = startEvent.y / screenHeight,
            endX = endEvent.x / screenWidth,
            endY = endEvent.y / screenHeight
        )

        val touchEvent = com.weinuo.quickcommands.model.TouchEvent(
            type = com.weinuo.quickcommands.model.TouchEventType.SWIPE,
            position = position,
            duration = 300L, // 默认滑动时长
            description = "滑动 from (${startEvent.x.toInt()}, ${startEvent.y.toInt()}) to (${endEvent.x.toInt()}, ${endEvent.y.toInt()})"
        )

        recordedEvents.add(touchEvent)
        Log.d(TAG, "录制滑动事件: ${touchEvent.getDetailedDescription(screenWidth, screenHeight)}")
    }

    /**
     * 录制多点触控事件
     */
    private fun recordMultiTouchEvent(timestamp: Long, event: MotionEvent) {
        val screenWidth = resources.displayMetrics.widthPixels
        val screenHeight = resources.displayMetrics.heightPixels

        // 使用第一个触摸点作为主要位置
        val x = event.getX(0) / screenWidth
        val y = event.getY(0) / screenHeight

        val position = com.weinuo.quickcommands.model.TouchPosition(
            startX = x,
            startY = y,
            endX = x,
            endY = y,
            touchPoints = event.pointerCount
        )

        val touchEvent = com.weinuo.quickcommands.model.TouchEvent(
            type = com.weinuo.quickcommands.model.TouchEventType.MULTI_TAP,
            position = position,
            duration = 100L,
            description = "多点触控 (${event.pointerCount}个触摸点)"
        )

        recordedEvents.add(touchEvent)
        Log.d(TAG, "录制多点触控事件: ${touchEvent.getDetailedDescription(screenWidth, screenHeight)}")
    }

    /**
     * 执行回放
     */
    @RequiresApi(Build.VERSION_CODES.N)
    private suspend fun executePlayback(events: List<com.weinuo.quickcommands.model.TouchEvent>, loopCount: Int, playbackSpeed: Float) {
        try {
            val loops = if (loopCount <= 0) Int.MAX_VALUE else loopCount

            for (loop in 1..loops) {
                if (currentState != STATE_PLAYING) break

                for ((index, event) in events.withIndex()) {
                    if (currentState != STATE_PLAYING) break

                    // 通知回放进度
                    notifyPlaybackProgress(index, events.size)

                    Log.d(TAG, "执行第${index + 1}个手势事件: ${event.getDisplayName()}, 类型: ${event.type}")

                    // 执行事件并等待完成
                    executeGestureEvent(event, playbackSpeed)

                    // 等待事件完成后的延迟时间
                    if (event.delayAfter > 0) {
                        val delay = (event.delayAfter.toFloat() / playbackSpeed).toLong()
                        Log.d(TAG, "事件${index + 1}完成，延迟${delay}ms后执行下一个")
                        delay(delay)
                    } else {
                        Log.d(TAG, "事件${index + 1}完成，无延迟，立即执行下一个")
                    }
                }

                // 循环间延时
                if (loop < loops && currentState == STATE_PLAYING) {
                    delay(1000) // 1秒延时
                }
            }

            // 回放完成
            if (currentState == STATE_PLAYING) {
                currentState = STATE_IDLE
                notifyStateChanged(STATE_IDLE)
            }

        } catch (e: Exception) {
            Log.e(TAG, "回放过程中发生错误", e)
            notifyError("回放过程中发生错误: ${e.message}")

            currentState = STATE_IDLE
            notifyStateChanged(STATE_IDLE)
        }
    }

    // 辅助方法占位符，将在下一部分实现
    private fun notifyStateChanged(state: Int) {
        recordingCallbacks.forEach { it.onRecordingStateChanged(state) }
    }

    private fun notifyRecordingProgress(eventCount: Int, duration: Long) {
        recordingCallbacks.forEach { it.onRecordingProgress(eventCount, duration) }
    }

    private fun notifyPlaybackProgress(currentEvent: Int, totalEvents: Int) {
        recordingCallbacks.forEach { it.onPlaybackProgress(currentEvent, totalEvents) }
    }

    private fun notifyError(error: String) {
        recordingCallbacks.forEach { it.onError(error) }
    }

    /**
     * 创建手势录制对象
     */
    private fun createGestureRecording(name: String): com.weinuo.quickcommands.model.GestureRecording {
        val screenWidth = resources.displayMetrics.widthPixels
        val screenHeight = resources.displayMetrics.heightPixels
        val duration = if (recordedEvents.isNotEmpty()) {
            // 计算总持续时间：所有事件的持续时间和延迟时间之和
            recordedEvents.sumOf { it.duration + it.delayAfter }
        } else {
            0L
        }

        return com.weinuo.quickcommands.model.GestureRecording(
            name = name,
            description = "录制于 ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}",
            createdTime = System.currentTimeMillis(),
            duration = duration,
            screenWidth = screenWidth,
            screenHeight = screenHeight,
            events = recordedEvents.toList(),
            version = 1
        )
    }



    /**
     * 检查手势执行权限和全局作用域
     */
    @RequiresApi(Build.VERSION_CODES.N)
    private fun checkGesturePermissions(): Boolean {
        val serviceInfo = serviceInfo
        if (serviceInfo == null) {
            Log.e(TAG, "无障碍服务信息为空")
            return false
        }

        // 检查是否有执行手势的权限
        val hasGestureCapability = serviceInfo.capabilities and AccessibilityServiceInfo.CAPABILITY_CAN_PERFORM_GESTURES != 0
        Log.d(TAG, "手势执行权限检查: hasGestureCapability=$hasGestureCapability")

        // 检查全局作用域
        val hasGlobalScope = serviceInfo.packageNames == null || serviceInfo.packageNames.isEmpty()
        Log.d(TAG, "全局作用域检查: hasGlobalScope=$hasGlobalScope")

        // 检查交互窗口权限
        val hasInteractiveWindows = serviceInfo.flags and AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS != 0
        Log.d(TAG, "交互窗口权限: hasInteractiveWindows=$hasInteractiveWindows")

        if (!hasGestureCapability) {
            Log.e(TAG, "无障碍服务没有执行手势的权限，请检查服务配置")
        }

        if (!hasGlobalScope) {
            Log.w(TAG, "无障碍服务作用域受限，可能无法在桌面执行手势")
            Log.w(TAG, "受限包名: ${serviceInfo.packageNames?.joinToString()}")
        }

        return hasGestureCapability
    }

    /**
     * 执行手势事件
     */
    @RequiresApi(Build.VERSION_CODES.N)
    private suspend fun executeGestureEvent(event: com.weinuo.quickcommands.model.TouchEvent, playbackSpeed: Float) {
        try {
            // 检查手势执行权限
            if (!checkGesturePermissions()) {
                Log.e(TAG, "无法执行手势，缺少必要权限")
                return
            }

            val gestureDescription = createGestureDescription(event)
            if (gestureDescription != null) {
                Log.d(TAG, "开始执行手势: ${event.getDisplayName()}, 坐标: (${event.position.startX}, ${event.position.startY})")

                // 使用回调等待手势完成
                val success = suspendCoroutine<Boolean> { continuation ->
                    val callback = object : AccessibilityService.GestureResultCallback() {
                        override fun onCompleted(gestureDescription: GestureDescription?) {
                            Log.d(TAG, "手势执行完成: ${event.getDisplayName()}")
                            continuation.resume(true)
                        }

                        override fun onCancelled(gestureDescription: GestureDescription?) {
                            Log.w(TAG, "手势执行被取消: ${event.getDisplayName()}")
                            continuation.resume(false)
                        }
                    }

                    // 确保在主线程执行手势
                    if (Looper.myLooper() == Looper.getMainLooper()) {
                        // 已经在主线程
                        try {
                            Log.d(TAG, "在主线程调用dispatchGesture: ${event.getDisplayName()}")
                            val result = dispatchGesture(gestureDescription, callback, null)
                            Log.d(TAG, "dispatchGesture返回结果: $result")
                            if (!result) {
                                Log.e(TAG, "手势分发失败: ${event.getDisplayName()}")
                                continuation.resume(false)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "dispatchGesture调用异常: ${event.getDisplayName()}", e)
                            continuation.resume(false)
                        }
                    } else {
                        // 切换到主线程执行
                        android.os.Handler(Looper.getMainLooper()).post {
                            try {
                                Log.d(TAG, "切换到主线程调用dispatchGesture: ${event.getDisplayName()}")
                                val result = dispatchGesture(gestureDescription, callback, null)
                                Log.d(TAG, "dispatchGesture返回结果: $result")
                                if (!result) {
                                    Log.e(TAG, "手势分发失败: ${event.getDisplayName()}")
                                    continuation.resume(false)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "dispatchGesture调用异常: ${event.getDisplayName()}", e)
                                continuation.resume(false)
                            }
                        }
                    }
                }

                if (!success) {
                    Log.w(TAG, "手势执行失败: ${event.getDisplayName()}")
                }

                // 在手势之间添加延时，确保手势完全完成
                val gestureDelay = when (event.type) {
                    com.weinuo.quickcommands.model.TouchEventType.TAP -> 150L
                    com.weinuo.quickcommands.model.TouchEventType.SWIPE -> 400L
                    com.weinuo.quickcommands.model.TouchEventType.LONG_PRESS -> 600L
                    else -> 200L
                }
                delay(gestureDelay)
                Log.d(TAG, "手势事件处理完成: ${event.getDisplayName()}")
            } else {
                Log.e(TAG, "无法创建手势描述: ${event.getDisplayName()}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "执行手势事件时发生错误: ${event.getDisplayName()}", e)
        }
    }

    /**
     * 获取状态栏高度
     */
    private fun getStatusBarHeight(): Int {
        var result = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    /**
     * 创建手势描述
     */
    @RequiresApi(Build.VERSION_CODES.N)
    private fun createGestureDescription(event: com.weinuo.quickcommands.model.TouchEvent): GestureDescription? {
        // 获取屏幕尺寸用于坐标转换
        val screenWidth = resources.displayMetrics.widthPixels
        val screenHeight = resources.displayMetrics.heightPixels

        // 转换为绝对坐标
        val absoluteEvent = event.toAbsoluteCoordinates(screenWidth, screenHeight)

        // 补偿状态栏高度偏移（悬浮窗录制时减去了状态栏高度，回放时需要加回来）
        val statusBarHeight = getStatusBarHeight()
        val correctedEvent = absoluteEvent.copy(
            position = absoluteEvent.position.copy(
                startY = absoluteEvent.position.startY + statusBarHeight,
                endY = absoluteEvent.position.endY + statusBarHeight
            )
        )

        Log.d(TAG, "创建手势描述: ${event.getDisplayName()}")
        Log.d(TAG, "屏幕尺寸: ${screenWidth}x${screenHeight}")
        Log.d(TAG, "状态栏高度: ${statusBarHeight}px")
        Log.d(TAG, "相对坐标: (${event.position.startX}, ${event.position.startY}) -> (${event.position.endX}, ${event.position.endY})")
        Log.d(TAG, "绝对坐标(原始): (${absoluteEvent.position.startX}, ${absoluteEvent.position.startY}) -> (${absoluteEvent.position.endX}, ${absoluteEvent.position.endY})")
        Log.d(TAG, "绝对坐标(修正): (${correctedEvent.position.startX}, ${correctedEvent.position.startY}) -> (${correctedEvent.position.endX}, ${correctedEvent.position.endY})")

        val builder = GestureDescription.Builder()

        when (correctedEvent.type) {
            com.weinuo.quickcommands.model.TouchEventType.TAP -> {
                // 单击 - 按照Android官方实现，需要有实际路径长度
                val path = Path().apply {
                    moveTo(correctedEvent.position.startX, correctedEvent.position.startY)
                    // 添加微小的移动以创建有效路径，这是Android官方推荐的做法
                    lineTo(correctedEvent.position.startX + 1, correctedEvent.position.startY)
                }
                // 使用系统默认的点击超时时间
                val tapTimeout = android.view.ViewConfiguration.getTapTimeout().toLong()
                val stroke = GestureDescription.StrokeDescription(path, 0, tapTimeout)
                builder.addStroke(stroke)
                Log.d(TAG, "创建点击手势，持续时间: ${tapTimeout}ms")
            }

            com.weinuo.quickcommands.model.TouchEventType.LONG_PRESS -> {
                // 长按
                val path = Path().apply {
                    moveTo(correctedEvent.position.startX, correctedEvent.position.startY)
                }
                val duration = maxOf(correctedEvent.duration, 500L) // 最少500ms
                val stroke = GestureDescription.StrokeDescription(path, 0, duration)
                builder.addStroke(stroke)
            }

            com.weinuo.quickcommands.model.TouchEventType.SWIPE -> {
                // 滑动
                val path = Path().apply {
                    moveTo(correctedEvent.position.startX, correctedEvent.position.startY)
                    lineTo(correctedEvent.position.endX, correctedEvent.position.endY)
                }
                val duration = maxOf(correctedEvent.duration, 300L) // 最少300ms
                val stroke = GestureDescription.StrokeDescription(path, 0, duration)
                builder.addStroke(stroke)
            }

            com.weinuo.quickcommands.model.TouchEventType.MULTI_TAP -> {
                // 多点触控
                val tapTimeout = android.view.ViewConfiguration.getTapTimeout().toLong()
                repeat(correctedEvent.position.touchPoints) {
                    val path = Path().apply {
                        moveTo(correctedEvent.position.startX, correctedEvent.position.startY)
                        // 添加微小的移动以创建有效路径
                        lineTo(correctedEvent.position.startX + 1, correctedEvent.position.startY)
                    }
                    val stroke = GestureDescription.StrokeDescription(path, 0, tapTimeout)
                    builder.addStroke(stroke)
                }
                Log.d(TAG, "创建多点触控手势，点数: ${correctedEvent.position.touchPoints}, 持续时间: ${tapTimeout}ms")
            }

            else -> {
                return null
            }
        }

        return builder.build()
    }

    /**
     * 回放录制的手势数据
     */
    fun playbackRecordedGesture(
        recordingId: String,
        loopCount: Int = 1,
        playbackSpeed: Float = 1.0f,
        delayBetweenLoops: Long = 1000L
    ): Boolean {
        return try {
            // 在协程中加载录制数据
            serviceScope.launch {
                try {
                    val manager = gestureRecordingManager
                    if (manager != null) {
                        val gestureRecording = manager.loadRecording(recordingId)
                        if (gestureRecording != null) {
                            Log.d(TAG, "成功加载录制数据: ${gestureRecording.name}, 事件数量: ${gestureRecording.events.size}")
                            gestureRecording.events.forEachIndexed { index, event ->
                                Log.d(TAG, "事件${index + 1}: ${event.getDisplayName()}, 位置: (${event.position.startX}, ${event.position.startY}) -> (${event.position.endX}, ${event.position.endY}), 延迟: ${event.delayAfter}ms")
                            }
                            // 开始回放
                            startPlaybackWithEvents(gestureRecording.events, loopCount, playbackSpeed)
                        } else {
                            Log.e(TAG, "无法加载手势录制数据: $recordingId")
                            notifyError("无法加载手势录制数据")
                        }
                    } else {
                        Log.e(TAG, "录制管理器未初始化")
                        notifyError("录制管理器未初始化")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "回放录制手势失败", e)
                    notifyError("回放录制手势失败: ${e.message}")
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "回放录制手势失败", e)
            false
        }
    }

    /**
     * 执行快速操作
     */
    fun executeQuickOperation(
        operationType: QuickOperationType,
        clickX: Float,
        clickY: Float,
        clickCount: Int,
        clickInterval: Long,
        longPressDuration: Long,
        swipeStartX: Float,
        swipeStartY: Float,
        swipeEndX: Float,
        swipeEndY: Float,
        swipeDuration: Long,
        loopCount: Int = 1,
        playbackSpeed: Float = 1.0f,
        delayBetweenLoops: Long = 1000L
    ): Boolean {
        return try {
            // 获取屏幕尺寸
            val screenWidth = resources.displayMetrics.widthPixels.toFloat()
            val screenHeight = resources.displayMetrics.heightPixels.toFloat()

            // 转换相对坐标为绝对坐标
            val absoluteClickX = clickX * screenWidth
            val absoluteClickY = clickY * screenHeight
            val absoluteSwipeStartX = swipeStartX * screenWidth
            val absoluteSwipeStartY = swipeStartY * screenHeight
            val absoluteSwipeEndX = swipeEndX * screenWidth
            val absoluteSwipeEndY = swipeEndY * screenHeight

            // 根据操作类型创建触摸事件
            val events = when (operationType) {
                QuickOperationType.SINGLE_CLICK -> {
                    createSingleClickEvents(absoluteClickX, absoluteClickY)
                }
                QuickOperationType.CONTINUOUS_CLICK -> {
                    createContinuousClickEvents(absoluteClickX, absoluteClickY, clickCount, clickInterval)
                }
                QuickOperationType.SWIPE_OPERATION -> {
                    createSwipeEvents(absoluteSwipeStartX, absoluteSwipeStartY, absoluteSwipeEndX, absoluteSwipeEndY, swipeDuration)
                }
                QuickOperationType.LONG_PRESS -> {
                    createLongPressEvents(absoluteClickX, absoluteClickY, longPressDuration)
                }
            }

            // 开始回放
            startPlaybackWithEvents(events, loopCount, playbackSpeed)
            true
        } catch (e: Exception) {
            Log.e(TAG, "执行快速操作失败", e)
            false
        }
    }

    /**
     * 创建单点点击事件
     */
    private fun createSingleClickEvents(x: Float, y: Float): List<com.weinuo.quickcommands.model.TouchEvent> {
        val position = com.weinuo.quickcommands.model.TouchPosition(
            startX = x,
            startY = y
        )

        return listOf(
            com.weinuo.quickcommands.model.TouchEvent(
                type = com.weinuo.quickcommands.model.TouchEventType.TAP,
                position = position
            )
        )
    }

    /**
     * 创建连续点击事件
     */
    private fun createContinuousClickEvents(x: Float, y: Float, count: Int, interval: Long): List<com.weinuo.quickcommands.model.TouchEvent> {
        val events = mutableListOf<com.weinuo.quickcommands.model.TouchEvent>()

        for (i in 0 until count) {
            val position = com.weinuo.quickcommands.model.TouchPosition(
                startX = x,
                startY = y
            )

            events.add(
                com.weinuo.quickcommands.model.TouchEvent(
                    type = com.weinuo.quickcommands.model.TouchEventType.TAP,
                    position = position,
                    delayAfter = if (i < count - 1) interval else 0L
                )
            )
        }

        return events
    }

    /**
     * 创建滑动事件
     */
    private fun createSwipeEvents(startX: Float, startY: Float, endX: Float, endY: Float, duration: Long): List<com.weinuo.quickcommands.model.TouchEvent> {
        val position = com.weinuo.quickcommands.model.TouchPosition(
            startX = startX,
            startY = startY,
            endX = endX,
            endY = endY
        )

        return listOf(
            com.weinuo.quickcommands.model.TouchEvent(
                type = com.weinuo.quickcommands.model.TouchEventType.SWIPE,
                position = position,
                duration = duration
            )
        )
    }

    /**
     * 创建长按事件
     */
    private fun createLongPressEvents(x: Float, y: Float, duration: Long): List<com.weinuo.quickcommands.model.TouchEvent> {
        val position = com.weinuo.quickcommands.model.TouchPosition(
            startX = x,
            startY = y
        )

        return listOf(
            com.weinuo.quickcommands.model.TouchEvent(
                type = com.weinuo.quickcommands.model.TouchEventType.LONG_PRESS,
                position = position,
                duration = duration
            )
        )
    }
}

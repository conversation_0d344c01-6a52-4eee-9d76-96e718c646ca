package com.weinuo.quickcommands.execution

import android.app.ActivityManager
import android.app.KeyguardManager
import android.app.UiModeManager
import android.bluetooth.BluetoothAdapter
import android.content.ClipboardManager
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.hardware.usb.UsbManager
import android.location.LocationManager
import android.media.AudioManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.BatteryManager
import android.os.PowerManager
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.Log
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.utils.AppStateMonitor
import com.weinuo.quickcommands.utils.SunriseSunsetCalculator
import com.weinuo.quickcommands.data.SettingsRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.Calendar

/**
 * 共享条件评估器
 *
 * 此类负责评估触发条件和中止条件。
 * 它为一键指令和条件指令提供统一的条件评估逻辑。
 */
class SharedConditionEvaluator(private val context: Context) {
    private val TAG = "SharedConditionEvaluator"
    private val settingsRepository = SettingsRepository(context)
    private val evaluatorScope = CoroutineScope(Dispatchers.Default + Job())

    // 用于跟踪已触发的延迟条件，防止重复触发
    private val triggeredDelayedConditions = mutableSetOf<String>()

    // 用于跟踪周期条件的最后触发时间，防止在同一个周期内重复触发
    private val lastPeriodicTriggerTimes = mutableMapOf<String, Long>()

    // 传感器状态缓存（仅用于兼容性和预初始化）
    private val lastSensorStates = mutableMapOf<String, Any?>()

    // 音量状态缓存
    private val lastVolumeStates = mutableMapOf<String, Int>()

    // 电池状态缓存
    private val lastBatteryLevels = mutableMapOf<String, Int>()
    private val lastBatteryTemperatures = mutableMapOf<String, Float>()

    /**
     * 检查条件列表，返回满足的条件
     *
     * @param conditions 要检查的条件列表
     * @return 满足的条件列表
     */
    fun checkConditions(conditions: List<SharedTriggerCondition>): List<SharedTriggerCondition> {
        if (conditions.isEmpty()) {
            return emptyList()
        }

        val satisfiedConditions = mutableListOf<SharedTriggerCondition>()

        for (condition in conditions) {
            if (isConditionSatisfied(condition)) {
                satisfiedConditions.add(condition)
            }
        }

        return satisfiedConditions
    }

    /**
     * 检查条件组合逻辑是否满足
     *
     * @param conditions 条件列表
     * @param requireAllConditions 是否需要所有条件都满足
     * @return 条件组合逻辑是否满足
     */
    fun areConditionsSatisfied(
        conditions: List<SharedTriggerCondition>,
        requireAllConditions: Boolean
    ): Boolean {
        if (conditions.isEmpty()) {
            return false
        }

        val satisfiedConditions = checkConditions(conditions)

        return if (requireAllConditions) {
            // 所有条件都满足
            satisfiedConditions.size == conditions.size
        } else {
            // 任一条件满足
            satisfiedConditions.isNotEmpty()
        }
    }

    /**
     * 检查单个条件是否满足
     *
     * @param condition 要检查的条件
     * @return 条件是否满足
     */
    private fun isConditionSatisfied(condition: SharedTriggerCondition): Boolean {
        return when (condition) {
            // 统一条件类型
            is AppStateCondition -> {
                if (condition.stateType == AppStateType.TASKER_LOCALE_PLUGIN_CONDITION) {
                    // 对于Tasker/Locale插件条件，使用专门的评估逻辑
                    isTaskerLocalePluginConditionSatisfied(condition)
                } else {
                    // 对于其他应用状态条件，使用原有逻辑
                    isAppStateConditionSatisfied(condition)
                }
            }
            is BatteryStateCondition -> isBatteryStateConditionSatisfied(condition)
            is ConnectionStateCondition -> isConnectionStateConditionSatisfied(condition)
            is SensorStateCondition -> isSensorStateConditionSatisfied(condition)

            // 统一时间条件
            is TimeBasedCondition -> isTimeBasedConditionSatisfied(condition)
            // 统一设备事件条件
            is DeviceEventCondition -> isDeviceEventConditionSatisfied(condition)
            is CommunicationStateCondition -> isCommunicationStateConditionSatisfied(condition)
            // 手动触发条件
            is ManualTriggerCondition -> isManualTriggerConditionSatisfied(condition)
            else -> {
                Log.e(TAG, "Unknown condition type: ${condition.type}")
                false
            }
        }
    }

    /**
     * 检查耳机是否连接
     *
     * @return 耳机是否连接
     */
    private fun isHeadphoneConnected(): Boolean {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        return audioManager.isWiredHeadsetOn
    }

    /**
     * 检查屏幕是否点亮
     *
     * @return 屏幕是否点亮
     */
    private fun isScreenOn(): Boolean {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT_WATCH) {
            powerManager.isInteractive
        } else {
            @Suppress("DEPRECATION")
            powerManager.isScreenOn
        }
    }

    /**
     * 检查屏幕是否解锁
     *
     * @return 屏幕是否解锁
     */
    private fun isScreenUnlocked(): Boolean {
        return try {
            val kgManager = context.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            val isLocked = kgManager.isKeyguardLocked
            !isLocked
        } catch (e: Exception) {
            Log.e(TAG, "Error checking keyguard state", e)
            false
        }
    }



    /**
     * 获取当前电池电量百分比
     *
     * @return 电池电量百分比
     */
    private fun getBatteryLevel(): Int {
        val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val level = batteryIntent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
        val scale = batteryIntent?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1

        return if (level != -1 && scale != -1) {
            (level * 100 / scale.toFloat()).toInt()
        } else {
            -1
        }
    }

    /**
     * 检查设备是否正在充电
     *
     * @return 设备是否正在充电
     */
    private fun isCharging(): Boolean {
        val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val status = batteryIntent?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1

        return status == BatteryManager.BATTERY_STATUS_CHARGING ||
               status == BatteryManager.BATTERY_STATUS_FULL
    }



    /**
     * 检查WiFi是否连接
     *
     * @return WiFi是否连接
     */
    private fun isWifiConnected(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.getActiveNetworkInfo()
            return networkInfo != null && networkInfo.isConnected && networkInfo.type == ConnectivityManager.TYPE_WIFI
        }
    }

    /**
     * 检查蓝牙是否连接
     *
     * @return 蓝牙是否连接
     */
    private fun isBluetoothConnected(): Boolean {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter() ?: return false

        // 检查蓝牙是否开启
        if (!bluetoothAdapter.isEnabled) {
            return false
        }

        // 检查是否有已连接的设备
        return try {
            val method = bluetoothAdapter.javaClass.getMethod("getConnectionState")
            val state = method.invoke(bluetoothAdapter) as Int
            state == BluetoothAdapter.STATE_CONNECTED
        } catch (e: Exception) {
            // 如果方法不可用，则尝试检查已配对设备是否有连接的
            bluetoothAdapter.bondedDevices?.any { device ->
                try {
                    val method = device.javaClass.getMethod("isConnected")
                    method.invoke(device) as Boolean
                } catch (e: Exception) {
                    false
                }
            } ?: false
        }
    }

    /**
     * 检查蓝牙是否开启
     *
     * @return 蓝牙是否开启
     */
    private fun isBluetoothEnabled(): Boolean {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter() ?: return false
        return bluetoothAdapter.isEnabled
    }

    /**
     * 检查WiFi是否开启
     *
     * @return WiFi是否开启
     */
    private fun isWifiEnabled(): Boolean {
        val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
        return wifiManager.isWifiEnabled
    }

    /**
     * 检查移动数据是否开启
     *
     * @return 移动数据是否开启
     */
    private fun isMobileDataEnabled(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                // Android 6.0+ 使用新的API
                val method = connectivityManager.javaClass.getDeclaredMethod("getMobileDataEnabled")
                method.isAccessible = true
                method.invoke(connectivityManager) as Boolean
            } else {
                // 较低版本使用反射访问
                val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as android.telephony.TelephonyManager
                val method = telephonyManager.javaClass.getDeclaredMethod("getDataEnabled")
                method.isAccessible = true
                method.invoke(telephonyManager) as Boolean
            }
        } catch (e: Exception) {
            Log.w(TAG, "Unable to check mobile data state", e)
            false
        }
    }

    /**
     * 检查移动数据是否连接
     *
     * @return 移动数据是否连接
     */
    private fun isMobileDataConnected(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.getActiveNetworkInfo()
            return networkInfo != null && networkInfo.isConnected && networkInfo.type == ConnectivityManager.TYPE_MOBILE
        }
    }

    /**
     * 检查可用内存是否低于指定阈值
     *
     * @param threshold 内存阈值
     * @param isPercentageMode true: 百分比模式, false: 绝对值模式(GB)
     * @return 可用内存是否低于阈值
     */
    private fun isMemoryBelowThreshold(threshold: Int, isPercentageMode: Boolean): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)

        return if (isPercentageMode) {
            // 百分比模式
            val availableMemoryPercentage = (memoryInfo.availMem * 100 / memoryInfo.totalMem).toInt()
            Log.d(TAG, "Available memory: ${availableMemoryPercentage}%, threshold: ${threshold}%")
            availableMemoryPercentage < threshold
        } else {
            // 绝对值模式 (GB)
            val availableMemoryGB = memoryInfo.availMem / (1024 * 1024 * 1024)
            Log.d(TAG, "Available memory: ${availableMemoryGB}GB, threshold: ${threshold}GB")
            availableMemoryGB < threshold
        }
    }







    /**
     * 清理触发状态
     * 用于重置延迟触发和周期触发条件的状态
     */
    fun clearTriggerStates() {
        triggeredDelayedConditions.clear()
        lastPeriodicTriggerTimes.clear()
        Log.d(TAG, "Trigger states cleared")
    }

    /**
     * 预初始化条件状态
     * 为某些条件类型预设初始状态，避免首次检查时无法触发的问题
     *
     * @param conditions 需要预初始化的条件列表
     */
    fun preInitializeConditionStates(conditions: List<SharedTriggerCondition>) {
        for (condition in conditions) {
            try {
                when (condition) {
                    is BatteryStateCondition -> preInitializeBatteryCondition(condition)
                    is ConnectionStateCondition -> preInitializeConnectionCondition(condition)
                    is DeviceEventCondition -> preInitializeDeviceEventCondition(condition)
                    is SensorStateCondition -> preInitializeSensorCondition(condition)
                    // 其他条件类型可以根据需要添加
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error pre-initializing condition ${condition.id}", e)
            }
        }
        Log.d(TAG, "Pre-initialized ${conditions.size} conditions")
    }

    /**
     * 预初始化电池条件状态
     */
    private fun preInitializeBatteryCondition(condition: BatteryStateCondition) {
        when (condition.conditionType) {
            BatteryConditionType.BATTERY_LEVEL -> {
                if (condition.levelSubType == BatteryLevelSubType.CHANGED) {
                    val conditionKey = "battery_level_${condition.id}"
                    val currentBatteryLevel = getBatteryLevel()
                    if (currentBatteryLevel != -1) {
                        lastBatteryLevels[conditionKey] = currentBatteryLevel
                        Log.d(TAG, "Pre-initialized battery level for condition ${condition.id}: $currentBatteryLevel%")
                    }
                }
            }
            BatteryConditionType.CHARGING_STATE -> {
                when (condition.chargingSubType) {
                    ChargingSubType.STARTED -> {
                        val conditionKey = "charging_started_${condition.id}"
                        lastChargingStates[conditionKey] = isCharging()
                        Log.d(TAG, "Pre-initialized charging state (STARTED) for condition ${condition.id}")
                    }
                    ChargingSubType.STOPPED -> {
                        val conditionKey = "charging_stopped_${condition.id}"
                        lastChargingStates[conditionKey] = isCharging()
                        Log.d(TAG, "Pre-initialized charging state (STOPPED) for condition ${condition.id}")
                    }
                    else -> { /* 其他充电状态不需要预初始化 */ }
                }
            }
            BatteryConditionType.BATTERY_TEMPERATURE -> {
                val conditionKey = "battery_temperature_${condition.id}"
                try {
                    val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
                    val temperature = batteryIntent?.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1) ?: -1
                    if (temperature != -1) {
                        val temperatureCelsius = temperature / 10.0f
                        lastBatteryTemperatures[conditionKey] = temperatureCelsius
                        Log.d(TAG, "Pre-initialized battery temperature for condition ${condition.id}: ${temperatureCelsius}°C")
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to pre-initialize battery temperature for condition ${condition.id}", e)
                }
            }
            else -> { /* 其他电池条件不需要预初始化 */ }
        }
    }

    /**
     * 预初始化连接条件状态
     */
    private fun preInitializeConnectionCondition(condition: ConnectionStateCondition) {
        // 连接状态条件通常不需要预初始化，因为它们主要检查当前状态
        // 但可以为某些需要状态变化检测的条件预设状态
        Log.d(TAG, "Connection condition ${condition.id} does not require pre-initialization")
    }

    /**
     * 预初始化设备事件条件状态
     */
    private fun preInitializeDeviceEventCondition(condition: DeviceEventCondition) {
        when (condition.eventType) {
            DeviceEventType.VOLUME_CHANGED -> {
                try {
                    val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                    val streamType = when (condition.volumeStreamType) {
                        VolumeStreamType.ALARM -> AudioManager.STREAM_ALARM
                        VolumeStreamType.MEDIA_MUSIC -> AudioManager.STREAM_MUSIC
                        VolumeStreamType.NOTIFICATION -> AudioManager.STREAM_NOTIFICATION
                        VolumeStreamType.RING -> AudioManager.STREAM_RING
                        VolumeStreamType.SYSTEM -> AudioManager.STREAM_SYSTEM
                        VolumeStreamType.VOICE_CALL -> AudioManager.STREAM_VOICE_CALL
                        VolumeStreamType.BLUETOOTH -> 6 // AudioManager.STREAM_BLUETOOTH_SCO
                        VolumeStreamType.ACCESSIBILITY -> AudioManager.STREAM_ACCESSIBILITY
                    }
                    val currentVolume = audioManager.getStreamVolume(streamType)
                    val maxVolume = audioManager.getStreamMaxVolume(streamType)
                    val currentVolumePercent = if (maxVolume > 0) (currentVolume * 100) / maxVolume else 0

                    val cacheKey = "volume_${condition.volumeStreamType.name}"
                    lastVolumeStates[cacheKey] = currentVolumePercent
                    Log.d(TAG, "Pre-initialized volume state for condition ${condition.id}: ${condition.volumeStreamType.displayName} = $currentVolumePercent%")
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to pre-initialize volume state for condition ${condition.id}", e)
                }
            }
            DeviceEventType.MEMORY_STATE -> {
                if (condition.memoryStateType == MemoryStateType.CHANGED) {
                    try {
                        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                        val memoryInfo = ActivityManager.MemoryInfo()
                        activityManager.getMemoryInfo(memoryInfo)
                        val currentMemoryPercentage = (memoryInfo.availMem * 100 / memoryInfo.totalMem).toInt()

                        val conditionKey = "memory_state_${condition.id}"
                        lastSensorStates[conditionKey] = currentMemoryPercentage
                        Log.d(TAG, "Pre-initialized memory state for condition ${condition.id}: $currentMemoryPercentage%")
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to pre-initialize memory state for condition ${condition.id}", e)
                    }
                }
            }
            else -> { /* 其他设备事件条件不需要预初始化 */ }
        }
    }

    /**
     * 预初始化传感器条件状态
     */
    private fun preInitializeSensorCondition(condition: SensorStateCondition) {
        when (condition.sensorType) {
            SensorStateType.SLEEP_SENSOR -> {
                try {
                    val currentTime = Calendar.getInstance()
                    val hour = currentTime.get(Calendar.HOUR_OF_DAY)
                    val isNightTime = hour >= 22 || hour <= 6
                    val isScreenOff = !isScreenOn()
                    val isDeviceLocked = !isScreenUnlocked()
                    val isSleeping = isNightTime && isScreenOff && isDeviceLocked

                    val conditionKey = "sleep_state_${condition.id}"
                    lastSensorStates[conditionKey] = isSleeping
                    Log.d(TAG, "Pre-initialized sleep state for condition ${condition.id}: sleeping=$isSleeping")
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to pre-initialize sleep state for condition ${condition.id}", e)
                }
            }
            else -> { /* 其他传感器条件暂时不需要预初始化 */ }
        }
    }

    /**
     * 清理特定条件的触发状态
     *
     * @param conditionId 条件ID
     */
    fun clearTriggerState(conditionId: String) {
        // 清理延迟触发状态
        triggeredDelayedConditions.removeAll { it.startsWith("${conditionId}_") }

        // 清理周期触发状态
        lastPeriodicTriggerTimes.remove(conditionId)

        Log.d(TAG, "Trigger state cleared for condition: $conditionId")
    }

    // 应用状态监控相关变量
    private var lastForegroundApp: String? = null

    // 用于跟踪硬件状态变化
    private val lastScreenStates = mutableMapOf<String, Boolean>()
    private val lastChargingStates = mutableMapOf<String, Boolean>()





    /**
     * 重置应用状态监控
     * 用于清除应用状态缓存，重新开始监控
     */
    fun resetAppStateMonitoring() {
        lastForegroundApp = null
        AppStateMonitor.clearCache()
        Log.d(TAG, "App state monitoring reset")
    }

    /**
     * 强制更新应用状态
     * 用于在条件检查前主动更新应用状态，提高检测准确性
     */
    fun forceUpdateAppState() {
        try {
            // 强制刷新当前前台应用
            evaluatorScope.launch {
                val currentApp = AppStateMonitor.getCurrentForegroundApp(context)
                Log.d(TAG, "Force updated app state: current app = $currentApp")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error force updating app state", e)
        }
    }

    // 用于跟踪连接状态变化
    private val lastConnectionStates = mutableMapOf<String, Any?>()

    /**
     * 检查连接状态条件是否满足
     *
     * @param condition 连接状态条件
     * @return 连接状态条件是否满足
     */
    private fun isConnectionStateConditionSatisfied(condition: ConnectionStateCondition): Boolean {
        return try {
            when (condition.connectionType) {
                ConnectionType.WIFI_STATE -> evaluateWifiStateCondition(condition)
                ConnectionType.WIFI_NETWORK -> evaluateWifiNetworkCondition(condition)
                ConnectionType.MOBILE_DATA -> evaluateMobileDataCondition(condition)
                ConnectionType.NETWORK_GENERAL -> evaluateNetworkGeneralCondition(condition)
                ConnectionType.VPN_STATE -> evaluateVpnStateCondition(condition)
                ConnectionType.ROAMING_STATE -> evaluateRoamingStateCondition(condition)
                ConnectionType.HOTSPOT_STATE -> evaluateHotspotStateCondition(condition)
                ConnectionType.BLUETOOTH_STATE -> evaluateBluetoothStateCondition(condition)
                ConnectionType.BLUETOOTH_DEVICE -> evaluateBluetoothDeviceCondition(condition)
                ConnectionType.HEADPHONE -> evaluateHeadphoneCondition(condition)
                ConnectionType.USB_DEVICE -> evaluateUsbDeviceCondition(condition)

                ConnectionType.IP_ADDRESS -> evaluateIpAddressCondition(condition)
                ConnectionType.CELL_TOWER -> evaluateCellTowerCondition(condition)
                ConnectionType.MOBILE_SIGNAL -> evaluateMobileSignalCondition(condition)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking connection state condition", e)
            false
        }
    }



    /**
     * 评估IP地址条件
     */
    private fun evaluateIpAddressCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            val currentIpAddress = getCurrentIpAddress()

            when (condition.subType) {
                ConnectionSubType.IP_ADDRESS_CHANGED -> {
                    val conditionKey = "ip_address_${condition.id}"
                    val lastIpAddress = lastConnectionStates[conditionKey] as? String

                    // 更新IP地址缓存
                    lastConnectionStates[conditionKey] = currentIpAddress

                    // 如果是第一次检查，不触发
                    if (lastIpAddress == null) {
                        Log.d(TAG, "IP address change check: first check, not triggering")
                        return false
                    }

                    // 检查IP地址是否发生变化
                    val ipChanged = currentIpAddress != lastIpAddress
                    Log.d(TAG, "IP address change check: last=$lastIpAddress, current=$currentIpAddress, changed=$ipChanged")
                    ipChanged
                }
                ConnectionSubType.IP_ADDRESS_OBTAINED -> {
                    // 检查是否获得了IP地址（从无IP到有IP）
                    val hasIpAddress = currentIpAddress != null
                    Log.d(TAG, "IP address obtained check: current=$currentIpAddress, hasIp=$hasIpAddress")
                    hasIpAddress
                }
                ConnectionSubType.IP_ADDRESS_LOST -> {
                    // 检查是否失去了IP地址（从有IP到无IP）
                    val hasNoIpAddress = currentIpAddress == null
                    Log.d(TAG, "IP address lost check: current=$currentIpAddress, hasNoIp=$hasNoIpAddress")
                    hasNoIpAddress
                }
                else -> {
                    Log.d(TAG, "IP address check: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking IP address condition", e)
            false
        }
    }

    /**
     * 获取当前设备的IP地址
     */
    private fun getCurrentIpAddress(): String? {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val activeNetwork = connectivityManager.activeNetwork ?: return null
            val linkProperties = connectivityManager.getLinkProperties(activeNetwork) ?: return null

            // 获取第一个IPv4地址
            for (linkAddress in linkProperties.linkAddresses) {
                val address = linkAddress.address
                if (!address.isLoopbackAddress && address is java.net.Inet4Address) {
                    return address.hostAddress
                }
            }
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current IP address", e)
            null
        }
    }

    /**
     * 评估USB设备状态条件
     */
    private fun evaluateUsbDeviceCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            val usbManager = context.getSystemService(Context.USB_SERVICE) as? UsbManager
            if (usbManager == null) {
                Log.d(TAG, "USB device check: UsbManager not available")
                return false
            }

            val connectedDevices = usbManager.deviceList
            val hasUsbDevices = connectedDevices.isNotEmpty()

            when (condition.subType) {
                ConnectionSubType.USB_DEVICE_CONNECTED -> {
                    Log.d(TAG, "USB device connected check: $hasUsbDevices (${connectedDevices.size} devices)")
                    hasUsbDevices
                }
                ConnectionSubType.USB_DEVICE_DISCONNECTED -> {
                    Log.d(TAG, "USB device disconnected check: ${!hasUsbDevices}")
                    !hasUsbDevices
                }
                else -> {
                    Log.d(TAG, "USB device check: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking USB device condition", e)
            false
        }
    }

    /**
     * 评估VPN连接状态条件
     */
    private fun evaluateVpnStateCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            val isVpnConnected = isVpnConnected()

            when (condition.subType) {
                ConnectionSubType.VPN_CONNECTED -> {
                    Log.d(TAG, "VPN connected check: $isVpnConnected")
                    isVpnConnected
                }
                ConnectionSubType.VPN_DISCONNECTED -> {
                    Log.d(TAG, "VPN disconnected check: ${!isVpnConnected}")
                    !isVpnConnected
                }
                else -> {
                    Log.d(TAG, "VPN state check: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking VPN state condition", e)
            false
        }
    }

    /**
     * 检查VPN是否连接
     */
    private fun isVpnConnected(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val activeNetwork = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

            // 检查是否使用VPN传输
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking VPN connection", e)
            false
        }
    }

    /**
     * 评估WiFi SSID条件
     */
    private fun evaluateWifiSsidCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            // 检查位置权限
            if (!hasLocationPermission()) {
                Log.d(TAG, "WiFi SSID check: location permission not granted")
                return false
            }

            val currentSsid = getCurrentWifiSsid()

            when (condition.subType) {
                ConnectionSubType.WIFI_NETWORK_CHANGED -> {
                    val conditionKey = "wifi_ssid_${condition.id}"
                    val lastSsid = lastConnectionStates[conditionKey] as? String

                    // 更新SSID缓存
                    lastConnectionStates[conditionKey] = currentSsid

                    // 如果是第一次检查，不触发
                    if (lastSsid == null) {
                        Log.d(TAG, "WiFi SSID check: first check, not triggering")
                        return false
                    }

                    val ssidChanged = currentSsid != lastSsid
                    Log.d(TAG, "WiFi SSID change check: last=$lastSsid, current=$currentSsid, changed=$ssidChanged")
                    ssidChanged
                }
                ConnectionSubType.WIFI_NETWORK_CONNECTED -> {
                    val targetSsid = condition.specificValue
                    val isConnectedToTarget = if (targetSsid.isEmpty()) {
                        currentSsid != null
                    } else {
                        currentSsid == targetSsid
                    }
                    Log.d(TAG, "WiFi SSID connected check: current=$currentSsid, target=$targetSsid, connected=$isConnectedToTarget")
                    isConnectedToTarget
                }
                ConnectionSubType.WIFI_NETWORK_DISCONNECTED -> {
                    val targetSsid = condition.specificValue
                    val isDisconnectedFromTarget = if (targetSsid.isEmpty()) {
                        currentSsid == null
                    } else {
                        currentSsid != targetSsid
                    }
                    Log.d(TAG, "WiFi SSID disconnected check: current=$currentSsid, target=$targetSsid, disconnected=$isDisconnectedFromTarget")
                    isDisconnectedFromTarget
                }
                else -> {
                    Log.d(TAG, "WiFi SSID check: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking WiFi SSID condition", e)
            false
        }
    }

    /**
     * 检查是否有位置权限
     */
    private fun hasLocationPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            android.Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ContextCompat.checkSelfPermission(
            context,
            android.Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 获取当前WiFi的SSID
     */
    private fun getCurrentWifiSsid(): String? {
        return try {
            if (!isWifiConnected()) {
                return null
            }

            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val wifiInfo = wifiManager.connectionInfo

            if (wifiInfo != null && wifiInfo.ssid != null) {
                // 移除SSID周围的引号
                var ssid = wifiInfo.ssid
                if (ssid.startsWith("\"") && ssid.endsWith("\"")) {
                    ssid = ssid.substring(1, ssid.length - 1)
                }
                ssid
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current WiFi SSID", e)
            null
        }
    }

    /**
     * 评估WiFi状态条件
     */
    private fun evaluateWifiStateCondition(condition: ConnectionStateCondition): Boolean {
        return when (condition.subType) {
            ConnectionSubType.WIFI_ENABLED -> {
                val isEnabled = isWifiEnabled()
                Log.d(TAG, "WiFi enabled check: $isEnabled")
                isEnabled
            }
            ConnectionSubType.WIFI_DISABLED -> {
                val isDisabled = !isWifiEnabled()
                Log.d(TAG, "WiFi disabled check: $isDisabled")
                isDisabled
            }
            ConnectionSubType.WIFI_CONNECTED -> {
                val isConnected = isWifiConnected()
                Log.d(TAG, "WiFi connected check: $isConnected")
                isConnected
            }
            ConnectionSubType.WIFI_DISCONNECTED -> {
                val isDisconnected = !isWifiConnected()
                Log.d(TAG, "WiFi disconnected check: $isDisconnected")
                isDisconnected
            }
            else -> {
                Log.d(TAG, "WiFi state check: unsupported subtype ${condition.subType}")
                false
            }
        }
    }

    /**
     * 评估WiFi网络条件
     */
    private fun evaluateWifiNetworkCondition(condition: ConnectionStateCondition): Boolean {
        return when (condition.subType) {
            ConnectionSubType.WIFI_NETWORK_CHANGED -> {
                // WiFi网络切换检测需要在监听器中实现
                Log.d(TAG, "WiFi network changed check")
                false
            }
            ConnectionSubType.WIFI_NETWORK_CONNECTED -> {
                val currentSsid = getCurrentWifiSsid()
                val isConnected = if (condition.specificValue.isNotEmpty()) {
                    currentSsid == condition.specificValue
                } else {
                    currentSsid?.isNotEmpty() == true
                }
                Log.d(TAG, "WiFi network connected check: $isConnected (current: $currentSsid, target: ${condition.specificValue})")
                isConnected
            }
            ConnectionSubType.WIFI_NETWORK_DISCONNECTED -> {
                val currentSsid = getCurrentWifiSsid()
                val isDisconnected = if (condition.specificValue.isNotEmpty()) {
                    currentSsid != condition.specificValue
                } else {
                    currentSsid?.isEmpty() != false
                }
                Log.d(TAG, "WiFi network disconnected check: $isDisconnected (current: $currentSsid, target: ${condition.specificValue})")
                isDisconnected
            }
            else -> {
                Log.d(TAG, "WiFi network check: unsupported subtype ${condition.subType}")
                false
            }
        }
    }

    /**
     * 评估移动数据条件
     */
    private fun evaluateMobileDataCondition(condition: ConnectionStateCondition): Boolean {
        return when (condition.subType) {
            ConnectionSubType.MOBILE_DATA_ENABLED -> {
                val isEnabled = isMobileDataEnabled()
                Log.d(TAG, "Mobile data enabled check: $isEnabled")
                isEnabled
            }
            ConnectionSubType.MOBILE_DATA_DISABLED -> {
                val isDisabled = !isMobileDataEnabled()
                Log.d(TAG, "Mobile data disabled check: $isDisabled")
                isDisabled
            }
            ConnectionSubType.MOBILE_DATA_CONNECTED -> {
                val isConnected = isMobileDataConnected()
                Log.d(TAG, "Mobile data connected check: $isConnected")
                isConnected
            }
            ConnectionSubType.MOBILE_DATA_DISCONNECTED -> {
                val isDisconnected = !isMobileDataConnected()
                Log.d(TAG, "Mobile data disconnected check: $isDisconnected")
                isDisconnected
            }
            else -> {
                Log.d(TAG, "Mobile data check: unsupported subtype ${condition.subType}")
                false
            }
        }
    }

    /**
     * 评估通用网络连接条件
     */
    private fun evaluateNetworkGeneralCondition(condition: ConnectionStateCondition): Boolean {
        return when (condition.subType) {
            ConnectionSubType.NETWORK_CONNECTED -> {
                val isConnected = isWifiConnected() || isMobileDataConnected()
                Log.d(TAG, "Network connected check: $isConnected")
                isConnected
            }
            ConnectionSubType.NETWORK_DISCONNECTED -> {
                val isDisconnected = !isWifiConnected() && !isMobileDataConnected()
                Log.d(TAG, "Network disconnected check: $isDisconnected")
                isDisconnected
            }
            else -> {
                Log.d(TAG, "Network general check: unsupported subtype ${condition.subType}")
                false
            }
        }
    }

    /**
     * 评估数据漫游状态条件
     */
    private fun evaluateRoamingStateCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            val isRoaming = isDataRoaming()

            when (condition.subType) {
                ConnectionSubType.ROAMING_ENABLED -> {
                    Log.d(TAG, "Roaming enabled check: $isRoaming")
                    isRoaming
                }
                ConnectionSubType.ROAMING_DISABLED -> {
                    Log.d(TAG, "Roaming disabled check: ${!isRoaming}")
                    !isRoaming
                }
                else -> {
                    Log.d(TAG, "Roaming state check: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking roaming state condition", e)
            false
        }
    }

    /**
     * 检查是否处于数据漫游状态
     */
    private fun isDataRoaming(): Boolean {
        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
            telephonyManager?.isNetworkRoaming ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking data roaming state", e)
            false
        }
    }

    /**
     * 评估个人热点状态条件
     */
    private fun evaluateHotspotStateCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            val isHotspotEnabled = isHotspotEnabled()

            when (condition.subType) {
                ConnectionSubType.HOTSPOT_ENABLED -> {
                    Log.d(TAG, "Hotspot enabled check: $isHotspotEnabled")
                    isHotspotEnabled
                }
                ConnectionSubType.HOTSPOT_DISABLED -> {
                    Log.d(TAG, "Hotspot disabled check: ${!isHotspotEnabled}")
                    !isHotspotEnabled
                }
                else -> {
                    Log.d(TAG, "Hotspot state check: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking hotspot state condition", e)
            false
        }
    }

    /**
     * 检查个人热点是否开启
     * 注意：Android 10+限制了热点状态的检测，此方法可能不完全准确
     */
    private fun isHotspotEnabled(): Boolean {
        return try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

            // 尝试通过反射获取热点状态（适用于较旧的Android版本）
            try {
                val method = wifiManager.javaClass.getMethod("isWifiApEnabled")
                method.invoke(wifiManager) as Boolean
            } catch (e: Exception) {
                // 反射失败，尝试其他方法
                Log.d(TAG, "Reflection method failed, trying alternative approach")

                // 检查是否有热点相关的网络接口
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val networks = connectivityManager.allNetworks

                for (network in networks) {
                    val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                    if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                        // 这是一个粗略的检测方法，可能不完全准确
                        val linkProperties = connectivityManager.getLinkProperties(network)
                        if (linkProperties?.interfaceName?.contains("ap") == true) {
                            return true
                        }
                    }
                }
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking hotspot state", e)
            false
        }
    }

    /**
     * 评估手机信号状态条件
     */
    private fun evaluateMobileSignalCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            val hasSignal = hasMobileSignal()

            when (condition.subType) {
                ConnectionSubType.MOBILE_SIGNAL_AVAILABLE -> {
                    Log.d(TAG, "Mobile signal available check: $hasSignal")
                    hasSignal
                }
                ConnectionSubType.MOBILE_SIGNAL_UNAVAILABLE -> {
                    Log.d(TAG, "Mobile signal unavailable check: ${!hasSignal}")
                    !hasSignal
                }
                else -> {
                    Log.d(TAG, "Mobile signal check: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking mobile signal condition", e)
            false
        }
    }

    /**
     * 检查是否有手机信号
     */
    private fun hasMobileSignal(): Boolean {
        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
            if (telephonyManager == null) {
                Log.d(TAG, "TelephonyManager not available")
                return false
            }

            // 检查网络类型
            val networkType = telephonyManager.networkType
            if (networkType == TelephonyManager.NETWORK_TYPE_UNKNOWN) {
                Log.d(TAG, "Network type unknown, no signal")
                return false
            }

            // 检查SIM卡状态
            val simState = telephonyManager.simState
            if (simState != TelephonyManager.SIM_STATE_READY) {
                Log.d(TAG, "SIM not ready, no signal")
                return false
            }

            // 检查信号强度（需要权限）
            try {
                if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.ACCESS_COARSE_LOCATION)
                    == PackageManager.PERMISSION_GRANTED) {

                    // 通过ConnectivityManager检查移动网络连接
                    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                    val networks = connectivityManager.allNetworks

                    for (network in networks) {
                        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                        if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true) {
                            Log.d(TAG, "Mobile signal available via cellular transport")
                            return true
                        }
                    }
                }
            } catch (e: Exception) {
                Log.d(TAG, "Error checking signal strength, falling back to basic check")
            }

            // 基本检查：有SIM卡且网络类型不是未知
            val hasBasicSignal = simState == TelephonyManager.SIM_STATE_READY &&
                               networkType != TelephonyManager.NETWORK_TYPE_UNKNOWN
            Log.d(TAG, "Basic signal check: simState=$simState, networkType=$networkType, hasSignal=$hasBasicSignal")
            hasBasicSignal

        } catch (e: Exception) {
            Log.e(TAG, "Error checking mobile signal", e)
            false
        }
    }



    /**
     * 评估耳机连接条件
     */
    private fun evaluateHeadphoneCondition(condition: ConnectionStateCondition): Boolean {
        return when (condition.subType) {
            ConnectionSubType.HEADPHONE_CONNECTED -> {
                val isConnected = isHeadphoneConnected()
                Log.d(TAG, "Headphone connected check: $isConnected")
                isConnected
            }
            ConnectionSubType.HEADPHONE_DISCONNECTED -> {
                val isDisconnected = !isHeadphoneConnected()
                Log.d(TAG, "Headphone disconnected check: $isDisconnected")
                isDisconnected
            }
            else -> {
                Log.d(TAG, "Headphone check: unsupported subtype ${condition.subType}")
                false
            }
        }
    }

    /**
     * 评估蓝牙状态条件
     */
    private fun evaluateBluetoothStateCondition(condition: ConnectionStateCondition): Boolean {
        return when (condition.subType) {
            ConnectionSubType.BLUETOOTH_ENABLED -> {
                val isEnabled = isBluetoothEnabled()
                Log.d(TAG, "Bluetooth enabled check: $isEnabled")
                isEnabled
            }
            ConnectionSubType.BLUETOOTH_DISABLED -> {
                val isDisabled = !isBluetoothEnabled()
                Log.d(TAG, "Bluetooth disabled check: $isDisabled")
                isDisabled
            }
            ConnectionSubType.BLUETOOTH_CONNECTED -> {
                val isConnected = isBluetoothConnected()
                Log.d(TAG, "Bluetooth connected check: $isConnected")
                isConnected
            }
            ConnectionSubType.BLUETOOTH_DISCONNECTED -> {
                val isDisconnected = !isBluetoothConnected()
                Log.d(TAG, "Bluetooth disconnected check: $isDisconnected")
                isDisconnected
            }
            else -> {
                Log.d(TAG, "Bluetooth state check: unsupported subtype ${condition.subType}")
                false
            }
        }
    }

    /**
     * 评估特定蓝牙设备条件
     */
    private fun evaluateBluetoothDeviceCondition(condition: ConnectionStateCondition): Boolean {
        return when (condition.subType) {
            ConnectionSubType.BLUETOOTH_DEVICE_CONNECTED -> {
                val targetDeviceName = condition.specificValue
                val isSpecificDeviceConnected = if (targetDeviceName.isEmpty()) {
                    // 检查是否有任何蓝牙设备连接
                    isAnyBluetoothDeviceConnected()
                } else {
                    // 检查特定设备是否连接
                    isSpecificBluetoothDeviceConnected(targetDeviceName)
                }
                Log.d(TAG, "Bluetooth device connected check: target=$targetDeviceName, connected=$isSpecificDeviceConnected")
                isSpecificDeviceConnected
            }
            ConnectionSubType.BLUETOOTH_DEVICE_DISCONNECTED -> {
                val targetDeviceName = condition.specificValue
                val isSpecificDeviceConnected = if (targetDeviceName.isEmpty()) {
                    // 检查是否没有任何蓝牙设备连接
                    !isAnyBluetoothDeviceConnected()
                } else {
                    // 检查特定设备是否断开
                    !isSpecificBluetoothDeviceConnected(targetDeviceName)
                }
                Log.d(TAG, "Bluetooth device disconnected check: target=$targetDeviceName, disconnected=$isSpecificDeviceConnected")
                isSpecificDeviceConnected
            }
            else -> {
                Log.d(TAG, "Bluetooth device check: unsupported subtype ${condition.subType}")
                false
            }
        }
    }

    /**
     * 检查是否有任何蓝牙设备连接
     */
    private fun isAnyBluetoothDeviceConnected(): Boolean {
        return try {
            if (!isBluetoothEnabled()) {
                return false
            }

            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled) {
                return false
            }

            // 检查是否需要蓝牙权限
            if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.BLUETOOTH_CONNECT)
                != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Bluetooth connect permission not granted")
                return false
            }

            // 获取已配对的设备
            val pairedDevices = bluetoothAdapter.bondedDevices
            if (pairedDevices.isEmpty()) {
                return false
            }

            // 检查是否有设备处于连接状态
            // 注意：这是一个简化的检测方法，实际的连接状态检测需要更复杂的逻辑
            for (device in pairedDevices) {
                try {
                    // 通过反射检查设备连接状态
                    val method = device.javaClass.getMethod("isConnected")
                    val isConnected = method.invoke(device) as Boolean
                    if (isConnected) {
                        Log.d(TAG, "Found connected Bluetooth device: ${device.name}")
                        return true
                    }
                } catch (e: Exception) {
                    // 反射失败，跳过此设备
                    Log.d(TAG, "Failed to check connection status for device: ${device.name}")
                }
            }

            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking any Bluetooth device connection", e)
            false
        }
    }

    /**
     * 检查特定蓝牙设备是否连接
     */
    private fun isSpecificBluetoothDeviceConnected(deviceName: String): Boolean {
        return try {
            if (!isBluetoothEnabled()) {
                return false
            }

            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled) {
                return false
            }

            // 检查是否需要蓝牙权限
            if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.BLUETOOTH_CONNECT)
                != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Bluetooth connect permission not granted")
                return false
            }

            // 获取已配对的设备
            val pairedDevices = bluetoothAdapter.bondedDevices

            // 查找目标设备
            for (device in pairedDevices) {
                if (device.name == deviceName) {
                    try {
                        // 通过反射检查设备连接状态
                        val method = device.javaClass.getMethod("isConnected")
                        val isConnected = method.invoke(device) as Boolean
                        Log.d(TAG, "Specific Bluetooth device check: $deviceName, connected=$isConnected")
                        return isConnected
                    } catch (e: Exception) {
                        Log.d(TAG, "Failed to check connection status for device: $deviceName")
                        return false
                    }
                }
            }

            Log.d(TAG, "Bluetooth device not found: $deviceName")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking specific Bluetooth device connection", e)
            false
        }
    }

    /**
     * 检查指定应用是否正在运行
     */
    private fun isAppRunning(packageName: String): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningApps = activityManager.runningAppProcesses

            if (runningApps != null) {
                for (processInfo in runningApps) {
                    if (processInfo.processName == packageName) {
                        return true
                    }
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if app is running: $packageName", e)
            false
        }
    }

    // 用于跟踪统一条件的状态变化
    private var lastAppStateForUnified: String? = null

    /**
     * 检查统一应用状态条件是否满足
     *
     * @param condition 统一应用状态条件
     * @return 统一应用状态条件是否满足
     */
    private fun isAppStateConditionSatisfied(condition: AppStateCondition): Boolean {
        return try {
            // 使用协程同步获取当前前台应用
            val currentForegroundApp = kotlinx.coroutines.runBlocking {
                AppStateMonitor.getCurrentForegroundApp(context)
            }

            // 检查应用状态变化
            val appStateChanged = currentForegroundApp != lastAppStateForUnified

            // 更新最后的前台应用
            val previousApp = lastAppStateForUnified
            lastAppStateForUnified = currentForegroundApp

            // 根据状态类型检查是否满足条件
            val result = when (condition.stateType) {
                AppStateType.FOREGROUND -> {
                    if (condition.targetPackageName.isEmpty()) {
                        // 任何应用进入前台
                        appStateChanged && previousApp == null && currentForegroundApp != null
                    } else {
                        // 指定应用进入前台
                        appStateChanged &&
                        currentForegroundApp == condition.targetPackageName &&
                        previousApp != condition.targetPackageName
                    }
                }
                AppStateType.BACKGROUND -> {
                    if (condition.targetPackageName.isEmpty()) {
                        // 任何应用进入后台
                        appStateChanged && previousApp != null && currentForegroundApp == null
                    } else {
                        // 指定应用进入后台
                        appStateChanged &&
                        previousApp == condition.targetPackageName &&
                        currentForegroundApp != condition.targetPackageName
                    }
                }
                AppStateType.STATE_CHANGED -> {
                    if (condition.targetPackageName.isEmpty()) {
                        // 任何应用状态变化
                        appStateChanged
                    } else {
                        // 指定应用状态变化
                        appStateChanged && (
                            previousApp == condition.targetPackageName ||
                            currentForegroundApp == condition.targetPackageName
                        )
                    }
                }
                AppStateType.LAUNCHED -> {
                    // 应用启动（从无前台应用到有前台应用，或从其他应用切换到目标应用）
                    if (condition.targetPackageName.isEmpty()) {
                        // 任何应用启动
                        appStateChanged && currentForegroundApp != null
                    } else {
                        // 指定应用启动
                        appStateChanged &&
                        currentForegroundApp == condition.targetPackageName &&
                        previousApp != condition.targetPackageName
                    }
                }
                AppStateType.CLOSED -> {
                    // 应用关闭（从有前台应用到无前台应用，或从目标应用切换到其他应用）
                    if (condition.targetPackageName.isEmpty()) {
                        // 任何应用关闭
                        appStateChanged && previousApp != null && currentForegroundApp == null
                    } else {
                        // 指定应用关闭
                        appStateChanged &&
                        previousApp == condition.targetPackageName &&
                        currentForegroundApp != condition.targetPackageName
                    }
                }
                AppStateType.INSTALLED -> {
                    // 应用安装 - 这需要通过PackageManager监听器来实现
                    // 在静态检查中总是返回false
                    Log.d(TAG, "App install condition: requires package manager monitoring")
                    false
                }
                AppStateType.UNINSTALLED -> {
                    // 应用删除 - 这需要通过PackageManager监听器来实现
                    // 在静态检查中总是返回false
                    Log.d(TAG, "App uninstall condition: requires package manager monitoring")
                    false
                }
                AppStateType.UPDATED -> {
                    // 应用更新 - 这需要通过PackageManager监听器来实现
                    // 在静态检查中总是返回false
                    Log.d(TAG, "App update condition: requires package manager monitoring")
                    false
                }

                AppStateType.BACKGROUND_TIME_EXCEEDED -> {
                    // 后台时间超过阈值条件 - 这应该通过专门的后台时间监控器处理
                    // 在这里不应该到达，因为已经在AppStateConditionMonitor中进行了分流
                    Log.w(TAG, "Background time exceeded condition should be handled by AppStateConditionMonitor")
                    false
                }
                AppStateType.INTERFACE_CLICK -> {
                    // 界面点击条件 - 这应该通过界面交互无障碍服务处理
                    // 在这里不应该到达，因为已经在AppStateConditionMonitor中进行了分流
                    Log.w(TAG, "Interface click condition should be handled by InterfaceInteractionAccessibilityService")
                    false
                }
                AppStateType.SCREEN_CONTENT -> {
                    // 屏幕内容条件 - 这应该通过界面交互无障碍服务处理
                    // 在这里不应该到达，因为已经在AppStateConditionMonitor中进行了分流
                    Log.w(TAG, "Screen content condition should be handled by InterfaceInteractionAccessibilityService")
                    false
                }
                AppStateType.TASKER_LOCALE_PLUGIN_CONDITION -> {
                    // Tasker/Locale插件条件 - 这应该通过专门的插件条件评估方法处理
                    // 在这里不应该到达，因为已经在上层进行了分流
                    Log.w(TAG, "Tasker/Locale plugin condition should be handled by specialized method")
                    false
                }
            }

            if (result) {
                Log.d(TAG, "AppState condition satisfied: ${condition.getDescription()}, " +
                        "previous=$previousApp, current=$currentForegroundApp, type=${condition.stateType}")
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking app state condition", e)
            false
        }
    }

    /**
     * 检查重新设计的电池状态条件是否满足
     * 支持层次化的电池条件检测：主类型 + 子类型 + 参数
     *
     * @param condition 电池状态条件
     * @return 电池状态条件是否满足
     */
    private fun isBatteryStateConditionSatisfied(condition: BatteryStateCondition): Boolean {
        return try {
            when (condition.conditionType) {
                BatteryConditionType.BATTERY_LEVEL -> {
                    evaluateBatteryLevelCondition(condition)
                }
                BatteryConditionType.CHARGING_STATE -> {
                    evaluateChargingStateCondition(condition)
                }
                BatteryConditionType.BATTERY_TEMPERATURE -> {
                    evaluateBatteryTemperatureCondition(condition)
                }
                BatteryConditionType.POWER_BUTTON -> {
                    evaluatePowerButtonCondition(condition)
                }
                BatteryConditionType.POWER_SAVE_MODE -> {
                    evaluatePowerSaveModeCondition(condition)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking battery state condition", e)
            false
        }
    }

    /**
     * 评估电池电量条件
     */
    private fun evaluateBatteryLevelCondition(condition: BatteryStateCondition): Boolean {
        val currentBatteryLevel = getBatteryLevel()

        return when (condition.levelSubType) {
            BatteryLevelSubType.BELOW -> {
                val result = currentBatteryLevel < condition.levelThreshold
                Log.d(TAG, "Battery level check (BELOW): level=$currentBatteryLevel, threshold=${condition.levelThreshold}, satisfied=$result")
                result
            }
            BatteryLevelSubType.ABOVE -> {
                val result = currentBatteryLevel > condition.levelThreshold
                Log.d(TAG, "Battery level check (ABOVE): level=$currentBatteryLevel, threshold=${condition.levelThreshold}, satisfied=$result")
                result
            }
            BatteryLevelSubType.CHANGED -> {
                val conditionKey = "battery_level_${condition.id}"
                val lastBatteryLevel = lastBatteryLevels[conditionKey]

                // 更新电量缓存
                lastBatteryLevels[conditionKey] = currentBatteryLevel

                // 如果是第一次检查，不触发
                if (lastBatteryLevel == null) {
                    Log.d(TAG, "Battery level check (CHANGED): first check, not triggering")
                    return false
                }

                // 检查电量变化是否超过阈值
                val levelChange = kotlin.math.abs(currentBatteryLevel - lastBatteryLevel)
                val result = levelChange >= condition.levelChangeThreshold
                Log.d(TAG, "Battery level check (CHANGED): last=$lastBatteryLevel, current=$currentBatteryLevel, change=$levelChange, threshold=${condition.levelChangeThreshold}, satisfied=$result")
                result
            }
            BatteryLevelSubType.LOW_WARNING -> {
                val result = currentBatteryLevel <= 15
                Log.d(TAG, "Battery level check (LOW_WARNING): level=$currentBatteryLevel, satisfied=$result")
                result
            }
        }
    }

    /**
     * 评估充电状态条件
     */
    private fun evaluateChargingStateCondition(condition: BatteryStateCondition): Boolean {
        val isCurrentlyCharging = isCharging()
        val currentBatteryLevel = getBatteryLevel()

        return when (condition.chargingSubType) {
            ChargingSubType.STARTED -> {
                val conditionKey = "charging_started_${condition.id}"
                val lastChargingState = lastChargingStates[conditionKey]

                // 更新充电状态缓存
                lastChargingStates[conditionKey] = isCurrentlyCharging

                // 如果是第一次检查，不触发
                if (lastChargingState == null) {
                    Log.d(TAG, "Charging state check (STARTED): first check, not triggering")
                    return false
                }

                // 检查是否从未充电变为充电
                val result = !lastChargingState && isCurrentlyCharging
                Log.d(TAG, "Charging state check (STARTED): last=$lastChargingState, current=$isCurrentlyCharging, satisfied=$result")
                result
            }
            ChargingSubType.STOPPED -> {
                val conditionKey = "charging_stopped_${condition.id}"
                val lastChargingState = lastChargingStates[conditionKey]

                // 更新充电状态缓存
                lastChargingStates[conditionKey] = isCurrentlyCharging

                // 如果是第一次检查，不触发
                if (lastChargingState == null) {
                    Log.d(TAG, "Charging state check (STOPPED): first check, not triggering")
                    return false
                }

                // 检查是否从充电变为未充电
                val result = lastChargingState && !isCurrentlyCharging
                Log.d(TAG, "Charging state check (STOPPED): last=$lastChargingState, current=$isCurrentlyCharging, satisfied=$result")
                result
            }
            ChargingSubType.FULLY_CHARGED -> {
                val result = currentBatteryLevel >= 100 && isCurrentlyCharging
                Log.d(TAG, "Charging state check (FULLY_CHARGED): level=$currentBatteryLevel, charging=$isCurrentlyCharging, satisfied=$result")
                result
            }
        }
    }

    /**
     * 评估电池温度条件
     * 注意：Android系统对电池温度的访问有限制，此功能可能需要特殊权限
     */
    private fun evaluateBatteryTemperatureCondition(condition: BatteryStateCondition): Boolean {
        return try {
            val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            val temperature = batteryIntent?.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1) ?: -1

            if (temperature == -1) {
                Log.w(TAG, "Unable to get battery temperature")
                return false
            }

            // 温度单位是0.1摄氏度，需要转换为摄氏度
            val temperatureCelsius = temperature / 10.0f

            val conditionKey = "battery_temperature_${condition.id}"
            val lastTemperature = lastBatteryTemperatures[conditionKey]

            // 更新温度缓存
            lastBatteryTemperatures[conditionKey] = temperatureCelsius

            // 如果是第一次检查，不触发
            if (lastTemperature == null) {
                Log.d(TAG, "Battery temperature check: first check, not triggering")
                return false
            }

            val result = when (condition.temperatureSubType) {
                TemperatureSubType.INCREASED -> {
                    val tempIncrease = temperatureCelsius - lastTemperature
                    val satisfied = tempIncrease >= condition.temperatureThreshold
                    Log.d(TAG, "Battery temperature check (INCREASED): last=${lastTemperature}°C, current=${temperatureCelsius}°C, increase=${tempIncrease}°C, threshold=${condition.temperatureThreshold}°C, satisfied=$satisfied")
                    satisfied
                }
                TemperatureSubType.DECREASED -> {
                    val tempDecrease = lastTemperature - temperatureCelsius
                    val satisfied = tempDecrease >= condition.temperatureThreshold
                    Log.d(TAG, "Battery temperature check (DECREASED): last=${lastTemperature}°C, current=${temperatureCelsius}°C, decrease=${tempDecrease}°C, threshold=${condition.temperatureThreshold}°C, satisfied=$satisfied")
                    satisfied
                }
                TemperatureSubType.CHANGED -> {
                    val tempChange = kotlin.math.abs(temperatureCelsius - lastTemperature)
                    val satisfied = tempChange >= condition.temperatureThreshold
                    Log.d(TAG, "Battery temperature check (CHANGED): last=${lastTemperature}°C, current=${temperatureCelsius}°C, change=${tempChange}°C, threshold=${condition.temperatureThreshold}°C, satisfied=$satisfied")
                    satisfied
                }
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking battery temperature condition", e)
            false
        }
    }

    /**
     * 评估电源键条件
     * 通过屏幕状态变化来推断电源键按下
     */
    private fun evaluatePowerButtonCondition(condition: BatteryStateCondition): Boolean {
        return try {
            // 通过屏幕状态变化来推断电源键按下
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val isScreenOn = powerManager.isInteractive

            // 获取上次屏幕状态
            val lastScreenState = getLastScreenState()

            // 检测屏幕状态变化
            val powerButtonPressed = when (condition.powerButtonSubType) {
                PowerButtonSubType.PRESSED -> {
                    // 检测到屏幕状态变化（开启或关闭都算按下）
                    val stateChanged = lastScreenState != isScreenOn

                    // 检查按下次数
                    if (stateChanged) {
                        incrementPowerButtonPressCount()
                        getPowerButtonPressCount() >= condition.powerButtonPressCount
                    } else {
                        false
                    }
                }
            }

            // 更新屏幕状态
            updateLastScreenState(isScreenOn)

            if (powerButtonPressed) {
                Log.d(TAG, "Power button ${condition.powerButtonSubType} detected (${condition.powerButtonPressCount} times)")
            }

            powerButtonPressed

        } catch (e: Exception) {
            Log.e(TAG, "Error checking power button condition", e)
            false
        }
    }

    /**
     * 获取上次屏幕状态
     */
    private fun getLastScreenState(): Boolean {
        val prefs = context.getSharedPreferences("condition_evaluator", Context.MODE_PRIVATE)
        return prefs.getBoolean("last_screen_state", true)
    }

    /**
     * 更新屏幕状态
     */
    private fun updateLastScreenState(isScreenOn: Boolean) {
        val prefs = context.getSharedPreferences("condition_evaluator", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("last_screen_state", isScreenOn).apply()
    }

    /**
     * 增加电源键按下次数
     */
    private fun incrementPowerButtonPressCount() {
        val prefs = context.getSharedPreferences("condition_evaluator", Context.MODE_PRIVATE)
        val currentTime = System.currentTimeMillis()
        val lastPressTime = prefs.getLong("last_power_press_time", 0)
        val currentCount = prefs.getInt("power_button_press_count", 0)

        // 如果距离上次按下超过3秒，重置计数
        val newCount = if (currentTime - lastPressTime > 3000) {
            1
        } else {
            currentCount + 1
        }

        prefs.edit()
            .putLong("last_power_press_time", currentTime)
            .putInt("power_button_press_count", newCount)
            .apply()
    }

    /**
     * 获取电源键按下次数
     */
    private fun getPowerButtonPressCount(): Int {
        val prefs = context.getSharedPreferences("condition_evaluator", Context.MODE_PRIVATE)
        return prefs.getInt("power_button_press_count", 0)
    }

    /**
     * 评估省电模式条件
     */
    private fun evaluatePowerSaveModeCondition(condition: BatteryStateCondition): Boolean {
        return try {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val isPowerSaveModeEnabled = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                powerManager.isPowerSaveMode
            } else {
                // 对于较低版本，检查系统设置
                try {
                    val powerSaveMode = Settings.Global.getInt(
                        context.contentResolver,
                        "low_power", 0
                    )
                    powerSaveMode == 1
                } catch (e: Exception) {
                    Log.w(TAG, "Unable to check power save mode on older Android version", e)
                    false
                }
            }

            val result = when (condition.powerSaveModeSubType) {
                PowerSaveModeSubType.ENABLED -> isPowerSaveModeEnabled
                PowerSaveModeSubType.DISABLED -> !isPowerSaveModeEnabled
            }

            Log.d(TAG, "Power save mode check (${condition.powerSaveModeSubType}): enabled=$isPowerSaveModeEnabled, satisfied=$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking power save mode condition", e)
            false
        }
    }

    /**
     * 检查通信状态条件是否满足
     *
     * 注意：此方法主要用于静态状态检查，实际的通信事件监听需要通过
     * CommunicationStateMonitor来实现
     *
     * @param condition 通信状态条件
     * @return 通信状态条件是否满足
     */
    private fun isCommunicationStateConditionSatisfied(condition: CommunicationStateCondition): Boolean {
        return try {
            // 检查权限
            if (!com.weinuo.quickcommands.utils.CommunicationPermissionUtil.hasAllCommunicationPermissions(context)) {
                Log.w(TAG, "Communication permissions not granted, condition cannot be evaluated")
                return false
            }

            when (condition.stateType) {
                CommunicationStateType.SMS_SENT,
                CommunicationStateType.SMS_RECEIVED,
                CommunicationStateType.OUTGOING_CALL,
                CommunicationStateType.INCOMING_CALL,
                CommunicationStateType.CALL_ENDED,
                CommunicationStateType.MISSED_CALL,
                CommunicationStateType.DIAL_NUMBER -> {
                    // 这些都是事件型条件，需要通过监听器来触发
                    // 在静态检查中总是返回false
                    Log.d(TAG, "Communication event condition (${condition.stateType}): requires event monitoring")
                    false
                }
                CommunicationStateType.CALL_ACTIVE -> {
                    // 通话中状态可以通过TelecomManager检查
                    isCallActive()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking communication state condition", e)
            false
        }
    }

    /**
     * 检查是否有活跃的通话
     *
     * @return 是否有活跃的通话
     */
    private fun isCallActive(): Boolean {
        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val telecomManager = context.getSystemService(Context.TELECOM_SERVICE) as? android.telecom.TelecomManager
                telecomManager?.isInCall ?: false
            } else {
                // 对于较低版本的Android，使用AudioManager检查
                val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                audioManager.mode == AudioManager.MODE_IN_CALL
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking call active state", e)
            false
        }
    }

    /**
     * 检查统一时间条件是否满足
     * 整合秒表、日出日落、日程时间、周期时间、延迟触发、周期触发等所有时间相关功能
     *
     * @param condition 统一时间条件
     * @return 时间条件是否满足
     */
    private fun isTimeBasedConditionSatisfied(condition: TimeBasedCondition): Boolean {
        return when (condition.timeConditionType) {
            TimeConditionType.STOPWATCH -> isStopwatchMatching(condition)
            TimeConditionType.SUN_EVENT -> isSunEventMatching(condition)
            TimeConditionType.SCHEDULED_TIME -> isScheduledTimeMatching(condition)
            TimeConditionType.PERIODIC_TIME -> isPeriodicTimeMatching(condition)
            TimeConditionType.DELAYED_TRIGGER -> isDelayedTriggerMatching(condition)
            TimeConditionType.PERIODIC_TRIGGER -> isPeriodicTriggerMatching(condition)
        }
    }

    /**
     * 检查秒表条件是否满足
     */
    private fun isStopwatchMatching(condition: TimeBasedCondition): Boolean {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - condition.startTime
        val targetMillis = (condition.stopwatchHours * 3600 +
                           condition.stopwatchMinutes * 60 +
                           condition.stopwatchSeconds) * 1000L

        // 检查是否已经触发过这个秒表条件
        val conditionKey = "${condition.id}_${condition.startTime}"
        if (triggeredDelayedConditions.contains(conditionKey)) {
            return false
        }

        // 秒表触发：检查是否已经到达指定时间
        val result = elapsedTime >= targetMillis

        if (result) {
            // 标记为已触发，防止重复触发
            triggeredDelayedConditions.add(conditionKey)
            Log.d(TAG, "Stopwatch: triggered and marked as completed. elapsed=${elapsedTime}ms, target=${targetMillis}ms")
        } else {
            Log.d(TAG, "Stopwatch: not yet triggered. elapsed=${elapsedTime}ms, target=${targetMillis}ms")
        }

        return result
    }

    /**
     * 检查日出日落条件是否满足
     */
    private fun isSunEventMatching(condition: TimeBasedCondition): Boolean {
        val currentCalendar = Calendar.getInstance()
        val currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY)
        val currentMinute = currentCalendar.get(Calendar.MINUTE)

        // 获取位置信息
        val latitude: Double
        val longitude: Double

        if (condition.latitude != null && condition.longitude != null) {
            // 使用指定位置
            latitude = condition.latitude
            longitude = condition.longitude
        } else {
            // 尝试获取系统位置
            val location = SunriseSunsetCalculator.getCurrentLocation(context)
            if (location == null) {
                Log.w(TAG, "Unable to get location for sun event calculation")
                return false
            }
            latitude = location.latitude
            longitude = location.longitude
        }

        // 计算日出或日落时间
        val sunEventTime = when (condition.sunEventType) {
            SunEventType.SUNRISE -> SunriseSunsetCalculator.calculateSunrise(latitude, longitude, currentCalendar)
            SunEventType.SUNSET -> SunriseSunsetCalculator.calculateSunset(latitude, longitude, currentCalendar)
        }

        if (sunEventTime == null) {
            Log.w(TAG, "Unable to calculate sun event time for ${condition.sunEventType} at ($latitude, $longitude)")
            return false
        }

        val sunEventHour = sunEventTime.get(Calendar.HOUR_OF_DAY)
        val sunEventMinute = sunEventTime.get(Calendar.MINUTE)

        // 检查当前时间是否匹配日出日落时间
        val result = currentHour == sunEventHour && currentMinute == sunEventMinute

        Log.d(TAG, "SunEvent: ${condition.sunEventType} at ${sunEventHour}:${sunEventMinute}, " +
                "current ${currentHour}:${currentMinute}, satisfied=$result")

        return result
    }

    /**
     * 检查日程时间条件是否满足
     */
    private fun isScheduledTimeMatching(condition: TimeBasedCondition): Boolean {
        val currentCalendar = Calendar.getInstance()
        val currentYear = currentCalendar.get(Calendar.YEAR)
        val currentMonth = currentCalendar.get(Calendar.MONTH) + 1 // Calendar.MONTH 是从0开始的
        val currentDay = currentCalendar.get(Calendar.DAY_OF_MONTH)
        val currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY)
        val currentMinute = currentCalendar.get(Calendar.MINUTE)

        // 检查时间是否匹配
        if (currentHour != condition.hour || currentMinute != condition.minute) {
            return false
        }

        // 创建起始时间的Calendar
        val startCalendar = Calendar.getInstance().apply {
            set(condition.year, condition.month - 1, condition.day, condition.hour, condition.minute, 0)
            set(Calendar.MILLISECOND, 0)
        }

        // 检查当前时间是否已经到达或超过起始时间
        if (currentCalendar.timeInMillis < startCalendar.timeInMillis) {
            return false
        }

        // 根据重复模式检查是否应该触发
        return when (condition.timeRepeatMode) {
            TimeRepeatMode.ONCE -> {
                // 仅一次：精确匹配起始日期
                condition.year == currentYear &&
                condition.month == currentMonth &&
                condition.day == currentDay
            }
            TimeRepeatMode.DAILY -> {
                // 每天：从起始日期开始，每天都触发
                true
            }
            TimeRepeatMode.WEEKLY -> {
                // 每周：从起始日期开始，每周的同一天触发
                val startDayOfWeek = startCalendar.get(Calendar.DAY_OF_WEEK)
                val currentDayOfWeek = currentCalendar.get(Calendar.DAY_OF_WEEK)
                startDayOfWeek == currentDayOfWeek
            }
            TimeRepeatMode.BI_WEEKLY -> {
                // 每两周：从起始日期开始，每两周的同一天触发
                val startDayOfWeek = startCalendar.get(Calendar.DAY_OF_WEEK)
                val currentDayOfWeek = currentCalendar.get(Calendar.DAY_OF_WEEK)

                if (startDayOfWeek == currentDayOfWeek) {
                    // 计算从起始日期到现在经过了多少周
                    val startDateOnly = Calendar.getInstance().apply {
                        set(condition.year, condition.month - 1, condition.day, 0, 0, 0)
                        set(Calendar.MILLISECOND, 0)
                    }
                    val currentDateOnly = Calendar.getInstance().apply {
                        set(currentYear, currentMonth - 1, currentDay, 0, 0, 0)
                        set(Calendar.MILLISECOND, 0)
                    }

                    val daysDiff = ((currentDateOnly.timeInMillis - startDateOnly.timeInMillis) / (24 * 60 * 60 * 1000)).toInt()
                    val weeksDiff = daysDiff / 7

                    Log.d(TAG, "BI_WEEKLY check: daysDiff=$daysDiff, weeksDiff=$weeksDiff, isEvenWeek=${weeksDiff % 2 == 0}")
                    weeksDiff % 2 == 0
                } else {
                    false
                }
            }
            TimeRepeatMode.MONTHLY -> {
                // 每月：从起始日期开始，每月的同一天触发
                condition.day == currentDay
            }
            TimeRepeatMode.YEARLY -> {
                // 每年：从起始日期开始，每年的同一月同一天触发
                condition.month == currentMonth && condition.day == currentDay
            }
        }
    }

    /**
     * 检查周期时间条件是否满足
     */
    private fun isPeriodicTimeMatching(condition: TimeBasedCondition): Boolean {
        val currentCalendar = Calendar.getInstance()
        val currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY)
        val currentMinute = currentCalendar.get(Calendar.MINUTE)

        // 检查时间是否匹配
        if (currentHour != condition.hour || currentMinute != condition.minute) {
            return false
        }

        // 根据重复模式检查是否应该触发
        val result = when (condition.scheduledRepeatMode) {
            ScheduledRepeatMode.ONCE -> {
                // 仅一次：需要外部逻辑控制，这里总是返回true
                true
            }
            ScheduledRepeatMode.DAILY -> {
                // 每天：总是触发
                true
            }
            ScheduledRepeatMode.CUSTOM -> {
                // 自定义：检查今天是否在选择的星期几中
                val today = Calendar.getInstance().get(Calendar.DAY_OF_WEEK)
                val todayEnum = DayOfWeek.fromCalendarValue(today)
                todayEnum != null && condition.selectedDays.contains(todayEnum)
            }
        }

        Log.d(TAG, "PeriodicTime: time=${condition.hour}:${condition.minute}, " +
                "mode=${condition.scheduledRepeatMode}, satisfied=$result")
        return result
    }

    /**
     * 检查延迟触发条件是否满足
     */
    private fun isDelayedTriggerMatching(condition: TimeBasedCondition): Boolean {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - condition.startTime
        val intervalMillis = condition.interval * condition.unit.milliseconds

        // 检查是否已经触发过这个延迟条件
        val conditionKey = "${condition.id}_${condition.startTime}"
        if (triggeredDelayedConditions.contains(conditionKey)) {
            return false
        }

        // 延迟触发：检查是否已经过了指定时间
        val result = elapsedTime >= intervalMillis

        if (result) {
            // 标记为已触发，防止重复触发
            triggeredDelayedConditions.add(conditionKey)
            Log.d(TAG, "DelayedTrigger: triggered and marked as completed. elapsed=${elapsedTime}ms, interval=${intervalMillis}ms")
        } else {
            Log.d(TAG, "DelayedTrigger: not yet triggered. elapsed=${elapsedTime}ms, interval=${intervalMillis}ms")
        }

        return result
    }

    /**
     * 检查周期触发条件是否满足
     */
    private fun isPeriodicTriggerMatching(condition: TimeBasedCondition): Boolean {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - condition.startTime
        val intervalMillis = condition.interval * condition.unit.milliseconds

        // 周期触发：检查是否到了下一个间隔点
        if (elapsedTime < intervalMillis) {
            // 还没到第一个间隔点
            return false
        } else {
            // 检查是否刚好到了某个间隔点（允许一定的误差）
            val intervalCount = elapsedTime / intervalMillis
            val remainder = elapsedTime % intervalMillis

            // 动态调整容差：对于较短的间隔使用较小的容差，对于较长的间隔使用较大的容差
            val tolerance = when {
                intervalMillis <= 60000L -> 500L // 1分钟以内：0.5秒容差
                intervalMillis <= 3600000L -> 2000L // 1小时以内：2秒容差
                else -> 5000L // 1小时以上：5秒容差
            }

            val isAtInterval = remainder <= tolerance || remainder >= (intervalMillis - tolerance)

            if (isAtInterval) {
                // 检查是否在同一个周期内已经触发过
                val conditionKey = condition.id
                val lastTriggerTime = lastPeriodicTriggerTimes[conditionKey] ?: 0L
                val currentIntervalStart = condition.startTime + (intervalCount * intervalMillis)

                if (lastTriggerTime >= currentIntervalStart) {
                    // 在当前周期内已经触发过
                    Log.d(TAG, "PeriodicTrigger: already triggered in current interval. " +
                            "lastTrigger=$lastTriggerTime, intervalStart=$currentIntervalStart")
                    return false
                }

                // 记录本次触发时间
                lastPeriodicTriggerTimes[conditionKey] = currentTime
                Log.d(TAG, "PeriodicTrigger: triggered. elapsed=${elapsedTime}ms, interval=${intervalMillis}ms, " +
                        "count=$intervalCount, remainder=$remainder, tolerance=$tolerance")
                return true
            } else {
                Log.d(TAG, "PeriodicTrigger: not at interval. elapsed=${elapsedTime}ms, interval=${intervalMillis}ms, " +
                        "count=$intervalCount, remainder=$remainder, tolerance=$tolerance")
                return false
            }
        }
    }

    /**
     * 检查设备事件条件是否满足
     * 统一的设备事件条件评估，支持17种设备事件类型的检测逻辑
     *
     * @param condition 设备事件条件
     * @return 设备事件条件是否满足
     */
    private fun isDeviceEventConditionSatisfied(condition: DeviceEventCondition): Boolean {
        return try {
            when (condition.eventType) {
                DeviceEventType.GPS_STATE -> evaluateGpsStateCondition(condition)
                DeviceEventType.LOGCAT_MESSAGE -> evaluateLogcatMessageCondition(condition)
                DeviceEventType.CLIPBOARD_CHANGED -> evaluateClipboardChangedCondition(condition)
                DeviceEventType.SCREEN_STATE -> evaluateScreenStateCondition(condition)
                DeviceEventType.DOCK_STATE -> evaluateDockStateCondition(condition)
                DeviceEventType.INTENT_RECEIVED -> evaluateIntentReceivedCondition(condition)
                DeviceEventType.SIM_CARD_STATE -> evaluateSimCardStateCondition(condition)
                DeviceEventType.DARK_THEME_CHANGED -> evaluateDarkThemeChangedCondition(condition)
                DeviceEventType.LOGIN_ATTEMPT_FAILED -> evaluateLoginAttemptFailedCondition(condition)
                DeviceEventType.SYSTEM_SETTING_CHANGED -> evaluateSystemSettingChangedCondition(condition)
                DeviceEventType.AUTO_SYNC_STATE -> evaluateAutoSyncStateCondition(condition)
                DeviceEventType.DEVICE_BOOT_COMPLETED -> evaluateDeviceBootCompletedCondition(condition)
                DeviceEventType.NOTIFICATION_EVENT -> evaluateNotificationEventCondition(condition)
                DeviceEventType.RINGER_MODE_CHANGED -> evaluateRingerModeChangedCondition(condition)
                DeviceEventType.MUSIC_PLAYBACK_STATE -> evaluateMusicPlaybackStateCondition(condition)
                DeviceEventType.AIRPLANE_MODE_STATE -> evaluateAirplaneModeStateCondition(condition)
                DeviceEventType.VOLUME_CHANGED -> evaluateVolumeChangedCondition(condition)
                DeviceEventType.MEMORY_STATE -> evaluateMemoryStateCondition(condition)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking device event condition: ${condition.eventType}", e)
            false
        }
    }

    /**
     * 评估GPS状态条件
     */
    private fun evaluateGpsStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            val isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)

            val result = when (condition.gpsStateType) {
                GpsStateType.ENABLED -> isGpsEnabled
                GpsStateType.DISABLED -> !isGpsEnabled
            }

            Log.d(TAG, "GPS state check: enabled=$isGpsEnabled, expected=${condition.gpsStateType}, satisfied=$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking GPS state", e)
            false
        }
    }

    /**
     * 评估Logcat消息条件
     * 注意：此功能需要特殊权限，实际实现需要通过logcat命令
     */
    private fun evaluateLogcatMessageCondition(condition: DeviceEventCondition): Boolean {
        return try {
            // 构建logcat命令，支持多个缓冲区
            val bufferArgs = condition.logcatBufferTypes.joinToString(" ") { bufferType ->
                when (bufferType) {
                    LogcatBufferType.MAIN -> "-b main"
                    LogcatBufferType.SYSTEM -> "-b system"
                    LogcatBufferType.CRASH -> "-b crash"
                    LogcatBufferType.KERNEL -> "-b kernel"
                    LogcatBufferType.RADIO -> "-b radio"
                    LogcatBufferType.EVENTS -> "-b events"
                }
            }

            val command = "logcat $bufferArgs -d -t 50" // 获取最近50行日志
            val process = Runtime.getRuntime().exec(command)
            val reader = process.inputStream.bufferedReader()

            val logLines = reader.readLines()
            process.waitFor()
            reader.close()

            // 检查日志内容
            for (line in logLines) {
                // 检查组件名称匹配
                if (condition.logcatComponent.isNotEmpty()) {
                    val componentMatches = if (condition.logcatComponent.contains("*") || condition.logcatComponent.contains("?")) {
                        // 通配符匹配
                        val regex = condition.logcatComponent
                            .replace("*", ".*")
                            .replace("?", ".")
                            .toRegex(if (condition.logcatCaseSensitive) setOf() else setOf(RegexOption.IGNORE_CASE))
                        regex.containsMatchIn(line)
                    } else {
                        // 精确匹配
                        if (condition.logcatCaseSensitive) {
                            line.contains(condition.logcatComponent)
                        } else {
                            line.contains(condition.logcatComponent, ignoreCase = true)
                        }
                    }

                    if (!componentMatches) continue
                }

                // 检查文字内容匹配
                if (condition.logcatText.isNotEmpty()) {
                    val textMatches = if (condition.logcatText.contains("*") || condition.logcatText.contains("?")) {
                        // 通配符匹配
                        val regex = condition.logcatText
                            .replace("*", ".*")
                            .replace("?", ".")
                            .toRegex(if (condition.logcatCaseSensitive) setOf() else setOf(RegexOption.IGNORE_CASE))
                        regex.containsMatchIn(line)
                    } else {
                        // 精确匹配
                        if (condition.logcatCaseSensitive) {
                            line.contains(condition.logcatText)
                        } else {
                            line.contains(condition.logcatText, ignoreCase = true)
                        }
                    }

                    if (textMatches) {
                        return true
                    }
                }
            }

            Log.d(TAG, "Logcat message condition not satisfied: no matching lines found")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating logcat message condition", e)
            false
        }
    }

    /**
     * 评估剪贴板变化条件
     */
    private fun evaluateClipboardChangedCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

            // 检查剪贴板是否有内容
            if (!clipboardManager.hasPrimaryClip()) {
                Log.d(TAG, "Clipboard is empty")
                return false
            }

            val clipData = clipboardManager.primaryClip
            if (clipData == null || clipData.itemCount == 0) {
                Log.d(TAG, "Clipboard data is null or empty")
                return false
            }

            val clipText = clipData.getItemAt(0).text?.toString() ?: ""

            // 如果没有指定匹配文字，则任何剪贴板内容都匹配
            if (condition.clipboardText.isEmpty()) {
                Log.d(TAG, "Clipboard condition satisfied: any content matches")
                return true
            }

            // 检查文字匹配
            val matches = if (condition.clipboardUseRegex) {
                try {
                    val regexOptions = if (condition.clipboardCaseSensitive) {
                        emptySet()
                    } else {
                        setOf(RegexOption.IGNORE_CASE)
                    }
                    val regex = condition.clipboardText.toRegex(regexOptions)
                    regex.containsMatchIn(clipText)
                } catch (e: Exception) {
                    Log.e(TAG, "Invalid regex pattern: ${condition.clipboardText}", e)
                    false
                }
            } else {
                // 支持通配符匹配
                if (condition.clipboardText.contains("*") || condition.clipboardText.contains("?")) {
                    val regexOptions = if (condition.clipboardCaseSensitive) {
                        emptySet()
                    } else {
                        setOf(RegexOption.IGNORE_CASE)
                    }
                    val regex = condition.clipboardText
                        .replace("*", ".*")
                        .replace("?", ".")
                        .toRegex(regexOptions)
                    regex.containsMatchIn(clipText)
                } else {
                    // 精确匹配（根据大小写敏感设置）
                    clipText.contains(condition.clipboardText, ignoreCase = !condition.clipboardCaseSensitive)
                }
            }

            if (matches) {
                Log.d(TAG, "Clipboard condition satisfied: content matches pattern")
            } else {
                Log.d(TAG, "Clipboard condition not satisfied: content does not match pattern")
            }

            matches
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating clipboard condition", e)
            false
        }
    }

    /**
     * 评估屏幕状态条件（整合原有屏幕状态逻辑）
     */
    private fun evaluateScreenStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            when (condition.screenEventType) {
                ScreenEventType.ON -> isScreenOn()
                ScreenEventType.OFF -> !isScreenOn()
                ScreenEventType.UNLOCKED -> isScreenUnlocked()
                ScreenEventType.AUTO_ROTATE_ENABLED -> isAutoRotateEnabled()
                ScreenEventType.AUTO_ROTATE_DISABLED -> !isAutoRotateEnabled()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking screen state condition", e)
            false
        }
    }

    /**
     * 评估底座连接条件
     */
    private fun evaluateDockStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            // 通过UiModeManager检测底座状态
            val uiModeManager = context.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
            val currentModeType = uiModeManager.currentModeType

            val result = when (condition.dockStateType) {
                DockStateType.ANY_DOCK -> {
                    // 检查是否连接到任何类型的底座
                    currentModeType == Configuration.UI_MODE_TYPE_DESK ||
                    currentModeType == Configuration.UI_MODE_TYPE_CAR
                }
                DockStateType.DESK_DOCK -> {
                    // 检查是否连接到桌面底座
                    currentModeType == Configuration.UI_MODE_TYPE_DESK
                }
                DockStateType.CAR_DOCK -> {
                    // 检查是否连接到汽车底座
                    currentModeType == Configuration.UI_MODE_TYPE_CAR
                }
                DockStateType.UNDOCKED -> {
                    // 检查是否未连接底座
                    currentModeType != Configuration.UI_MODE_TYPE_DESK &&
                    currentModeType != Configuration.UI_MODE_TYPE_CAR
                }
            }

            Log.d(TAG, "Dock state condition: ${condition.dockStateType.displayName} = $result (current mode: $currentModeType)")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating dock state condition", e)
            false
        }
    }

    /**
     * 评估Intent接收条件
     */
    private fun evaluateIntentReceivedCondition(condition: DeviceEventCondition): Boolean {
        // Intent接收是一个事件型条件，需要通过BroadcastReceiver监听
        // 在静态检查中无法直接评估，需要通过事件监听机制实现
        // 这里我们可以检查Intent动作是否有效
        return try {
            if (condition.intentAction.isEmpty()) {
                Log.d(TAG, "Intent received condition: no action specified")
                return false
            }

            // 检查Intent动作格式是否有效
            val isValidAction = condition.intentAction.matches(Regex("^[a-zA-Z][a-zA-Z0-9_]*(?:\\.[a-zA-Z][a-zA-Z0-9_]*)*$"))

            if (!isValidAction) {
                Log.d(TAG, "Intent received condition: invalid action format '${condition.intentAction}'")
                return false
            }

            // 在静态检查中，我们假设Intent条件需要通过事件监听来触发
            Log.d(TAG, "Intent received condition: requires event monitoring for action '${condition.intentAction}'")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating intent received condition", e)
            false
        }
    }

    /**
     * 评估SIM卡状态条件
     */
    private fun evaluateSimCardStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            val simState = telephonyManager.simState

            val result = when (condition.simCardStateType) {
                SimCardStateType.INSERTED -> {
                    // SIM卡已插入且可用
                    simState == TelephonyManager.SIM_STATE_READY ||
                    simState == TelephonyManager.SIM_STATE_PIN_REQUIRED ||
                    simState == TelephonyManager.SIM_STATE_PUK_REQUIRED ||
                    simState == TelephonyManager.SIM_STATE_NETWORK_LOCKED
                }
                SimCardStateType.REMOVED -> {
                    // SIM卡已移除或不存在
                    simState == TelephonyManager.SIM_STATE_ABSENT
                }
                SimCardStateType.STATE_CHANGED -> {
                    // 这是一个状态变化检测，需要通过监听器实现
                    // 在静态检查中，我们检查SIM卡是否处于非稳定状态
                    simState == TelephonyManager.SIM_STATE_UNKNOWN ||
                    simState == TelephonyManager.SIM_STATE_CARD_IO_ERROR ||
                    simState == TelephonyManager.SIM_STATE_CARD_RESTRICTED
                }
            }

            val stateDescription = when (simState) {
                TelephonyManager.SIM_STATE_ABSENT -> "无SIM卡"
                TelephonyManager.SIM_STATE_PIN_REQUIRED -> "需要PIN码"
                TelephonyManager.SIM_STATE_PUK_REQUIRED -> "需要PUK码"
                TelephonyManager.SIM_STATE_NETWORK_LOCKED -> "网络锁定"
                TelephonyManager.SIM_STATE_READY -> "就绪"
                TelephonyManager.SIM_STATE_NOT_READY -> "未就绪"
                TelephonyManager.SIM_STATE_CARD_IO_ERROR -> "卡IO错误"
                TelephonyManager.SIM_STATE_CARD_RESTRICTED -> "卡受限"
                else -> "未知状态($simState)"
            }

            Log.d(TAG, "SIM card state condition: ${condition.simCardStateType.displayName} = $result (current state: $stateDescription)")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating SIM card state condition", e)
            false
        }
    }

    /**
     * 评估深色主题变更条件
     */
    private fun evaluateDarkThemeChangedCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val configuration = context.resources.configuration
            val isDarkTheme = (configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES

            val result = when (condition.darkThemeStateType) {
                DarkThemeStateType.ENABLED -> isDarkTheme
                DarkThemeStateType.DISABLED -> !isDarkTheme
            }

            Log.d(TAG, "Dark theme check: enabled=$isDarkTheme, expected=${condition.darkThemeStateType}, satisfied=$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking dark theme state", e)
            false
        }
    }

    /**
     * 评估登录尝试失败条件
     */
    private fun evaluateLoginAttemptFailedCondition(condition: DeviceEventCondition): Boolean {
        // 登录失败检测需要设备管理器权限或通过系统日志监控
        // 这是一个事件型条件，需要通过监听机制实现
        return try {
            // 检查是否有KeyguardManager权限
            val keyguardManager = context.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager

            // 在静态检查中，我们只能检查设备是否支持锁屏
            val isSecure = keyguardManager.isKeyguardSecure

            if (!isSecure) {
                Log.d(TAG, "Login attempt failed condition: device has no secure lock screen")
                return false
            }

            // 登录失败次数需要通过事件监听来跟踪
            // 在静态检查中无法获取实时的失败次数
            Log.d(TAG, "Login attempt failed condition: requires event monitoring (threshold: ${condition.loginFailureThreshold})")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating login attempt failed condition", e)
            false
        }
    }

    /**
     * 评估系统设置更改条件
     */
    private fun evaluateSystemSettingChangedCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val contentResolver = context.contentResolver

            // 检查所有选中的设置类型
            for (settingType in condition.systemSettingTypes) {
                val settingValue = when (settingType) {
                    SystemSettingType.SYSTEM -> {
                        if (condition.systemSettingKey.isNotEmpty()) {
                            Settings.System.getString(contentResolver, condition.systemSettingKey)
                        } else {
                            null
                        }
                    }
                    SystemSettingType.SECURE -> {
                        if (condition.systemSettingKey.isNotEmpty()) {
                            Settings.Secure.getString(contentResolver, condition.systemSettingKey)
                        } else {
                            null
                        }
                    }
                    SystemSettingType.GLOBAL -> {
                        if (condition.systemSettingKey.isNotEmpty()) {
                            Settings.Global.getString(contentResolver, condition.systemSettingKey)
                        } else {
                            null
                        }
                    }
                }

                // 如果没有指定设置键，则无法检查
                if (condition.systemSettingKey.isEmpty()) {
                    continue
                }

                // 如果没有指定期望值，则只要设置存在就匹配
                if (condition.systemSettingValue.isEmpty()) {
                    if (settingValue != null) {
                        Log.d(TAG, "System setting condition: key '${condition.systemSettingKey}' exists in ${settingType.displayName}")
                        return true
                    }
                } else {
                    // 检查设置值是否匹配（支持正则表达式）
                    val matches = if (condition.systemSettingUseRegex && settingValue != null) {
                        try {
                            settingValue.matches(Regex(condition.systemSettingValue))
                        } catch (e: Exception) {
                            Log.w(TAG, "Invalid regex pattern: ${condition.systemSettingValue}", e)
                            settingValue == condition.systemSettingValue
                        }
                    } else {
                        settingValue == condition.systemSettingValue
                    }

                    if (matches) {
                        Log.d(TAG, "System setting condition: '${condition.systemSettingKey}' = '$settingValue', expected = '${condition.systemSettingValue}', matches in ${settingType.displayName}")
                        return true
                    }
                }
            }

            // 如果没有任何设置类型匹配，返回false
            Log.d(TAG, "System setting condition: no matching settings found")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating system setting condition", e)
            false
        }
    }

    /**
     * 评估自动同步状态条件
     */
    private fun evaluateAutoSyncStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val isAutoSyncEnabled = ContentResolver.getMasterSyncAutomatically()

            val result = when (condition.autoSyncStateType) {
                AutoSyncStateType.ENABLED -> isAutoSyncEnabled
                AutoSyncStateType.DISABLED -> !isAutoSyncEnabled
            }

            Log.d(TAG, "Auto sync state check: enabled=$isAutoSyncEnabled, expected=${condition.autoSyncStateType}, satisfied=$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking auto sync state", e)
            false
        }
    }

    /**
     * 评估设备启动完成条件
     */
    private fun evaluateDeviceBootCompletedCondition(condition: DeviceEventCondition): Boolean {
        // 这是一个事件型条件，需要通过BroadcastReceiver监听BOOT_COMPLETED
        // 在静态检查中总是返回false
        Log.d(TAG, "Device boot completed check: requires event monitoring")
        return false
    }

    /**
     * 评估通知事件条件
     */
    private fun evaluateNotificationEventCondition(condition: DeviceEventCondition): Boolean {
        // 通知事件监控需要通知使用权限
        // 这是一个事件型条件，需要通过NotificationListenerService实现
        return try {
            // 检查应用选择模式
            if (condition.notificationAppSelectionMode == NotificationAppSelectionMode.SELECTED) {
                if (condition.notificationSelectedApps.isNotEmpty()) {
                    // 检查选中的应用是否已安装
                    val packageManager = context.packageManager
                    for (packageName in condition.notificationSelectedApps) {
                        try {
                            packageManager.getPackageInfo(packageName, 0)
                        } catch (e: PackageManager.NameNotFoundException) {
                            Log.d(TAG, "Notification event condition: app '$packageName' not installed")
                            // 根据包括/排除模式决定是否继续
                            if (condition.notificationAppIncludeMode == NotificationAppIncludeMode.INCLUDE) {
                                return false // 包括模式下，如果有应用未安装则失败
                            }
                        }
                    }
                }
            }

            // 检查通知使用权限状态
            val notificationListenerEnabled = Settings.Secure.getString(
                context.contentResolver,
                "enabled_notification_listeners"
            )?.contains(context.packageName) ?: false

            if (!notificationListenerEnabled) {
                Log.d(TAG, "Notification event condition: notification listener permission not granted")
                return false
            }

            // 通知事件需要通过NotificationListenerService监听
            // 在静态检查中无法获取实时的通知事件
            Log.d(TAG, "Notification event condition: requires event monitoring (type: ${condition.notificationEventType.displayName}, apps: ${condition.notificationAppSelectionMode.displayName})")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating notification event condition", e)
            false
        }
    }

    /**
     * 评估铃声模式变更条件
     */
    private fun evaluateRingerModeChangedCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val currentRingerMode = audioManager.ringerMode

            val result = when (condition.ringerModeType) {
                RingerModeType.NORMAL -> currentRingerMode == AudioManager.RINGER_MODE_NORMAL
                RingerModeType.VIBRATE -> currentRingerMode == AudioManager.RINGER_MODE_VIBRATE
                RingerModeType.SILENT -> currentRingerMode == AudioManager.RINGER_MODE_SILENT
            }

            Log.d(TAG, "Ringer mode check: current=$currentRingerMode, expected=${condition.ringerModeType}, satisfied=$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking ringer mode", e)
            false
        }
    }

    /**
     * 评估音乐播放状态条件
     */
    private fun evaluateMusicPlaybackStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val isMusicActive = audioManager.isMusicActive

            val result = when (condition.musicPlaybackType) {
                MusicPlaybackType.STARTED -> isMusicActive
                MusicPlaybackType.STOPPED -> !isMusicActive
            }

            Log.d(TAG, "Music playback check: active=$isMusicActive, expected=${condition.musicPlaybackType}, satisfied=$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking music playback state", e)
            false
        }
    }

    /**
     * 评估飞行模式状态条件
     */
    private fun evaluateAirplaneModeStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val isAirplaneModeOn = Settings.Global.getInt(
                context.contentResolver,
                Settings.Global.AIRPLANE_MODE_ON,
                0
            ) != 0

            val result = when (condition.airplaneModeType) {
                AirplaneModeType.ENABLED -> isAirplaneModeOn
                AirplaneModeType.DISABLED -> !isAirplaneModeOn
            }

            Log.d(TAG, "Airplane mode check: enabled=$isAirplaneModeOn, expected=${condition.airplaneModeType}, satisfied=$result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking airplane mode state", e)
            false
        }
    }

    /**
     * 评估音量变化条件
     */
    private fun evaluateVolumeChangedCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            // 根据音量流类型获取当前音量
            val streamType = when (condition.volumeStreamType) {
                VolumeStreamType.ALARM -> AudioManager.STREAM_ALARM
                VolumeStreamType.MEDIA_MUSIC -> AudioManager.STREAM_MUSIC
                VolumeStreamType.NOTIFICATION -> AudioManager.STREAM_NOTIFICATION
                VolumeStreamType.RING -> AudioManager.STREAM_RING
                VolumeStreamType.SYSTEM -> AudioManager.STREAM_SYSTEM
                VolumeStreamType.VOICE_CALL -> AudioManager.STREAM_VOICE_CALL
                VolumeStreamType.BLUETOOTH -> 6 // AudioManager.STREAM_BLUETOOTH_SCO
                VolumeStreamType.ACCESSIBILITY -> AudioManager.STREAM_ACCESSIBILITY
            }

            val currentVolume = audioManager.getStreamVolume(streamType)
            val maxVolume = audioManager.getStreamMaxVolume(streamType)
            val currentVolumePercent = if (maxVolume > 0) (currentVolume * 100) / maxVolume else 0

            // 获取缓存的音量值
            val cacheKey = "volume_${condition.volumeStreamType.name}"
            val lastVolumePercent = lastVolumeStates[cacheKey] ?: currentVolumePercent

            // 计算音量变化幅度
            val volumeChange = kotlin.math.abs(currentVolumePercent - lastVolumePercent)

            // 更新缓存
            lastVolumeStates[cacheKey] = currentVolumePercent

            // 检查是否超过阈值
            val result = volumeChange >= condition.volumeThreshold

            Log.d(TAG, "Volume change condition: ${condition.volumeStreamType.displayName} changed from $lastVolumePercent% to $currentVolumePercent% (change: $volumeChange%, threshold: ${condition.volumeThreshold}%) = $result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating volume change condition", e)
            false
        }
    }





    /**
     * 检查自动旋转是否启用
     */
    private fun isAutoRotateEnabled(): Boolean {
        return try {
            Settings.System.getInt(
                context.contentResolver,
                Settings.System.ACCELEROMETER_ROTATION,
                0
            ) == 1
        } catch (e: Exception) {
            Log.e(TAG, "Error checking auto rotate setting", e)
            false
        }
    }

    /**
     * 检查传感器状态条件是否满足
     * 注意：传感器状态条件现在由专门的SensorStateMonitor处理
     * 这个方法主要用于兼容性和初始状态检查
     *
     * @param condition 传感器状态条件
     * @return 传感器状态条件是否满足
     */
    private fun isSensorStateConditionSatisfied(condition: SensorStateCondition): Boolean {
        // 传感器状态条件现在由SensorStateMonitor实时监听和触发
        // 这里只返回false，因为真正的触发由监听器处理
        Log.d(TAG, "Sensor state condition check: ${condition.getDescription()} - handled by SensorStateMonitor")
        return false
    }















    /**
     * 检查手动触发条件是否满足
     * 手动触发条件通常由外部触发器（如快捷方式、小组件、悬浮按钮）调用
     *
     * @param condition 手动触发条件
     * @return 手动触发条件是否满足
     */
    private fun isManualTriggerConditionSatisfied(condition: ManualTriggerCondition): Boolean {
        return when (condition.triggerType) {
            ManualTriggerType.DYNAMIC_SHORTCUT -> evaluateDynamicShortcutTrigger(condition)
            ManualTriggerType.STATIC_SHORTCUT -> evaluateStaticShortcutTrigger(condition)
            ManualTriggerType.DESKTOP_WIDGET -> evaluateDesktopWidgetTrigger(condition)
            ManualTriggerType.FLOATING_BUTTON -> evaluateFloatingButtonTrigger(condition)
            ManualTriggerType.FINGERPRINT_GESTURE -> evaluateFingerprintGestureTrigger(condition)
            ManualTriggerType.HOME_BUTTON_LONG_PRESS -> evaluateHomeButtonLongPressTrigger(condition)
            ManualTriggerType.MEDIA_KEY_PRESS -> evaluateMediaKeyPressTrigger(condition)
            ManualTriggerType.SHORTCUT_OPENING -> evaluateShortcutOpeningTrigger(condition)
            ManualTriggerType.SCREEN_SWIPE -> evaluateScreenSwipeTrigger(condition)
            ManualTriggerType.VOLUME_BUTTON_LONG_PRESS -> evaluateVolumeButtonLongPressTrigger(condition)
            ManualTriggerType.VOLUME_KEY_PRESS -> evaluateVolumeKeyPressTrigger(condition)
            ManualTriggerType.POWER_BUTTON_LONG_PRESS -> evaluatePowerButtonLongPressTrigger(condition)
        }
    }

    /**
     * 评估动态快捷方式触发条件
     * 动态快捷方式触发通常由外部调用，这里返回false表示需要外部触发
     */
    private fun evaluateDynamicShortcutTrigger(condition: ManualTriggerCondition): Boolean {
        Log.d(TAG, "Dynamic shortcut trigger evaluation: ${condition.shortcutName}")
        // 动态快捷方式触发需要外部调用，这里总是返回false
        // 实际触发由ShortcutManager和ConditionalCommandExecutorActivity处理
        return false
    }

    /**
     * 评估静态快捷方式触发条件
     * 静态快捷方式触发通过槽位管理，这里返回false表示需要外部触发
     */
    private fun evaluateStaticShortcutTrigger(condition: ManualTriggerCondition): Boolean {
        Log.d(TAG, "Static shortcut trigger evaluation: slot ${condition.slotIndex}")
        // 静态快捷方式触发需要外部调用，这里总是返回false
        // 实际触发由StaticShortcutHandlerActivity处理
        return false
    }

    /**
     * 评估桌面小组件触发条件
     * 桌面小组件触发通过槽位管理，这里返回false表示需要外部触发
     */
    private fun evaluateDesktopWidgetTrigger(condition: ManualTriggerCondition): Boolean {
        Log.d(TAG, "Desktop widget trigger evaluation: slot ${condition.slotIndex}")
        // 桌面小组件触发需要外部调用，这里总是返回false
        // 实际触发由WidgetClickHandlerActivity处理
        return false
    }

    /**
     * 评估悬浮按钮触发条件
     * 悬浮按钮触发通过按钮点击，这里返回false表示需要外部触发
     */
    private fun evaluateFloatingButtonTrigger(condition: ManualTriggerCondition): Boolean {
        Log.d(TAG, "Floating button trigger evaluation: ${condition.buttonText}")
        // 悬浮按钮触发需要外部调用，这里总是返回false
        // 实际触发由FloatingButtonService处理
        return false
    }

    /**
     * 评估指纹手势触发条件
     * 指纹手势触发通过手势识别服务，这里返回false表示需要外部触发
     */
    private fun evaluateFingerprintGestureTrigger(condition: ManualTriggerCondition): Boolean {
        Log.d(TAG, "Fingerprint gesture trigger evaluation: ${condition.fingerprintGestureType.displayName}")
        // 指纹手势触发需要外部调用，这里总是返回false
        // 实际触发由GestureRecognitionAccessibilityService处理
        return false
    }

    /**
     * 评估基站连接条件
     * 检测基站进入、离开和改变状态
     */
    private fun evaluateCellTowerCondition(condition: ConnectionStateCondition): Boolean {
        return try {
            // 检查位置权限
            if (!hasLocationPermission()) {
                Log.d(TAG, "Cell tower condition: location permission not granted")
                return false
            }

            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

            when (condition.subType) {
                ConnectionSubType.CELL_TOWER_ENTERED -> {
                    // 进入基站区域检测
                    // 这需要通过位置监听和基站信息变化来实现
                    // 在静态检查中，我们检查是否有基站连接
                    val hasSignal = telephonyManager.signalStrength?.level ?: 0 > 0
                    Log.d(TAG, "Cell tower entered check: has signal = $hasSignal")
                    hasSignal
                }
                ConnectionSubType.CELL_TOWER_EXITED -> {
                    // 离开基站区域检测
                    val hasSignal = telephonyManager.signalStrength?.level ?: 0 > 0
                    Log.d(TAG, "Cell tower exited check: no signal = ${!hasSignal}")
                    !hasSignal
                }
                ConnectionSubType.CELL_TOWER_CHANGED -> {
                    // 基站改变检测
                    // 这需要通过监听基站信息变化来实现
                    // 在静态检查中，我们检查是否有移动网络连接
                    val hasMobileConnection = isMobileDataConnected()
                    Log.d(TAG, "Cell tower changed check: has mobile connection = $hasMobileConnection")
                    hasMobileConnection
                }
                else -> {
                    Log.d(TAG, "Cell tower condition: unsupported subtype ${condition.subType}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating cell tower condition", e)
            false
        }
    }

    /**
     * 评估内存状态条件
     * 检测内存低于阈值、高于阈值和内存变化
     */
    private fun evaluateMemoryStateCondition(condition: DeviceEventCondition): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            when (condition.memoryStateType) {
                MemoryStateType.BELOW_THRESHOLD -> {
                    // 内存低于阈值
                    if (condition.isPercentageMode) {
                        // 百分比模式
                        val availableMemoryPercentage = (memoryInfo.availMem * 100 / memoryInfo.totalMem).toInt()
                        val result = availableMemoryPercentage < condition.memoryThreshold
                        Log.d(TAG, "Memory below threshold (percentage): ${availableMemoryPercentage}% < ${condition.memoryThreshold}% = $result")
                        result
                    } else {
                        // 绝对值模式 (GB)
                        val availableMemoryGB = memoryInfo.availMem / (1024 * 1024 * 1024)
                        val result = availableMemoryGB < condition.memoryThreshold
                        Log.d(TAG, "Memory below threshold (GB): ${availableMemoryGB}GB < ${condition.memoryThreshold}GB = $result")
                        result
                    }
                }
                MemoryStateType.ABOVE_THRESHOLD -> {
                    // 内存高于阈值
                    if (condition.isPercentageMode) {
                        // 百分比模式
                        val availableMemoryPercentage = (memoryInfo.availMem * 100 / memoryInfo.totalMem).toInt()
                        val result = availableMemoryPercentage > condition.memoryThreshold
                        Log.d(TAG, "Memory above threshold (percentage): ${availableMemoryPercentage}% > ${condition.memoryThreshold}% = $result")
                        result
                    } else {
                        // 绝对值模式 (GB)
                        val availableMemoryGB = memoryInfo.availMem / (1024 * 1024 * 1024)
                        val result = availableMemoryGB > condition.memoryThreshold
                        Log.d(TAG, "Memory above threshold (GB): ${availableMemoryGB}GB > ${condition.memoryThreshold}GB = $result")
                        result
                    }
                }
                MemoryStateType.CHANGED -> {
                    // 内存变化检测
                    val conditionKey = "memory_state_${condition.id}"
                    val currentMemoryPercentage = (memoryInfo.availMem * 100 / memoryInfo.totalMem).toInt()
                    val lastMemoryPercentage = lastSensorStates[conditionKey] as? Int

                    lastSensorStates[conditionKey] = currentMemoryPercentage

                    if (lastMemoryPercentage != null) {
                        val memoryChange = kotlin.math.abs(currentMemoryPercentage - lastMemoryPercentage)
                        val result = memoryChange >= condition.memoryChangeThreshold
                        Log.d(TAG, "Memory change check: ${memoryChange}% >= ${condition.memoryChangeThreshold}% = $result")
                        result
                    } else {
                        Log.d(TAG, "Memory change check: no previous data")
                        false
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating memory state condition", e)
            false
        }
    }



    /**
     * 检查Tasker/Locale插件条件是否满足（基于AppStateCondition）
     * 通过Intent查询插件的条件状态
     *
     * @param condition 应用状态条件（包含Tasker/Locale插件配置）
     * @return 插件条件是否满足
     */
    private fun isTaskerLocalePluginConditionSatisfied(condition: AppStateCondition): Boolean {
        return try {
            // 检查插件包名是否有效
            if (condition.targetPackageName.isEmpty()) {
                Log.d(TAG, "Tasker/Locale condition: no plugin package specified")
                return false
            }

            // 检查插件是否已安装
            val packageManager = context.packageManager
            try {
                packageManager.getPackageInfo(condition.targetPackageName, 0)
            } catch (e: PackageManager.NameNotFoundException) {
                Log.d(TAG, "Tasker/Locale condition: plugin '${condition.targetPackageName}' not installed")
                return false
            }

            // 构建查询Intent
            val queryIntent = Intent(condition.pluginAction).apply {
                setPackage(condition.targetPackageName)

                // 添加额外参数
                if (condition.pluginExtras.isNotEmpty()) {
                    try {
                        // 简单的JSON解析，实际实现可能需要更复杂的解析逻辑
                        putExtra("extras", condition.pluginExtras)
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to parse plugin extras: ${condition.pluginExtras}", e)
                    }
                }
            }

            // 检查是否有Activity能处理这个Intent
            val activities = packageManager.queryIntentActivities(queryIntent, PackageManager.GET_META_DATA)
            if (activities.isEmpty()) {
                Log.d(TAG, "Tasker/Locale condition: no activity found for action '${condition.pluginAction}' in package '${condition.targetPackageName}'")
                return false
            }

            // 对于条件插件，我们需要通过特定的方式查询状态
            // 这里实现一个简化的状态检查逻辑
            val result = evaluatePluginConditionStateFromAppState(condition)

            Log.d(TAG, "Tasker/Locale condition: plugin '${condition.targetAppName}' state = $result, expected = ${condition.expectedState}")

            // 根据期望状态返回结果
            when (condition.expectedState) {
                TaskerLocaleConditionState.SATISFIED -> result == TaskerLocaleConditionState.SATISFIED
                TaskerLocaleConditionState.UNSATISFIED -> result == TaskerLocaleConditionState.UNSATISFIED
                TaskerLocaleConditionState.UNKNOWN -> result == TaskerLocaleConditionState.UNKNOWN
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating Tasker/Locale condition", e)
            false
        }
    }

    /**
     * 评估插件条件状态（基于AppStateCondition）
     * 实现真正的 Tasker/Locale 插件状态查询
     */
    private fun evaluatePluginConditionStateFromAppState(condition: AppStateCondition): TaskerLocaleConditionState {
        return try {
            if (condition.targetPackageName.isEmpty()) {
                Log.w(TAG, "Plugin package name is empty")
                return TaskerLocaleConditionState.UNKNOWN
            }

            // 检查插件应用是否已安装
            val packageManager = context.packageManager
            val isPluginInstalled = try {
                packageManager.getPackageInfo(condition.targetPackageName, 0)
                true
            } catch (e: Exception) {
                Log.w(TAG, "Plugin ${condition.targetPackageName} is not installed")
                false
            }

            if (!isPluginInstalled) {
                return TaskerLocaleConditionState.UNKNOWN
            }

            // 尝试查询插件条件状态
            val pluginState = queryPluginConditionState(condition)

            Log.d(TAG, "Plugin ${condition.targetPackageName} condition state: $pluginState")
            pluginState
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating plugin condition state", e)
            TaskerLocaleConditionState.UNKNOWN
        }
    }

    /**
     * 查询插件条件状态
     * 使用 Tasker/Locale 插件标准协议查询条件状态
     */
    private fun queryPluginConditionState(condition: AppStateCondition): TaskerLocaleConditionState {
        return try {
            // 创建查询 Intent
            val queryIntent = Intent().apply {
                // 使用标准的 Tasker/Locale 插件查询 action
                action = condition.pluginAction.ifEmpty { "com.twofortyfouram.locale.intent.action.QUERY_CONDITION" }
                setPackage(condition.targetPackageName)

                // 添加配置数据
                if (condition.pluginExtras.isNotEmpty()) {
                    try {
                        // 尝试解析配置数据并添加到 Intent
                        putExtra("com.twofortyfouram.locale.intent.extra.BUNDLE", android.os.Bundle().apply {
                            putString("config_data", condition.pluginExtras)

                            // 如果是 JSON 格式，尝试解析
                            if (condition.pluginExtras.trim().startsWith("{")) {
                                putString("json_config", condition.pluginExtras)
                            }
                        })
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to parse plugin extras: ${condition.pluginExtras}", e)
                    }
                }

                // 添加超时标识
                putExtra("timeout_seconds", condition.timeoutSeconds)
            }

            // 检查是否有组件可以处理这个查询
            val packageManager = context.packageManager
            val resolveInfos = packageManager.queryBroadcastReceivers(queryIntent, 0)

            if (resolveInfos.isEmpty()) {
                Log.w(TAG, "No broadcast receiver found for plugin condition query: ${condition.targetPackageName}")
                return TaskerLocaleConditionState.UNKNOWN
            }

            // 发送查询广播并等待响应
            // 注意：这是一个简化的实现，实际的插件查询可能需要异步处理
            // 大多数插件会通过文件、SharedPreferences 或其他方式存储状态

            // 尝试通过 SharedPreferences 读取插件状态（常见的插件状态存储方式）
            val pluginState = readPluginStateFromPreferences(condition)
            if (pluginState != TaskerLocaleConditionState.UNKNOWN) {
                return pluginState
            }

            // 如果无法从 SharedPreferences 读取，尝试发送查询广播
            context.sendBroadcast(queryIntent)

            // 等待一小段时间后再次尝试读取状态
            Thread.sleep(100) // 短暂等待
            val updatedState = readPluginStateFromPreferences(condition)

            updatedState

        } catch (e: Exception) {
            Log.e(TAG, "Error querying plugin condition state", e)
            TaskerLocaleConditionState.UNKNOWN
        }
    }

    /**
     * 从 SharedPreferences 读取插件状态
     * 许多 Tasker/Locale 插件使用 SharedPreferences 存储条件状态
     */
    private fun readPluginStateFromPreferences(condition: AppStateCondition): TaskerLocaleConditionState {
        return try {
            // 尝试读取插件的 SharedPreferences
            // 常见的插件状态存储格式
            val pluginContext = context.createPackageContext(condition.targetPackageName, Context.CONTEXT_IGNORE_SECURITY)

            // 尝试几种常见的 SharedPreferences 名称
            val possiblePrefNames = listOf(
                "${condition.targetPackageName}_preferences",
                "plugin_preferences",
                "condition_state",
                "tasker_state"
            )

            for (prefName in possiblePrefNames) {
                try {
                    val prefs = pluginContext.getSharedPreferences(prefName, Context.MODE_PRIVATE)

                    // 尝试几种常见的状态键名
                    val possibleKeys = listOf(
                        "condition_satisfied",
                        "state",
                        "is_active",
                        "condition_state",
                        condition.targetPackageName + "_state"
                    )

                    for (key in possibleKeys) {
                        if (prefs.contains(key)) {
                            val value = prefs.getBoolean(key, false)
                            return if (value) TaskerLocaleConditionState.SATISFIED else TaskerLocaleConditionState.UNSATISFIED
                        }
                    }

                    // 尝试读取字符串状态
                    for (key in possibleKeys) {
                        val stringValue = prefs.getString(key, null)
                        if (stringValue != null) {
                            return when (stringValue.lowercase()) {
                                "true", "satisfied", "active", "1" -> TaskerLocaleConditionState.SATISFIED
                                "false", "unsatisfied", "inactive", "0" -> TaskerLocaleConditionState.UNSATISFIED
                                else -> TaskerLocaleConditionState.UNKNOWN
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 继续尝试下一个 SharedPreferences 名称
                    continue
                }
            }

            TaskerLocaleConditionState.UNKNOWN

        } catch (e: Exception) {
            Log.w(TAG, "Cannot read plugin state from SharedPreferences: ${e.message}")
            TaskerLocaleConditionState.UNKNOWN
        }
    }

    /**
     * 评估主屏幕按钮长按触发条件
     *
     * 注意：此方法主要用于条件匹配，实际的长按检测需要在辅助服务中实现
     *
     * @param condition 主屏幕按钮长按条件
     * @return 条件是否满足
     */
    private fun evaluateHomeButtonLongPressTrigger(condition: ManualTriggerCondition): Boolean {
        // 手动触发条件通常由外部触发器调用，这里返回false表示需要外部触发
        // 实际的长按检测逻辑应该在辅助服务中实现
        Log.d(TAG, "Home button long press trigger evaluation: threshold=${condition.homeButtonLongPressThreshold}ms")
        return false
    }

    /**
     * 评估媒体键按下触发条件
     *
     * 注意：此方法主要用于条件匹配，实际的媒体键检测需要在媒体键监听器中实现
     *
     * @param condition 媒体键按下条件
     * @return 条件是否满足
     */
    private fun evaluateMediaKeyPressTrigger(condition: ManualTriggerCondition): Boolean {
        // 手动触发条件通常由外部触发器调用，这里返回false表示需要外部触发
        // 实际的媒体键检测逻辑应该在媒体键监听器中实现
        Log.d(TAG, "Media key press trigger evaluation: type=${condition.mediaKeyType.displayName}, count=${condition.mediaKeyPressCount}, timeout=${condition.mediaKeyPressTimeout}ms")
        return false
    }

    /**
     * 评估快捷方式打开触发条件
     *
     * @param condition 快捷方式打开触发条件
     * @return 条件是否满足
     */
    private fun evaluateShortcutOpeningTrigger(condition: ManualTriggerCondition): Boolean {
        // 手动触发条件通常由外部触发器调用，这里返回false表示需要外部触发
        // 实际的快捷方式打开检测逻辑应该在快捷方式监听器中实现
        Log.d(TAG, "Shortcut opening trigger evaluation: name=${condition.shortcutOpeningName}")
        return false
    }

    /**
     * 评估滑动屏幕触发条件
     *
     * @param condition 滑动屏幕触发条件
     * @return 条件是否满足
     */
    private fun evaluateScreenSwipeTrigger(condition: ManualTriggerCondition): Boolean {
        // 手动触发条件通常由外部触发器调用，这里返回false表示需要外部触发
        // 实际的滑动手势检测逻辑应该在手势监听器中实现
        Log.d(TAG, "Screen swipe trigger evaluation: startCorner=${condition.swipeStartCorner.displayName}, direction=${condition.swipeDirection.displayName}")
        return false
    }

    /**
     * 评估音量按钮长按触发条件
     *
     * @param condition 音量按钮长按触发条件
     * @return 条件是否满足
     */
    private fun evaluateVolumeButtonLongPressTrigger(condition: ManualTriggerCondition): Boolean {
        // 手动触发条件通常由外部触发器调用，这里返回false表示需要外部触发
        // 实际的音量按钮长按检测逻辑应该在按键监听器中实现
        Log.d(TAG, "Volume button long press trigger evaluation: type=${condition.volumeButtonType.displayName}, threshold=${condition.volumeButtonLongPressThreshold}ms")
        return false
    }

    /**
     * 评估音量键按下触发条件
     *
     * @param condition 音量键按下触发条件
     * @return 条件是否满足
     */
    private fun evaluateVolumeKeyPressTrigger(condition: ManualTriggerCondition): Boolean {
        // 手动触发条件通常由外部触发器调用，这里返回false表示需要外部触发
        // 实际的音量键按下检测逻辑应该在按键监听器中实现
        Log.d(TAG, "Volume key press trigger evaluation: type=${condition.volumeKeyType.displayName}, preserveVolume=${condition.volumeKeyPreserveVolume}")
        return false
    }

    /**
     * 评估电源键长按触发条件
     *
     * @param condition 电源键长按触发条件
     * @return 条件是否满足
     */
    private fun evaluatePowerButtonLongPressTrigger(condition: ManualTriggerCondition): Boolean {
        // 手动触发条件通常由外部触发器调用，这里返回false表示需要外部触发
        // 实际的电源键长按检测逻辑应该在按键监听器中实现
        Log.d(TAG, "Power button long press trigger evaluation: threshold=${condition.powerButtonLongPressThreshold}ms")
        return false
    }


}

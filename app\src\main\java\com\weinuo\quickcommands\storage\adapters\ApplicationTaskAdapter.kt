package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation
import com.weinuo.quickcommands.storage.AppListStorageEngine

/**
 * 应用程序任务存储适配器
 *
 * 负责ApplicationTask的原生数据类型存储和重建。
 * 由于ApplicationTask字段众多且复杂，此适配器重点处理核心字段，
 * 复杂的集合类型（如SimpleAppInfo列表、IntentParam列表）通过JSON序列化存储。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - JavaScript相关：javascriptCode
 * - Shell脚本相关：shellScript、shellExecutionMode、shellTimeoutMinutes、shellTimeoutSeconds、shellWaitForCompletion
 * - Tasker插件相关：taskerPluginPackage、taskerPluginAction、taskerPluginExtras
 * - 应用启动相关：appPackageName、appName
 * - 快捷方式相关：shortcutId、shortcutName
 * - 网站相关：websiteUrl、urlEncodeParams
 * - 强制停止相关：forceStopScope、forceStopPackageName、forceStopAppName、skipForegroundApp、skipMusicPlayingApp、skipVpnApp、sortByBackgroundTime
 * - 内存管理相关：enableMemoryThresholdCheck、memoryThreshold、memoryThresholdIsPercentage
 * - 策略相关：appSortingStrategy、memoryCheckFrequency、enableCustomCheckFrequency、customCheckFrequencySize
 * - 清理策略相关：enableCustomCleanupStrategy、cleanupStrategyId、useSimpleMode、simpleStrategyType
 * - 应用重要性相关：enableAppImportanceFilter
 * - 冻结应用相关：freezeScope、openAppAfterUnfreeze
 * - 解冻应用相关：unfreezeScope
 *
 * 复杂字段通过JSON存储：
 * - forceStopSelectedApps（SimpleAppInfo列表）
 * - selectedVpnApps（SimpleAppInfo列表）
 * - selectedImportanceLevels（AppImportance集合）
 * - intentParams（IntentParam列表）
 * - freezeSelectedApps（SimpleAppInfo列表）
 * - unfreezeSelectedApps（SimpleAppInfo列表）
 *
 * @param storageManager 原生类型存储管理器
 */
class ApplicationTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<ApplicationTask>(storageManager) {

    companion object {
        private const val TAG = "ApplicationTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"

        // JavaScript字段
        private const val FIELD_JAVASCRIPT_CODE = "javascript_code"

        // Shell脚本字段
        private const val FIELD_SHELL_SCRIPT = "shell_script"
        private const val FIELD_SHELL_EXECUTION_MODE = "shell_execution_mode"
        private const val FIELD_SHELL_TIMEOUT_MINUTES = "shell_timeout_minutes"
        private const val FIELD_SHELL_TIMEOUT_SECONDS = "shell_timeout_seconds"
        private const val FIELD_SHELL_WAIT_FOR_COMPLETION = "shell_wait_for_completion"

        // Tasker插件字段
        private const val FIELD_TASKER_PLUGIN_PACKAGE = "tasker_plugin_package"
        private const val FIELD_TASKER_PLUGIN_ACTION = "tasker_plugin_action"
        private const val FIELD_TASKER_PLUGIN_EXTRAS = "tasker_plugin_extras"

        // 应用启动字段
        private const val FIELD_APP_PACKAGE_NAME = "app_package_name"
        private const val FIELD_APP_NAME = "app_name"

        // 快捷方式字段
        private const val FIELD_SHORTCUT_ID = "shortcut_id"
        private const val FIELD_SHORTCUT_NAME = "shortcut_name"

        // 网站字段
        private const val FIELD_WEBSITE_URL = "website_url"
        private const val FIELD_URL_ENCODE_PARAMS = "url_encode_params"

        // 强制停止字段
        private const val FIELD_FORCE_STOP_SCOPE = "force_stop_scope"
        private const val FIELD_SKIP_FOREGROUND_APP = "skip_foreground_app"
        private const val FIELD_SKIP_MUSIC_PLAYING_APP = "skip_music_playing_app"
        private const val FIELD_SKIP_VPN_APP = "skip_vpn_app"
        private const val FIELD_AUTO_INCLUDE_NEW_APPS = "auto_include_new_apps"
        private const val FIELD_SORT_BY_BACKGROUND_TIME = "sort_by_background_time"
        private const val FIELD_ENABLE_REALTIME_FOREGROUND_DETECTION = "enable_realtime_foreground_detection"

        // 自定义强制停止命令字段
        private const val FIELD_USE_CUSTOM_FORCE_STOP_COMMAND = "use_custom_force_stop_command"
        private const val FIELD_CUSTOM_FORCE_STOP_COMMAND = "custom_force_stop_command"
        private const val FIELD_EFFECTIVE_FORCE_STOP_COMMAND = "effective_force_stop_command"

        // 冻结应用字段
        private const val FIELD_FREEZE_SCOPE = "freeze_scope"

        // 解冻应用字段
        private const val FIELD_UNFREEZE_SCOPE = "unfreeze_scope"
        private const val FIELD_OPEN_APP_AFTER_UNFREEZE = "open_app_after_unfreeze"

        // 内存管理字段
        private const val FIELD_ENABLE_MEMORY_THRESHOLD_CHECK = "enable_memory_threshold_check"
        private const val FIELD_MEMORY_THRESHOLD = "memory_threshold"
        private const val FIELD_MEMORY_THRESHOLD_IS_PERCENTAGE = "memory_threshold_is_percentage"

        // 策略字段
        private const val FIELD_APP_SORTING_STRATEGY = "app_sorting_strategy"
        private const val FIELD_MEMORY_CHECK_FREQUENCY = "memory_check_frequency"
        private const val FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY = "enable_custom_check_frequency"
        private const val FIELD_CUSTOM_CHECK_FREQUENCY_SIZE = "custom_check_frequency_size"

        // 清理策略字段
        private const val FIELD_ENABLE_CUSTOM_CLEANUP_STRATEGY = "enable_custom_cleanup_strategy"
        private const val FIELD_CLEANUP_STRATEGY_ID = "cleanup_strategy_id"
        private const val FIELD_USE_SIMPLE_MODE = "use_simple_mode"
        private const val FIELD_SIMPLE_STRATEGY_TYPE = "simple_strategy_type"

        // 应用重要性字段
        private const val FIELD_ENABLE_APP_IMPORTANCE_FILTER = "enable_app_importance_filter"

        // JSON存储的复杂字段
        private const val FIELD_FORCE_STOP_SELECTED_APPS_JSON = "force_stop_selected_apps_json"
        private const val FIELD_SELECTED_VPN_APPS_JSON = "selected_vpn_apps_json"
        private const val FIELD_SELECTED_IMPORTANCE_LEVELS_JSON = "selected_importance_levels_json"
        private const val FIELD_INTENT_PARAMS_JSON = "intent_params_json"
    }

    override fun getTaskType() = "application"

    /**
     * 保存应用程序任务
     * 将ApplicationTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的应用程序任务
     * @return 操作是否成功
     */
    override fun save(task: ApplicationTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存应用程序任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存应用程序任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),

                // JavaScript字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_JAVASCRIPT_CODE),
                    task.javascriptCode
                ),

                // Shell脚本字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHELL_SCRIPT),
                    task.shellScript
                ),
                saveEnum(generateKey(task.id, FIELD_SHELL_EXECUTION_MODE), task.shellExecutionMode),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHELL_TIMEOUT_MINUTES),
                    task.shellTimeoutMinutes
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHELL_TIMEOUT_SECONDS),
                    task.shellTimeoutSeconds
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHELL_WAIT_FOR_COMPLETION),
                    task.shellWaitForCompletion
                ),

                // Tasker插件字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TASKER_PLUGIN_PACKAGE),
                    task.taskerPluginPackage
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TASKER_PLUGIN_ACTION),
                    task.taskerPluginAction
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TASKER_PLUGIN_EXTRAS),
                    task.taskerPluginExtras
                ),

                // 应用启动字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_APP_PACKAGE_NAME),
                    task.appPackageName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_APP_NAME),
                    task.appName
                ),

                // 快捷方式字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHORTCUT_ID),
                    task.shortcutId
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHORTCUT_NAME),
                    task.shortcutName
                ),

                // 网站字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_WEBSITE_URL),
                    task.websiteUrl
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_URL_ENCODE_PARAMS),
                    task.urlEncodeParams
                ),

                // 强制停止字段
                saveEnum(generateKey(task.id, FIELD_FORCE_STOP_SCOPE), task.forceStopScope),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SKIP_FOREGROUND_APP),
                    task.skipForegroundApp
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SKIP_MUSIC_PLAYING_APP),
                    task.skipMusicPlayingApp
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SKIP_VPN_APP),
                    task.skipVpnApp
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_AUTO_INCLUDE_NEW_APPS),
                    task.autoIncludeNewApps
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SORT_BY_BACKGROUND_TIME),
                    task.sortByBackgroundTime
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_REALTIME_FOREGROUND_DETECTION),
                    task.enableRealtimeForegroundDetection
                ),

                // 自定义强制停止命令字段
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_USE_CUSTOM_FORCE_STOP_COMMAND),
                    task.useCustomForceStopCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_FORCE_STOP_COMMAND),
                    task.customForceStopCommand
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EFFECTIVE_FORCE_STOP_COMMAND),
                    task.effectiveForceStopCommand
                ),

                // 内存管理字段
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_MEMORY_THRESHOLD_CHECK),
                    task.enableMemoryThresholdCheck
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_MEMORY_THRESHOLD),
                    task.memoryThreshold
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_MEMORY_THRESHOLD_IS_PERCENTAGE),
                    task.memoryThresholdIsPercentage
                ),

                // 策略字段
                saveEnum(generateKey(task.id, FIELD_APP_SORTING_STRATEGY), task.appSortingStrategy),
                saveEnum(generateKey(task.id, FIELD_MEMORY_CHECK_FREQUENCY), task.memoryCheckFrequency),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY),
                    task.enableCustomCheckFrequency
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CUSTOM_CHECK_FREQUENCY_SIZE),
                    task.customCheckFrequencySize
                ),

                // 清理策略字段
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_CUSTOM_CLEANUP_STRATEGY),
                    task.enableCustomCleanupStrategy
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CLEANUP_STRATEGY_ID),
                    task.cleanupStrategyId
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_USE_SIMPLE_MODE),
                    task.useSimpleMode
                ),
                saveEnum(generateKey(task.id, FIELD_SIMPLE_STRATEGY_TYPE), task.simpleStrategyType),

                // 应用重要性字段
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_APP_IMPORTANCE_FILTER),
                    task.enableAppImportanceFilter
                ),

                // 冻结应用字段
                saveEnum(generateKey(task.id, FIELD_FREEZE_SCOPE), task.freezeScope),

                // 解冻应用字段
                saveEnum(generateKey(task.id, FIELD_UNFREEZE_SCOPE), task.unfreezeScope),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_OPEN_APP_AFTER_UNFREEZE),
                    task.openAppAfterUnfreeze
                )
            ))

            // 保存复杂字段（JSON格式）
            operations.addAll(saveComplexFields(task.id, task))

            // 执行批量存储操作
            val success = storageManager.executeBatch(operations)

            if (success) {
                logSaveSuccess(task.id)
            } else {
                logSaveError(task.id, "Failed to execute batch operations")
            }

            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception: ${e.message}")
            Log.e(TAG, "Exception while saving ApplicationTask: ${task.id}", e)
            false
        }
    }

    /**
     * 保存复杂字段（原生数据类型存储）
     *
     * @param taskId 任务ID
     * @param task 任务实例
     * @return 存储操作列表
     */
    private fun saveComplexFields(taskId: String, task: ApplicationTask): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        try {
            // 使用AppListStorageEngine保存应用列表
            val appListEngine = AppListStorageEngine(storageManager)

            // 保存forceStopSelectedApps（SimpleAppInfo列表）
            appListEngine.saveAppList("task_${taskId}_force_stop_selected_apps", task.forceStopSelectedApps)

            // 保存selectedVpnApps（SimpleAppInfo列表）
            appListEngine.saveAppList("task_${taskId}_selected_vpn_apps", task.selectedVpnApps)

            // 保存freezeSelectedApps（SimpleAppInfo列表）
            appListEngine.saveAppList("task_${taskId}_freeze_selected_apps", task.freezeSelectedApps)

            // 保存unfreezeSelectedApps（SimpleAppInfo列表）
            appListEngine.saveAppList("task_${taskId}_unfreeze_selected_apps", task.unfreezeSelectedApps)

            // 保存selectedImportanceLevels（AppImportance集合）- 使用原生数据类型存储
            operations.add(StorageOperation.createIntOperation(
                StorageDomain.TASKS,
                generateKey(taskId, "selected_importance_levels_count"),
                task.selectedImportanceLevels.size
            ))

            task.selectedImportanceLevels.forEachIndexed { index, importance ->
                operations.add(StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(taskId, "selected_importance_levels_$index"),
                    importance.name
                ))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error saving complex fields for task: $taskId", e)
        }

        return operations
    }

    /**
     * 加载应用程序任务
     * 从原生数据类型重建ApplicationTask对象
     *
     * @param taskId 任务ID
     * @return 加载的应用程序任务，失败时返回null
     */
    override fun load(taskId: String): ApplicationTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载应用程序任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "应用程序任务不存在: $taskId")
                return null
            }

            // 加载复杂字段
            val complexFields = loadComplexFields(taskId)

            ApplicationTask(
                id = taskId,
                operation = loadEnum(
                    generateKey(taskId, FIELD_OPERATION),
                    ApplicationOperation::class.java,
                    ApplicationOperation.LAUNCH_APP
                ),

                // JavaScript字段
                javascriptCode = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_JAVASCRIPT_CODE)
                ),

                // Shell脚本字段
                shellScript = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHELL_SCRIPT)
                ),
                shellExecutionMode = loadEnum(
                    generateKey(taskId, FIELD_SHELL_EXECUTION_MODE),
                    ShellExecutionMode::class.java,
                    ShellExecutionMode.NORMAL
                ),
                shellTimeoutMinutes = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHELL_TIMEOUT_MINUTES),
                    0
                ),
                shellTimeoutSeconds = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHELL_TIMEOUT_SECONDS),
                    30
                ),
                shellWaitForCompletion = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHELL_WAIT_FOR_COMPLETION),
                    false
                ),

                // Tasker插件字段
                taskerPluginPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TASKER_PLUGIN_PACKAGE)
                ),
                taskerPluginAction = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TASKER_PLUGIN_ACTION)
                ),
                taskerPluginExtras = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TASKER_PLUGIN_EXTRAS)
                ),

                // 应用启动字段
                appPackageName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_APP_PACKAGE_NAME)
                ),
                appName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_APP_NAME)
                ),

                // 快捷方式字段
                shortcutId = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHORTCUT_ID)
                ),
                shortcutName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHORTCUT_NAME)
                ),

                // 网站字段
                websiteUrl = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_WEBSITE_URL)
                ),
                urlEncodeParams = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_URL_ENCODE_PARAMS),
                    false
                ),

                // 强制停止字段
                forceStopScope = loadEnum(
                    generateKey(taskId, FIELD_FORCE_STOP_SCOPE),
                    ForceStopScope::class.java,
                    ForceStopScope.SELECTED_APP
                ),
                forceStopSelectedApps = complexFields.forceStopSelectedApps,
                skipForegroundApp = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SKIP_FOREGROUND_APP),
                    true
                ),
                skipMusicPlayingApp = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SKIP_MUSIC_PLAYING_APP),
                    true
                ),
                skipVpnApp = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SKIP_VPN_APP),
                    false
                ),
                selectedVpnApps = complexFields.selectedVpnApps,
                autoIncludeNewApps = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_AUTO_INCLUDE_NEW_APPS),
                    false
                ),
                sortByBackgroundTime = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SORT_BY_BACKGROUND_TIME),
                    false
                ),
                enableRealtimeForegroundDetection = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_REALTIME_FOREGROUND_DETECTION),
                    false
                ),

                // 自定义强制停止命令字段
                useCustomForceStopCommand = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_USE_CUSTOM_FORCE_STOP_COMMAND),
                    false
                ),
                customForceStopCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_FORCE_STOP_COMMAND)
                ),
                effectiveForceStopCommand = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EFFECTIVE_FORCE_STOP_COMMAND)
                ).ifEmpty { "am force-stop [package_name]" },

                // 内存管理字段
                enableMemoryThresholdCheck = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_MEMORY_THRESHOLD_CHECK),
                    false
                ),
                memoryThreshold = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_MEMORY_THRESHOLD),
                    3
                ),
                memoryThresholdIsPercentage = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_MEMORY_THRESHOLD_IS_PERCENTAGE),
                    false
                ),

                // 策略字段
                appSortingStrategy = loadEnum(
                    generateKey(taskId, FIELD_APP_SORTING_STRATEGY),
                    AppSortingStrategy::class.java,
                    AppSortingStrategy.BACKGROUND_TIME_ONLY
                ),
                memoryCheckFrequency = loadEnum(
                    generateKey(taskId, FIELD_MEMORY_CHECK_FREQUENCY),
                    MemoryCheckFrequency::class.java,
                    MemoryCheckFrequency.BALANCED
                ),
                enableCustomCheckFrequency = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY),
                    false
                ),
                customCheckFrequencySize = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CUSTOM_CHECK_FREQUENCY_SIZE),
                    5
                ),

                // 清理策略字段
                enableCustomCleanupStrategy = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_CUSTOM_CLEANUP_STRATEGY),
                    false
                ),
                cleanupStrategyId = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLEANUP_STRATEGY_ID)
                ),
                useSimpleMode = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_USE_SIMPLE_MODE),
                    true
                ),
                simpleStrategyType = loadEnum(
                    generateKey(taskId, FIELD_SIMPLE_STRATEGY_TYPE),
                    SimpleStrategyType::class.java,
                    SimpleStrategyType.PROTECT_IMPORTANT
                ),

                // 应用重要性字段
                enableAppImportanceFilter = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_APP_IMPORTANCE_FILTER),
                    false
                ),

                // 冻结应用字段
                freezeScope = loadEnum(
                    generateKey(taskId, FIELD_FREEZE_SCOPE),
                    ForceStopScope::class.java,
                    ForceStopScope.SELECTED_APP
                ),

                // 解冻应用字段
                unfreezeScope = loadEnum(
                    generateKey(taskId, FIELD_UNFREEZE_SCOPE),
                    ForceStopScope::class.java,
                    ForceStopScope.SELECTED_APP
                ),
                openAppAfterUnfreeze = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_OPEN_APP_AFTER_UNFREEZE),
                    false
                ),
                selectedImportanceLevels = complexFields.selectedImportanceLevels,
                freezeSelectedApps = complexFields.freezeSelectedApps,
                unfreezeSelectedApps = complexFields.unfreezeSelectedApps

                // ApplicationTask没有intentParams属性，这部分代码被移除

            ).also {
                Log.d(TAG, "应用程序任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception: ${e.message}")
            Log.e(TAG, "Exception while loading ApplicationTask: $taskId", e)
            null
        }
    }

    /**
     * 复杂字段数据类
     * 用于临时存储从JSON反序列化的复杂字段
     */
    private data class ComplexFields(
        val forceStopSelectedApps: List<SimpleAppInfo> = emptyList(),
        val selectedVpnApps: List<SimpleAppInfo> = emptyList(),
        val selectedImportanceLevels: Set<AppImportance> = emptySet(),
        val freezeSelectedApps: List<SimpleAppInfo> = emptyList(),
        val unfreezeSelectedApps: List<SimpleAppInfo> = emptyList()
    )

    /**
     * 加载复杂字段（原生数据类型存储）
     *
     * @param taskId 任务ID
     * @return 复杂字段数据
     */
    private fun loadComplexFields(taskId: String): ComplexFields {
        return try {
            // 使用AppListStorageEngine加载应用列表
            val appListEngine = AppListStorageEngine(storageManager)

            // 加载forceStopSelectedApps
            val forceStopSelectedApps = appListEngine.loadAppList("task_${taskId}_force_stop_selected_apps")

            // 加载selectedVpnApps
            val selectedVpnApps = appListEngine.loadAppList("task_${taskId}_selected_vpn_apps")

            // 加载freezeSelectedApps
            val freezeSelectedApps = appListEngine.loadAppList("task_${taskId}_freeze_selected_apps")

            // 加载unfreezeSelectedApps
            val unfreezeSelectedApps = appListEngine.loadAppList("task_${taskId}_unfreeze_selected_apps")

            // 加载selectedImportanceLevels（使用原生数据类型存储）
            val importanceLevelsCount = storageManager.loadInt(
                StorageDomain.TASKS,
                generateKey(taskId, "selected_importance_levels_count"),
                0
            )
            val selectedImportanceLevels = mutableSetOf<AppImportance>()
            for (index in 0 until importanceLevelsCount) {
                val importanceName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, "selected_importance_levels_$index")
                )
                if (importanceName.isNotEmpty()) {
                    try {
                        selectedImportanceLevels.add(AppImportance.valueOf(importanceName))
                    } catch (e: IllegalArgumentException) {
                        Log.w(TAG, "Invalid AppImportance value: $importanceName")
                    }
                }
            }

            ComplexFields(
                forceStopSelectedApps = forceStopSelectedApps,
                selectedVpnApps = selectedVpnApps,
                selectedImportanceLevels = selectedImportanceLevels.toSet(),
                freezeSelectedApps = freezeSelectedApps,
                unfreezeSelectedApps = unfreezeSelectedApps
            )

        } catch (e: Exception) {
            Log.e(TAG, "Error loading complex fields for task: $taskId", e)
            ComplexFields()
        }
    }
}

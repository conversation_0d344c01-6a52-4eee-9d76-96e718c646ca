package com.weinuo.quickcommands.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.clickable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.components.SkipOptionsSection
import com.weinuo.quickcommands.ui.screens.LocalNavController
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.ui.activities.AppSelectionActivity
import com.weinuo.quickcommands.data.SettingsRepository

import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType

import java.util.UUID

/**
 * 应用状态配置数据提供器
 *
 * 提供应用状态特定的配置项列表，为每个状态类别定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object AppStateConfigProvider {

    /**
     * 获取应用状态配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 应用状态配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<AppStateCategoryType>> {
        return listOf(
            // 状态变化类配置项（包含后台时间超过阈值）
            ConfigurationCardItem(
                id = "state_change",
                title = context.getString(R.string.app_state_change),
                description = context.getString(R.string.app_state_change_description),
                operationType = AppStateCategoryType.STATE_CHANGE,
                permissionRequired = true,
                content = { type, onComplete ->
                    AppStateChangeConfigContent(type, onComplete)
                }
            ),

            // 生命周期类配置项
            ConfigurationCardItem(
                id = "lifecycle",
                title = context.getString(R.string.app_lifecycle),
                description = context.getString(R.string.app_lifecycle_description),
                operationType = AppStateCategoryType.LIFECYCLE,
                permissionRequired = true,
                content = { type, onComplete ->
                    AppLifecycleConfigContent(type, onComplete)
                }
            ),

            // 应用管理类配置项
            ConfigurationCardItem(
                id = "package_management",
                title = context.getString(R.string.app_package_management),
                description = context.getString(R.string.app_package_management_description),
                operationType = AppStateCategoryType.PACKAGE_MANAGEMENT,
                permissionRequired = false,
                content = { type, onComplete ->
                    AppPackageManagementConfigContent(type, onComplete)
                }
            ),

            // 界面点击配置项（新增）
            ConfigurationCardItem(
                id = "interface_click",
                title = context.getString(R.string.app_interface_click),
                description = context.getString(R.string.app_interface_click_description),
                operationType = AppStateCategoryType.INTERFACE_INTERACTION,
                permissionRequired = true, // 需要无障碍服务权限
                content = { type, onComplete ->
                    InterfaceClickConfigContent(type, onComplete)
                }
            ),

            // 屏幕内容配置项（新增）
            ConfigurationCardItem(
                id = "screen_content",
                title = context.getString(R.string.app_screen_content),
                description = context.getString(R.string.app_screen_content_description),
                operationType = AppStateCategoryType.INTERFACE_INTERACTION,
                permissionRequired = true, // 需要无障碍服务权限
                content = { type, onComplete ->
                    ScreenContentConfigContent(type, onComplete)
                }
            ),

            // Tasker/Locale插件配置项
            ConfigurationCardItem(
                id = "tasker_locale_plugin",
                title = "Tasker/Locale插件",
                description = "监听Tasker/Locale插件状态",
                operationType = AppStateCategoryType.TASKER_LOCALE_PLUGIN,
                permissionRequired = false,
                content = { type, onComplete ->
                    TaskerLocalePluginConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * 状态变化类配置内容组件
 * 支持前台、后台、状态变化、后台时间超过阈值等多种状态类型
 * 支持单选和多选应用模式
 */
@Composable
fun AppStateChangeConfigContent(
    categoryType: AppStateCategoryType,
    onComplete: (Any) -> Unit
) {
    var selectedStateType by rememberSaveable { mutableStateOf(AppStateType.STATE_CHANGED) }
    var selectedDetectionMode by rememberSaveable { mutableStateOf(AppDetectionMode.ANY_APP) }

    // 统一的多选应用相关状态（适用于所有状态类型）
    var selectedApps by rememberSaveable { mutableStateOf(emptyList<SimpleAppInfo>()) }
    var appStateTriggerMode by rememberSaveable { mutableStateOf(AppStateTriggerMode.ANY_APP) }

    // 兼容性字段（单选应用，逐步废弃）
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 后台时间相关配置
    var backgroundTimeThresholdMinutes by rememberSaveable { mutableStateOf(5) }
    var skipForegroundApp by rememberSaveable { mutableStateOf(true) }
    var skipMusicPlayingApp by rememberSaveable { mutableStateOf(true) }
    var skipVpnApp by rememberSaveable { mutableStateOf(false) }
    var selectedVpnApps by rememberSaveable { mutableStateOf(emptyList<SimpleAppInfo>()) }
    var autoIncludeNewApps by rememberSaveable { mutableStateOf(false) }

    // 检测频率配置
    var checkFrequency by rememberSaveable { mutableStateOf(ConditionCheckFrequency.BALANCED) }
    var enableCustomCheckFrequency by rememberSaveable { mutableStateOf(false) }
    var customCheckFrequencySeconds by rememberSaveable { mutableStateOf(5) }
    var backgroundTimeCheckFrequency by rememberSaveable { mutableStateOf(ConditionCheckFrequency.SLOW) }
    var enableCustomBackgroundTimeCheckFrequency by rememberSaveable { mutableStateOf(false) }
    var customBackgroundTimeCheckFrequencySeconds by rememberSaveable { mutableStateOf(30) }

    // 根据保存的字段重建SimpleAppInfo对象（单选模式）
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }



    val context = LocalContext.current
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    selectedApps = loadedApps
                    // 自动切换到指定应用模式
                    selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 单选应用ActivityResultLauncher
    val singleAppSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    val selectedApp = loadedApps.first()
                    selectedAppPackageName = selectedApp.packageName
                    selectedAppName = selectedApp.appName
                    selectedAppIsSystemApp = selectedApp.isSystemApp
                    // 自动切换到指定应用模式
                    selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }



    // VPN应用选择ActivityResultLauncher
    val vpnAppSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    selectedVpnApps = loadedApps
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 状态类型选择
        Text(
            text = "状态类型",
            style = MaterialTheme.typography.titleMedium
        )

        val availableStateTypes = listOf(
            AppStateType.FOREGROUND,
            AppStateType.BACKGROUND,
            AppStateType.STATE_CHANGED,
            AppStateType.BACKGROUND_TIME_EXCEEDED
        )

        availableStateTypes.forEach { stateType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedStateType == stateType,
                        onClick = { selectedStateType = stateType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedStateType == stateType,
                    onClick = { selectedStateType = stateType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (stateType) {
                            AppStateType.FOREGROUND -> "应用进入前台"
                            AppStateType.BACKGROUND -> "应用进入后台"
                            AppStateType.STATE_CHANGED -> "应用状态变化"
                            AppStateType.BACKGROUND_TIME_EXCEEDED -> "后台时间超过阈值"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (stateType) {
                            AppStateType.FOREGROUND -> "当应用从后台切换到前台时触发"
                            AppStateType.BACKGROUND -> "当应用从前台切换到后台时触发"
                            AppStateType.STATE_CHANGED -> "当应用前台后台状态发生变化时触发"
                            AppStateType.BACKGROUND_TIME_EXCEEDED -> "当应用在后台时间超过设定阈值时触发"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 检测模式选择（所有状态类型统一）
        Text(
            text = "检测模式",
            style = MaterialTheme.typography.titleMedium
        )

        AppDetectionMode.values().forEach { detectionMode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedDetectionMode == detectionMode,
                        onClick = { selectedDetectionMode = detectionMode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedDetectionMode == detectionMode,
                    onClick = { selectedDetectionMode = detectionMode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = detectionMode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (detectionMode) {
                            AppDetectionMode.ANY_APP -> "监控所有应用的状态变化"
                            AppDetectionMode.SELECTED_APPS -> "只监控选中的应用的状态变化"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 应用选择部分（统一为多选模式）
        if (selectedDetectionMode == AppDetectionMode.SELECTED_APPS) {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "选择要监控的应用",
                style = MaterialTheme.typography.titleMedium
            )

            // 多选应用按钮
            OutlinedButton(
                onClick = {
                    val resultKey = "app_state_multi_selection_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createMultiSelectionIntent(
                        context = context,
                        selectedAppPackageNames = selectedApps.map { it.packageName },
                        resultKey = resultKey
                    )
                    appSelectionLauncher.launch(intent)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedApps.isEmpty()) {
                        "点击选择应用"
                    } else {
                        "已选择 ${selectedApps.size} 个应用"
                    }
                )
            }

            // 显示已选择的应用列表
            if (selectedApps.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "已选择的应用:",
                    style = MaterialTheme.typography.bodyMedium
                )
                selectedApps.take(3).forEach { app ->
                    Text(
                        text = "• ${app.appName}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                if (selectedApps.size > 3) {
                    Text(
                        text = "• 还有 ${selectedApps.size - 3} 个应用...",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 触发模式选择（仅在指定应用模式下显示）
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "触发模式",
                style = MaterialTheme.typography.titleMedium
            )

            AppStateTriggerMode.values().forEach { mode ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = appStateTriggerMode == mode,
                            onClick = { appStateTriggerMode = mode }
                        )
                        .padding(vertical = 4.dp)
                ) {
                    RadioButton(
                        selected = appStateTriggerMode == mode,
                        onClick = { appStateTriggerMode = mode }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column {
                        Text(
                            text = mode.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = when (mode) {
                                AppStateTriggerMode.ANY_APP -> "选中的应用中任意一个满足条件就触发"
                                AppStateTriggerMode.ALL_APPS -> "选中的应用全部都满足条件才触发"
                            },
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }

        // 通用检测频率配置（非后台时间条件）
        if (selectedStateType != AppStateType.BACKGROUND_TIME_EXCEEDED) {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "检测频率设置",
                style = MaterialTheme.typography.titleMedium
            )

            Text(
                text = "设置应用状态检测的频率，频率越高响应越快但耗电越多",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // 预设检测频率选项
            ConditionCheckFrequency.values().forEach { frequency ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = checkFrequency == frequency,
                            onClick = { checkFrequency = frequency }
                        )
                        .padding(vertical = 4.dp)
                ) {
                    RadioButton(
                        selected = checkFrequency == frequency,
                        onClick = { checkFrequency = frequency }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column {
                        Text(
                            text = frequency.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = frequency.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            // 自定义检测频率选项
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Checkbox(
                    checked = enableCustomCheckFrequency,
                    onCheckedChange = { enableCustomCheckFrequency = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "启用自定义检测频率",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            if (enableCustomCheckFrequency) {
                var customFrequencyText by rememberSaveable { mutableStateOf(customCheckFrequencySeconds.toString()) }
                var customFrequencyError by rememberSaveable { mutableStateOf(false) }

                OutlinedTextField(
                    value = customFrequencyText,
                    onValueChange = { newValue ->
                        customFrequencyText = newValue
                        val frequency = newValue.toIntOrNull()
                        if (frequency != null && frequency >= 1 && frequency <= 300) {
                            customCheckFrequencySeconds = frequency
                            customFrequencyError = false
                        } else {
                            customFrequencyError = true
                        }
                    },
                    label = { Text("检测间隔") },
                    suffix = { Text("秒") },
                    placeholder = { Text("1-300秒") },
                    isError = customFrequencyError,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth()
                )

                if (customCheckFrequencySeconds <= 2) {
                    Text(
                        text = "⚠️ 检测频率过高可能会显著增加耗电量",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        }

        // 后台时间超过阈值的额外配置
        if (selectedStateType == AppStateType.BACKGROUND_TIME_EXCEEDED) {
            // 后台时间阈值设置
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "后台时间阈值（分钟）",
                style = MaterialTheme.typography.titleMedium
            )

            OutlinedTextField(
                value = backgroundTimeThresholdMinutes.toString(),
                onValueChange = {
                    it.toIntOrNull()?.let { value ->
                        if (value > 0) backgroundTimeThresholdMinutes = value
                    }
                },
                label = { Text("阈值（分钟）") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("5") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )

            // 检测频率配置
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "检测频率设置",
                style = MaterialTheme.typography.titleMedium
            )

            Text(
                text = "设置后台时间检测的频率，频率越高响应越快但耗电越多",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // 预设检测频率选项

            ConditionCheckFrequency.values().forEach { frequency ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = backgroundTimeCheckFrequency == frequency,
                            onClick = { backgroundTimeCheckFrequency = frequency }
                        )
                        .padding(vertical = 4.dp)
                ) {
                    RadioButton(
                        selected = backgroundTimeCheckFrequency == frequency,
                        onClick = { backgroundTimeCheckFrequency = frequency }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column {
                        Text(
                            text = frequency.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = frequency.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            // 自定义检测频率选项
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Checkbox(
                    checked = enableCustomBackgroundTimeCheckFrequency,
                    onCheckedChange = { enableCustomBackgroundTimeCheckFrequency = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "启用自定义检测频率",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            if (enableCustomBackgroundTimeCheckFrequency) {
                var customFrequencyText by rememberSaveable { mutableStateOf(customBackgroundTimeCheckFrequencySeconds.toString()) }
                var customFrequencyError by rememberSaveable { mutableStateOf(false) }

                OutlinedTextField(
                    value = customFrequencyText,
                    onValueChange = { newValue ->
                        customFrequencyText = newValue
                        val frequency = newValue.toIntOrNull()
                        if (frequency != null && frequency >= 5 && frequency <= 600) {
                            customBackgroundTimeCheckFrequencySeconds = frequency
                            customFrequencyError = false
                        } else {
                            customFrequencyError = true
                        }
                    },
                    label = { Text("检测间隔") },
                    suffix = { Text("秒") },
                    placeholder = { Text("5-600秒") },
                    isError = customFrequencyError,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth()
                )

                if (customBackgroundTimeCheckFrequencySeconds <= 10) {
                    Text(
                        text = "⚠️ 检测频率过高可能会显著增加耗电量",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }

            // 例外处理配置
            Spacer(modifier = Modifier.height(16.dp))
            com.weinuo.quickcommands.ui.components.SkipOptionsSection(
                title = "例外处理",
                skipForegroundApp = skipForegroundApp,
                onSkipForegroundAppChanged = { skipForegroundApp = it },
                skipMusicPlayingApp = skipMusicPlayingApp,
                onSkipMusicPlayingAppChanged = { skipMusicPlayingApp = it },
                skipVpnApp = skipVpnApp,
                onSkipVpnAppChanged = { skipVpnApp = it },
                selectedVpnApps = selectedVpnApps,
                onVpnAppsChanged = { selectedVpnApps = it },
                onNavigateToVpnAppSelection = { selectedPackageNames ->
                    val resultKey = "vpn_app_selection_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createMultiSelectionIntent(
                        context = context,
                        selectedAppPackageNames = selectedPackageNames,
                        resultKey = resultKey
                    )
                    vpnAppSelectionLauncher.launch(intent)
                },
                stateKey = "background_time_config"
            )

            // 自动包含新安装应用
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = autoIncludeNewApps,
                    onCheckedChange = { autoIncludeNewApps = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = "自动包含新安装应用",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "新安装的应用将自动添加到监控列表中",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = when (selectedStateType) {
                    AppStateType.BACKGROUND_TIME_EXCEEDED -> {
                        // 后台时间条件
                        AppStateCondition(
                            id = UUID.randomUUID().toString(),
                            categoryType = categoryType,
                            stateType = selectedStateType,
                            detectionMode = selectedDetectionMode,
                            selectedApps = selectedApps,
                            appStateTriggerMode = appStateTriggerMode,
                            backgroundTimeThresholdMinutes = backgroundTimeThresholdMinutes,
                            skipForegroundApp = skipForegroundApp,
                            skipMusicPlayingApp = skipMusicPlayingApp,
                            skipVpnApp = skipVpnApp,
                            selectedVpnApps = selectedVpnApps,
                            autoIncludeNewApps = autoIncludeNewApps,
                            checkFrequency = checkFrequency,
                            enableCustomCheckFrequency = enableCustomCheckFrequency,
                            customCheckFrequencySeconds = customCheckFrequencySeconds,
                            backgroundTimeCheckFrequency = backgroundTimeCheckFrequency,
                            enableCustomBackgroundTimeCheckFrequency = enableCustomBackgroundTimeCheckFrequency,
                            customBackgroundTimeCheckFrequencySeconds = customBackgroundTimeCheckFrequencySeconds
                        )
                    }
                    else -> {
                        // 其他状态变化条件
                        AppStateCondition(
                            id = UUID.randomUUID().toString(),
                            categoryType = categoryType,
                            stateType = selectedStateType,
                            detectionMode = selectedDetectionMode,
                            selectedApps = selectedApps,
                            appStateTriggerMode = appStateTriggerMode,
                            checkFrequency = checkFrequency,
                            enableCustomCheckFrequency = enableCustomCheckFrequency,
                            customCheckFrequencySeconds = customCheckFrequencySeconds,
                            backgroundTimeCheckFrequency = backgroundTimeCheckFrequency,
                            enableCustomBackgroundTimeCheckFrequency = enableCustomBackgroundTimeCheckFrequency,
                            customBackgroundTimeCheckFrequencySeconds = customBackgroundTimeCheckFrequencySeconds
                        )
                    }
                }
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (selectedDetectionMode) {
                AppDetectionMode.ANY_APP -> true
                AppDetectionMode.SELECTED_APPS -> selectedApps.isNotEmpty()
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 生命周期类配置内容组件
 */
@Composable
fun AppLifecycleConfigContent(
    categoryType: AppStateCategoryType,
    onComplete: (Any) -> Unit
) {
    var selectedStateType by rememberSaveable { mutableStateOf(AppStateType.LAUNCHED) }
    var selectedDetectionMode by rememberSaveable { mutableStateOf(AppDetectionMode.ANY_APP) }

    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val context = LocalContext.current
    val navController = LocalNavController.current
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 单选应用ActivityResultLauncher
    val singleAppSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    val selectedApp = loadedApps.first()
                    selectedAppPackageName = selectedApp.packageName
                    selectedAppName = selectedApp.appName
                    selectedAppIsSystemApp = selectedApp.isSystemApp
                    // 自动切换到指定应用模式
                    selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 获取导航键
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            // 从原生数据类型存储加载应用选择结果
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val selectedApp = selectedAppsResult.first()
                // 自动切换到指定应用模式
                selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                // 更新选中的应用
                selectedAppPackageName = selectedApp.packageName
                selectedAppName = selectedApp.appName
                selectedAppIsSystemApp = selectedApp.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            // 清理临时存储
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 状态类型选择
        Text(
            text = "生命周期类型",
            style = MaterialTheme.typography.titleMedium
        )

        val availableStateTypes = listOf(
            AppStateType.LAUNCHED, AppStateType.CLOSED
        )

        availableStateTypes.forEach { stateType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedStateType == stateType,
                        onClick = { selectedStateType = stateType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedStateType == stateType,
                    onClick = { selectedStateType = stateType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (stateType) {
                            AppStateType.LAUNCHED -> "应用启动"
                            AppStateType.CLOSED -> "应用关闭"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (stateType) {
                            AppStateType.LAUNCHED -> "当应用启动时触发"
                            AppStateType.CLOSED -> "当应用关闭时触发"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 检测模式选择
        Text(
            text = "检测模式",
            style = MaterialTheme.typography.titleMedium
        )

        AppDetectionMode.values().forEach { detectionMode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedDetectionMode == detectionMode,
                        onClick = { selectedDetectionMode = detectionMode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedDetectionMode == detectionMode,
                    onClick = { selectedDetectionMode = detectionMode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = detectionMode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (detectionMode) {
                            AppDetectionMode.ANY_APP -> "监控所有应用的生命周期"
                            AppDetectionMode.SELECTED_APPS -> "只监控选中应用的生命周期"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 应用选择（仅在指定应用模式下显示）
        if (selectedDetectionMode == AppDetectionMode.SELECTED_APPS) {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "选择应用",
                style = MaterialTheme.typography.titleMedium
            )

            // 应用选择按钮
            OutlinedButton(
                onClick = {
                    val resultKey = "app_state_single_selection_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createSingleSelectionIntent(
                        context = context,
                        resultKey = resultKey
                    )
                    singleAppSelectionLauncher.launch(intent)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedApp != null) {
                        "已选择: ${selectedApp!!.appName}"
                    } else {
                        "点击选择应用"
                    }
                )
            }

            if (selectedApp != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "包名: ${selectedApp!!.packageName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = AppStateCondition(
                    id = UUID.randomUUID().toString(),
                    categoryType = categoryType,
                    stateType = selectedStateType,
                    detectionMode = selectedDetectionMode,
                    targetPackageName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.packageName ?: ""),
                    targetAppName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.appName ?: "")
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedDetectionMode == AppDetectionMode.ANY_APP ||
                     (selectedDetectionMode == AppDetectionMode.SELECTED_APPS && selectedApp != null)
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 应用管理类配置内容组件
 */
@Composable
fun AppPackageManagementConfigContent(
    categoryType: AppStateCategoryType,
    onComplete: (Any) -> Unit
) {
    var selectedStateType by rememberSaveable { mutableStateOf(AppStateType.INSTALLED) }
    var selectedDetectionMode by rememberSaveable { mutableStateOf(AppDetectionMode.ANY_APP) }

    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val context = LocalContext.current
    val navController = LocalNavController.current
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 单选应用ActivityResultLauncher
    val singleAppSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    val selectedApp = loadedApps.first()
                    selectedAppPackageName = selectedApp.packageName
                    selectedAppName = selectedApp.appName
                    selectedAppIsSystemApp = selectedApp.isSystemApp
                    // 自动切换到指定应用模式
                    selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 监听应用选择结果
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 使用字段分离的方式获取应用选择结果
        val selectedAppPackageNameResult = savedStateHandle?.get<String>("selected_app_package_name")
        val selectedAppNameResult = savedStateHandle?.get<String>("selected_app_name")
        val selectedAppIsSystemAppResult = savedStateHandle?.get<Boolean>("selected_app_is_system_app")

        if (!selectedAppPackageNameResult.isNullOrEmpty()) {
            // 自动切换到指定应用模式
            selectedDetectionMode = AppDetectionMode.SELECTED_APPS
            // 更新选中的应用
            selectedAppPackageName = selectedAppPackageNameResult
            selectedAppName = selectedAppNameResult ?: ""
            selectedAppIsSystemApp = selectedAppIsSystemAppResult ?: false
            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_app_package_name")
            savedStateHandle.remove<String>("selected_app_name")
            savedStateHandle.remove<Boolean>("selected_app_is_system_app")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 状态类型选择
        Text(
            text = "应用管理类型",
            style = MaterialTheme.typography.titleMedium
        )

        val availableStateTypes = listOf(
            AppStateType.INSTALLED, AppStateType.UNINSTALLED, AppStateType.UPDATED
        )

        availableStateTypes.forEach { stateType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedStateType == stateType,
                        onClick = { selectedStateType = stateType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedStateType == stateType,
                    onClick = { selectedStateType = stateType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (stateType) {
                            AppStateType.INSTALLED -> "应用安装"
                            AppStateType.UNINSTALLED -> "应用删除"
                            AppStateType.UPDATED -> "应用更新"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (stateType) {
                            AppStateType.INSTALLED -> "当应用安装时触发"
                            AppStateType.UNINSTALLED -> "当应用删除时触发"
                            AppStateType.UPDATED -> "当应用更新时触发"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 检测模式选择
        Text(
            text = "检测模式",
            style = MaterialTheme.typography.titleMedium
        )

        AppDetectionMode.values().forEach { detectionMode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedDetectionMode == detectionMode,
                        onClick = { selectedDetectionMode = detectionMode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedDetectionMode == detectionMode,
                    onClick = { selectedDetectionMode = detectionMode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = detectionMode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (detectionMode) {
                            AppDetectionMode.ANY_APP -> "监控所有应用的管理事件"
                            AppDetectionMode.SELECTED_APPS -> "只监控选中应用的管理事件"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 应用选择（仅在指定应用模式下显示）
        if (selectedDetectionMode == AppDetectionMode.SELECTED_APPS) {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "选择应用",
                style = MaterialTheme.typography.titleMedium
            )

            // 应用选择按钮
            OutlinedButton(
                onClick = {
                    val resultKey = "app_state_single_selection_2_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createSingleSelectionIntent(
                        context = context,
                        resultKey = resultKey
                    )
                    singleAppSelectionLauncher.launch(intent)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedApp != null) {
                        "已选择: ${selectedApp!!.appName}"
                    } else {
                        "点击选择应用"
                    }
                )
            }

            if (selectedApp != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "包名: ${selectedApp!!.packageName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = AppStateCondition(
                    id = UUID.randomUUID().toString(),
                    categoryType = categoryType,
                    stateType = selectedStateType,
                    detectionMode = selectedDetectionMode,
                    targetPackageName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.packageName ?: ""),
                    targetAppName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.appName ?: "")
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedDetectionMode == AppDetectionMode.ANY_APP ||
                     (selectedDetectionMode == AppDetectionMode.SELECTED_APPS && selectedApp != null)
        ) {
            Text("确认配置")
        }
    }
}

/**
 * Tasker/Locale插件条件配置内容组件
 */
@Composable
fun TaskerLocalePluginConfigContent(
    categoryType: AppStateCategoryType,
    onComplete: (Any) -> Unit
) {
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedPluginPackageName by rememberSaveable { mutableStateOf("") }
    var selectedPluginName by rememberSaveable { mutableStateOf("") }
    var selectedPluginIsSystemApp by rememberSaveable { mutableStateOf(false) }
    var pluginAction by rememberSaveable { mutableStateOf("com.twofortyfouram.locale.intent.action.QUERY_CONDITION") }
    var pluginExtras by rememberSaveable { mutableStateOf("") }
    var expectedState by rememberSaveable { mutableStateOf(TaskerLocaleConditionState.SATISFIED) }
    var checkInterval by rememberSaveable { mutableStateOf(30) }
    var timeoutSeconds by rememberSaveable { mutableStateOf(10) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedPlugin = if (selectedPluginPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedPluginPackageName,
            appName = selectedPluginName,
            isSystemApp = selectedPluginIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 插件应用选择ActivityResultLauncher
    val pluginAppSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    val selectedApp = loadedApps.first()
                    selectedPluginPackageName = selectedApp.packageName
                    selectedPluginName = selectedApp.appName
                    selectedPluginIsSystemApp = selectedApp.isSystemApp
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 获取导航键
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            // 从原生数据类型存储加载应用选择结果
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedPluginPackageName = app.packageName
                selectedPluginName = app.appName
                selectedPluginIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            // 清理临时存储
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Tasker/Locale插件设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 插件选择
        Text(
            text = "选择插件应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                val resultKey = "plugin_app_selection_${System.currentTimeMillis()}"
                val intent = AppSelectionActivity.createSingleSelectionIntent(
                    context = context,
                    filterType = "TASKER_CONDITION_PLUGIN",
                    resultKey = resultKey
                )
                pluginAppSelectionLauncher.launch(intent)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedPlugin != null) {
                    "已选择: ${selectedPlugin!!.appName}"
                } else {
                    "点击选择插件应用"
                }
            )
        }

        // 插件动作配置
        Text(
            text = "插件动作",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = pluginAction,
            onValueChange = { pluginAction = it },
            label = { Text("Intent Action") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("com.twofortyfouram.locale.intent.action.QUERY_CONDITION") }
        )

        // 插件额外参数
        Text(
            text = "插件参数（可选）",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = pluginExtras,
            onValueChange = { pluginExtras = it },
            label = { Text("额外参数 (JSON格式)") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("{}") },
            minLines = 3
        )

        // 期望状态选择
        Text(
            text = "期望状态",
            style = MaterialTheme.typography.titleMedium
        )

        TaskerLocaleConditionState.values().forEach { state ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = expectedState == state,
                    onClick = { expectedState = state }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = state.displayName,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 检查间隔设置
        Text(
            text = "检查间隔（秒）",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = checkInterval.toString(),
            onValueChange = {
                it.toIntOrNull()?.let { value ->
                    if (value > 0) checkInterval = value
                }
            },
            label = { Text("检查间隔") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("30") }
        )

        // 超时时间设置
        Text(
            text = "超时时间（秒）",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = timeoutSeconds.toString(),
            onValueChange = {
                it.toIntOrNull()?.let { value ->
                    if (value > 0) timeoutSeconds = value
                }
            },
            label = { Text("超时时间") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("10") }
        )

        // 显示插件配置说明和警告
        if (selectedPlugin != null) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainerLow
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "插件说明",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = "• 将定期查询插件的状态",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "• 查询频率由检查间隔控制，过于频繁可能影响性能",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "• 插件必须支持 Tasker/Locale 条件协议",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    if (checkInterval < 10) {
                        Text(
                            text = "⚠️ 检查间隔过短可能影响系统性能",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = AppStateCondition(
                    id = UUID.randomUUID().toString(),
                    categoryType = categoryType,
                    stateType = AppStateType.TASKER_LOCALE_PLUGIN_CONDITION,
                    detectionMode = AppDetectionMode.SELECTED_APPS,
                    targetPackageName = selectedPlugin?.packageName ?: "",
                    targetAppName = selectedPlugin?.appName ?: "",
                    pluginAction = pluginAction,
                    pluginExtras = pluginExtras,
                    expectedState = expectedState,
                    checkInterval = checkInterval,
                    timeoutSeconds = timeoutSeconds
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedPlugin != null && pluginAction.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}



/**
 * 界面点击配置内容组件
 */
@Composable
fun InterfaceClickConfigContent(
    categoryType: AppStateCategoryType,
    onComplete: (Any) -> Unit
) {
    var selectedDetectionMode by rememberSaveable { mutableStateOf(AppDetectionMode.ANY_APP) }

    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 界面点击相关配置
    var clickTargetText by rememberSaveable { mutableStateOf("") }
    var caseSensitive by rememberSaveable { mutableStateOf(false) }
    var useRegex by rememberSaveable { mutableStateOf(false) }
    var interfaceInteractionType by rememberSaveable { mutableStateOf(InterfaceInteractionType.CLICK) }
    var includeOverlayLayers by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val context = LocalContext.current
    val navController = LocalNavController.current
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 单选应用ActivityResultLauncher
    val singleAppSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    val selectedApp = loadedApps.first()
                    selectedAppPackageName = selectedApp.packageName
                    selectedAppName = selectedApp.appName
                    selectedAppIsSystemApp = selectedApp.isSystemApp
                    // 自动切换到指定应用模式
                    selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 获取导航键
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            // 从原生数据类型存储加载应用选择结果
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val selectedApp = selectedAppsResult.first()
                // 自动切换到指定应用模式
                selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                // 更新选中的应用
                selectedAppPackageName = selectedApp.packageName
                selectedAppName = selectedApp.appName
                selectedAppIsSystemApp = selectedApp.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            // 清理临时存储
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 功能说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "界面点击条件",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "此条件将在所选应用中检测到对特定文本内容的点击时触发。支持普通文本匹配和正则表达式匹配。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 检测模式选择
        Text(
            text = "检测模式",
            style = MaterialTheme.typography.titleMedium
        )

        AppDetectionMode.values().forEach { detectionMode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedDetectionMode == detectionMode,
                        onClick = { selectedDetectionMode = detectionMode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedDetectionMode == detectionMode,
                    onClick = { selectedDetectionMode = detectionMode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = detectionMode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (detectionMode) {
                            AppDetectionMode.ANY_APP -> "监控所有应用的界面交互"
                            AppDetectionMode.SELECTED_APPS -> "只监控选中应用的界面交互"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 应用选择（仅在指定应用模式下显示）
        if (selectedDetectionMode == AppDetectionMode.SELECTED_APPS) {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "选择应用",
                style = MaterialTheme.typography.titleMedium
            )

            // 应用选择按钮
            OutlinedButton(
                onClick = {
                    val resultKey = "app_state_single_selection_3_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createSingleSelectionIntent(
                        context = context,
                        resultKey = resultKey
                    )
                    singleAppSelectionLauncher.launch(intent)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedApp != null) {
                        "已选择: ${selectedApp!!.appName}"
                    } else {
                        "点击选择应用"
                    }
                )
            }

            if (selectedApp != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "包名: ${selectedApp!!.packageName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 交互类型选择
        Text(
            text = "交互类型",
            style = MaterialTheme.typography.titleMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            InterfaceInteractionType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { interfaceInteractionType = type },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = interfaceInteractionType == type,
                        onClick = { interfaceInteractionType = type }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = type.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 点击目标文本配置
        Text(
            text = "目标文本",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = clickTargetText,
            onValueChange = { clickTargetText = it },
            label = { Text("要检测点击的文本内容") },
            placeholder = { Text("例如：确定、取消、登录等") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = false,
            maxLines = 3
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 匹配选项
        Text(
            text = "匹配选项",
            style = MaterialTheme.typography.titleMedium
        )

        // 区分大小写
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = caseSensitive,
                onCheckedChange = { caseSensitive = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "区分大小写",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 使用正则表达式
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useRegex,
                onCheckedChange = { useRegex = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "使用正则表达式",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "支持更复杂的文本匹配模式",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 包括叠加层
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = includeOverlayLayers,
                onCheckedChange = { includeOverlayLayers = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "包括叠加层",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "检测悬浮窗、对话框等叠加层中的内容",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = AppStateCondition(
                    id = UUID.randomUUID().toString(),
                    categoryType = categoryType,
                    stateType = AppStateType.INTERFACE_CLICK,
                    detectionMode = selectedDetectionMode,
                    targetPackageName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.packageName ?: ""),
                    targetAppName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.appName ?: ""),
                    clickTargetText = clickTargetText,
                    caseSensitive = caseSensitive,
                    useRegex = useRegex,
                    interfaceInteractionType = interfaceInteractionType,
                    includeOverlayLayers = includeOverlayLayers
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = (selectedDetectionMode == AppDetectionMode.ANY_APP ||
                     (selectedDetectionMode == AppDetectionMode.SELECTED_APPS && selectedApp != null)) &&
                     clickTargetText.isNotEmpty()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 屏幕内容配置内容组件
 */
@Composable
fun ScreenContentConfigContent(
    categoryType: AppStateCategoryType,
    onComplete: (Any) -> Unit
) {
    var selectedDetectionMode by rememberSaveable { mutableStateOf(AppDetectionMode.ANY_APP) }

    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 屏幕内容相关配置
    var screenContentText by rememberSaveable { mutableStateOf("") }
    var screenContentTriggerMode by rememberSaveable { mutableStateOf(ScreenContentTriggerMode.APPEAR) }
    var caseSensitive by rememberSaveable { mutableStateOf(false) }
    var useRegex by rememberSaveable { mutableStateOf(false) }
    var screenContentMatchType by rememberSaveable { mutableStateOf(ScreenContentMatchType.TEXT_CONTENT) }
    var includeOverlayLayers by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val context = LocalContext.current
    val navController = LocalNavController.current
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 单选应用ActivityResultLauncher
    val singleAppSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    val selectedApp = loadedApps.first()
                    selectedAppPackageName = selectedApp.packageName
                    selectedAppName = selectedApp.appName
                    selectedAppIsSystemApp = selectedApp.isSystemApp
                    // 自动切换到指定应用模式
                    selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 获取导航键
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            // 从原生数据类型存储加载应用选择结果
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val selectedApp = selectedAppsResult.first()
                // 自动切换到指定应用模式
                selectedDetectionMode = AppDetectionMode.SELECTED_APPS
                // 更新选中的应用
                selectedAppPackageName = selectedApp.packageName
                selectedAppName = selectedApp.appName
                selectedAppIsSystemApp = selectedApp.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            // 清理临时存储
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 功能说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "屏幕内容条件",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "此条件将在所选应用中当屏幕出现或移出某些文本内容时触发。支持普通文本匹配和正则表达式匹配。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 检测模式选择
        Text(
            text = "检测模式",
            style = MaterialTheme.typography.titleMedium
        )

        AppDetectionMode.values().forEach { detectionMode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedDetectionMode == detectionMode,
                        onClick = { selectedDetectionMode = detectionMode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedDetectionMode == detectionMode,
                    onClick = { selectedDetectionMode = detectionMode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = detectionMode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (detectionMode) {
                            AppDetectionMode.ANY_APP -> "监控所有应用的屏幕内容变化"
                            AppDetectionMode.SELECTED_APPS -> "只监控选中应用的屏幕内容变化"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 应用选择（仅在指定应用模式下显示）
        if (selectedDetectionMode == AppDetectionMode.SELECTED_APPS) {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "选择应用",
                style = MaterialTheme.typography.titleMedium
            )

            // 应用选择按钮
            OutlinedButton(
                onClick = {
                    val resultKey = "app_state_single_selection_4_${System.currentTimeMillis()}"
                    val intent = AppSelectionActivity.createSingleSelectionIntent(
                        context = context,
                        resultKey = resultKey
                    )
                    singleAppSelectionLauncher.launch(intent)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedApp != null) {
                        "已选择: ${selectedApp!!.appName}"
                    } else {
                        "点击选择应用"
                    }
                )
            }

            if (selectedApp != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "包名: ${selectedApp!!.packageName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 匹配类型选择
        Text(
            text = "匹配类型",
            style = MaterialTheme.typography.titleMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            ScreenContentMatchType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { screenContentMatchType = type },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = screenContentMatchType == type,
                        onClick = { screenContentMatchType = type }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = type.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 屏幕内容配置
        Text(
            text = when (screenContentMatchType) {
                ScreenContentMatchType.TEXT_CONTENT -> "文本内容"
                ScreenContentMatchType.VIEW_ID -> "查看ID"
            },
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = screenContentText,
            onValueChange = { screenContentText = it },
            label = {
                Text(when (screenContentMatchType) {
                    ScreenContentMatchType.TEXT_CONTENT -> "要检测的文本内容"
                    ScreenContentMatchType.VIEW_ID -> "要检测的视图ID"
                })
            },
            placeholder = {
                Text(when (screenContentMatchType) {
                    ScreenContentMatchType.TEXT_CONTENT -> "例如：登录成功、加载中、错误信息等"
                    ScreenContentMatchType.VIEW_ID -> "例如：com.example.app:id/login_button"
                })
            },
            modifier = Modifier.fillMaxWidth(),
            singleLine = screenContentMatchType == ScreenContentMatchType.VIEW_ID,
            maxLines = if (screenContentMatchType == ScreenContentMatchType.VIEW_ID) 1 else 3
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 触发模式选择
        Text(
            text = stringResource(R.string.trigger_mode),
            style = MaterialTheme.typography.titleMedium
        )

        ScreenContentTriggerMode.values().forEach { triggerMode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = screenContentTriggerMode == triggerMode,
                        onClick = { screenContentTriggerMode = triggerMode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = screenContentTriggerMode == triggerMode,
                    onClick = { screenContentTriggerMode = triggerMode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = triggerMode.displayName,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 匹配选项
        Text(
            text = "匹配选项",
            style = MaterialTheme.typography.titleMedium
        )

        // 区分大小写（仅在文本内容匹配时显示）
        if (screenContentMatchType == ScreenContentMatchType.TEXT_CONTENT) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = caseSensitive,
                    onCheckedChange = { caseSensitive = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "区分大小写",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 使用正则表达式（仅在文本内容匹配时显示）
        if (screenContentMatchType == ScreenContentMatchType.TEXT_CONTENT) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = useRegex,
                    onCheckedChange = { useRegex = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = "使用正则表达式",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "支持更复杂的文本匹配模式",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 包括叠加层
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = includeOverlayLayers,
                onCheckedChange = { includeOverlayLayers = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "包括叠加层",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "检测悬浮窗、对话框等叠加层中的内容",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = AppStateCondition(
                    id = UUID.randomUUID().toString(),
                    categoryType = categoryType,
                    stateType = AppStateType.SCREEN_CONTENT,
                    detectionMode = selectedDetectionMode,
                    targetPackageName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.packageName ?: ""),
                    targetAppName = if (selectedDetectionMode == AppDetectionMode.ANY_APP) "" else (selectedApp?.appName ?: ""),
                    screenContentText = screenContentText,
                    screenContentTriggerMode = screenContentTriggerMode,
                    caseSensitive = if (screenContentMatchType == ScreenContentMatchType.TEXT_CONTENT) caseSensitive else false,
                    useRegex = if (screenContentMatchType == ScreenContentMatchType.TEXT_CONTENT) useRegex else false,
                    screenContentMatchType = screenContentMatchType,
                    includeOverlayLayers = includeOverlayLayers
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = (selectedDetectionMode == AppDetectionMode.ANY_APP ||
                     (selectedDetectionMode == AppDetectionMode.SELECTED_APPS && selectedApp != null)) &&
                     screenContentText.isNotEmpty()
        ) {
            Text("确认配置")
        }
    }
}
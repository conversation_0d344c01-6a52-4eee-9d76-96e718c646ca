# AppImportanceManagementScreen 重构总结

## 修改概述

参考 AppSelectionScreen 的设计，对应用重要性管理界面进行了重构，主要包括：

1. **搜索框统一**：将原有的 OutlinedTextField 改为 UnifiedSearchTextField
2. **移除复选框**：删除了"显示系统应用"复选框
3. **分组显示**：改为用户应用和系统应用分组显示，支持展开/收起
4. **运行状态检测**：添加与 AppSelectionScreen 一致的正在运行应用检测功能

## 具体修改内容

### 1. 导入更新

```kotlin
// 新增导入

```

### 2. 状态变量更新
```kotlin
// 移除
var showSystemApps by rememberSaveable { mutableStateOf(true) }

// 新增
val focusManager = LocalFocusManager.current
var userAppsExpanded by remember { mutableStateOf(true) }
var systemAppsExpanded by remember { mutableStateOf(false) }

// 运行状态检测相关（与AppSelectionScreen保持一致）
val usageStatsManager = remember { context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager }
var cachedStats by remember { mutableStateOf<List<android.app.usage.UsageStats>?>(null) }
var cachedStatsTimestamp by remember { mutableStateOf(0L) }
val CACHE_VALID_TIME = 5000L // 缓存有效期5秒
```

### 3. 搜索框重构
**修改前**：
```kotlin
OutlinedTextField(
    value = searchQuery,
    onValueChange = { searchQuery = it },
    label = { Text("搜索应用") },
    leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
    modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp)
)
```

**修改后**：
```kotlin
UnifiedSearchTextField(
    searchQuery = searchQuery,
    onSearchQueryChange = {
        searchQuery = it
        // 搜索时自动展开所有分组
        if (it.isNotEmpty()) {
            userAppsExpanded = true
            systemAppsExpanded = true
        }
    },
    onClearSearch = {
        searchQuery = ""
        focusManager.clearFocus()
    },
    placeholder = "搜索应用名称或包名",
    modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp)
)
```

### 4. 移除复选框
完全删除了以下代码：
```kotlin
// 显示选项
Row(
    modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 16.dp),
    verticalAlignment = Alignment.CenterVertically
) {
    Checkbox(
        checked = showSystemApps,
        onCheckedChange = { showSystemApps = it }
    )
    Text(
        text = "显示系统应用",
        modifier = Modifier.padding(start = 8.dp)
    )
}
```

### 5. 应用列表分组重构
**修改前**：简单的标题 + 列表
```kotlin
// 用户应用
if (filteredUserApps.isNotEmpty()) {
    item {
        Text(
            text = "用户应用 (${filteredUserApps.size})",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(vertical = 8.dp)
        )
    }
    items(filteredUserApps) { app -> ... }
}

// 系统应用
if (showSystemApps && filteredSystemApps.isNotEmpty()) {
    item {
        Text(
            text = "系统应用 (${filteredSystemApps.size})",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(vertical = 8.dp)
        )
    }
    items(filteredSystemApps) { app -> ... }
}
```

**修改后**：可展开分组 + 搜索时扁平显示
```kotlin
// 显示分组还是扁平列表
val showGrouped = searchQuery.isBlank()

if (showGrouped) {
    // 分组显示
    if (filteredUserApps.isNotEmpty()) {
        // 用户应用分组
        item {
            ExpandableGroupHeader(
                title = "用户应用",
                count = filteredUserApps.size,
                expanded = userAppsExpanded,
                onToggle = { userAppsExpanded = !userAppsExpanded }
            )
        }

        if (userAppsExpanded) {
            items(filteredUserApps) { app -> ... }
        }
    }

    if (filteredSystemApps.isNotEmpty()) {
        // 系统应用分组
        item {
            ExpandableGroupHeader(
                title = "系统应用",
                count = filteredSystemApps.size,
                expanded = systemAppsExpanded,
                onToggle = { systemAppsExpanded = !systemAppsExpanded }
            )
        }

        if (systemAppsExpanded) {
            items(filteredSystemApps) { app -> ... }
        }
    }
} else {
    // 搜索时显示扁平列表
    val allFilteredApps = filteredUserApps + filteredSystemApps
    items(allFilteredApps) { app -> ... }
}
```

### 6. 运行状态检测功能
**新增运行状态检测方法**：
```kotlin
/**
 * 检查是否有使用情况访问权限（与AppSelectionScreen保持一致）
 */
fun hasUsageStatsPermission(): Boolean {
    val appOps = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
    val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        appOps.unsafeCheckOpNoThrow(
            AppOpsManager.OPSTR_GET_USAGE_STATS,
            Process.myUid(),
            context.packageName
        )
    } else {
        appOps.checkOpNoThrow(
            AppOpsManager.OPSTR_GET_USAGE_STATS,
            Process.myUid(),
            context.packageName
        )
    }
    return mode == AppOpsManager.MODE_ALLOWED
}

/**
 * 检查应用是否正在运行（与AppSelectionScreen保持一致）
 * 仅使用UsageStatsManager的queryUsageStats方法，提高检测速度
 */
fun isAppRunning(packageName: String): Boolean {
    // 使用UsageStatsManager的queryUsageStats
    if (hasUsageStatsPermission()) {
        try {
            val currentTime = System.currentTimeMillis()

            // 使用缓存的统计数据，如果缓存有效
            val stats = if (currentTime - cachedStatsTimestamp < CACHE_VALID_TIME && cachedStats != null) {
                cachedStats!!
            } else {
                val endTime = currentTime
                val startTime = endTime - 30 * 60 * 1000 // 30分钟前

                // 获取使用统计
                val newStats = usageStatsManager.queryUsageStats(
                    UsageStatsManager.INTERVAL_DAILY,
                    startTime,
                    endTime
                )

                // 更新缓存
                cachedStats = newStats
                cachedStatsTimestamp = currentTime

                newStats
            }

            // 检查应用是否在最近使用的列表中
            val recentTimeThreshold = currentTime - 15 * 60 * 1000 // 15分钟内认为是最近使用
            val appStat = stats.find { it.packageName == packageName }

            // 如果应用在最近使用过，认为它正在运行
            if (appStat != null && appStat.lastTimeUsed >= recentTimeThreshold) {
                return true
            }

            // 如果应用今天有被使用过，也认为它可能在运行
            if (appStat != null && appStat.totalTimeInForeground > 0) {
                return true
            }
        } catch (e: Exception) {
            Log.e("AppImportanceManagement", "Error checking if app is running with UsageStats", e)
        }
    }

    // 如果没有权限或检测失败，返回false
    return false
}
```

**修改AppImportanceItem组件**：
```kotlin
@Composable
private fun AppImportanceItem(
    app: AppInfo,
    currentImportance: AppImportance?,
    onImportanceChanged: (AppImportance?) -> Unit,
    isAppRunning: (String) -> Boolean = { false } // 新增参数
) {
    // ... 其他代码

    // 应用信息显示部分修改
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = app.packageName,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )
        // 显示正在运行状态（与AppSelectionScreen保持一致）
        if (isAppRunning(app.packageName)) {
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "正在运行",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .background(
                        MaterialTheme.colorScheme.primaryContainer,
                        RoundedCornerShape(4.dp)
                    )
                    .padding(horizontal = 6.dp, vertical = 2.dp)
            )
        }
    }
}
```

**更新所有AppImportanceItem调用**：
```kotlin
AppImportanceItem(
    app = app,
    currentImportance = pendingChanges[app.packageName] ?: appImportanceMap[app.packageName],
    onImportanceChanged = { importance ->
        pendingChanges = pendingChanges.toMutableMap().apply {
            put(app.packageName, importance)
        }
        hasUnsavedChanges = true
    },
    isAppRunning = ::isAppRunning // 新增参数
)
```

### 7. 新增 ExpandableGroupHeader 组件
```kotlin
@Composable
private fun ExpandableGroupHeader(
    title: String,
    count: Int,
    expanded: Boolean,
    onToggle: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggle() },
        shape = RoundedCornerShape(8.dp),
        color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "$title ($count)",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.weight(1f)
            )
            Icon(
                imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                contentDescription = if (expanded) "收起" else "展开",
                tint = MaterialTheme.colorScheme.secondary
            )
        }
    }
}
```

## 功能特性

### 1. 统一的搜索体验
- 使用 UnifiedSearchTextField 组件，与 AppSelectionScreen 保持一致
- 支持清除搜索功能
- 搜索时自动展开所有分组

### 2. 智能分组显示
- **正常状态**：分组显示，用户应用默认展开，系统应用默认收起
- **搜索状态**：扁平显示所有匹配的应用，便于查找

### 3. 运行状态检测
- **一致的检测逻辑**：与 AppSelectionScreen 使用相同的 UsageStatsManager 检测方法
- **缓存机制**：5秒缓存有效期，提高性能
- **视觉指示**：正在运行的应用在包名旁显示"正在运行"标签
- **权限处理**：自动检查使用情况访问权限

### 4. 交互优化
- 点击分组标题可展开/收起
- 展开/收起状态独立管理
- 搜索时自动展开所有分组，提高搜索效率

## 设计一致性

现在 AppImportanceManagementScreen 与 AppSelectionScreen 在以下方面保持一致：

1. **搜索框**：都使用 UnifiedSearchTextField
2. **分组设计**：都使用 ExpandableGroupHeader 组件
3. **运行状态检测**：使用相同的 UsageStatsManager 检测逻辑和缓存机制
4. **运行状态显示**：相同的"正在运行"标签样式和位置
5. **交互逻辑**：搜索时自动展开分组，正常时支持手动展开/收起
6. **视觉风格**：分组标题样式、颜色、间距保持一致

## 用户体验改进

1. **简化界面**：移除了复选框，减少界面复杂度
2. **更好的组织**：分组显示让大量应用更易管理
3. **灵活查看**：可以选择性查看用户应用或系统应用
4. **高效搜索**：搜索时扁平显示，快速定位目标应用

## 测试建议

1. **基本功能测试**：
   - 验证分组展开/收起功能
   - 验证搜索功能正常工作
   - 验证应用重要性设置功能

2. **运行状态检测测试**：
   - 验证正在运行的应用显示"正在运行"标签
   - 测试使用情况访问权限检查
   - 验证缓存机制工作正常
   - 测试运行状态检测的准确性

3. **交互测试**：
   - 测试搜索时自动展开分组
   - 测试清除搜索后恢复分组状态
   - 测试分组标题点击响应

4. **数据测试**：
   - 测试大量应用时的性能
   - 测试空列表状态
   - 测试搜索无结果状态

5. **权限测试**：
   - 测试有使用情况访问权限时的运行状态检测
   - 测试无权限时的降级处理

package com.weinuo.quickcommands.ui.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.weinuo.quickcommands.ui.screens.SmartReminderDetailConfigScreen
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme

/**
 * 智慧提醒详细配置Activity
 *
 * 采用独立Activity架构，提供全屏配置体验，无底部导航栏。
 * 支持智慧提醒的详细配置功能，包括新建和编辑模式。
 *
 * 设计特点：
 * - 独立Activity架构，提供更大的配置空间
 * - 无底部导航栏，专注于配置功能
 * - 支持多种智慧提醒类型的配置
 * - 完整的配置验证和保存逻辑
 *
 * @see SmartReminderDetailConfigScreen
 */
class SmartReminderDetailConfigActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_REMINDER_TYPE_ID = "reminder_type_id"
        private const val EXTRA_INITIAL_CONFIG = "initial_config"
        
        /**
         * 启动智慧提醒详细配置界面（新建模式）
         *
         * @param context 上下文
         * @param reminderTypeId 智慧提醒类型ID
         */
        fun startForCreate(context: Context, reminderTypeId: String) {
            val intent = Intent(context, SmartReminderDetailConfigActivity::class.java).apply {
                putExtra(EXTRA_REMINDER_TYPE_ID, reminderTypeId)
            }
            context.startActivity(intent)
        }
        
        /**
         * 启动智慧提醒详细配置界面（编辑模式）
         *
         * @param context 上下文
         * @param reminderTypeId 智慧提醒类型ID
         * @param initialConfig 初始配置数据
         */
        fun startForEdit(context: Context, reminderTypeId: String, initialConfig: String? = null) {
            val intent = Intent(context, SmartReminderDetailConfigActivity::class.java).apply {
                putExtra(EXTRA_REMINDER_TYPE_ID, reminderTypeId)
                if (initialConfig != null) {
                    putExtra(EXTRA_INITIAL_CONFIG, initialConfig)
                }
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 获取传递的参数
        val reminderTypeId = intent.getStringExtra(EXTRA_REMINDER_TYPE_ID) ?: ""
        val initialConfigKey = intent.getStringExtra(EXTRA_INITIAL_CONFIG)
        
        // 加载初始配置（编辑模式）
        val initialConfig = if (initialConfigKey != null) {
            try {
                // 这里可以根据需要从存储中加载初始配置
                // 暂时返回null，表示新建模式
                null
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
        
        setContent {
            QuickCommandsTheme {
                SmartReminderDetailConfigScreen(
                    reminderTypeId = reminderTypeId,
                    initialConfig = initialConfig,
                    onConfigured = { configResult ->
                        // 配置完成，关闭Activity
                        finish()
                    },
                    onNavigateBack = {
                        // 返回，关闭Activity
                        finish()
                    },
                    navController = null // 独立Activity不需要NavController
                )
            }
        }
    }
}

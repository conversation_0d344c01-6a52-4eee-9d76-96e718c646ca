<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 背景环 -->
    <item>
        <shape
            android:innerRadiusRatio="3"
            android:shape="ring"
            android:thicknessRatio="8"
            android:useLevel="false">
            <solid android:color="#E0E0E0" />
        </shape>
    </item>
    
    <!-- 进度环 -->
    <item>
        <rotate
            android:fromDegrees="-90"
            android:toDegrees="-90">
            <shape
                android:innerRadiusRatio="3"
                android:shape="ring"
                android:thicknessRatio="8"
                android:useLevel="true">
                <solid android:color="#FFFFFF" />
            </shape>
        </rotate>
    </item>
    
</layer-list>

package com.weinuo.quickcommands.ui.components.integrated

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.ui.res.painterResource
import com.weinuo.quickcommands.R
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.config.SearchTextFieldConfig

/**
 * 整合设计风格的搜索框组件
 *
 * 天空蓝主题专用的搜索框实现
 * 完全重写实现，抛弃MD3组件，解决文字对齐问题
 * 特点：
 * - 固定44dp高度
 * - 固定28dp圆角半径
 * - 统一的颜色主题
 * - 搜索图标和清除按钮
 * - 键盘搜索操作支持
 * - 精确的文字居中对齐
 */
@Composable
fun IntegratedSearchTextField(
    config: SearchTextFieldConfig
) {
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current
    var isFocused by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }

    // 获取全局设置中的搜索框提示文字字重设置
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 根据设置确定提示文字字重
    val placeholderFontWeight = when (globalSettings.searchFieldPlaceholderFontWeight) {
        "regular" -> FontWeight.Normal
        "medium" -> FontWeight.Medium
        else -> FontWeight.Medium // 默认为Medium
    }

    // 根据设置确定搜索图标资源
    val searchIconResource = when (globalSettings.searchFieldIconWeight) {
        "regular" -> R.drawable.ic_search_sky_blue_regular
        "medium" -> R.drawable.ic_search_sky_blue_medium
        "bold" -> R.drawable.ic_search_sky_blue_bold
        else -> R.drawable.ic_search_sky_blue_medium // 默认为中黑体
    }

    // 颜色配置
    val containerColor = if (isFocused) {
        MaterialTheme.colorScheme.surfaceContainerHigh
    } else {
        MaterialTheme.colorScheme.surfaceContainer
    }

    val contentColor = MaterialTheme.colorScheme.onSurface
    val placeholderColor = MaterialTheme.colorScheme.onSurfaceVariant
    val iconColor = MaterialTheme.colorScheme.onSurfaceVariant

    // 文字样式
    val textStyle = MaterialTheme.typography.bodyLarge.copy(
        color = contentColor
    )

    Box(
        modifier = config.modifier
            .height(44.dp) // 固定44dp高度
            .clip(RoundedCornerShape(28.dp))
            .background(containerColor)
            .padding(horizontal = 16.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 搜索图标 - 天空蓝主题根据全局设置使用不同粗细的SVG图标
            Icon(
                painter = painterResource(id = searchIconResource),
                contentDescription = "搜索",
                tint = iconColor,
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 文本输入区域
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.CenterStart
            ) {
                BasicTextField(
                    value = config.searchQuery,
                    onValueChange = config.onSearchQueryChange,
                    modifier = Modifier
                        .fillMaxWidth()
                        .onFocusChanged { focusState ->
                            isFocused = focusState.isFocused
                        },
                    enabled = config.enabled,
                    textStyle = textStyle,
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Search
                    ),
                    keyboardActions = KeyboardActions(
                        onSearch = {
                            focusManager.clearFocus()
                        }
                    ),
                    singleLine = true,
                    cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                    interactionSource = interactionSource,
                    visualTransformation = VisualTransformation.None
                )

                // 占位符文本 - 天空蓝主题根据全局设置使用不同字重
                if (config.searchQuery.isEmpty()) {
                    Text(
                        text = config.placeholder,
                        style = textStyle.copy(
                            color = placeholderColor,
                            fontWeight = placeholderFontWeight
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            // 清除按钮
            if (config.searchQuery.isNotEmpty()) {
                Spacer(modifier = Modifier.width(8.dp))
                IconButton(
                    onClick = config.onClearSearch,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清除",
                        tint = iconColor,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

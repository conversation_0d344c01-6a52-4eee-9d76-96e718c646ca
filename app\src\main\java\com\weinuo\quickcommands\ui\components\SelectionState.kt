package com.weinuo.quickcommands.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue

/**
 * 应用列表选择状态管理
 *
 * 用于管理应用列表的选择模式状态，包括是否处于选择模式、已选择的应用列表等
 */
class SelectionState {
    // 是否处于选择模式
    var isInSelectionMode by mutableStateOf(false)
        private set

    // 已选择的应用列表
    private val _selectedItems = mutableStateListOf<String>()
    val selectedItems: List<String> get() = _selectedItems

    // 获取已选择的应用数量
    val selectedCount: Int get() = _selectedItems.size

    // 进入选择模式
    fun enterSelectionMode() {
        isInSelectionMode = true
    }

    // 退出选择模式
    fun exitSelectionMode() {
        isInSelectionMode = false
        _selectedItems.clear()
    }

    // 切换应用的选择状态
    fun toggleSelection(packageName: String) {
        if (_selectedItems.contains(packageName)) {
            _selectedItems.remove(packageName)
            // 当所有项目都被取消选择时，自动退出选择模式
            if (_selectedItems.isEmpty()) {
                exitSelectionMode()
            }
        } else {
            _selectedItems.add(packageName)
        }
    }

    // 检查应用是否被选中
    fun isSelected(packageName: String): Boolean {
        return _selectedItems.contains(packageName)
    }

    // 全选
    fun selectAll(allPackageNames: List<String>) {
        _selectedItems.clear()
        _selectedItems.addAll(allPackageNames)
    }

    // 取消全选
    fun deselectAll() {
        _selectedItems.clear()
        // 当取消全选时，自动退出选择模式
        exitSelectionMode()
    }

    // 是否全部已选择
    fun isAllSelected(allPackageNames: List<String>): Boolean {
        return allPackageNames.isNotEmpty() && _selectedItems.size == allPackageNames.size
    }
}

/**
 * 创建并记住一个SelectionState实例
 */
@Composable
fun rememberSelectionState(): SelectionState {
    return remember { SelectionState() }
}

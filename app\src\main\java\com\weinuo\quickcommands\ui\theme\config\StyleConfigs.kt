package com.weinuo.quickcommands.ui.theme.config

import androidx.compose.material3.Typography
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 圆角配置
 */
data class CornerRadiusConfig(
    val extraSmall: Dp = 4.dp,
    val small: Dp = 8.dp,
    val medium: Dp = 12.dp,
    val large: Dp = 16.dp,
    val extraLarge: Dp = 28.dp
) {
    /**
     * 根据尺寸类型获取圆角值
     */
    fun getRadius(size: CornerSize): Dp = when (size) {
        CornerSize.EXTRA_SMALL -> extraSmall
        CornerSize.SMALL -> small
        CornerSize.MEDIUM -> medium
        CornerSize.LARGE -> large
        CornerSize.EXTRA_LARGE -> extraLarge
    }
}

/**
 * 高度配置
 */
data class ElevationConfig(
    val none: Dp = 0.dp,
    val low: Dp = 1.dp,
    val medium: Dp = 3.dp,
    val high: Dp = 6.dp,
    val extraHigh: Dp = 12.dp
) {
    /**
     * 根据高度级别获取高度值
     */
    fun getElevation(level: ElevationLevel): Dp = when (level) {
        ElevationLevel.NONE -> none
        ElevationLevel.LOW -> low
        ElevationLevel.MEDIUM -> medium
        ElevationLevel.HIGH -> high
        ElevationLevel.EXTRA_HIGH -> extraHigh
    }
}

/**
 * 间距配置
 */
data class SpacingConfig(
    val extraSmall: Dp = 4.dp,
    val small: Dp = 8.dp,
    val medium: Dp = 16.dp,
    val large: Dp = 24.dp,
    val extraLarge: Dp = 32.dp
) {
    /**
     * 根据间距尺寸获取间距值
     */
    fun getSpacing(size: SpacingSize): Dp = when (size) {
        SpacingSize.EXTRA_SMALL -> extraSmall
        SpacingSize.SMALL -> small
        SpacingSize.MEDIUM -> medium
        SpacingSize.LARGE -> large
        SpacingSize.EXTRA_LARGE -> extraLarge
    }
}

/**
 * 字体配置
 */
data class TypographyConfig(
    val typography: Typography,
    val lineHeightMultiplier: Float = 1.0f,
    val letterSpacingMultiplier: Float = 1.0f
)

/**
 * 视觉效果配置
 */
data class EffectsConfig(
    val blurEnabled: Boolean = false,
    val blurRadius: Dp = 20.dp,
    val transparencyEnabled: Boolean = false,
    val shadowEnabled: Boolean = true,
    val gradientEnabled: Boolean = false,
    val animationsEnabled: Boolean = true
)

/**
 * 边框配置
 */
data class BorderConfig(
    val width: Dp = 1.dp,
    val color: Color = Color.Gray,
    val enabled: Boolean = true
)

/**
 * 阴影配置
 */
data class ShadowConfig(
    val enabled: Boolean = true,
    val color: Color = Color.Black.copy(alpha = 0.1f),
    val offsetX: Dp = 0.dp,
    val offsetY: Dp = 2.dp,
    val blurRadius: Dp = 4.dp,
    val spreadRadius: Dp = 0.dp
)

/**
 * 动画配置
 */
data class AnimationConfig(
    val enabled: Boolean = true,
    val duration: Int = 300,
    val easing: String = "ease_in_out"
)

/**
 * 透明度配置
 */
data class OpacityConfig(
    val disabled: Float = 0.38f,
    val inactive: Float = 0.6f,
    val active: Float = 1.0f,
    val hover: Float = 0.08f,
    val pressed: Float = 0.12f,
    val focused: Float = 0.12f,
    val selected: Float = 0.16f,
    val dragging: Float = 0.16f
) {
    /**
     * 根据状态获取透明度值
     */
    fun getOpacity(state: OpacityState): Float = when (state) {
        OpacityState.DISABLED -> disabled
        OpacityState.INACTIVE -> inactive
        OpacityState.ACTIVE -> active
        OpacityState.HOVER -> hover
        OpacityState.PRESSED -> pressed
        OpacityState.FOCUSED -> focused
        OpacityState.SELECTED -> selected
        OpacityState.DRAGGING -> dragging
    }
}

/**
 * 尺寸配置
 */
data class DimensionConfig(
    val minTouchTarget: Dp = 48.dp,
    val iconSize: Dp = 24.dp,
    val smallIconSize: Dp = 16.dp,
    val largeIconSize: Dp = 32.dp,
    val avatarSize: Dp = 40.dp,
    val buttonHeight: Dp = 40.dp,
    val textFieldHeight: Dp = 56.dp,
    val appBarHeight: Dp = 64.dp,
    val bottomNavHeight: Dp = 80.dp,
    val fabSize: Dp = 56.dp,
    val smallFabSize: Dp = 40.dp,
    val largeFabSize: Dp = 96.dp
)

/**
 * 交互状态配置
 */
data class InteractionStateConfig(
    val backgroundColor: Color? = null,
    val contentColor: Color? = null,
    val borderColor: Color? = null,
    val elevation: Dp? = null,
    val scale: Float = 1.0f,
    val alpha: Float = 1.0f,
    val animationDuration: Int = 150
)

/**
 * 动画规格配置
 */
data class AnimationSpec(
    val duration: Int,
    val delay: Int = 0,
    val easing: String = "ease"
)

/**
 * 弹性动画规格配置
 */
data class SpringAnimationSpec(
    val damping: Float = 0.8f,
    val stiffness: Float = 400f,
    val visibilityThreshold: Float = 0.01f
)

/**
 * 缓动动画规格配置
 */
data class EasingAnimationSpec(
    val duration: Int,
    val easing: String = "ease"
)

/**
 * 圆角尺寸枚举
 */
enum class CornerSize {
    EXTRA_SMALL,
    SMALL,
    MEDIUM,
    LARGE,
    EXTRA_LARGE
}

/**
 * 高度级别枚举
 */
enum class ElevationLevel {
    NONE,
    LOW,
    MEDIUM,
    HIGH,
    EXTRA_HIGH
}

/**
 * 间距尺寸枚举
 */
enum class SpacingSize {
    EXTRA_SMALL,
    SMALL,
    MEDIUM,
    LARGE,
    EXTRA_LARGE
}

/**
 * 透明度状态枚举
 */
enum class OpacityState {
    DISABLED,
    INACTIVE,
    ACTIVE,
    HOVER,
    PRESSED,
    FOCUSED,
    SELECTED,
    DRAGGING
}

/**
 * 卡片样式配置
 *
 * 统一管理所有卡片组件的样式属性，解决硬编码样式值导致的主题切换不一致问题
 */
data class CardStyleConfig(
    // 基础卡片样式
    val defaultCornerRadius: Dp = 12.dp,
    val defaultElevation: Dp = 0.dp,
    val defaultHorizontalPadding: Dp = 13.dp,
    val defaultVerticalPadding: Dp = 18.dp,
    val settingsVerticalPadding: Dp = 16.dp,       // 设置界面卡片专用垂直边距

    // 间距配置
    val itemSpacing: Dp = 8.dp,                    // 页面中卡片之间的间距
    val sectionSpacing: Dp = 16.dp,

    // 内容间距配置
    val contentVerticalSpacing: Dp = 4.dp,
    val contentHorizontalSpacing: Dp = 8.dp,

    // 选择状态样式
    val selectedElevation: Dp = 2.dp,
    val selectedBorderWidth: Dp = 2.dp
) {
    /**
     * 获取内容卡片的PaddingValues（快捷指令、模板等）
     */
    fun getPaddingValues(): androidx.compose.foundation.layout.PaddingValues =
        androidx.compose.foundation.layout.PaddingValues(
            horizontal = defaultHorizontalPadding,
            vertical = defaultVerticalPadding
        )

    /**
     * 获取设置界面卡片的PaddingValues
     */
    fun getSettingsPaddingValues(): androidx.compose.foundation.layout.PaddingValues =
        androidx.compose.foundation.layout.PaddingValues(
            horizontal = defaultHorizontalPadding,
            vertical = settingsVerticalPadding
        )
}

/**
 * 底部导航栏样式配置
 *
 * 统一管理底部导航栏组件的样式属性，支持用户自定义配置
 */
data class BottomNavigationStyleConfig(
    // 基础尺寸参数
    val height: Dp = 80.dp,                           // 底部导航栏高度
    val horizontalPadding: Dp = 16.dp,                // 水平内边距
    val verticalPadding: Dp = 7.dp,                   // 垂直内边距

    // 导航项样式参数
    val itemCornerRadius: Dp = 16.dp,                 // 导航项圆角大小
    val itemOuterPadding: Dp = 4.dp,                  // 导航项外边距
    val itemVerticalPadding: Dp = 8.dp,               // 导航项垂直内边距
    val itemHorizontalPadding: Dp = 12.dp,            // 导航项水平内边距

    // 图标文字参数
    val iconSize: Dp = 24.dp,                         // 图标大小
    val iconTextSpacing: Dp = 4.dp,                   // 图标与文字间距
    val textFontSize: androidx.compose.ui.unit.TextUnit = 11.sp, // 文字字体大小

    // 字重参数
    val selectedFontWeight: androidx.compose.ui.text.font.FontWeight = androidx.compose.ui.text.font.FontWeight.Medium,   // 选中状态字重
    val unselectedFontWeight: androidx.compose.ui.text.font.FontWeight = androidx.compose.ui.text.font.FontWeight.Normal, // 未选中状态字重

    // 动画参数
    val colorAnimationDuration: Int = 150,            // 颜色动画时长（ms）
    val backgroundAnimationDuration: Int = 150,       // 背景动画时长（ms）

    // 布局参数
    val itemArrangement: androidx.compose.foundation.layout.Arrangement.Horizontal = androidx.compose.foundation.layout.Arrangement.SpaceEvenly // 导航项排列方式
)

/**
 * 页面布局配置
 *
 * 统一管理所有页面布局的间距和边距属性，解决硬编码布局值导致的主题切换不一致问题
 */
data class PageLayoutConfig(
    // 页面内容边距配置
    val contentHorizontalPadding: Dp = 16.dp,    // 页面内容水平边距

    // 搜索框配置
    val searchFieldMargin: Dp = 16.dp,           // 搜索框外边距

    // 页面间距配置
    val headerSpacing: Dp = 16.dp,               // 页面标题间距
    val bottomPadding: Dp = 88.dp,               // 页面底部边距（为底部导航栏留空间）
    val scrollContentSpacing: Dp = 8.dp          // 滚动内容间距
)



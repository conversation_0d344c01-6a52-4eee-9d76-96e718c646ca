package com.weinuo.quickcommands.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.shizuku.ShizukuManager
import com.weinuo.quickcommands.smartreminder.NewAppReminderHandler
import com.weinuo.quickcommands.utils.OverlayPermissionUtil
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.*

/**
 * 智慧提醒悬浮窗服务
 *
 * 用于显示非侵入式的智慧提醒弹窗，替代通知机制。
 * 特点：
 * - 非侵入式：用户可以暂时忽略弹窗继续使用手机
 * - 手动关闭：用户需要主动关闭弹窗
 * - 不增加前台服务：作为普通服务运行
 * - 悬浮窗权限：需要悬浮窗权限才能显示
 *
 * 设计原则：
 * - 简洁明了的提醒内容
 * - 不阻挡用户操作
 * - 支持多种提醒类型
 * - 自动管理生命周期
 */
class SmartReminderOverlayService : Service() {

    companion object {
        private const val TAG = "SmartReminderOverlay"
        private const val EXTRA_TITLE = "extra_title"
        private const val EXTRA_MESSAGE = "extra_message"
        private const val EXTRA_REMINDER_TYPE = "extra_reminder_type"

        /**
         * 显示智慧提醒弹窗
         */
        fun showReminder(
            context: Context,
            title: String,
            message: String,
            reminderType: String = "default"
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, reminderType)
            }
            context.startService(intent)
        }

        /**
         * 显示新应用提醒弹窗
         */
        fun showNewAppReminder(
            context: Context,
            title: String,
            message: String,
            appInfo: NewAppReminderHandler.AppInfo
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "new_app")
                putExtra("extra_app_package_name", appInfo.packageName)
                putExtra("extra_app_name", appInfo.appName)
            }
            context.startService(intent)
        }

        /**
         * 显示音乐应用提醒弹窗
         */
        fun showMusicAppReminder(
            context: Context,
            title: String,
            message: String,
            packageName: String,
            appName: String
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "music_app")
                putExtra("extra_app_package_name", packageName)
                putExtra("extra_app_name", appName)
            }
            context.startService(intent)
        }

        /**
         * 显示多音乐应用提醒弹窗
         */
        fun showMultiMusicAppReminder(
            context: Context,
            title: String,
            message: String,
            selectedApps: List<com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.SelectedMusicApp>,
            buttonConfigs: List<com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.MusicAppButtonConfig>
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "multi_music_app")

                // 传递应用列表
                val appPackageNames = selectedApps.map { it.packageName }.toTypedArray()
                val appNames = selectedApps.map { it.appName }.toTypedArray()
                putExtra("extra_app_package_names", appPackageNames)
                putExtra("extra_app_names", appNames)

                // 传递按钮配置
                val buttonConfigIds = buttonConfigs.map { it.id }.toTypedArray()
                val buttonConfigAppPackageNames = buttonConfigs.map { it.appPackageName }.toTypedArray()
                val buttonConfigPositions = buttonConfigs.map { it.buttonPosition }.toTypedArray()
                val buttonConfigSizes = buttonConfigs.map { it.buttonSize }.toIntArray()
                val buttonConfigMarginXs = buttonConfigs.map { it.buttonMarginX }.toIntArray()
                val buttonConfigMarginYs = buttonConfigs.map { it.buttonMarginY }.toIntArray()
                val buttonConfigEnabled = buttonConfigs.map { it.isEnabled }.toBooleanArray()

                putExtra("extra_button_config_ids", buttonConfigIds)
                putExtra("extra_button_config_app_package_names", buttonConfigAppPackageNames)
                putExtra("extra_button_config_positions", buttonConfigPositions)
                putExtra("extra_button_config_sizes", buttonConfigSizes)
                putExtra("extra_button_config_margin_xs", buttonConfigMarginXs)
                putExtra("extra_button_config_margin_ys", buttonConfigMarginYs)
                putExtra("extra_button_config_enabled", buttonConfigEnabled)
            }
            context.startService(intent)
        }

        /**
         * 显示购物应用提醒弹窗
         */
        fun showShoppingAppReminder(
            context: Context,
            title: String,
            message: String,
            appInfo: com.weinuo.quickcommands.smartreminder.ShoppingAppReminderHandler.AppInfo
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "shopping_app")
                putExtra("extra_app_package_name", appInfo.packageName)
                putExtra("extra_app_name", appInfo.appName)
            }
            context.startService(intent)
        }

        /**
         * 显示应用链接提醒弹窗
         */
        fun showAppLinkReminder(
            context: Context,
            title: String,
            message: String,
            appInfo: com.weinuo.quickcommands.smartreminder.AppLinkReminderHandler.AppInfo
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "app_link")
                putExtra("extra_app_package_name", appInfo.packageName)
                putExtra("extra_app_name", appInfo.appName)
            }
            context.startService(intent)
        }

        /**
         * 显示分享网址提醒弹窗
         */
        fun showShareUrlReminder(
            context: Context,
            title: String,
            message: String,
            urlInfo: com.weinuo.quickcommands.smartreminder.ShareUrlReminderHandler.UrlInfo
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "share_url")
                putExtra("extra_url", urlInfo.url)
                putExtra("extra_domain", urlInfo.domain ?: "")
                putExtra("extra_display_text", urlInfo.displayText)
            }
            context.startService(intent)
        }

        /**
         * 显示地址提醒弹窗
         */
        fun showAddressReminder(
            context: Context,
            title: String,
            message: String,
            packageName: String,
            appName: String
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "address")
                putExtra("extra_app_package_name", packageName)
                putExtra("extra_app_name", appName)
            }
            context.startService(intent)
        }

        /**
         * 显示多地图应用提醒弹窗
         */
        fun showMultiAddressReminder(
            context: Context,
            title: String,
            message: String,
            selectedApps: List<com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.SelectedMapApp>,
            buttonConfigs: List<com.weinuo.quickcommands.storage.SmartReminderConfigAdapter.AddressReminderButtonConfig>
        ) {
            val intent = Intent(context, SmartReminderOverlayService::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_MESSAGE, message)
                putExtra(EXTRA_REMINDER_TYPE, "multi_address")

                // 传递应用列表
                val appPackageNames = selectedApps.map { it.packageName }.toTypedArray()
                val appNames = selectedApps.map { it.appName }.toTypedArray()
                putExtra("extra_app_package_names", appPackageNames)
                putExtra("extra_app_names", appNames)

                // 传递按钮配置
                val buttonConfigIds = buttonConfigs.map { it.id }.toTypedArray()
                val buttonConfigAppPackageNames = buttonConfigs.map { it.appPackageName }.toTypedArray()
                val buttonConfigPositions = buttonConfigs.map { it.buttonPosition }.toTypedArray()
                val buttonConfigSizes = buttonConfigs.map { it.buttonSize }.toIntArray()
                val buttonConfigMarginXs = buttonConfigs.map { it.buttonMarginX }.toIntArray()
                val buttonConfigMarginYs = buttonConfigs.map { it.buttonMarginY }.toIntArray()
                val buttonConfigEnabled = buttonConfigs.map { it.isEnabled }.toBooleanArray()

                putExtra("extra_button_config_ids", buttonConfigIds)
                putExtra("extra_button_config_app_package_names", buttonConfigAppPackageNames)
                putExtra("extra_button_config_positions", buttonConfigPositions)
                putExtra("extra_button_config_sizes", buttonConfigSizes)
                putExtra("extra_button_config_margin_xs", buttonConfigMarginXs)
                putExtra("extra_button_config_margin_ys", buttonConfigMarginYs)
                putExtra("extra_button_config_enabled", buttonConfigEnabled)
            }
            context.startService(intent)
        }
    }

    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var multiAppOverlayViews: List<View> = emptyList()
    private var currentIntent: Intent? = null
    private var isShowing = false
    private var autoDismissJob: Job? = null
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 保存Intent以便在其他方法中访问
        currentIntent = intent

        val title = intent?.getStringExtra(EXTRA_TITLE) ?: "智慧提醒"
        val message = intent?.getStringExtra(EXTRA_MESSAGE) ?: ""
        val reminderType = intent?.getStringExtra(EXTRA_REMINDER_TYPE) ?: "default"

        if (!isShowing) {
            showOverlay(title, message, reminderType)
        }

        return START_NOT_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        autoDismissJob?.cancel()
        serviceScope.cancel()
        hideAllOverlays()
    }

    /**
     * 显示悬浮窗
     */
    private fun showOverlay(title: String, message: String, reminderType: String) {
        try {
            // 检查悬浮窗权限
            if (!OverlayPermissionUtil.hasOverlayPermission(this)) {
                Log.e(TAG, "No overlay permission, cannot show smart reminder")
                stopSelf()
                return
            }

            windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager

            // 处理多应用提醒类型
            when (reminderType) {
                "multi_music_app" -> {
                    showMultiAppOverlay(title, message, reminderType)
                }
                "multi_address" -> {
                    showMultiAppOverlay(title, message, reminderType)
                }
                else -> {
                    // 创建单个悬浮窗布局
                    overlayView = createOverlayView(title, message, reminderType)

                    // 根据配置设置悬浮窗参数
                    val layoutParams = createLayoutParams(reminderType)

                    // 添加到窗口管理器
                    windowManager?.addView(overlayView, layoutParams)
                    isShowing = true
                }
            }

            Log.d(TAG, "Smart reminder overlay shown: $title")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing overlay", e)
            stopSelf()
        }
    }

    /**
     * 显示多应用悬浮窗
     */
    private fun showMultiAppOverlay(title: String, message: String, reminderType: String) {
        try {
            val appPackageNames = currentIntent?.getStringArrayExtra("extra_app_package_names") ?: return
            val appNames = currentIntent?.getStringArrayExtra("extra_app_names") ?: return
            val buttonConfigIds = currentIntent?.getStringArrayExtra("extra_button_config_ids") ?: return
            val buttonConfigAppPackageNames = currentIntent?.getStringArrayExtra("extra_button_config_app_package_names") ?: return
            val buttonConfigPositions = currentIntent?.getStringArrayExtra("extra_button_config_positions") ?: return
            val buttonConfigSizes = currentIntent?.getIntArrayExtra("extra_button_config_sizes") ?: return
            val buttonConfigMarginXs = currentIntent?.getIntArrayExtra("extra_button_config_margin_xs") ?: return
            val buttonConfigMarginYs = currentIntent?.getIntArrayExtra("extra_button_config_margin_ys") ?: return
            val buttonConfigEnabled = currentIntent?.getBooleanArrayExtra("extra_button_config_enabled") ?: return

            // 创建多个悬浮窗按钮
            val overlayViews = mutableListOf<View>()

            for (i in buttonConfigIds.indices) {
                if (!buttonConfigEnabled[i]) continue

                val appPackageName = buttonConfigAppPackageNames[i]
                val appName = appNames.find { name ->
                    appPackageNames.any { pkg -> pkg == appPackageName }
                } ?: continue

                // 创建单个按钮的悬浮窗
                val buttonView = createMultiAppButtonView(appPackageName, appName, reminderType)
                val layoutParams = createMultiAppButtonLayoutParams(
                    buttonConfigPositions[i],
                    buttonConfigSizes[i],
                    buttonConfigMarginXs[i],
                    buttonConfigMarginYs[i]
                )

                windowManager?.addView(buttonView, layoutParams)
                overlayViews.add(buttonView)
            }

            // 如果只有一个应用，显示单个图标；多个应用时显示多个图标
            if (overlayViews.isNotEmpty()) {
                isShowing = true
                // 存储所有悬浮窗视图以便后续管理
                multiAppOverlayViews = overlayViews

                // 设置自动消失
                setupMultiAppAutoDismiss(reminderType)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error showing multi app overlay", e)
            stopSelf()
        }
    }

    /**
     * 设置多应用悬浮窗自动消失
     */
    private fun setupMultiAppAutoDismiss(reminderType: String) {
        try {
            // 加载配置
            val configAdapter = com.weinuo.quickcommands.storage.SmartReminderConfigAdapter(this)
            val autoDismissEnabled: Boolean
            val autoDismissSeconds: Int

            when (reminderType) {
                "multi_music_app" -> {
                    val config = configAdapter.loadMusicAppReminderConfig("music_app_reminder")
                    autoDismissEnabled = config.autoDismissEnabled
                    autoDismissSeconds = config.autoDismissSeconds
                }
                "multi_address" -> {
                    val config = configAdapter.loadAddressReminderConfig("address_reminder")
                    autoDismissEnabled = config.autoDismissEnabled
                    autoDismissSeconds = config.autoDismissSeconds
                }
                else -> return
            }

            if (autoDismissEnabled && autoDismissSeconds > 0) {
                // 启动自动消失倒计时
                startMultiAppAutoDismissCountdown(autoDismissSeconds)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up multi app auto dismiss", e)
        }
    }

    /**
     * 启动多应用自动消失倒计时
     */
    private fun startMultiAppAutoDismissCountdown(durationSeconds: Int) {
        autoDismissJob?.cancel()

        autoDismissJob = serviceScope.launch {
            delay(durationSeconds * 1000L)
            if (isActive && isShowing) {
                hideAllOverlays()
                stopSelf()
            }
        }
    }

    /**
     * 创建多应用按钮视图
     */
    private fun createMultiAppButtonView(packageName: String, appName: String, reminderType: String): View {
        val view = LayoutInflater.from(this).inflate(R.layout.overlay_smart_reminder, null)

        val circularButton = view.findViewById<View>(R.id.circular_reminder_button)
        val iconView = view.findViewById<ImageView>(R.id.iv_reminder_icon)

        // 设置应用图标
        setAppIcon(iconView, packageName, appName)

        // 设置点击事件
        circularButton?.setOnClickListener {
            openSpecificApp(packageName, appName)
            hideAllOverlays()
            stopSelf()
        }

        return view
    }

    /**
     * 创建多应用按钮布局参数
     */
    private fun createMultiAppButtonLayoutParams(
        position: String,
        size: Int,
        marginX: Int,
        marginY: Int
    ): WindowManager.LayoutParams {
        // 将dp转换为px
        val density = resources.displayMetrics.density
        val sizeInPx = (size * density).toInt()
        val marginXInPx = (marginX * density).toInt()
        val marginYInPx = (marginY * density).toInt()

        val params = WindowManager.LayoutParams(
            sizeInPx,
            sizeInPx,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
            android.graphics.PixelFormat.TRANSLUCENT
        )

        // 根据位置设置重力和边距
        when (position) {
            "top_left" -> {
                params.gravity = android.view.Gravity.TOP or android.view.Gravity.START
                params.x = marginXInPx
                params.y = marginYInPx
            }
            "top_right" -> {
                params.gravity = android.view.Gravity.TOP or android.view.Gravity.END
                params.x = marginXInPx
                params.y = marginYInPx
            }
            "bottom_left" -> {
                params.gravity = android.view.Gravity.BOTTOM or android.view.Gravity.START
                params.x = marginXInPx
                params.y = marginYInPx
            }
            "bottom_right" -> {
                params.gravity = android.view.Gravity.BOTTOM or android.view.Gravity.END
                params.x = marginXInPx
                params.y = marginYInPx
            }
            else -> {
                params.gravity = android.view.Gravity.BOTTOM or android.view.Gravity.END
                params.x = marginXInPx
                params.y = marginYInPx
            }
        }

        return params
    }

    /**
     * 设置应用图标
     */
    private fun setAppIcon(iconView: ImageView?, packageName: String, appName: String) {
        if (iconView == null) return

        try {
            Log.d(TAG, "Setting app icon for: $appName ($packageName)")

            // 获取应用图标
            val packageManager = packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            val appIcon = applicationInfo.loadIcon(packageManager)

            // 获取按钮容器，修改其背景和图标显示
            val buttonContainer = iconView.parent as? FrameLayout
            if (buttonContainer != null) {
                // 移除蓝色背景，设置为透明
                buttonContainer.background = null

                // 让ImageView填满整个按钮区域
                val layoutParams = iconView.layoutParams as FrameLayout.LayoutParams
                layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.gravity = android.view.Gravity.CENTER
                iconView.layoutParams = layoutParams

                // 设置图标，使用CENTER_CROP确保图标填满整个区域，适配不同形状
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP

                // 移除白色tint，显示原始图标颜色
                iconView.imageTintList = null

                // 添加圆形裁剪，确保图标显示为圆形
                iconView.clipToOutline = true
                iconView.outlineProvider = object : android.view.ViewOutlineProvider() {
                    override fun getOutline(view: android.view.View, outline: android.graphics.Outline) {
                        outline.setOval(0, 0, view.width, view.height)
                    }
                }
            } else {
                // 如果无法获取容器，至少设置图标
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP
                iconView.imageTintList = null
            }

            iconView.contentDescription = "打开 $appName"
            Log.d(TAG, "Set app icon for: $appName")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting app icon", e)
            // 设置默认图标
            iconView.setImageResource(R.drawable.ic_apps_24)
            iconView.contentDescription = "打开 $appName"
        }
    }

    /**
     * 打开指定应用
     */
    private fun openSpecificApp(packageName: String, appName: String) {
        try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
                Log.d(TAG, "Opened app: $appName ($packageName)")
            } else {
                Log.w(TAG, "No launch intent found for: $packageName")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error opening app: $packageName", e)
        }
    }

    /**
     * 根据配置创建悬浮窗布局参数
     */
    private fun createLayoutParams(reminderType: String): WindowManager.LayoutParams {
        // 加载配置
        val configAdapter = SmartReminderConfigAdapter(this)
        val config = when (reminderType) {
            "screen_rotation" -> {
                val rotationConfig = configAdapter.loadScreenRotationConfig("screen_rotation_reminder")
                ButtonConfig(
                    buttonPosition = rotationConfig.buttonPosition,
                    buttonMarginX = rotationConfig.buttonMarginX,
                    buttonMarginY = rotationConfig.buttonMarginY
                )
            }
            "flashlight" -> {
                val flashlightConfig = configAdapter.loadFlashlightReminderConfig("flashlight_reminder")
                ButtonConfig(
                    buttonPosition = flashlightConfig.buttonPosition,
                    buttonMarginX = flashlightConfig.buttonMarginX,
                    buttonMarginY = flashlightConfig.buttonMarginY
                )
            }
            "new_app" -> {
                val newAppConfig = configAdapter.loadNewAppReminderConfig("new_app_reminder")
                ButtonConfig(
                    buttonPosition = newAppConfig.buttonPosition,
                    buttonMarginX = newAppConfig.buttonMarginX,
                    buttonMarginY = newAppConfig.buttonMarginY
                )
            }
            "music_app" -> {
                val musicAppConfig = configAdapter.loadMusicAppReminderConfig("music_app_reminder")
                val firstButtonConfig = musicAppConfig.buttonConfigs.firstOrNull()
                ButtonConfig(
                    buttonPosition = firstButtonConfig?.buttonPosition ?: "bottom_right",
                    buttonMarginX = firstButtonConfig?.buttonMarginX ?: 24,
                    buttonMarginY = firstButtonConfig?.buttonMarginY ?: 24
                )
            }
            "address" -> {
                val addressConfig = configAdapter.loadAddressReminderConfig("address_reminder")
                val firstButtonConfig = addressConfig.buttonConfigs.firstOrNull()
                ButtonConfig(
                    buttonPosition = firstButtonConfig?.buttonPosition ?: "bottom_right",
                    buttonMarginX = firstButtonConfig?.buttonMarginX ?: 24,
                    buttonMarginY = firstButtonConfig?.buttonMarginY ?: 24
                )
            }
            "shopping_app" -> {
                val shoppingAppConfig = configAdapter.loadShoppingAppReminderConfig("shopping_app_reminder")
                ButtonConfig(
                    buttonPosition = shoppingAppConfig.buttonPosition,
                    buttonMarginX = shoppingAppConfig.buttonMarginX,
                    buttonMarginY = shoppingAppConfig.buttonMarginY
                )
            }
            "app_link" -> {
                val appLinkConfig = configAdapter.loadAppLinkReminderConfig("app_link_reminder")
                ButtonConfig(
                    buttonPosition = appLinkConfig.buttonPosition,
                    buttonMarginX = appLinkConfig.buttonMarginX,
                    buttonMarginY = appLinkConfig.buttonMarginY
                )
            }
            "share_url" -> {
                val shareUrlConfig = configAdapter.loadShareUrlReminderConfig("share_url_reminder")
                ButtonConfig(
                    buttonPosition = shareUrlConfig.buttonPosition,
                    buttonMarginX = shareUrlConfig.buttonMarginX,
                    buttonMarginY = shareUrlConfig.buttonMarginY
                )
            }
            else -> ButtonConfig() // 默认配置
        }

        // 转换dp到像素
        val density = resources.displayMetrics.density
        val marginXPx = (config.buttonMarginX * density).toInt()
        val marginYPx = (config.buttonMarginY * density).toInt()

        // 根据位置设置gravity
        val gravity = when (config.buttonPosition) {
            "bottom_left" -> Gravity.BOTTOM or Gravity.START
            "bottom_right" -> Gravity.BOTTOM or Gravity.END
            "top_left" -> Gravity.TOP or Gravity.START
            "top_right" -> Gravity.TOP or Gravity.END
            else -> Gravity.BOTTOM or Gravity.START
        }

        return WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.WRAP_CONTENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
            format = PixelFormat.TRANSLUCENT
            this.gravity = gravity
            x = marginXPx
            y = marginYPx
        }
    }

    /**
     * 创建悬浮窗视图 - 圆形浮动按钮
     */
    private fun createOverlayView(title: String, message: String, reminderType: String): View {
        val inflater = LayoutInflater.from(this)
        val view = inflater.inflate(R.layout.overlay_smart_reminder, null)

        // 加载配置并设置按钮尺寸
        val configAdapter = SmartReminderConfigAdapter(this)
        val buttonSize = when (reminderType) {
            "screen_rotation" -> {
                val rotationConfig = configAdapter.loadScreenRotationConfig("screen_rotation_reminder")
                rotationConfig.buttonSize
            }
            "flashlight" -> {
                val flashlightConfig = configAdapter.loadFlashlightReminderConfig("flashlight_reminder")
                flashlightConfig.buttonSize
            }
            "new_app" -> {
                val newAppConfig = configAdapter.loadNewAppReminderConfig("new_app_reminder")
                newAppConfig.buttonSize
            }
            "music_app" -> {
                val musicAppConfig = configAdapter.loadMusicAppReminderConfig("music_app_reminder")
                musicAppConfig.buttonConfigs.firstOrNull()?.buttonSize ?: 56
            }
            "shopping_app" -> {
                val shoppingAppConfig = configAdapter.loadShoppingAppReminderConfig("shopping_app_reminder")
                shoppingAppConfig.buttonSize
            }
            "app_link" -> {
                val appLinkConfig = configAdapter.loadAppLinkReminderConfig("app_link_reminder")
                appLinkConfig.buttonSize
            }
            "share_url" -> {
                val shareUrlConfig = configAdapter.loadShareUrlReminderConfig("share_url_reminder")
                shareUrlConfig.buttonSize
            }
            "address" -> {
                val addressConfig = configAdapter.loadAddressReminderConfig("address_reminder")
                addressConfig.buttonConfigs.firstOrNull()?.buttonSize ?: 56
            }
            else -> 56 // 默认尺寸
        }

        // 设置圆形按钮尺寸
        val circularButton = view.findViewById<FrameLayout>(R.id.circular_reminder_button)
        val density = resources.displayMetrics.density
        val buttonSizePx = (buttonSize * density).toInt()

        circularButton?.layoutParams = circularButton?.layoutParams?.apply {
            width = buttonSizePx
            height = buttonSizePx
        }

        // 设置圆形按钮点击事件
        circularButton?.setOnClickListener {
            when (reminderType) {
                "screen_rotation" -> {
                    // 直接开启屏幕自动旋转功能
                    enableAutoRotation()
                    hideOverlay()
                    stopSelf()
                }
                "new_app" -> {
                    // 打开新安装的应用
                    openNewApp()
                    hideOverlay()
                    stopSelf()
                }
                "music_app" -> {
                    // 打开音乐应用
                    openMusicApp()
                    hideOverlay()
                    stopSelf()
                }
                "shopping_app" -> {
                    // 打开购物应用
                    openShoppingApp()
                    hideOverlay()
                    stopSelf()
                }
                "app_link" -> {
                    // 打开应用链接对应的应用
                    openAppLinkApp()
                    hideOverlay()
                    stopSelf()
                }
                "share_url" -> {
                    // 分享网址
                    shareUrl()
                    hideOverlay()
                    stopSelf()
                }
                "address" -> {
                    // 打开地图应用
                    openMapApp()
                    hideOverlay()
                    stopSelf()
                }
                else -> {
                    hideOverlay()
                    stopSelf()
                }
            }
        }

        // 设置图标
        val iconView = view.findViewById<ImageView>(R.id.iv_reminder_icon)
        when (reminderType) {
            "screen_rotation" -> {
                iconView?.setImageResource(R.drawable.ic_screen_rotation_24)
                iconView?.contentDescription = "屏幕旋转提醒"
            }
            "flashlight" -> {
                iconView?.setImageResource(R.drawable.ic_lightbulb_24)
                iconView?.contentDescription = "手电筒提醒"
            }
            "new_app" -> {
                // 设置新应用图标
                setNewAppIcon(iconView)
            }
            "music_app" -> {
                // 设置音乐应用图标
                setMusicAppIcon(iconView)
            }
            "shopping_app" -> {
                // 设置购物应用图标
                setShoppingAppIcon(iconView)
            }
            "app_link" -> {
                // 设置应用链接图标
                setAppLinkIcon(iconView)
            }
            "share_url" -> {
                // 设置分享网址图标
                setShareUrlIcon(iconView)
            }
            "address" -> {
                // 设置地图应用图标
                setMapAppIcon(iconView)
            }
        }

        // 设置进度条尺寸（比按钮稍大）
        val progressBar = view.findViewById<ProgressBar>(R.id.progress_auto_dismiss)
        val progressSizePx = buttonSizePx + (4 * density).toInt() // 比按钮大4dp
        progressBar?.layoutParams = progressBar?.layoutParams?.apply {
            width = progressSizePx
            height = progressSizePx
        }

        // 检查是否需要自动消失
        setupAutoDismiss(view, reminderType)

        return view
    }

    /**
     * 设置自动消失功能
     */
    private fun setupAutoDismiss(view: View, reminderType: String) {
        try {
            val configAdapter = SmartReminderConfigAdapter(this)

            // 根据提醒类型读取相应配置
            val (autoDismissEnabled, autoDismissSeconds) = when (reminderType) {
                "screen_rotation" -> {
                    val rotationConfig = configAdapter.loadScreenRotationConfig("screen_rotation_reminder")
                    rotationConfig.autoDismissEnabled to rotationConfig.autoDismissSeconds
                }
                "flashlight" -> {
                    val flashlightConfig = configAdapter.loadFlashlightReminderConfig("flashlight_reminder")
                    flashlightConfig.autoDismissEnabled to flashlightConfig.autoDismissSeconds
                }
                "new_app" -> {
                    val newAppConfig = configAdapter.loadNewAppReminderConfig("new_app_reminder")
                    newAppConfig.autoDismissEnabled to newAppConfig.autoDismissSeconds
                }
                "music_app" -> {
                    val musicAppConfig = configAdapter.loadMusicAppReminderConfig("music_app_reminder")
                    musicAppConfig.autoDismissEnabled to musicAppConfig.autoDismissSeconds
                }
                else -> false to 5 // 默认配置
            }

            if (autoDismissEnabled && autoDismissSeconds > 0) {
                val progressIndicator = view.findViewById<ProgressBar>(R.id.progress_auto_dismiss)
                progressIndicator?.visibility = View.VISIBLE

                // 启动自动消失倒计时
                startAutoDismissCountdown(progressIndicator, autoDismissSeconds)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up auto dismiss", e)
        }
    }

    /**
     * 启动自动消失倒计时 - 圆形进度条
     */
    private fun startAutoDismissCountdown(progressIndicator: ProgressBar?, durationSeconds: Int) {
        autoDismissJob?.cancel()

        if (progressIndicator == null) return

        autoDismissJob = serviceScope.launch {
            val totalMillis = durationSeconds * 1000L
            val updateInterval = 50L // 每50ms更新一次进度
            val totalSteps = totalMillis / updateInterval

            progressIndicator.max = 10000 // 使用10000作为最大值，提供更平滑的进度
            progressIndicator.progress = 0

            for (step in 0..totalSteps) {
                if (!isActive) break

                // 计算当前进度百分比
                val progressPercent = (step.toFloat() / totalSteps.toFloat() * 10000).toInt()
                progressIndicator.progress = progressPercent

                if (step == totalSteps) {
                    // 倒计时结束，自动关闭
                    hideAllOverlays()
                    stopSelf()
                    break
                }

                delay(updateInterval)
            }
        }
    }

    /**
     * 隐藏悬浮窗
     */
    private fun hideOverlay() {
        try {
            autoDismissJob?.cancel()

            if (isShowing && overlayView != null) {
                windowManager?.removeView(overlayView)
                isShowing = false
                overlayView = null
                Log.d(TAG, "Smart reminder overlay hidden")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error hiding overlay", e)
        }
    }

    /**
     * 隐藏所有悬浮窗（包括多应用悬浮窗）
     */
    private fun hideAllOverlays() {
        try {
            autoDismissJob?.cancel()

            // 隐藏单个悬浮窗
            if (isShowing && overlayView != null) {
                windowManager?.removeView(overlayView)
                overlayView = null
            }

            // 隐藏多应用悬浮窗
            multiAppOverlayViews.forEach { view ->
                try {
                    windowManager?.removeView(view)
                } catch (e: Exception) {
                    Log.w(TAG, "Error removing multi app overlay view", e)
                }
            }
            multiAppOverlayViews = emptyList()

            isShowing = false
            Log.d(TAG, "All smart reminder overlays hidden")

        } catch (e: Exception) {
            Log.e(TAG, "Error hiding all overlays", e)
        }
    }

    /**
     * 直接开启屏幕自动旋转功能
     */
    private fun enableAutoRotation() {
        serviceScope.launch {
            try {
                // 使用Shizuku直接开启屏幕自动旋转
                val command = "settings put system accelerometer_rotation 1"
                val result = ShizukuManager.executeCommand(command)

                if (result.contains("Error:") || result.contains("错误")) {
                    Log.e(TAG, "Failed to enable auto rotation via Shizuku: $result")
                } else {
                    Log.d(TAG, "Auto rotation enabled successfully via Shizuku")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error enabling auto rotation", e)
            }
        }
    }

    /**
     * 设置新应用图标
     */
    private fun setNewAppIcon(iconView: ImageView?) {
        if (iconView == null) return

        try {
            val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return
            val appName = currentIntent?.getStringExtra("extra_app_name") ?: return

            // 获取应用图标
            val packageManager = packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            val appIcon = applicationInfo.loadIcon(packageManager)

            // 获取按钮容器，修改其背景和图标显示
            val buttonContainer = iconView.parent as? FrameLayout
            if (buttonContainer != null) {
                // 移除蓝色背景，设置为透明
                buttonContainer.background = null

                // 让ImageView填满整个按钮区域
                val layoutParams = iconView.layoutParams as FrameLayout.LayoutParams
                layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.gravity = android.view.Gravity.CENTER
                iconView.layoutParams = layoutParams

                // 设置图标，使用CENTER_CROP确保图标填满整个区域，适配不同形状
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP

                // 移除白色tint，显示原始图标颜色
                iconView.imageTintList = null

                // 添加圆形裁剪，确保图标显示为圆形
                iconView.clipToOutline = true
                iconView.outlineProvider = object : android.view.ViewOutlineProvider() {
                    override fun getOutline(view: android.view.View, outline: android.graphics.Outline) {
                        outline.setOval(0, 0, view.width, view.height)
                    }
                }
            } else {
                // 如果无法获取容器，至少设置图标
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP
                iconView.imageTintList = null
            }

            iconView.contentDescription = "打开 $appName"
            Log.d(TAG, "Set app icon for: $appName")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting new app icon", e)
            // 设置默认图标
            iconView.setImageResource(R.drawable.ic_apps_24)
            iconView.contentDescription = "打开新应用"
        }
    }

    /**
     * 设置音乐应用图标
     */
    private fun setMusicAppIcon(iconView: ImageView?) {
        if (iconView == null) return

        try {
            val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return
            val appName = currentIntent?.getStringExtra("extra_app_name") ?: return

            Log.d(TAG, "Setting music app icon for: $appName ($packageName)")

            // 获取应用图标
            val packageManager = packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            val appIcon = applicationInfo.loadIcon(packageManager)

            // 获取按钮容器，修改其背景和图标显示
            val buttonContainer = iconView.parent as? FrameLayout
            if (buttonContainer != null) {
                // 移除蓝色背景，设置为透明
                buttonContainer.background = null

                // 让ImageView填满整个按钮区域
                val layoutParams = iconView.layoutParams as FrameLayout.LayoutParams
                layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.gravity = android.view.Gravity.CENTER
                iconView.layoutParams = layoutParams

                // 设置图标，使用CENTER_CROP确保图标填满整个区域，适配不同形状
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP

                // 移除白色tint，显示原始图标颜色
                iconView.imageTintList = null

                // 添加圆形裁剪，确保图标显示为圆形
                iconView.clipToOutline = true
                iconView.outlineProvider = object : android.view.ViewOutlineProvider() {
                    override fun getOutline(view: android.view.View, outline: android.graphics.Outline) {
                        outline.setOval(0, 0, view.width, view.height)
                    }
                }
            } else {
                // 如果无法获取容器，至少设置图标
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP
                iconView.imageTintList = null
            }

            iconView.contentDescription = "打开 $appName"
            Log.d(TAG, "Set music app icon for: $appName")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting music app icon", e)
            // 设置默认音乐图标
            iconView.setImageResource(R.drawable.ic_music_note_24)
            iconView.contentDescription = "打开音乐应用"
        }
    }

    /**
     * 打开新安装的应用
     */
    private fun openNewApp() {
        serviceScope.launch {
            try {
                val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return@launch
                val appName = currentIntent?.getStringExtra("extra_app_name") ?: return@launch

                Log.d(TAG, "Attempting to open app: $appName ($packageName)")

                // 获取应用的启动Intent
                val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(launchIntent)
                    Log.d(TAG, "Successfully opened app: $appName")
                } else {
                    Log.w(TAG, "No launch intent found for app: $packageName")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error opening new app", e)
            }
        }
    }

    /**
     * 打开音乐应用
     */
    private fun openMusicApp() {
        serviceScope.launch {
            try {
                val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return@launch
                val appName = currentIntent?.getStringExtra("extra_app_name") ?: return@launch

                Log.d(TAG, "Attempting to open music app: $appName ($packageName)")

                // 获取应用的启动Intent
                val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(launchIntent)
                    Log.d(TAG, "Successfully opened music app: $appName")
                } else {
                    Log.w(TAG, "No launch intent found for music app: $packageName")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error opening music app", e)
            }
        }
    }

    /**
     * 设置购物应用图标
     */
    private fun setShoppingAppIcon(iconView: ImageView?) {
        if (iconView == null) return

        try {
            val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return
            val appName = currentIntent?.getStringExtra("extra_app_name") ?: return

            Log.d(TAG, "Setting shopping app icon for: $appName ($packageName)")

            // 获取应用图标
            val appIcon = packageManager.getApplicationIcon(packageName)

            // 获取按钮容器，修改其背景和图标显示
            val buttonContainer = iconView.parent as? FrameLayout
            if (buttonContainer != null) {
                // 移除蓝色背景，设置为透明
                buttonContainer.background = null

                // 让ImageView填满整个按钮区域
                val layoutParams = iconView.layoutParams as FrameLayout.LayoutParams
                layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.gravity = android.view.Gravity.CENTER
                iconView.layoutParams = layoutParams

                // 设置图标，使用CENTER_CROP确保图标填满整个区域，适配不同形状
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP

                // 移除白色tint，显示原始图标颜色
                iconView.imageTintList = null

                // 添加圆形裁剪，确保图标显示为圆形
                iconView.clipToOutline = true
                iconView.outlineProvider = object : android.view.ViewOutlineProvider() {
                    override fun getOutline(view: android.view.View, outline: android.graphics.Outline) {
                        outline.setOval(0, 0, view.width, view.height)
                    }
                }
            } else {
                // 如果无法获取容器，至少设置图标
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP
                iconView.imageTintList = null
            }

            iconView.contentDescription = "打开 $appName"
            Log.d(TAG, "Set shopping app icon for: $appName")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting shopping app icon", e)
            // 设置默认购物图标
            iconView.setImageResource(R.drawable.ic_shopping_cart_24)
            iconView.contentDescription = "打开购物应用"
        }
    }

    /**
     * 打开购物应用
     */
    private fun openShoppingApp() {
        serviceScope.launch {
            try {
                val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return@launch
                val appName = currentIntent?.getStringExtra("extra_app_name") ?: return@launch

                Log.d(TAG, "Attempting to open shopping app: $appName ($packageName)")

                // 获取应用的启动Intent
                val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(launchIntent)
                    Log.d(TAG, "Successfully opened shopping app: $appName")
                } else {
                    Log.w(TAG, "No launch intent found for shopping app: $packageName")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error opening shopping app", e)
            }
        }
    }



    /**
     * 设置应用链接图标
     */
    private fun setAppLinkIcon(iconView: ImageView?) {
        if (iconView == null) return

        try {
            val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return
            val appName = currentIntent?.getStringExtra("extra_app_name") ?: return

            Log.d(TAG, "Setting app link icon for: $appName ($packageName)")

            // 获取应用图标
            val appIcon = packageManager.getApplicationIcon(packageName)

            // 获取按钮容器，修改其背景和图标显示
            val buttonContainer = iconView.parent as? FrameLayout
            if (buttonContainer != null) {
                // 移除蓝色背景，设置为透明
                buttonContainer.background = null

                // 让ImageView填满整个按钮区域
                val layoutParams = iconView.layoutParams as FrameLayout.LayoutParams
                layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.gravity = android.view.Gravity.CENTER
                iconView.layoutParams = layoutParams

                // 设置图标，使用CENTER_CROP确保图标填满整个区域，适配不同形状
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP

                // 移除白色tint，显示原始图标颜色
                iconView.imageTintList = null

                // 添加圆形裁剪，确保图标显示为圆形
                iconView.clipToOutline = true
                iconView.outlineProvider = object : android.view.ViewOutlineProvider() {
                    override fun getOutline(view: android.view.View, outline: android.graphics.Outline) {
                        outline.setOval(0, 0, view.width, view.height)
                    }
                }
            } else {
                // 如果无法获取容器，至少设置图标
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP
                iconView.imageTintList = null
            }

            iconView.contentDescription = "打开 $appName"
            Log.d(TAG, "Set app link icon for: $appName")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting app link icon", e)
            // 设置默认应用图标
            iconView.setImageResource(R.drawable.ic_apps_24)
            iconView.contentDescription = "打开应用"
        }
    }

    /**
     * 打开应用链接对应的应用
     */
    private fun openAppLinkApp() {
        serviceScope.launch {
            try {
                val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return@launch
                val appName = currentIntent?.getStringExtra("extra_app_name") ?: return@launch

                Log.d(TAG, "Attempting to open app link app: $appName ($packageName)")

                // 获取应用的启动Intent
                val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(launchIntent)
                    Log.d(TAG, "Successfully opened app link app: $appName")
                } else {
                    Log.w(TAG, "No launch intent found for app link app: $packageName")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error opening app link app", e)
            }
        }
    }

    /**
     * 设置分享网址图标
     */
    private fun setShareUrlIcon(iconView: ImageView?) {
        if (iconView == null) return

        try {
            Log.d(TAG, "Setting share URL icon")

            // 设置分享图标
            iconView.setImageResource(R.drawable.ic_share_24)
            iconView.scaleType = ImageView.ScaleType.CENTER_INSIDE
            iconView.imageTintList = null
            iconView.contentDescription = "分享网址"

            Log.d(TAG, "Set share URL icon successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting share URL icon", e)
            // 设置默认分享图标
            iconView.setImageResource(R.drawable.ic_share_24)
            iconView.contentDescription = "分享网址"
        }
    }

    /**
     * 分享网址
     */
    private fun shareUrl() {
        serviceScope.launch {
            try {
                val url = currentIntent?.getStringExtra("extra_url") ?: return@launch
                val displayText = currentIntent?.getStringExtra("extra_display_text") ?: url

                Log.d(TAG, "Attempting to share URL: $displayText")

                // 创建分享Intent
                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    type = "text/plain"
                    putExtra(Intent.EXTRA_TEXT, url)
                    putExtra(Intent.EXTRA_SUBJECT, "分享网址")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }

                // 显示应用选择器
                val chooserIntent = Intent.createChooser(shareIntent, "分享网址到").apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }

                startActivity(chooserIntent)
                Log.d(TAG, "Successfully opened share chooser for URL: $displayText")

            } catch (e: Exception) {
                Log.e(TAG, "Error sharing URL", e)
            }
        }
    }

    /**
     * 设置地图应用图标
     */
    private fun setMapAppIcon(iconView: ImageView?) {
        if (iconView == null) return

        try {
            val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return
            val appName = currentIntent?.getStringExtra("extra_app_name") ?: return

            Log.d(TAG, "Setting map app icon for: $appName ($packageName)")

            // 获取应用图标
            val appIcon = try {
                packageManager.getApplicationIcon(packageName)
            } catch (e: Exception) {
                Log.w(TAG, "Failed to get app icon for: $packageName", e)
                // 使用默认地图图标
                iconView.setImageResource(R.drawable.ic_map_24)
                iconView.contentDescription = "打开地图应用"
                return
            }

            // 获取按钮容器，修改其背景和图标显示
            val buttonContainer = iconView.parent as? FrameLayout
            if (buttonContainer != null) {
                // 移除蓝色背景，设置为透明
                buttonContainer.background = null

                // 让ImageView填满整个按钮区域
                val layoutParams = iconView.layoutParams as FrameLayout.LayoutParams
                layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT
                layoutParams.gravity = android.view.Gravity.CENTER
                iconView.layoutParams = layoutParams

                // 设置图标，使用CENTER_CROP确保图标填满整个区域，适配不同形状
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP

                // 移除白色tint，显示原始图标颜色
                iconView.imageTintList = null

                // 添加圆形裁剪，确保图标显示为圆形
                iconView.clipToOutline = true
                iconView.outlineProvider = object : android.view.ViewOutlineProvider() {
                    override fun getOutline(view: android.view.View, outline: android.graphics.Outline) {
                        outline.setOval(0, 0, view.width, view.height)
                    }
                }
            } else {
                // 如果无法获取容器，至少设置图标
                iconView.setImageDrawable(appIcon)
                iconView.scaleType = ImageView.ScaleType.CENTER_CROP
                iconView.imageTintList = null
            }

            iconView.contentDescription = "打开 $appName"
            Log.d(TAG, "Set map app icon for: $appName")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting map app icon", e)
            // 设置默认地图图标
            iconView.setImageResource(R.drawable.ic_map_24)
            iconView.contentDescription = "打开地图应用"
        }
    }

    /**
     * 打开地图应用
     */
    private fun openMapApp() {
        serviceScope.launch {
            try {
                val packageName = currentIntent?.getStringExtra("extra_app_package_name") ?: return@launch
                val appName = currentIntent?.getStringExtra("extra_app_name") ?: return@launch

                Log.d(TAG, "Attempting to open map app: $appName ($packageName)")

                // 获取应用的启动Intent
                val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(launchIntent)
                    Log.d(TAG, "Successfully opened map app: $appName")
                } else {
                    Log.w(TAG, "No launch intent found for map app: $packageName")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error opening map app", e)
            }
        }
    }

    /**
     * 按钮配置数据类
     */
    private data class ButtonConfig(
        val buttonPosition: String = "bottom_left",
        val buttonMarginX: Int = 24,
        val buttonMarginY: Int = 24
    )
}

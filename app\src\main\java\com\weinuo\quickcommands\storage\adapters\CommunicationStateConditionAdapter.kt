package com.weinuo.quickcommands.storage.adapters

import android.util.Log
import com.weinuo.quickcommands.model.*
import com.weinuo.quickcommands.storage.NativeTypeStorageManager
import com.weinuo.quickcommands.storage.AppListStorageEngine
import com.weinuo.quickcommands.storage.StorageDomain
import com.weinuo.quickcommands.storage.StorageOperation
import com.weinuo.quickcommands.storage.StorageType

/**
 * 通信状态条件存储适配器
 *
 * 负责CommunicationStateCondition的原生数据类型存储和重建。
 * 将复杂的通信状态条件对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、stateType
 * - 筛选参数：filterType、filterMode
 * - 联系人参数：selectedContactIds、selectedContactNames、selectedGroupId、selectedGroupName
 * - 特定号码：specificNumber
 *
 * 存储格式示例：
 * condition_{id}_type = "communication_state"
 * condition_{id}_state_type = "SMS_RECEIVED"
 * condition_{id}_filter_type = "SPECIFIC_CONTACT"
 * condition_{id}_filter_mode = "INCLUDE"
 * condition_{id}_selected_contact_ids_count = 2
 * condition_{id}_selected_contact_ids_0 = "contact_001"
 * condition_{id}_selected_contact_ids_1 = "contact_002"
 * condition_{id}_selected_contact_names_count = 2
 * condition_{id}_selected_contact_names_0 = "张三"
 * condition_{id}_selected_contact_names_1 = "李四"
 * condition_{id}_selected_group_id = "group_001"
 * condition_{id}_selected_group_name = "工作联系人"
 * condition_{id}_specific_number = "13800138000"
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class CommunicationStateConditionAdapter(
    storageManager: NativeTypeStorageManager,
    appListEngine: AppListStorageEngine
) : BaseConditionAdapter<CommunicationStateCondition>(storageManager, appListEngine) {

    companion object {
        private const val TAG = "CommunicationStateConditionAdapter"

        // 字段名常量
        private const val FIELD_STATE_TYPE = "state_type"
        private const val FIELD_FILTER_TYPE = "filter_type"
        private const val FIELD_FILTER_MODE = "filter_mode"
        private const val FIELD_SELECTED_CONTACT_IDS = "selected_contact_ids"
        private const val FIELD_SELECTED_CONTACT_NAMES = "selected_contact_names"
        private const val FIELD_SELECTED_GROUP_ID = "selected_group_id"
        private const val FIELD_SELECTED_GROUP_NAME = "selected_group_name"
        private const val FIELD_SPECIFIC_NUMBER = "specific_number"
    }

    override fun getConditionType() = "communication_state"

    override fun save(condition: CommunicationStateCondition): Boolean {
        Log.d(TAG, "开始保存通信状态条件: ${condition.id}")

        return try {
            val prefix = getPrefix(condition.id)
            val operations = mutableListOf<StorageOperation>()

            // 基础字段
            operations.addAll(saveBaseFields(condition.id, condition))

            // 通信状态条件特有字段
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_STATE_TYPE}", condition.stateType.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_FILTER_TYPE}", condition.filterType.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_FILTER_MODE}", condition.filterMode.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SELECTED_GROUP_ID}", condition.selectedGroupId, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SELECTED_GROUP_NAME}", condition.selectedGroupName, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SPECIFIC_NUMBER}", condition.specificNumber, StorageType.STRING)
            ))

            // 保存联系人ID列表
            operations.addAll(saveStringListHelper("${prefix}${FIELD_SELECTED_CONTACT_IDS}", condition.selectedContactIds))

            // 保存联系人名称列表
            operations.addAll(saveStringListHelper("${prefix}${FIELD_SELECTED_CONTACT_NAMES}", condition.selectedContactNames))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "通信状态条件保存成功: ${condition.id}")
            } else {
                Log.e(TAG, "通信状态条件保存失败: ${condition.id}")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "保存通信状态条件时发生异常: ${condition.id}", e)
            false
        }
    }

    override fun load(conditionId: String): CommunicationStateCondition? {
        Log.d(TAG, "开始加载通信状态条件: $conditionId")

        return try {
            val prefix = getPrefix(conditionId)

            // 检查条件是否存在
            val conditionType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}type")
            if (conditionType != getConditionType()) {
                Log.w(TAG, "条件类型不匹配或条件不存在: $conditionId, 期望: ${getConditionType()}, 实际: $conditionType")
                return null
            }

            // 加载枚举字段
            val stateType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_STATE_TYPE}").let {
                try {
                    CommunicationStateType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的通信状态类型: $it, 使用默认值")
                    CommunicationStateType.SMS_RECEIVED
                }
            }

            val filterType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_FILTER_TYPE}").let {
                try {
                    ContactFilterType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的联系人筛选类型: $it, 使用默认值")
                    ContactFilterType.ANY_CONTACT
                }
            }

            val filterMode = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_FILTER_MODE}").let {
                try {
                    ContactFilterMode.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的筛选模式: $it, 使用默认值")
                    ContactFilterMode.INCLUDE
                }
            }

            // 加载字符串字段
            val selectedGroupId = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SELECTED_GROUP_ID}")
            val selectedGroupName = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SELECTED_GROUP_NAME}")
            val specificNumber = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SPECIFIC_NUMBER}")

            // 加载列表字段
            val selectedContactIds = loadStringListHelper("${prefix}${FIELD_SELECTED_CONTACT_IDS}")
            val selectedContactNames = loadStringListHelper("${prefix}${FIELD_SELECTED_CONTACT_NAMES}")

            // 重建CommunicationStateCondition对象
            val condition = CommunicationStateCondition(
                id = conditionId,
                stateType = stateType,
                filterType = filterType,
                filterMode = filterMode,
                selectedContactIds = selectedContactIds,
                selectedContactNames = selectedContactNames,
                selectedGroupId = selectedGroupId,
                selectedGroupName = selectedGroupName,
                specificNumber = specificNumber
            )

            Log.d(TAG, "通信状态条件加载成功: $conditionId")
            condition

        } catch (e: Exception) {
            Log.e(TAG, "加载通信状态条件时发生异常: $conditionId", e)
            null
        }
    }

    /**
     * 保存字符串列表
     */
    private fun saveStringListHelper(key: String, list: List<String>): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        // 保存列表大小
        operations.add(StorageOperation(StorageDomain.CONDITIONS, "${key}_count", list.size, StorageType.INT))

        // 保存每个字符串项
        list.forEachIndexed { index, item ->
            operations.add(StorageOperation(StorageDomain.CONDITIONS, "${key}_${index}", item, StorageType.STRING))
        }

        return operations
    }

    /**
     * 加载字符串列表
     */
    private fun loadStringListHelper(key: String): List<String> {
        val count = storageManager.loadInt(StorageDomain.CONDITIONS, "${key}_count")
        val list = mutableListOf<String>()

        for (index in 0 until count) {
            val item = storageManager.loadString(StorageDomain.CONDITIONS, "${key}_${index}")
            if (item.isNotEmpty()) {
                list.add(item)
            }
        }

        return list
    }
}

package com.weinuo.quickcommands.service

import android.app.Service
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import androidx.annotation.RequiresApi

/**
 * 剪贴板刷新叠加层服务
 * 
 * 用于在Android 10+上创建临时不可见叠加层来读取剪贴板内容
 * 这是因为Android 10+限制了后台应用读取剪贴板的能力
 */
@RequiresApi(Build.VERSION_CODES.Q)
class ClipboardRefreshOverlayService : Service() {
    
    companion object {
        private const val TAG = "ClipboardRefreshOverlay"
        private const val OVERLAY_DURATION = 100L // 叠加层显示时间（毫秒）
    }
    
    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private val handler = Handler(Looper.getMainLooper())
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Starting clipboard refresh overlay service")
        
        try {
            createOverlay()
            
            // 延迟移除叠加层并读取剪贴板
            handler.postDelayed({
                readClipboard()
                removeOverlay()
                stopSelf()
            }, OVERLAY_DURATION)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating clipboard refresh overlay", e)
            stopSelf()
        }
        
        return START_NOT_STICKY
    }
    
    /**
     * 创建临时不可见叠加层
     */
    private fun createOverlay() {
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        
        // 创建一个1x1像素的不可见视图
        overlayView = View(this).apply {
            alpha = 0.01f // 几乎完全透明
        }
        
        // 设置叠加层参数
        val layoutParams = WindowManager.LayoutParams(
            1, // 宽度1像素
            1, // 高度1像素
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 0
            y = 0
        }
        
        // 添加叠加层到窗口管理器
        windowManager?.addView(overlayView, layoutParams)
        
        Log.d(TAG, "Clipboard refresh overlay created")
    }
    
    /**
     * 读取剪贴板内容
     */
    private fun readClipboard() {
        try {
            val clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = clipboardManager.primaryClip
            
            if (clipData != null && clipData.itemCount > 0) {
                val clipText = clipData.getItemAt(0).text?.toString() ?: ""
                Log.d(TAG, "Clipboard content refreshed: ${clipText.take(50)}${if (clipText.length > 50) "..." else ""}")
            } else {
                Log.d(TAG, "Clipboard is empty")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error reading clipboard", e)
        }
    }
    
    /**
     * 移除叠加层
     */
    private fun removeOverlay() {
        try {
            overlayView?.let { view ->
                windowManager?.removeView(view)
                overlayView = null
            }
            windowManager = null
            
            Log.d(TAG, "Clipboard refresh overlay removed")
        } catch (e: Exception) {
            Log.e(TAG, "Error removing overlay", e)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        removeOverlay()
        handler.removeCallbacksAndMessages(null)
        Log.d(TAG, "Clipboard refresh overlay service destroyed")
    }
}

package com.weinuo.quickcommands.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme

/**
 * 气泡通知专用Activity
 *
 * 这个Activity专门用于处理气泡通知的展开内容。
 * 根据Android气泡通知的最佳实践，气泡通知需要一个轻量级的Activity
 * 来显示通知的详细内容或提供交互界面。
 */
class BubbleActivity : ComponentActivity() {

    companion object {
        const val EXTRA_TITLE = "bubble_title"
        const val EXTRA_MESSAGE = "bubble_message"
        const val EXTRA_TIMESTAMP = "bubble_timestamp"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取传递的参数
        val title = intent.getStringExtra(EXTRA_TITLE) ?: "通知"
        val message = intent.getStringExtra(EXTRA_MESSAGE) ?: "这是一个气泡通知"
        val timestamp = intent.getLongExtra(EXTRA_TIMESTAMP, System.currentTimeMillis())

        setContent {
            QuickCommandsTheme {
                BubbleContent(
                    title = title,
                    message = message,
                    timestamp = timestamp,
                    onClose = { finish() }
                )
            }
        }
    }
}

/**
 * 气泡通知内容组件
 *
 * @param title 通知标题
 * @param message 通知消息
 * @param timestamp 通知时间戳
 * @param onClose 关闭回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BubbleContent(
    title: String,
    message: String,
    timestamp: Long,
    onClose: () -> Unit
) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()

    // 格式化时间戳
    val timeText = remember(timestamp) {
        val date = java.util.Date(timestamp)
        val formatter = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault())
        formatter.format(date)
    }

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 顶部栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "气泡通知",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary
                )

                Text(
                    text = timeText,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Divider()

            // 通知标题
            if (title.isNotBlank()) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            // 通知内容
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(16.dp),
                    textAlign = TextAlign.Start
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 打开主应用按钮
                OutlinedButton(
                    onClick = {
                        // 打开主应用
                        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
                        intent?.let {
                            it.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                            context.startActivity(it)
                        }
                        onClose()
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("打开应用")
                }

                // 关闭按钮
                Button(
                    onClick = onClose,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("关闭")
                }
            }
        }
    }
}

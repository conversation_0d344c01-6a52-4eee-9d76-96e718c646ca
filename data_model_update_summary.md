# ApplicationTask 数据模型更新总结

## 概述
完成了第三阶段的数据模型更新，为ApplicationTask添加了新字段以支持应用列表和后台应用管理功能的整合。

## 新增字段

### 1. 应用重要性筛选配置
```kotlin
val enableAppImportanceFilter: Boolean = false, // 启用应用重要性筛选
val selectedImportanceLevels: Set<AppImportance> = emptySet() // 选择的重要性级别
```

### 2. ~~音乐应用配置~~ (已删除)
~~用户指定音乐应用功能已删除，因为系统已能自动检测正在播放音乐的应用~~

## 已有字段优化

### 1. 清理了重复字段
- 删除了重复的 `forceStopAppPackage` 字段
- 删除了重复的内存管理和清理策略配置字段
- 统一了字段注释和文档

### 2. 重新组织字段结构
```kotlin
// 跳过条件配置
val skipForegroundApp: Boolean = true,
val skipMusicPlayingApp: Boolean = true,
val skipVpnApp: Boolean = true,
val selectedVpnApps: List<SimpleAppInfo> = emptyList(), // 用户指定的VPN应用列表

// 智能排序和策略
val sortByBackgroundTime: Boolean = false,

// 智能内存管理配置
val enableMemoryThresholdCheck: Boolean = false,
val memoryThreshold: Int = 3,
val memoryThresholdIsPercentage: Boolean = false,
val groupCheckMode: GroupCheckMode = GroupCheckMode.BALANCED,
val customGroupSize: Int = 5,

// 自定义清理策略配置
val enableCustomCleanupStrategy: Boolean = false,
val cleanupStrategyId: String = "",
val useSimpleMode: Boolean = true,
val simpleStrategyType: SimpleStrategyType = SimpleStrategyType.PROTECT_IMPORTANT,

// 应用重要性筛选配置
val enableAppImportanceFilter: Boolean = false,
val selectedImportanceLevels: Set<AppImportance> = emptySet()
```

## SharedTaskRegistry 更新

### 1. createTaskFromJson 方法
- 添加了对新字段的JSON解析支持
- 支持 `enableAppImportanceFilter` 布尔值解析
- 支持 `selectedImportanceLevels` 字符串列表到枚举集合的转换
- ~~支持 `selectedMusicApps` 应用列表解析~~ (已删除)

### 2. createOrUpdateTask 方法
- 同步更新了创建和更新逻辑
- 确保新字段在任务更新时正确处理

## SharedExecutionHandler 更新

### 1. forceStopSelectedAppsWithAdvancedOptions 方法签名更新
```kotlin
private suspend fun forceStopSelectedAppsWithAdvancedOptions(
    // ... 现有参数
    selectedMusicApps: List<SimpleAppInfo> = emptyList(),
    // ... 其他参数
    enableAppImportanceFilter: Boolean = false,
    selectedImportanceLevels: Set<AppImportance> = emptySet()
): Boolean
```

### 2. 音乐应用检测逻辑
- 使用自动检测机制识别正在播放音乐的应用
- 无需用户手动指定音乐应用

### 3. 应用重要性筛选逻辑
- 添加了应用重要性设置加载
- 实现了基于重要性级别的应用筛选
- 支持多重要性级别选择

### 4. executeForceStopApp 方法调用更新
- ~~传递新的 `selectedMusicApps` 参数~~ (已删除)
- 传递新的 `enableAppImportanceFilter` 和 `selectedImportanceLevels` 参数

## getDescription 方法更新

### 1. 新增显示选项
- 显示应用重要性筛选信息
- 显示用户指定的VPN应用数量
- 显示用户指定的音乐应用数量

### 2. 描述格式优化
```kotlin
if (enableAppImportanceFilter && selectedImportanceLevels.isNotEmpty()) {
    val importanceText = selectedImportanceLevels.joinToString(",") { it.displayName }
    skipOptions.add("重要性:$importanceText")
}
if (selectedVpnApps.isNotEmpty()) {
    skipOptions.add("VPN应用:${selectedVpnApps.size}个")
}
// 音乐应用显示已删除，因为使用自动检测
```

## 实现方式

采用了直接重构的方式，所有更改都是破坏性的：
- 删除了重复字段
- 重新组织了字段结构
- 更新了方法签名

## 下一步

数据模型更新已完成，接下来需要：
1. 更新UI组件以支持新字段
2. 实现增强的ForceStopAppConfigContent组件
3. 集成应用列表显示和管理功能
4. 测试所有新功能的正确性

## 技术要点

1. **类型安全**: 使用强类型的枚举和数据类
2. **空安全**: 所有新字段都有合理的默认值
3. **性能优化**: 使用Set而不是List存储重要性级别，提高查找效率
4. **扩展性**: 新字段设计考虑了未来功能扩展的可能性

## 重要更新

### 删除selectedMusicApps字段
根据用户反馈，删除了 `selectedMusicApps` 字段及相关功能，因为：
1. 系统已能自动检测正在播放音乐的应用
2. 无需用户手动指定音乐应用
3. 简化了配置流程，提升用户体验

### 最终数据模型
现在ApplicationTask支持以下功能：
- ✅ 应用重要性筛选配置
- ✅ 用户指定的VPN应用列表
- ✅ 自动音乐应用检测（无需手动配置）
- ✅ 完整的智能清理策略配置
- ✅ 内存阈值和分组清理设置

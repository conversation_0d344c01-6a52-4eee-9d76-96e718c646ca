package com.weinuo.quickcommands.ui.recording

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.weinuo.quickcommands.model.TouchEvent
import com.weinuo.quickcommands.model.GestureRecording
import com.weinuo.quickcommands.storage.GestureRecordingNativeManager
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 录制模式枚举
 */
enum class RecordingMode {
    FULLSCREEN,         // 全屏录制模式（原有模式）
    FLOATING,          // 悬浮窗录制模式（传统）
    FLOATING_ADVANCED  // 悬浮窗录制模式（高级）
}

/**
 * 手势录制界面的ViewModel
 * 管理录制状态、事件收集和数据处理
 * 支持全屏录制和悬浮窗录制两种模式
 *
 * 注意：已改用原生存储方式，不再使用JSON序列化
 */
class GestureRecordingViewModel(context: Context) : ViewModel() {

    // 手势录制原生存储管理器
    private val gestureRecordingManager = GestureRecordingNativeManager(context)

    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

    private val _recordedEvents = MutableStateFlow<List<TouchEvent>>(emptyList())
    val recordedEvents: StateFlow<List<TouchEvent>> = _recordedEvents.asStateFlow()

    private val _recordingDuration = MutableStateFlow(0L)
    val recordingDuration: StateFlow<Long> = _recordingDuration.asStateFlow()

    private val _recordingMode = MutableStateFlow(RecordingMode.FULLSCREEN)
    val recordingMode: StateFlow<RecordingMode> = _recordingMode.asStateFlow()

    private val _isFloatingMode = MutableStateFlow(false)
    val isFloatingMode: StateFlow<Boolean> = _isFloatingMode.asStateFlow()

    private var recordingStartTime = 0L
    private var durationUpdateJob: Job? = null

    /**
     * 设置录制模式
     */
    fun setRecordingMode(mode: RecordingMode) {
        if (_isRecording.value) return // 录制中不允许切换模式

        _recordingMode.value = mode
        _isFloatingMode.value = (mode == RecordingMode.FLOATING || mode == RecordingMode.FLOATING_ADVANCED)
    }

    /**
     * 开始录制
     * @param mode 录制模式，如果不指定则使用当前模式
     */
    fun startRecording(mode: RecordingMode? = null) {
        if (_isRecording.value) return

        // 如果指定了模式，则切换到该模式
        mode?.let { setRecordingMode(it) }

        _isRecording.value = true
        _recordedEvents.value = emptyList()
        recordingStartTime = System.currentTimeMillis()
        _recordingDuration.value = 0L

        // 启动时长更新任务
        durationUpdateJob = viewModelScope.launch {
            while (_isRecording.value) {
                _recordingDuration.value = System.currentTimeMillis() - recordingStartTime
                delay(100) // 每100ms更新一次
            }
        }
    }

    /**
     * 停止录制
     */
    fun stopRecording() {
        if (!_isRecording.value) return

        _isRecording.value = false
        durationUpdateJob?.cancel()
        durationUpdateJob = null

        // 优化录制数据
        optimizeRecordedEvents()
    }

    /**
     * 开始悬浮窗录制模式
     */
    fun startFloatingRecording() {
        startRecording(RecordingMode.FLOATING)
    }

    /**
     * 开始全屏录制模式
     */
    fun startFullscreenRecording() {
        startRecording(RecordingMode.FULLSCREEN)
    }

    /**
     * 检查是否为悬浮窗模式
     */
    fun isFloatingMode(): Boolean {
        return _recordingMode.value == RecordingMode.FLOATING || _recordingMode.value == RecordingMode.FLOATING_ADVANCED
    }

    /**
     * 检查是否为高级悬浮窗模式
     */
    fun isAdvancedFloatingMode(): Boolean {
        return _recordingMode.value == RecordingMode.FLOATING_ADVANCED
    }

    /**
     * 添加触摸事件
     */
    fun addTouchEvent(event: TouchEvent) {
        if (!_isRecording.value) return

        val currentEvents = _recordedEvents.value.toMutableList()

        // 直接添加事件，不需要调整时间戳
        val adjustedEvent = event

        currentEvents.add(adjustedEvent)
        _recordedEvents.value = currentEvents
    }

    /**
     * 保存录制数据
     * @return 录制ID，失败返回空字符串
     */
    suspend fun saveRecording(): String {
        val events = _recordedEvents.value
        if (events.isEmpty()) return ""

        val modeDescription = when (_recordingMode.value) {
            RecordingMode.FULLSCREEN -> "全屏录制"
            RecordingMode.FLOATING -> "悬浮窗录制（传统）"
            RecordingMode.FLOATING_ADVANCED -> "悬浮窗录制（高级）"
        }

        val recording = GestureRecording(
            name = "录制_${System.currentTimeMillis()}",
            description = "${modeDescription}的手势操作",
            events = events,
            duration = _recordingDuration.value,
            createdTime = System.currentTimeMillis()
        )

        return try {
            gestureRecordingManager.saveRecording(recording) ?: ""
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 优化录制事件
     * 移除冗余的移动事件，保持关键的触摸点
     */
    private fun optimizeRecordedEvents() {
        val events = _recordedEvents.value
        if (events.size <= 2) return

        val optimizedEvents = mutableListOf<TouchEvent>()
        var lastEvent: TouchEvent? = null

        for (event in events) {
            when (event.type) {
                // 保留所有非移动事件
                com.weinuo.quickcommands.model.TouchEventType.TAP,
                com.weinuo.quickcommands.model.TouchEventType.LONG_PRESS -> {
                    optimizedEvents.add(event)
                    lastEvent = event
                }
                // 对滑动事件进行采样
                com.weinuo.quickcommands.model.TouchEventType.SWIPE -> {
                    lastEvent?.let { last ->
                        val distance = kotlin.math.sqrt(
                            (event.position.startX - last.position.startX) * (event.position.startX - last.position.startX) +
                            (event.position.startY - last.position.startY) * (event.position.startY - last.position.startY)
                        )

                        // 如果距离足够大，则保留此事件
                        if (distance > 0.01f) {
                            optimizedEvents.add(event)
                            lastEvent = event
                        }
                    } ?: run {
                        optimizedEvents.add(event)
                        lastEvent = event
                    }
                }
                else -> {
                    optimizedEvents.add(event)
                    lastEvent = event
                }
            }
        }

        _recordedEvents.value = optimizedEvents
    }

    /**
     * 清除录制数据
     */
    fun clearRecording() {
        if (_isRecording.value) {
            stopRecording()
        }
        _recordedEvents.value = emptyList()
        _recordingDuration.value = 0L
    }

    /**
     * 获取录制统计信息
     */
    fun getRecordingStats(): RecordingStats {
        val events = _recordedEvents.value
        val tapEvents = events.count { it.type == com.weinuo.quickcommands.model.TouchEventType.TAP }
        val longPressEvents = events.count { it.type == com.weinuo.quickcommands.model.TouchEventType.LONG_PRESS }
        val swipeEvents = events.count { it.type == com.weinuo.quickcommands.model.TouchEventType.SWIPE }
        val multiTapEvents = events.count { it.type == com.weinuo.quickcommands.model.TouchEventType.MULTI_TAP }

        return RecordingStats(
            totalEvents = events.size,
            tapEvents = tapEvents,
            longPressEvents = longPressEvents,
            swipeEvents = swipeEvents,
            multiTapEvents = multiTapEvents,
            duration = _recordingDuration.value
        )
    }

    override fun onCleared() {
        super.onCleared()
        durationUpdateJob?.cancel()
    }
}

/**
 * 录制统计信息
 */
data class RecordingStats(
    val totalEvents: Int,
    val tapEvents: Int,
    val longPressEvents: Int,
    val swipeEvents: Int,
    val multiTapEvents: Int,
    val duration: Long
)

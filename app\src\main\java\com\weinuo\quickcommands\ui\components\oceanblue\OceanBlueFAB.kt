package com.weinuo.quickcommands.ui.components.oceanblue

import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import com.weinuo.quickcommands.ui.theme.config.FloatingActionButtonConfig

/**
 * 海洋蓝主题专用浮动操作按钮
 *
 * 特点：
 * - 分层设计风格
 * - 使用阴影表现层次
 * - 标准Material 3 FAB
 * - 海洋蓝主题专有组件
 * - 遵循Material Design 3规范
 * - 清晰的视觉分离
 * - 适合复杂信息展示的分层界面
 *
 * 设计规格：
 * - 使用Material 3标准FloatingActionButton
 * - 支持标准的阴影效果表现层次
 * - 使用主题颜色系统
 * - 标准的交互反馈
 * - 符合分层设计理念
 */
@Composable
fun OceanBlueFAB(
    config: FloatingActionButtonConfig,
    modifier: Modifier = Modifier
) {
    FloatingActionButton(
        onClick = config.onClick,
        modifier = modifier.then(config.modifier),
        shape = config.shape as? Shape ?: FloatingActionButtonDefaults.shape,
        containerColor = config.containerColor ?: MaterialTheme.colorScheme.primaryContainer,
        contentColor = config.contentColor ?: MaterialTheme.colorScheme.onPrimaryContainer,
        elevation = config.elevation as? FloatingActionButtonElevation ?: FloatingActionButtonDefaults.elevation()
    ) {
        config.content()
    }
}

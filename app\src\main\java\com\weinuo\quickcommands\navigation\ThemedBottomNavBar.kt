package com.weinuo.quickcommands.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.weinuo.quickcommands.ui.components.themed.ThemedBottomNavigation
import com.weinuo.quickcommands.ui.theme.config.NavigationTab
import com.weinuo.quickcommands.ui.effects.HazeManager
import com.weinuo.quickcommands.utils.ExperimentalFeatureDetector
import dev.chrisbanes.haze.HazeState

/**
 * 主题感知的底部导航栏组件
 *
 * 这是对现有BottomNavBar的主题感知版本，
 * 将现有的导航逻辑适配到新的主题系统中，
 * 支持iOS风格的模糊效果。
 */
@Composable
fun ThemedBottomNavBar(
    navController: NavController,
    experimentalFeatureDetector: ExperimentalFeatureDetector? = null,
    hazeState: HazeState? = null,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    // 获取或创建HazeState
    val actualHazeState = hazeState ?: HazeManager.getInstance(context).globalHazeState

    // 将Screen对象转换为NavigationTab
    val tabs = bottomNavItems.map { screen ->
        NavigationTab(
            label = stringResource(id = screen.titleResId),
            icon = screen.unselectedIcon,
            selectedIcon = screen.selectedIcon
        )
    }

    // 计算当前选中的索引
    val selectedIndex = bottomNavItems.indexOfFirst { screen ->
        currentDestination?.route == screen.route
    }.takeIf { it >= 0 } ?: 0

    // 使用主题感知的底部导航
    ThemedBottomNavigation(
        tabs = tabs,
        selectedIndex = selectedIndex,
        onTabSelected = { index ->
            val selectedScreen = bottomNavItems[index]

            // 如果是全局设置导航项且有实验性功能检测器，处理点击检测
            if (selectedScreen == Screen.GlobalSettings && experimentalFeatureDetector != null) {
                experimentalFeatureDetector.handleClick(ExperimentalFeatureDetector.ClickTarget.NAVIGATION_ITEM)
            }

            navController.navigate(selectedScreen.route) {
                // 避免创建多个返回栈
                popUpTo(navController.graph.findStartDestination().id) {
                    saveState = true
                }
                // 避免多次点击创建多个实例
                launchSingleTop = true
                // 恢复状态
                restoreState = true
            }
        },
        hazeState = actualHazeState,
        modifier = modifier
    )
}
